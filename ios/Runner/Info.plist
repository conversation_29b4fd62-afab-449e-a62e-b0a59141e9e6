<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>FLTEnableImpeller</key>
		<false />
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Rooo Driver</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Rooo Driver</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb8206121312849982</string>

					<string>com.googleusercontent.apps.972284281345-csbm4ti46p9t2vmr2id07giekub8v368</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>


		<key>FacebookAppID</key>
		<string>8206121312849982</string>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>rooo_driver</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fb-messenger-share-api</string>
			<string>comgooglemaps</string>
			<string>baidumap</string>
			<string>iosamap</string>
			<string>waze</string>
			<string>yandexmaps</string>
			<string>yandexnavi</string>
			<string>citymapper</string>
			<string>mapswithme</string>
			<string>osmandmaps</string>
			<string>dgis</string>
			<string>qqmap</string>
			<string>here-location</string>
			<string>tomtomgo</string>
			<string>copilot</string>
			<string>com.sygic.aura</string>
			<string>nmap</string>
			<string>kakaomap</string>
			<string>tmap</string>
			<string>szn-mapy</string>
			<string>mappls</string>

		</array>


		<key>GIDClientID</key>
		<string>972284281345-csbm4ti46p9t2vmr2id07giekub8v368.apps.googleusercontent.com</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
			<key>NSAllowsArbitraryLoadsInWebContent</key>
			<true />
		</dict>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>Need BLE permission</string>
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>Need BLE permission</string>
		<key>NSCameraUsageDescription</key>
		<string>Rooo Driver app requires access to the Camera to upload your profile photo.</string>
		<key>NSContactsUsageDescription</key>
		<string>Rooo Driver requires contacts read permission to save emergency contacts.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Your location is continuously tracked even when the app is in the background to accurately match you with nearby riders, navigate you to pick-up and drop-off locations, calculate fares, and ensure safety during trips. This ensures you receive ride requests efficiently and that riders can see your real-time progress</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Your location is continuously tracked even when the app is in the background to accurately match you with nearby riders, navigate you to pick-up and drop-off locations, calculate fares, and ensure safety during trips. This ensures you receive ride requests efficiently and that riders can see your real-time progress</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Your location is continuously tracked even when the app is in the background to accurately match you with nearby riders, navigate you to pick-up and drop-off locations, calculate fares, and ensure safety during trips. This ensures you receive ride requests efficiently and that riders can see your real-time progress</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Need microphone access for VOIP call feature</string>
		<key>NSMotionUsageDescription</key>
		<string>Rooo Driver app requires access to the Camera to upload your profile photo.</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Rooo Driver requires access to the photo library to upload your profile photo.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Rooo Driver requires access to the photo library to upload your profile photo.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UIBackgroundModes</key>
		<array>
			<string>audio</string>
			<string>fetch</string>
			<string>voip</string>

			<string>location</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>io.flutter.embedded_views_preview</key>
		<true />
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>
	</dict>
</plist>