<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>665125955955-1tbnqgvcdujmuagvl18jkh3ed988v9rg.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.665125955955-1tbnqgvcdujmuagvl18jkh3ed988v9rg</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>665125955955-7q60j1ncht4olaa2rvidu4je5nb1d0mo.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCtFcK3Lbmx2lxEAk1mXsEpYG_KG0rqVQA</string>
	<key>GCM_SENDER_ID</key>
	<string>665125955955</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>app.rooo.driver</string>
	<key>PROJECT_ID</key>
	<string>rooo-321ce</string>
	<key>STORAGE_BUCKET</key>
	<string>rooo-321ce.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:665125955955:ios:bdc360560b3e92b11b3d06</string>
</dict>
</plist>