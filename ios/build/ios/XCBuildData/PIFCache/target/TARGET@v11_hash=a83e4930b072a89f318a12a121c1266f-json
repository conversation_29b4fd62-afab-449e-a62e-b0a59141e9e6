{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b97dfa1145e1d20be07dfac8a80998cc", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_express_engine/zego_express_engine-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_express_engine/zego_express_engine-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/zego_express_engine/zego_express_engine.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_express_engine", "PRODUCT_NAME": "zego_express_engine", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894cd0562671ecb22af0c10c1dca73fc7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98939d23bb87ac5462df47be674a7d3b36", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_express_engine/zego_express_engine-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_express_engine/zego_express_engine-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/zego_express_engine/zego_express_engine.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_express_engine", "PRODUCT_NAME": "zego_express_engine", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985252cf5997605f237cc5c59fc0ee2ab2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98939d23bb87ac5462df47be674a7d3b36", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_express_engine/zego_express_engine-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_express_engine/zego_express_engine-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/zego_express_engine/zego_express_engine.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_express_engine", "PRODUCT_NAME": "zego_express_engine", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981cc643caf9933b86f59120a057302f15", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98982efbcc14e7644721402d52c9089c5f", "guid": "bfdfe7dc352907fc980b868725387e9842f0646ce7f6af4bafd1de47849c0417", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98585621bfad785e136c877a6b99112819", "guid": "bfdfe7dc352907fc980b868725387e9835f9c5a1d774b8e92ded697f4141c592", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca9748423b7a923acca70989fe2b018e", "guid": "bfdfe7dc352907fc980b868725387e98a048419b8fc3d5845ba2d1189fbf4328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98096ad9e6adeed4f5e86ae7c808d802de", "guid": "bfdfe7dc352907fc980b868725387e9803b7c355f2819e14ee3e148f4776973e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980366cfb9eb372712479ac1042e64f45f", "guid": "bfdfe7dc352907fc980b868725387e9881f2d15e507f3bd0adc4b23357f93599", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba3d8a21f031f22c6c23fe9e1e94984", "guid": "bfdfe7dc352907fc980b868725387e98f356f09b8f70c6391f43e033218d1189", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5b80ebeff62693ed620871927ea9695", "guid": "bfdfe7dc352907fc980b868725387e98e710947d47832a6c573035c500d7bb2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829e4365bb2d680d13442008e7c61e86c", "guid": "bfdfe7dc352907fc980b868725387e98a04512c53e33f7d3408362d76dbe9ee2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3bac10f242ccf53fba23b0a027e64bc", "guid": "bfdfe7dc352907fc980b868725387e98c8fbdf1fb85218e1e3dc63ef2cfe1252", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982664a44cf20c034fa0cd3fb9cac5f673", "guid": "bfdfe7dc352907fc980b868725387e983464a0eb7bd598f60d3abddccee6aee6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d036af5ab666b0f4a0ac61e4b665c806", "guid": "bfdfe7dc352907fc980b868725387e98e51f2b1816f9b81294706f871a75cbe5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818f19bcced76c3e242395e889f4494c2", "guid": "bfdfe7dc352907fc980b868725387e98931b96a2b187dcd15fcf5502e3f82baa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98156ee5a55aa8280e5b049d1b5800c4f2", "guid": "bfdfe7dc352907fc980b868725387e98fa427346719a567fc7ce72a4a19c9619", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e211bed3ca4371dd876d35e4edfe7eb", "guid": "bfdfe7dc352907fc980b868725387e98d1671000e95c3bd00659d45894d3c176", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340fe6b0058074a6655faded7a2ed463", "guid": "bfdfe7dc352907fc980b868725387e98732fb55ada913b6fd2361c6936b1d1a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811c7347324a17f677f839760afcbceb4", "guid": "bfdfe7dc352907fc980b868725387e98562b87d6223e586836b8251a11f7483b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c7edcadd283a0836c653a546a14d3ab", "guid": "bfdfe7dc352907fc980b868725387e98c25604310b131bea023d38472261c7fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f070f14839a4cc5c5b9ae3d8ec1a2f3", "guid": "bfdfe7dc352907fc980b868725387e98624b1d2ee2d30920645df382d29e51c0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983c725de32dbe6694b416036b8001362f", "type": "com.apple.buildphase.headers"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "bfdfe7dc352907fc980b868725387e98f6ea9bc0a423fba9e82c7f64ed1e2771", "inputFileListPaths": ["${PODS_ROOT}/Target Support Files/zego_express_engine/zego_express_engine-xcframeworks-input-files.xcfilelist"], "inputFilePaths": [], "name": "[CP] Copy XCFrameworks", "originalObjectID": "696CB38CD9D744EB8E60DDA3F849A200", "outputFileListPaths": ["${PODS_ROOT}/Target Support Files/zego_express_engine/zego_express_engine-xcframeworks-output-files.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"${PODS_ROOT}/Target Support Files/zego_express_engine/zego_express_engine-xcframeworks.sh\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e985e84758d7cb08ea43f651981597e4b2f", "inputFileListPaths": [], "inputFilePaths": ["${PODS_TARGET_SRCROOT}/DEPS"], "name": "[CP-User] Download native dependency", "originalObjectID": "242ED056C3924B9CEBEBDBF43BEF9819", "outputFileListPaths": [], "outputFilePaths": ["${PODS_TARGET_SRCROOT}/libs/*"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "sh ${PODS_TARGET_SRCROOT}/download.sh", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f299c063e873a9c265121676a0751e4c", "guid": "bfdfe7dc352907fc980b868725387e9847e249c53d8622930e116f58751962a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840eb31c4ea5951fd749d1ad963bcb9e0", "guid": "bfdfe7dc352907fc980b868725387e9884f81c728955c132f489b89095ca3abd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9cfda7e9059cb0e61f4101134cc3cb2", "guid": "bfdfe7dc352907fc980b868725387e98755d81908077553a913d8e712fad79cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e9ff0906381d145cefefd06c5d5453", "guid": "bfdfe7dc352907fc980b868725387e98bda7b507f763933a378bc5697fee23d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a139c981f454f37cb68d2c7656a12de4", "guid": "bfdfe7dc352907fc980b868725387e982fad3950301ba0becd496dba44a643ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce4bf1ff0649eb2ddb0703420b99ddc4", "guid": "bfdfe7dc352907fc980b868725387e980ba20283fce16e648fd605d4678467ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de9c04cd62542aef263be2485980f82e", "guid": "bfdfe7dc352907fc980b868725387e98967e4f25bffa5d10123d5c6dd4e07d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98646950b0547d2316d01b8c2d076bd475", "guid": "bfdfe7dc352907fc980b868725387e98f6b3f0197c6bc4e80398636e30550645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984119e06ac064d134e15509771b4c8fd5", "guid": "bfdfe7dc352907fc980b868725387e982008bbf4344e69c081808b4a074cb1a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835722fd767dce90d7249541b35189572", "guid": "bfdfe7dc352907fc980b868725387e9871a24835990a25c00b9c2fa9da11382d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823546348e43c013a00acdfcdd31fa585", "guid": "bfdfe7dc352907fc980b868725387e98d9a41e84f682a057a3510e6c179af78c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2473bbcb604ab64a0450cc9b88c7fa0", "guid": "bfdfe7dc352907fc980b868725387e9871107708211e2b0f4a4efadaa381c673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824eb73b58fbe8971e3fc5570ba0a934c", "guid": "bfdfe7dc352907fc980b868725387e9831f20029091ddaeba63e6c9c612b84fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61be49b9b81b089ebbb10bf52998f0b", "guid": "bfdfe7dc352907fc980b868725387e98efb05c42b1c58e3a3f1cb23938fa05d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98962a7478f43db253a57d705599556cb1", "guid": "bfdfe7dc352907fc980b868725387e983f49b99ab9726b0a318c432138879a8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988085c2029cc72069bcb552b888da4da7", "guid": "bfdfe7dc352907fc980b868725387e9852ff9b681d31686258359006d04f8872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c25e850b6f5c9393c2fb02789e46ad4", "guid": "bfdfe7dc352907fc980b868725387e9854a427b0c490473898abaa3d5ec0b81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016fe6d7650364599730781fa20c37cf", "guid": "bfdfe7dc352907fc980b868725387e982b671e2a68b1c2f035f7015b758c692d"}], "guid": "bfdfe7dc352907fc980b868725387e98cd4105631366780c63c290a7f9afc965", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9831796dd643d6d4c1e76c7deebde55e1d"}], "guid": "bfdfe7dc352907fc980b868725387e98f038cc6537299b69480edab8fc4d0270", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9859da43a1ef863daf0b3a7eb7f1c5c48a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98e0dabaf2081d80624f8efb13f2fad9bd", "name": "zego_express_engine", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815f3c0fd2c89d60decf5911d3d98a96f", "name": "zego_express_engine.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}