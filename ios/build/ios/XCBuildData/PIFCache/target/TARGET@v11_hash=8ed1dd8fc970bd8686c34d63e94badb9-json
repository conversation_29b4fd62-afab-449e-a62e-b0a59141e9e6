{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983237dfb9b704503ccd3b5fd8ca2fa8d7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_express_engine/zego_express_engine-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_express_engine/zego_express_engine-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/zego_express_engine/zego_express_engine.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_express_engine", "PRODUCT_NAME": "zego_express_engine", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894cd0562671ecb22af0c10c1dca73fc7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888e6798a945099b290dfcbc914647231", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_express_engine/zego_express_engine-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_express_engine/zego_express_engine-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/zego_express_engine/zego_express_engine.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_express_engine", "PRODUCT_NAME": "zego_express_engine", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985252cf5997605f237cc5c59fc0ee2ab2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888e6798a945099b290dfcbc914647231", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_express_engine/zego_express_engine-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_express_engine/zego_express_engine-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/zego_express_engine/zego_express_engine.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_express_engine", "PRODUCT_NAME": "zego_express_engine", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981cc643caf9933b86f59120a057302f15", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986eff1b78907b83de61016d8c65163ad3", "guid": "bfdfe7dc352907fc980b868725387e9842f0646ce7f6af4bafd1de47849c0417", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f26d6b8f852bbc928631b9b762011b3", "guid": "bfdfe7dc352907fc980b868725387e9871536144ae5d67a4dfe437748085de0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eca7f4c964e0cb5ee2e3dc533d835532", "guid": "bfdfe7dc352907fc980b868725387e98a76301bf12afd1da665dbf8e50982792", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98412d2c9eb5fffdb00124214090fdf644", "guid": "bfdfe7dc352907fc980b868725387e981539460398692c9fe752c541046cafc6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a3def9277210a51a08cb46b9cef0c71", "guid": "bfdfe7dc352907fc980b868725387e98d6ac86ba08a2ad564a0d50fb5486f86d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812eee6e43d568c8f6f7c58efe1e04d04", "guid": "bfdfe7dc352907fc980b868725387e98d233f1f7438eb8ce8bee971dd437cab7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5fa2084dd2b9fe213c0a8efcb267f1f", "guid": "bfdfe7dc352907fc980b868725387e986c48eb61a3e22156475b852871f6eac2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f848dec2889800e1dffcbae7a61c1406", "guid": "bfdfe7dc352907fc980b868725387e98bfa011c0da9352c9626966eba37c0784", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98990f9557470da7b6d0d91ddd5f41feb2", "guid": "bfdfe7dc352907fc980b868725387e986542b78669d87229df7ce2d34002518e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd0ebf04e148a8124360815e565394c3", "guid": "bfdfe7dc352907fc980b868725387e985db10f58795b3557451d3fd8e3d03bd1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983962a3e43ebc4c52158ca189ee2876d4", "guid": "bfdfe7dc352907fc980b868725387e98988109174b9fefed1706e9af531a4929", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98521b4bba19dcfd81175991d0d4e34eba", "guid": "bfdfe7dc352907fc980b868725387e980de21a9714a6d8c1896ae75d2e079d32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98125481ae8ef1626a50bab076fc4411e3", "guid": "bfdfe7dc352907fc980b868725387e9825a90ecbe008ea87f85ecc56573c009f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884df57db630e087e1eb6f57494992335", "guid": "bfdfe7dc352907fc980b868725387e985fa8f69b24327ff367c301325c9ce11b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dc235979d13c5defbbf7fbe9704a7fb", "guid": "bfdfe7dc352907fc980b868725387e987af003bfab430b139dc10a06ebdd90a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e826f9cb89ccf9bc31b5f007f090016", "guid": "bfdfe7dc352907fc980b868725387e9875c7141feb0683423d838aad0b662097", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985431bbf57e7e0e936f78c95a3807af26", "guid": "bfdfe7dc352907fc980b868725387e98a5ea11a1726663fe2801784cc70d591b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ac39657dcfcbc5c9da8e057bea187ba", "guid": "bfdfe7dc352907fc980b868725387e98e1c9b99eb1e48bef5a3797a0403a14f7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983c725de32dbe6694b416036b8001362f", "type": "com.apple.buildphase.headers"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "bfdfe7dc352907fc980b868725387e98f6ea9bc0a423fba9e82c7f64ed1e2771", "inputFileListPaths": ["${PODS_ROOT}/Target Support Files/zego_express_engine/zego_express_engine-xcframeworks-input-files.xcfilelist"], "inputFilePaths": [], "name": "[CP] Copy XCFrameworks", "originalObjectID": "696CB38CD9D744EB8E60DDA3F849A200", "outputFileListPaths": ["${PODS_ROOT}/Target Support Files/zego_express_engine/zego_express_engine-xcframeworks-output-files.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"${PODS_ROOT}/Target Support Files/zego_express_engine/zego_express_engine-xcframeworks.sh\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e985e84758d7cb08ea43f651981597e4b2f", "inputFileListPaths": [], "inputFilePaths": ["${PODS_TARGET_SRCROOT}/DEPS"], "name": "[CP-User] Download native dependency", "originalObjectID": "242ED056C3924B9CEBEBDBF43BEF9819", "outputFileListPaths": [], "outputFilePaths": ["${PODS_TARGET_SRCROOT}/libs/*"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "sh ${PODS_TARGET_SRCROOT}/download.sh", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986ec2a31042351361ef53b2b8735b3218", "guid": "bfdfe7dc352907fc980b868725387e9847e249c53d8622930e116f58751962a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c5eaca157962fa16a9b487ab7a1949", "guid": "bfdfe7dc352907fc980b868725387e985d5e319eeeca590b65c6a206432293d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98445b8b1f7713c4a1b154f2af46eecc24", "guid": "bfdfe7dc352907fc980b868725387e980aa93facfcf1cc27f44bc3353cf781f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98227c9d85ab5b995e67d09c20327d0f88", "guid": "bfdfe7dc352907fc980b868725387e985a46e35652b96149e20894a44f75125e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e64873c5776363904420d45b2fcf7c8", "guid": "bfdfe7dc352907fc980b868725387e98118ff7683374732be95bf0e10ff1d4d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98089f8bd00f76ee0b675023bbe601eeb6", "guid": "bfdfe7dc352907fc980b868725387e98104a4dce6f704a9ac8c28371539f3fb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4597ab290c329fbb483747cfb4e5487", "guid": "bfdfe7dc352907fc980b868725387e981ab1f3d87a58cd116a5c2a3ce52cc9fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1ea709901eae12bc12db2888aa2a44", "guid": "bfdfe7dc352907fc980b868725387e98e9913eb5b73841be925893d97c672976"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982527825a744afd77bc2fc22867ffaf3b", "guid": "bfdfe7dc352907fc980b868725387e98692c7028d86f0d67fb5c76452b058e03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a939f75c18096c2e5cf7b808449d2f5", "guid": "bfdfe7dc352907fc980b868725387e98cbf7b80f309dc12dbb1d7105a6b20499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be9638dfa5f8228f0f8d62ff5b374ea9", "guid": "bfdfe7dc352907fc980b868725387e98d91d88892b46b919c3711497aef8ddf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986661c13450a23fc73e147d02fe1dec14", "guid": "bfdfe7dc352907fc980b868725387e98bd2e84000f01106f552e29b924babde1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985362c29160034ed795646d6c5020908f", "guid": "bfdfe7dc352907fc980b868725387e9882f3105a6f9386d016987bb8c4b54669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e7d245098c4e2249a095caf6efc0fc1", "guid": "bfdfe7dc352907fc980b868725387e989c7442501a7c7c7e317e1c0866e769ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98390f26bea7535b2c9e1bfdb8e82a82a5", "guid": "bfdfe7dc352907fc980b868725387e9874e192edb0b909e7117f76c1ac88aa47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824bfd6d9b768a6d2352851bb06ae936e", "guid": "bfdfe7dc352907fc980b868725387e98ac910b36ed72a8b4dee6e0f039e50a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e605467cff55dc666f3ca0b36eca38ae", "guid": "bfdfe7dc352907fc980b868725387e98991da954eb8e8fe42568e8021fa2a06e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc3dc0c3f7faf9090ef52fb9b0f891c", "guid": "bfdfe7dc352907fc980b868725387e98c02f2d7fc1ec23a70a1166d3ec5d29a4"}], "guid": "bfdfe7dc352907fc980b868725387e98cd4105631366780c63c290a7f9afc965", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9831796dd643d6d4c1e76c7deebde55e1d"}], "guid": "bfdfe7dc352907fc980b868725387e98f038cc6537299b69480edab8fc4d0270", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9859da43a1ef863daf0b3a7eb7f1c5c48a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98e0dabaf2081d80624f8efb13f2fad9bd", "name": "zego_express_engine", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815f3c0fd2c89d60decf5911d3d98a96f", "name": "zego_express_engine.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}