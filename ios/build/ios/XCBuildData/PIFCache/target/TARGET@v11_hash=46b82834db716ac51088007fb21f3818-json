{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868cbd3e69105734246dab0b35d140b19", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_callkit_incoming_yoer", "PRODUCT_NAME": "flutter_callkit_incoming_yoer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d05ee4b8bc791ae8b7abd27e84791376", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983df328ec76cfeca7e166b80dc6f43ea5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_callkit_incoming_yoer", "PRODUCT_NAME": "flutter_callkit_incoming_yoer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839ec054f298d21cc442f968ba3179188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983df328ec76cfeca7e166b80dc6f43ea5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_callkit_incoming_yoer/flutter_callkit_incoming_yoer.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_callkit_incoming_yoer", "PRODUCT_NAME": "flutter_callkit_incoming_yoer", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98997b85ec9d62b6472fb4c44d7ea756c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98674c6b3edf54ee2952b8c8160e7b3ec5", "guid": "bfdfe7dc352907fc980b868725387e98a3cd2b4b9081db11572e594b999a251b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fd96f1e0e9e6464e4b1648999ec7c4d", "guid": "bfdfe7dc352907fc980b868725387e98dec7bf731fd00c280bfa2cc3b96c5377", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981e55ad730523f13d0054eff2a081e5e6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896423e93df65f01c65eb81d3ca61a101", "guid": "bfdfe7dc352907fc980b868725387e986c38a125da1e54960e569c1dde925be2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98512b2d7b8f7948de28eb6cc12bc8bc87", "guid": "bfdfe7dc352907fc980b868725387e982cf20d5a4238b17899f7a3473866e157"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be1e1d02e751b9fd4e71043ed605a85", "guid": "bfdfe7dc352907fc980b868725387e98fe57064f5ef6d283be5e94be4e247953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880fd65c4ced7335207bad1ac3e434570", "guid": "bfdfe7dc352907fc980b868725387e98411428e98512790d5c0aac53eba9365d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986364a69d38085950f3d947d97f23c323", "guid": "bfdfe7dc352907fc980b868725387e983624b5e2875bedaa45b290cf2bfb0ce3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51bbd3748d0e912684c683bac1842ad", "guid": "bfdfe7dc352907fc980b868725387e98a36c3215bb9b16cef2ef64b2540ea9f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3b325bc9e32c7e17ad4f6320e3dbb21", "guid": "bfdfe7dc352907fc980b868725387e98bdb2e02f4914a6aca4a4caeafadc176c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813cadf561597e93b1c88d5c2e0d18df0", "guid": "bfdfe7dc352907fc980b868725387e98a994c6d0fc065894da4fcba8a959fc6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98140f657d9460a41a69348010f81b9baf", "guid": "bfdfe7dc352907fc980b868725387e98f00174d9ed370c58fb7a4bbd15e2120f"}], "guid": "bfdfe7dc352907fc980b868725387e981e5bb097c0c864226eea287902735aa4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98af5b670756ec3ca22c6492fb8c54fcaa"}], "guid": "bfdfe7dc352907fc980b868725387e980fcfac36225422138c00634e39350a37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98adfbc247b9171cb4301bca3638a1938b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981c0768badcb962e4e9f1efa0f12f70a9", "name": "CryptoSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e983ef179a5373bc7706869e99d4489da64", "name": "flutter_callkit_incoming_yoer", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981fbe5164b0e7c612885e00d707407be2", "name": "flutter_callkit_incoming_yoer.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}