{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb3464411f1676641396129cf38c9717", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b0491debad13020a165ab8d8fb8e7a6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810cb150c594b1344763797e43cf85015", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5d17fb1d91f0a4f64158124b05fa286", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810cb150c594b1344763797e43cf85015", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d48975f249f96592ebac62cabeb71693", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98128e2abbafc8a1bf7b6979faea8175d0", "guid": "bfdfe7dc352907fc980b868725387e9883c10ad6045e2edb3b45c1c6c0413b92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a4fdc38bf986b19d561fd705b949d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98782482d4e7fd8a2e3d3e59e0d417eabd", "guid": "bfdfe7dc352907fc980b868725387e98fffc9a4d9e96fb8b4119c90c4eacd262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d42a91569f578c1e7cdcc79ba7ec567f", "guid": "bfdfe7dc352907fc980b868725387e98fbf4e0dd73429c245efc16a9fbb485ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c9923da717302c26ecbce4882dc25d7", "guid": "bfdfe7dc352907fc980b868725387e983f9371b33304b54095914bef06d86e34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e22bc92728e8a33a7a0f623949abfe", "guid": "bfdfe7dc352907fc980b868725387e9892c0cda3786a8935cb19803eba04ca61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834053269cccc9eb370608b147f1c9abf", "guid": "bfdfe7dc352907fc980b868725387e989c19c287ffac11cab63504df80860c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e169c368ba51e045f6b87170698ac45", "guid": "bfdfe7dc352907fc980b868725387e98d3459a198c67f6523f5082682c21247d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ed0137907f81c9c2f3c19388089a92", "guid": "bfdfe7dc352907fc980b868725387e98338317a7adff47e613a8f252d9068304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd6d0ad673db8b072ab824be628b344a", "guid": "bfdfe7dc352907fc980b868725387e985c3a6b77cc4594cd656a1fa6f7bafc61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1999a771e7f245ac87b34d4dd20697b", "guid": "bfdfe7dc352907fc980b868725387e98de12194520ac87660a877ce2309b60bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885f78e6a6062b4ac7fab2b022c4bb869", "guid": "bfdfe7dc352907fc980b868725387e9881be7c10a0033d5520170c6edb94e120"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef43853088938c8f329aa1eb7f4340f9", "guid": "bfdfe7dc352907fc980b868725387e987466aef4b1e68967ae4c1da6ed9b0f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc4c3a31c467cbc635fde2a1aeb70f44", "guid": "bfdfe7dc352907fc980b868725387e987b53ec7c57a5d56a369259eb456771f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f07c4821449c317447d5a35da9198bfd", "guid": "bfdfe7dc352907fc980b868725387e98957d4e5821e70f403db776d890e37387"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ea1feea55fdbde23bd57c5a108a2f4", "guid": "bfdfe7dc352907fc980b868725387e98a153e78414d0801fc8912546b343e22a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981149a7c89fd64902c53660c3cea9bfcd", "guid": "bfdfe7dc352907fc980b868725387e98401b96db8ff97f6854b4ac2857be5ef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f4b88eb9e937e66bc73e429be81d458", "guid": "bfdfe7dc352907fc980b868725387e98094cf6999406005330b09ed8c6e18df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c189d380bd8d41957438c450f6e6e4e", "guid": "bfdfe7dc352907fc980b868725387e98b80b5ee515683c8473d68021ff8183fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989336b1470b8cb8b0b81e3f1a74141f36", "guid": "bfdfe7dc352907fc980b868725387e98525b0a161a68fc2b417eb1e244ccca54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981167d3ae9db235e686a8041147b3021d", "guid": "bfdfe7dc352907fc980b868725387e98f3d0239cb14081e59e3d63d12dc09941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ebfab8704aaa650b4c6ff3a9da89482", "guid": "bfdfe7dc352907fc980b868725387e98aad87b212645a346faacb095af033664"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a18a16c370b5bc39f38a8a1d6877992", "guid": "bfdfe7dc352907fc980b868725387e9857ebf1d4479f0790db0f57fb157237dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfcefa6ae9738dc48aad9d740cfafb89", "guid": "bfdfe7dc352907fc980b868725387e981ad364f72cab94ea1a4b0728d3111068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887a7e7c9fdfd80e1bb63925a3409ba32", "guid": "bfdfe7dc352907fc980b868725387e98738c3d88df8998bf875bfb06c61f50e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd0078c87ca6984016ccd9e991342cc", "guid": "bfdfe7dc352907fc980b868725387e98c85572dee130fff7d58af5835cf81e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b960f38876502936647d3a9f0f0139ca", "guid": "bfdfe7dc352907fc980b868725387e98a935478d82a3f41a22d346e6e910294d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083c503680a47b9f1cb3de254c20ae8b", "guid": "bfdfe7dc352907fc980b868725387e980f6d816fd97292083a3be26ab524d5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e7b3155378c71f1f0ae5f960645d7a5", "guid": "bfdfe7dc352907fc980b868725387e9874a5b6ebd9d705d149f34dae7a2dbb91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899a8fd129277cbab65038d4225e54289", "guid": "bfdfe7dc352907fc980b868725387e9864263e3a1657f8883d339bcb3ee4ea53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a9f475106fb52fa6e7b86378a47c098", "guid": "bfdfe7dc352907fc980b868725387e98b9fe76e5db012fe46e25b594281b60cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acae4399a82ff23491775c0f863ed6cf", "guid": "bfdfe7dc352907fc980b868725387e98345998f7e90249a902acb261b09ac2a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898de72706c2eb8159b29ba1068c004b7", "guid": "bfdfe7dc352907fc980b868725387e98f8027c783d6add8a06c7ba0ec4517148"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddac0031e26a6b0af318d30c65f664ca", "guid": "bfdfe7dc352907fc980b868725387e9849859b32db184889cf6b02b859f2c618"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873af4772f246ab3b294f2b73b31af786", "guid": "bfdfe7dc352907fc980b868725387e983c2d82c6d2674900e12e8653b9dde8ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823db7f9360051f36169c47355ab37769", "guid": "bfdfe7dc352907fc980b868725387e981c4bd8d279e2891906d36301438a9802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de62207cd8b03e890c10adedf03bed92", "guid": "bfdfe7dc352907fc980b868725387e983eae5f062665f09bc134202c53868dc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98679fead71aa067bacf707d42177ba4d1", "guid": "bfdfe7dc352907fc980b868725387e980c75f1ef782aedb7e87398c72432a5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e51b4980e374abe599bdd9faf133b3b", "guid": "bfdfe7dc352907fc980b868725387e98de061dd64641379142e5fb5bc78716d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983829a964d970358124925c0d72c6f2d1", "guid": "bfdfe7dc352907fc980b868725387e98ba72fe09cfd1d37e665fcf5a9472ee95"}], "guid": "bfdfe7dc352907fc980b868725387e98579fbecb47bc990731107675682637c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9817874d657b3f6cd3a4f0acf89676b8a6"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e0578284590adbf8b73f59c73cb7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acb6d21f8b6048502a790d59f4fc84cb", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98be611f6cbf3440ae2e558dafe4f7c459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}