{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9854fb5925b68f592206163007f0ecf9c9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844ab2e60de7e7e784a6d05bd93864648", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844ab2e60de7e7e784a6d05bd93864648", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fa4be4bf41b9f3ed2a58d1333c09f843", "guid": "bfdfe7dc352907fc980b868725387e983ee766c010960aeda0478990128b3391", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac661d75e8e144f1d83986fa580eeb87", "guid": "bfdfe7dc352907fc980b868725387e980cc5f532b3bceb239025cb2a468037cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb2700ca5627910534a2e0ffee55313", "guid": "bfdfe7dc352907fc980b868725387e982b7b4c514c2955c192b846e336a4f441", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b52c95e9410d1ce4575abaf92e73b5d", "guid": "bfdfe7dc352907fc980b868725387e98f05b8af8b03ed439ef7358447b6f87ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988213560d8f7d62da3f8a482785c24060", "guid": "bfdfe7dc352907fc980b868725387e98dcaaaf8bb7301dfc82fde60afc4208e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79defc879decc34b3a943dc4a217681", "guid": "bfdfe7dc352907fc980b868725387e981f62e1a85bb0b6f7363bf896e4370edd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335fc91a34115039f8fd1f2d316e401d", "guid": "bfdfe7dc352907fc980b868725387e989f7df4c24ef371c2b0abe2b0e91fd6e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da658582a0829d38bab3ab7eea178932", "guid": "bfdfe7dc352907fc980b868725387e98a22d2dfb2cada2301046f3d150152522", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f45017d839d55b756733d23ae59e6b3c", "guid": "bfdfe7dc352907fc980b868725387e987840d848d1c88878c3891c4686940bf5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98141756abe4a9230e4d881e752e03c3a8", "guid": "bfdfe7dc352907fc980b868725387e98ff56723f3b99bdce51f230633182aabd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988037e1ea2409fa2d662cc52cc74a5078", "guid": "bfdfe7dc352907fc980b868725387e98909a2d2619aa26809a0a744d98739995", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe664c5df0f5a50e6e8d2474d41cb2b2", "guid": "bfdfe7dc352907fc980b868725387e98f4129f53571b9d74695df15b159b95be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896c4ee3817f33c8f4f1e19dd34f8ed0e", "guid": "bfdfe7dc352907fc980b868725387e98a1b668ceab70de76715827732da13727", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af25efeeffdc637ff9932dafaeb92118", "guid": "bfdfe7dc352907fc980b868725387e98c5cb2aac4ddc5f6290b89677f5951dbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7fbd1a74b8953630b9e8d87fec12c05", "guid": "bfdfe7dc352907fc980b868725387e98085384f64d52999b9333c2892318257c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b81223f8ab41bec372e31e9d3e3a7e0", "guid": "bfdfe7dc352907fc980b868725387e98a49da54db2839713efb4fae6021fdcc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b54ceb55329d6bcea7b9831ca7cba052", "guid": "bfdfe7dc352907fc980b868725387e982ce3825e8bcea83378afe121cf2b5779", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c39e0dea9403bacd83ec8d16cc69513", "guid": "bfdfe7dc352907fc980b868725387e988382b90026367cb27318351a19b644dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc6e15d41eb497342941fbbcfea4ee3d", "guid": "bfdfe7dc352907fc980b868725387e983d7db809df3ed22140ce906638918597", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b82e252a72d2b52351dc4047260507be", "guid": "bfdfe7dc352907fc980b868725387e98c2612fa078bd1e0f8fdceba60ed4dfd2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9826f067badae33bdf46a7d973c820bdbb", "guid": "bfdfe7dc352907fc980b868725387e988a3172c16de6576eebfba3e57b180321"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d350db5e95a1d79dc34a6708dbf4d83c", "guid": "bfdfe7dc352907fc980b868725387e9899af1588dab42a6bdb7d59a672315c0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c71fe2bdbaf9fb928804b91dce71e7c6", "guid": "bfdfe7dc352907fc980b868725387e98bde205b7ae728ffad61b8139fc09965d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d36d1c5724689c9cb56f877167e4ebc", "guid": "bfdfe7dc352907fc980b868725387e988d3c174b73b89821381d760d142f93bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828eca290b593e5b1557b3ec1e6e0f13a", "guid": "bfdfe7dc352907fc980b868725387e9839c3321c7a986163c6de73053faf729c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9d26291eab5aff23c02a5d88f36fdc4", "guid": "bfdfe7dc352907fc980b868725387e986c1a070aeaf6f6438c533a3743f53c60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b9ca1c73120e2d1634d56320cf1e008", "guid": "bfdfe7dc352907fc980b868725387e981cb9ff1d04fcc512f32670b3782a2333"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d23f49c0dff64107001ca303a7ea984", "guid": "bfdfe7dc352907fc980b868725387e989d5d368afbf88fcc5a75f2f1d797681a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878db7b3260d21f6e23ad7bce85d8c83d", "guid": "bfdfe7dc352907fc980b868725387e988f1e9b91df0425d2e36d1b138636cc6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981abb94746f8371adb0c21c2e7abb2b00", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0bd7a9883c6cf7703e3a3b86c997928", "guid": "bfdfe7dc352907fc980b868725387e98525321fdf903d06b39e72dd580e59f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ceed5eca15ad92b00a40eafe663abe5", "guid": "bfdfe7dc352907fc980b868725387e98f7ff008514bbb1fa250de04a4a80c3e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885bff385920c8e3aa687033edf97b12b", "guid": "bfdfe7dc352907fc980b868725387e980e257a4fd2eec366edb8abb8d82aa515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d02d25a1b04a20084e593147140aff39", "guid": "bfdfe7dc352907fc980b868725387e98c0e2c4f6673ca9ea1a92412912c8e9a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6bd84a6ede84de8f02c8885cbed03a4", "guid": "bfdfe7dc352907fc980b868725387e98681bf22c866b73a64e39926577c75208"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980006161012b375f039fe41c8194cbb36", "guid": "bfdfe7dc352907fc980b868725387e980932d2bfdf3f8860939b0d981b82c5a1"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}