{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aac6625b2d9c9730e25c7d184ea074f0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98822afe3462012dfac0b3db84e5c2c91a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98822afe3462012dfac0b3db84e5c2c91a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf49a66953727e8e6e005d15f5812171", "guid": "bfdfe7dc352907fc980b868725387e981c80b1aed9ea61b4bdfa2682393b1822", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bef9e4bdce17a8310d355abc0cbf5bd", "guid": "bfdfe7dc352907fc980b868725387e98dd93c8e9a69d7330c101c7eb5903f5af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988871abe17fcb7cb506d23acc0e80ce25", "guid": "bfdfe7dc352907fc980b868725387e9855a23697b102025fb93a72422f191f5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e208fc2604cb8f566743030539c99835", "guid": "bfdfe7dc352907fc980b868725387e987029a8515732bab4879453564b0ccd86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e35516f7f8f851175ff48271cc7c493f", "guid": "bfdfe7dc352907fc980b868725387e98ec140dbd734a0fdd2dcfd4c6c8663513", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe66824a902e922b1638529602d3917", "guid": "bfdfe7dc352907fc980b868725387e98f1da7c189d76f568081d6e52979e2979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674eee70b1caf5204d643578e353f12f", "guid": "bfdfe7dc352907fc980b868725387e98f9352a0c063bcd57ac0ce1b6b70fc943", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa9f3d945f7dee86a404652fa221548", "guid": "bfdfe7dc352907fc980b868725387e9803a34c4e4aab5ff267205b8cae98b707", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a8c888808a976d06bd87f468fe9d16", "guid": "bfdfe7dc352907fc980b868725387e986c7fd18c28a701b0eee626f006c28a89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982151534dec6a4bd57738339e3c4e112a", "guid": "bfdfe7dc352907fc980b868725387e987c9eda89583e367dd8aad82d627f0828", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beec2eb2928abfb44686d1d8bf506351", "guid": "bfdfe7dc352907fc980b868725387e989ed323e71cf5dcf71a74f56108fa1ab5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986933e5e9df58d2cf1e63f6e9c73b66e3", "guid": "bfdfe7dc352907fc980b868725387e98e1d93f7d29e5a4818b34cb77c1a9e0f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98098cb892147eaf5a25c4bfad67914a0c", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981398a2718e25ce56c93391bffae074d7", "guid": "bfdfe7dc352907fc980b868725387e98e782185ab2b828ef2d4e39e33a094ab9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c776623a40af76f74bd515a4f93c4b58", "guid": "bfdfe7dc352907fc980b868725387e98609cb6e0496ee0ae9feaea0dd4e301b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a9cfec7835710a714b92bc6f27cc75", "guid": "bfdfe7dc352907fc980b868725387e986ec53d6c3a7da9d6c4b877654a53e492", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805304af7b033c85fb160b0b3d5bdc1bf", "guid": "bfdfe7dc352907fc980b868725387e9812cea8bb4cd6d45ebd46bd6f3e51015f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa6158367bdd45f9ab83af6beaf1279", "guid": "bfdfe7dc352907fc980b868725387e98c424752b4841387e11f8c9b094f9d40b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890b1a43840ec55b6d7e1db234940e4fc", "guid": "bfdfe7dc352907fc980b868725387e98f50037cc2253234c688fef565bcc7ef3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98271c834de84e4d48d48f47716e41cd3b", "guid": "bfdfe7dc352907fc980b868725387e987d9f8933148c05004460eab2dd43b87e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986098f7473183cfffe212c5f78b31da82", "guid": "bfdfe7dc352907fc980b868725387e98e81ac65ea8cc78e5c5e9f5b0e2608c7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbfbcf68b698a83ad4619ea1481cdd2b", "guid": "bfdfe7dc352907fc980b868725387e98b65b4e294015352d2eb01ef9541b8a1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98970734c5cd61db440fa2f7fa0b3d3abd", "guid": "bfdfe7dc352907fc980b868725387e986a24b6c989b3dbe18a55623e6ca69723", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985785b85ba5f530c62f66d6831b9c0862", "guid": "bfdfe7dc352907fc980b868725387e98598eeea3e9320c79460b2b77867fe68e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbc5ececee3b514594889b1a9885ee0e", "guid": "bfdfe7dc352907fc980b868725387e986002824cd08dce22f72ba90a1e3adf98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd129b3bc50ea0e944e9ffe61b58e643", "guid": "bfdfe7dc352907fc980b868725387e989455a1e619faa7dfbce4cfc5ace6eaab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a559efc0234aa3fce5a33fd4738150c0", "guid": "bfdfe7dc352907fc980b868725387e98b3fe8f49a074019b14073a06c8b09607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbe8759fd828872bf2ddfa258b335444", "guid": "bfdfe7dc352907fc980b868725387e98d27db33baf6f00010d6c00f6ac011cb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6936a2aacbe5be92e98f97e5a699ff2", "guid": "bfdfe7dc352907fc980b868725387e988add6e2634723ce378a6772d9b8e9008"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2968606f90f6321cfb9fe140d2e891b", "guid": "bfdfe7dc352907fc980b868725387e982de3c1823d27318b4392da0faedb56ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5bfec88898b8941d0a83fe441238c41", "guid": "bfdfe7dc352907fc980b868725387e983e5d823f38a040225e281bf23d0eb589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f202fa088b8b77750a6ba6439575c577", "guid": "bfdfe7dc352907fc980b868725387e984b22d083cc4324e8867fd30d35ea7b29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98975d8c3fcc4378bcf593fd8b715078ad", "guid": "bfdfe7dc352907fc980b868725387e985f136772974c7d64c05da05e36fca520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1ade15a0a97a4185332807f36afe7cd", "guid": "bfdfe7dc352907fc980b868725387e989a762dbdff7464d366f41d8b048882ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96358d03ce1e37355374d32f742eeb8", "guid": "bfdfe7dc352907fc980b868725387e988d2526f581ac12165ccef48048c006ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a49a305b5b04c7d57d2ad4f77e50a83e", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f117553bbdf8d68a60ca33b1198e24f7", "guid": "bfdfe7dc352907fc980b868725387e9835e621543c349c7e73f36567c33c08b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e31287cbcea33ac4455c19789ab4f19f", "guid": "bfdfe7dc352907fc980b868725387e981b0db355ed5212127eba406aa4eba8f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bec020e9ca227a838a55df30d031356", "guid": "bfdfe7dc352907fc980b868725387e981967bd80f144fcad8682bc5f2a1f79f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8219e41adf53535180ff45cac002a2", "guid": "bfdfe7dc352907fc980b868725387e9843486162a48b47ef5160af11148d7233"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886a2fdcd7e8772d114a009c7baebabb4", "guid": "bfdfe7dc352907fc980b868725387e98b154fee533e2ba1c4cb2300d49e3f6dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345fcc3f69129fe731447a6b2d655744", "guid": "bfdfe7dc352907fc980b868725387e98c9a63c0e293ddfe788b85649ceb935f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e162ad0f0745695a9584eff477cd245f", "guid": "bfdfe7dc352907fc980b868725387e98b3deadf21cb05439797bdcfd0270a3a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd68c9dfb64ac06a62591b6e3b975da", "guid": "bfdfe7dc352907fc980b868725387e981c87fc23a7cf8c788ba994de6d482bd0"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}