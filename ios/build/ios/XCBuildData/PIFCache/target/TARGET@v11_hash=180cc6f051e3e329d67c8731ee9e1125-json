{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d361e06e1579f0170be675939b049f80", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mapbox_maps_flutter", "PRODUCT_NAME": "mapbox_maps_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9825d5211bc42c875280c09caf2ea4615c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b4781e7a76b4e9d55ec3be5168cb0af", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mapbox_maps_flutter", "PRODUCT_NAME": "mapbox_maps_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cab12179d974bd02a1f7e28ea0a4d6b4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b4781e7a76b4e9d55ec3be5168cb0af", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/mapbox_maps_flutter/mapbox_maps_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mapbox_maps_flutter", "PRODUCT_NAME": "mapbox_maps_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98777c155217768e0c0ff4acd91573cb7a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803782033052b18323ebacd06fd24fba9", "guid": "bfdfe7dc352907fc980b868725387e98c6dd1251387a696d37581137d6190a45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cc973386645e3373c95208b566e11e1", "guid": "bfdfe7dc352907fc980b868725387e98bbffac30d75d748b07b8291aeb19db11", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b98b8ad69a7c2f9830bdf637ad25c919", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986dc418810cea99f2d8c865bc15fc5cf1", "guid": "bfdfe7dc352907fc980b868725387e984c037d5c5f407c3e345b7e2298202685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880f0a7ffaf8f654423327c9f53ba92a4", "guid": "bfdfe7dc352907fc980b868725387e98b9070a81178281d896a3a6bcf31f7647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee738a9861eecda914b9aa0f0ab0b6f", "guid": "bfdfe7dc352907fc980b868725387e985dc0de763bd8076a372ae15d2046b041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3f113dbb8f0f7c6a0516c40bc062270", "guid": "bfdfe7dc352907fc980b868725387e9868e75b8d951ed0a040f971ee5420a15c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f797c654bb5685d09e9079f7323e47f4", "guid": "bfdfe7dc352907fc980b868725387e98a816b72dca4582440b2d7dca30e5ddbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b215c3816ce2d2ca99da9d113b5759ed", "guid": "bfdfe7dc352907fc980b868725387e98913c4ca1018893284c8290548d9b9975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a730c0d4914d2d3c7e557a24719c7d1", "guid": "bfdfe7dc352907fc980b868725387e9858d00ac5dc9186b3ef613c2e63c83464"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988452ab8c9f395622f9f8c056d74b0c9f", "guid": "bfdfe7dc352907fc980b868725387e980bd0b48d2ff6681ded41eeff5263994c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d47fde6363ff4ac7711b22bee085e248", "guid": "bfdfe7dc352907fc980b868725387e98c553fc85e0c63932dbc8413f3b18c511"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12631a0b4b402e14ce3635e648d1ee7", "guid": "bfdfe7dc352907fc980b868725387e986b78278ce465585525fb5dbb0db41343"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986522919cde1ce8c92773402c1c67eb1b", "guid": "bfdfe7dc352907fc980b868725387e9863649a984b50e36a429de4d788c1a993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b36dd8db9515577ed5bdd6f6fed07f", "guid": "bfdfe7dc352907fc980b868725387e9833a5d0c8b74324f98d5c82baefb0e74b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866bfe22a279480b875abd6c21f8ea93f", "guid": "bfdfe7dc352907fc980b868725387e985b82534434a3f18530c3f21c463f25ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6253e0bec7e0fc45cd5cd631ed5ffb4", "guid": "bfdfe7dc352907fc980b868725387e9884771dddc8a5d8c93610587377d36bae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff1ee1298add5ad53b5424a52818d527", "guid": "bfdfe7dc352907fc980b868725387e98f338ba480169675309788ad5eb65a069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d838104c0906b7a7087e6211179a5562", "guid": "bfdfe7dc352907fc980b868725387e983d9995e3d3c14c2a9a355fc2716f61cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988054b5ae3fb2240fc073b80c412a0c7d", "guid": "bfdfe7dc352907fc980b868725387e98559af95a3b9235b3d649ec1fc8c280c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497dc9c4a7e030b6df2b1b98d27f9db6", "guid": "bfdfe7dc352907fc980b868725387e984cfd86b5c6fe3b7391f9242585385092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50e022d950df3fd4741573d024b65f6", "guid": "bfdfe7dc352907fc980b868725387e988ea06cd9355539f07cb3eca3b628489b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822fc76612f5019e732e6c47e2bc60fd4", "guid": "bfdfe7dc352907fc980b868725387e982d4395a9d7f6d63ad995543210b8b444"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e89cb67733362c62724247435aebbebf", "guid": "bfdfe7dc352907fc980b868725387e98ba6f55cd518f89a832effa5f3bd398fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b405ba8444a7512b25757645ce2278f9", "guid": "bfdfe7dc352907fc980b868725387e9859d758ba46693cda4f1c22c6c41409d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce4e76bf9bea359b2a8784c390006e8c", "guid": "bfdfe7dc352907fc980b868725387e98f940a93aab7d1765879c6fae8d54546b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98513407e5064c51414b7f3c43e3d713f2", "guid": "bfdfe7dc352907fc980b868725387e9804f4b41c21c480e7dad6f8959d10a3a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861b762c7cb84a76b1dcbe28369737733", "guid": "bfdfe7dc352907fc980b868725387e980c90060ab8e5dca95facfad7bb7235a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982faede22f0fac788d2962ae3f71e8dd4", "guid": "bfdfe7dc352907fc980b868725387e98e98086129eda6b9a3285906266b4e1ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb3866d593749b83e5544b790e154fa4", "guid": "bfdfe7dc352907fc980b868725387e987471412d17e1cec5c9d9175e053e085c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d76fd5273063c910068042f3e049fa", "guid": "bfdfe7dc352907fc980b868725387e98e84e08a636c0aee92e7950cee5518358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d8432c89ff62df378c8f9c812a4458e", "guid": "bfdfe7dc352907fc980b868725387e9871dc2f0552911badb57e1600649e78a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9053a87b32c2730fb35d0f0032891a5", "guid": "bfdfe7dc352907fc980b868725387e98ec44e3222aa935df1c40d59880768d89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5d4ea71f40f018680572c82dcd9dbf2", "guid": "bfdfe7dc352907fc980b868725387e98f08bbbe6272977e6a750fe12fe97347f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d8f6f299b633e111efc4c94f1d1bad", "guid": "bfdfe7dc352907fc980b868725387e98e15d8c1eb458a60e48cbc1d625397988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e9ffb896e916f33f2dd397c6b209f9", "guid": "bfdfe7dc352907fc980b868725387e9878e087ebc4f08a2ed1f700129d254e95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d71ce8003c1de6f1bb102ca3627725c6", "guid": "bfdfe7dc352907fc980b868725387e985dbed3654de75cc73f54d214f1055600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689464194f0ef893b48e8a2d831e6a9d", "guid": "bfdfe7dc352907fc980b868725387e98309c18ee37204af1e251b737240f9ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888622648daff859d6d85357fd813275a", "guid": "bfdfe7dc352907fc980b868725387e981add6fb4b4232f6a73233f70204ce920"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846889fa5ccc682464cf892b859413aee", "guid": "bfdfe7dc352907fc980b868725387e9889b6f377c8f2b3d0d9a965863d1dea34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980507a7892f837d85b493555ad1cfb183", "guid": "bfdfe7dc352907fc980b868725387e98a3915d0b2c95a58835390f2317a38ba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98101a5d42baa0eebedfcb6c02ab688244", "guid": "bfdfe7dc352907fc980b868725387e98db567a412018a3c0456a825326fb3d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229b970726cfcb22b34bac25994965ec", "guid": "bfdfe7dc352907fc980b868725387e9883b622722a5b8a99ab63eb70a84320e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f99359057e60e0a96df6db3c9719cc8c", "guid": "bfdfe7dc352907fc980b868725387e984d1ac07e5b7aa574d547d21dbfcdb004"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f7c81f18ef5bee9bd40de0f2e59bda2", "guid": "bfdfe7dc352907fc980b868725387e9878f9aad88f65f7329d8e287c7befa428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989311503489c43a95fffff70a271b9039", "guid": "bfdfe7dc352907fc980b868725387e98afa3820892f2cba42af858540db9d6f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d225e7b2fd77930518e4467d4f0df9a", "guid": "bfdfe7dc352907fc980b868725387e981f4ccfba9cc951db7e0ed04abb9d948b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bccb51f552da35c70e4375273568d2e", "guid": "bfdfe7dc352907fc980b868725387e98910a4040fdb2534aa2d7c1fbf19b4ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846b8d3352c5d966bbb0d7c417b01dc85", "guid": "bfdfe7dc352907fc980b868725387e9836a1fef484deb30bbc8beb5856e2d840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d947c56205cff512e7fdcb967e798ca", "guid": "bfdfe7dc352907fc980b868725387e98cbd351f33c4937378c94749eeda857ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb86d85cd394076ebc3126e44d53ac72", "guid": "bfdfe7dc352907fc980b868725387e985edb3508c81fc2c48f508f7cd8fe84c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98443280b6275141bf3d8cfe8f1db1d23d", "guid": "bfdfe7dc352907fc980b868725387e9876bdc62f0c0628e6a328a9b15dd93b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c67a790e71f20eb310de73f33fbf7f37", "guid": "bfdfe7dc352907fc980b868725387e98f848a9b5410e2e9a3cf67ba261bfcc51"}], "guid": "bfdfe7dc352907fc980b868725387e98bd76cdbefaee3ca00255e5a709e305d6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9832070803f5db5a9dae614cebfcb2a2ec"}], "guid": "bfdfe7dc352907fc980b868725387e98c62aa5790add542e9c72a71c341cf047", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a1667c019a1e094cb78edb6d4969a21d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98dfb03d8133a4c4d0ce289957deff5e40", "name": "MapboxMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e986fc9ace0c8808d101bf5c63f54197cca", "name": "<PERSON><PERSON>"}], "guid": "bfdfe7dc352907fc980b868725387e98d1b046a0c587f9fad447e9b5f311f327", "name": "mapbox_maps_flutter", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98874ae0603cd389f3468cc23a44b3f238", "name": "mapbox_maps_flutter.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}