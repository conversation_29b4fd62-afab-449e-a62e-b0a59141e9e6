{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832f7ec9f90157f98f969e8f4f65d3116", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Masonry/Masonry-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Masonry/Masonry-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Masonry/Masonry.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Masonry", "PRODUCT_NAME": "Masonry", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888810d14ce2ebfbdac2ed74db0f16bd4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e0fb6c91ef39648fcb9a42efbe0d3b8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Masonry/Masonry-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Masonry/Masonry-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Masonry/Masonry.modulemap", "PRODUCT_MODULE_NAME": "Masonry", "PRODUCT_NAME": "Masonry", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f6b91e56a75fadae9c52ecc526e38bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e0fb6c91ef39648fcb9a42efbe0d3b8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Masonry/Masonry-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Masonry/Masonry-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Masonry/Masonry.modulemap", "PRODUCT_MODULE_NAME": "Masonry", "PRODUCT_NAME": "Masonry", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98343b936a849d8c7f44d1edcde8e2f51b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988ca23ec7982f1777921e21b9aba9bf11", "guid": "bfdfe7dc352907fc980b868725387e98063f7878a6c49bccfb27e0922ae80ce5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860362dd76caf356a483b4e517958cb6f", "guid": "bfdfe7dc352907fc980b868725387e98e7e65dc6f62275b79ed7507b00e8e01e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db64051971df6e71439052ca9fc9ba80", "guid": "bfdfe7dc352907fc980b868725387e9887faecc14d75f728cee3234a4dbde6c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e336372c055e7f9f0f733239e8ae0b80", "guid": "bfdfe7dc352907fc980b868725387e98a7d9c3be4901defd8247a1e2e185df3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be50cb7380f24fe1e40ca367cc8d6e52", "guid": "bfdfe7dc352907fc980b868725387e9879eedbf7dd76651fead8d6d3516424ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873e4378f51babda5cfbece688e157100", "guid": "bfdfe7dc352907fc980b868725387e982539391fb360ca46df62bbcf298351aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dac3156b74832e3024e9633ef52e9e4", "guid": "bfdfe7dc352907fc980b868725387e98c896b860e12f216e8746f2e0e1a45085", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f9f48ee854e7fe34cdf05a6eadbc29", "guid": "bfdfe7dc352907fc980b868725387e987473d9adb37cb5aaf06ad701b1a665f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e27dfbab5d9d56e5861fdcd540dd8b", "guid": "bfdfe7dc352907fc980b868725387e9866357b9891df691bb64006ea88522966", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98823aa031fbb467ee8da07e8ed3baeb67", "guid": "bfdfe7dc352907fc980b868725387e987c2f3cb17a71467d951892b2fc35e5f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3b97f46d2800635a40a159eb1acf00f", "guid": "bfdfe7dc352907fc980b868725387e98ded8712f6a06403dbb4e1d03794bdf56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982c0d478a501d5e71d9e10ce82cb849", "guid": "bfdfe7dc352907fc980b868725387e988b206964c93ab6f9d934bcd78e39cea6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a369235625c82a8c55243e62fb49b2a0", "guid": "bfdfe7dc352907fc980b868725387e98f8395731d4b28568de6f82dbc88d9325", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9421f9672bc2433efb7ac282bba7d21", "guid": "bfdfe7dc352907fc980b868725387e989106de51707b3dc27ab82648b1744367", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd5ec11f43a7e6e6dbd9bf32ece3246", "guid": "bfdfe7dc352907fc980b868725387e980d6f91b37c45138e9c4ab97a6e9eef50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989314f8c70b10ece95704b1eaa07d4544", "guid": "bfdfe7dc352907fc980b868725387e98e1c81caa7c23341aeebe4ba11ff7d4b2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c34dd8f86c37c8a063461e3f5a02ceb7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b87932ea399503906b824efb531a32d8", "guid": "bfdfe7dc352907fc980b868725387e98861feda59fd48c4ac11a6f9d1835947a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982200021379e7caa7a56263392196a869", "guid": "bfdfe7dc352907fc980b868725387e9878d8cdee6dfc864ab782c9f55f2c22d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ec75ad19487b9e79d8a2fe889a58418", "guid": "bfdfe7dc352907fc980b868725387e98e7fb6e086b373edcc1e5bdd0fcbe403c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987768fbd04ae7a17268f8f2b58734169e", "guid": "bfdfe7dc352907fc980b868725387e988e06492b39d05806e6515366cc8de996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d99deba28400f72e220fb67c0465a0", "guid": "bfdfe7dc352907fc980b868725387e98d96b8ec7ee20c4c3a1ef1b6ebb3f44e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e7699368dc9344da19895ad59410613", "guid": "bfdfe7dc352907fc980b868725387e98fba3df6c120d65c4a8ad3836c1b3b65b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ae83c0f3a3dc1abb7c02f176dbf6dcc", "guid": "bfdfe7dc352907fc980b868725387e987dc4860f6c31f686d059e88dda952558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d8fa3a6cda579a804302b43bf3fcf1", "guid": "bfdfe7dc352907fc980b868725387e987c9fb2a32249b05d82c545b8dac7d191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c526203a66349612cc60f0bb8e0f71", "guid": "bfdfe7dc352907fc980b868725387e981d14c954b00492e9492f9988551184e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e18d4b17a8b67bf5b2bc3db433814015", "guid": "bfdfe7dc352907fc980b868725387e98c1406d7535b15403efef52291afea1c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988409ffc217be0d80d6949c703d3ddd04", "guid": "bfdfe7dc352907fc980b868725387e98e5f8490a66213e94add9293732fd7069"}], "guid": "bfdfe7dc352907fc980b868725387e98de026a844c120ccdb67a7627808be9f7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e984f70dbefd0e25e7d5404facd1b065aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d38f5a7d519c077fa06220df9cf84", "guid": "bfdfe7dc352907fc980b868725387e9895d8e5ce56eeadfa14b4a992750ad64e"}], "guid": "bfdfe7dc352907fc980b868725387e9852ed3f8ac9dee5a23e6a443699923a69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b05126febce7785b32f28b42780cf931", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e3be389a010182c334a5c542b48f8dc5", "name": "Masonry", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98df6836b9870d920dfe4f848ea264a4aa", "name": "Masonry.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}