{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce3de8931df89e94cd081572beebd35b", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/ZIPFoundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "ZIPFoundation", "INFOPLIST_FILE": "Target Support Files/ZIPFoundation/ResourceBundle-ZIPFoundation_Privacy-ZIPFoundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "ZIPFoundation_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98416461e06d63ff629435eca9234ac07d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ddbd2a905dafafd56e69ac86905944ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/ZIPFoundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "ZIPFoundation", "INFOPLIST_FILE": "Target Support Files/ZIPFoundation/ResourceBundle-ZIPFoundation_Privacy-ZIPFoundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "ZIPFoundation_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b61c346d1464d69c5ac0dba1cdc2adc5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ddbd2a905dafafd56e69ac86905944ef", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/ZIPFoundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "ZIPFoundation", "INFOPLIST_FILE": "Target Support Files/ZIPFoundation/ResourceBundle-ZIPFoundation_Privacy-ZIPFoundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "ZIPFoundation_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988c0cde001bd19219b00de70745507d4e", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98df7929514af7588bd24e75014bf00292", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9893add0e1dc04e6be0a7a0ca07c0ecb51", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f44d9cf039ce25dcf997669eab287f8", "guid": "bfdfe7dc352907fc980b868725387e98e337d7109223fc29167680ca38e00d0a"}], "guid": "bfdfe7dc352907fc980b868725387e98a5331c0c7a096a0b682209db9487027f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98d5f04766c1f13418203923b4c884e6a9", "name": "ZIPFoundation-ZIPFoundation_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989765d8ec3a1ba0574d0be2bef3a4b67a", "name": "ZIPFoundation_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}