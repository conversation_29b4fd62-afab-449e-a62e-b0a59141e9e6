{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983396bdf1bcd3eab2959e4dda4bf06140", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_callkit/zego_callkit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_callkit/zego_callkit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_callkit/zego_callkit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_callkit", "PRODUCT_NAME": "zego_callkit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3f25a4504a9a3f4ce0d4c0dc6ef4e97", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9821abbfb768d56679da50cd8b2b16651e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_callkit/zego_callkit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_callkit/zego_callkit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_callkit/zego_callkit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_callkit", "PRODUCT_NAME": "zego_callkit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b12158b04f3aaaef0e88f19db99c28c0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9821abbfb768d56679da50cd8b2b16651e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_callkit/zego_callkit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_callkit/zego_callkit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_callkit/zego_callkit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_callkit", "PRODUCT_NAME": "zego_callkit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9ed88622c432d8085a32f93b3f46f73", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ca616ea2617a29191da4807c24e0661d", "guid": "bfdfe7dc352907fc980b868725387e98708d100256f09565dd8b0187573e72ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891a8d2dcf53cff13cf4104bc4f319cbc", "guid": "bfdfe7dc352907fc980b868725387e98e36cb9aac593f6fe5fc34b27e0247de3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af6eb416e2a8adb7b2b39e0762e0b001", "guid": "bfdfe7dc352907fc980b868725387e98a77d0fbc9e50db315d2b9997db9f04b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98115535a8b4ba57bb7cd55232283bf1ea", "guid": "bfdfe7dc352907fc980b868725387e98d41322758cc9af3f511d5db6f18acc5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b51861f2dbe1699b1a0eba220d3205", "guid": "bfdfe7dc352907fc980b868725387e986166bc68b640cc897206d22737627dd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b136ebd418bd9b437a2b251e59908148", "guid": "bfdfe7dc352907fc980b868725387e982124f63c8e824a7489426923e3841be6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985219215be5a8754ae2259d6c0b42554a", "guid": "bfdfe7dc352907fc980b868725387e98a4ff109d97c2ae08d06bc23041242d5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987518785f4630865fc95e06e0f2f4ee32", "guid": "bfdfe7dc352907fc980b868725387e98b441879aca880a7cd1424f27320b9369", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed8960d9a1bedb4567d0211ead76e3e3", "guid": "bfdfe7dc352907fc980b868725387e98b4a1e666e5e6d7b3612bb7c342f1548d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983b70f0ca5c6211da39f7d77af346dc43", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdac2696c94586cdf2db17fad1fe2476", "guid": "bfdfe7dc352907fc980b868725387e98174aac6324b58c33f987107d80cc34df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab516689c85c36cf576547581bc82f91", "guid": "bfdfe7dc352907fc980b868725387e989fcd8a916f76474c3ffb1828df7805c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853eb69d922a7f9c2a54dcd6fd92b6ab2", "guid": "bfdfe7dc352907fc980b868725387e98aca29b235cc533eb1c8683b5849fa7b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e871809af866221bbc7d0219a7ffd237", "guid": "bfdfe7dc352907fc980b868725387e984887d6474453ccecccb5c77335b8fe6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e405dd386268043d1b5f5f71695d181", "guid": "bfdfe7dc352907fc980b868725387e9812f35e0db32b90e2b6112f1cd3119adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aebddc3f0ab78ae886632c0589b0c92", "guid": "bfdfe7dc352907fc980b868725387e98a817a0c82db89736f5a762b885da617b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751ed890d78e8fbd5e489597e0ffb774", "guid": "bfdfe7dc352907fc980b868725387e981f0b9e6d5b0e5041f1657eeb3c6840fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8eee31dcf5563dd98905b164038746", "guid": "bfdfe7dc352907fc980b868725387e9894f1f3012cd1c3a00edbb7d174ae002f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d2b6aa461a4e725d40006d4782d86", "guid": "bfdfe7dc352907fc980b868725387e98b739a75d746578849f648e23196ec4cb"}], "guid": "bfdfe7dc352907fc980b868725387e98ee00469bfa1898b56b8fc4b03b9e46c8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e983c5e2b232cfe218edd24892f617d0294"}], "guid": "bfdfe7dc352907fc980b868725387e985e5fef602a87eeeacad2f5dd57afeebf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989d6a9363b4ca7dd3f8e27539cfd5a2fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e985cb10b10fd31529de5352ae05ca3017d", "name": "zego_callkit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e7bf37f4bd36231ee0d582d77f755ed2", "name": "zego_callkit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}