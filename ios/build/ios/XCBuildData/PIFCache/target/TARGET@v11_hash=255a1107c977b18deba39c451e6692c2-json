{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d653e27b641dd7ec2472a6c559a497f7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832f07e3611e1cd4202d5dcef2d33891e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e789e1ca55a3459210cf02a8e0b2d89", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe2765f0b0bca1c79efbef9097587fa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e789e1ca55a3459210cf02a8e0b2d89", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fe9281b56eef94ea17cbbe720807df4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98025de68e67953e373b9fc5a98149a609", "guid": "bfdfe7dc352907fc980b868725387e984374c5929f93b58326de248a0af0e08e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988cd46bae394cf19ea3bb6fa9cf3bc30f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98425dbcf8383db14a405ffc1eb33ea50e", "guid": "bfdfe7dc352907fc980b868725387e98cdb0d144103ed4143c96e504e8b1d579"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13e4fbe60b3e4b68cfe864d77a7e4cf", "guid": "bfdfe7dc352907fc980b868725387e986b437f6e398474a4590a548a121b8069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98486a2af1539e59c6910e640f880fb07e", "guid": "bfdfe7dc352907fc980b868725387e98db2fefd8129138c40db11403a5bfe81d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a26f877eca7b2429662689c8a40c76f", "guid": "bfdfe7dc352907fc980b868725387e98e08a6679f47a2c2b62f46551447022ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ecb95577b34dffa5b11a3826bc1c02", "guid": "bfdfe7dc352907fc980b868725387e9879f81259637f407bac584aa628bbd8da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c9e41efe8dd8d4d75593c3c48248c0", "guid": "bfdfe7dc352907fc980b868725387e98bd7072f7753032a76fe0641a011e8c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985102a3ff3df4a6eb23fb66af7ce41ee2", "guid": "bfdfe7dc352907fc980b868725387e984e1e03417478c8590617ec85b166f463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5bf7660925128a3baa3146fafea4261", "guid": "bfdfe7dc352907fc980b868725387e9853eb335452f4015e784c5261cfebe3e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890b743989f589dcded92b4e8bbfa9386", "guid": "bfdfe7dc352907fc980b868725387e989b9d7a484734d69adf29e7b1ce09efdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988954812bc9f0ed8e167ae8322c56bb6a", "guid": "bfdfe7dc352907fc980b868725387e9858f2ab01379395b2a3dc63f605a85d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1d28954dc5b92804446fc29dc67a5b", "guid": "bfdfe7dc352907fc980b868725387e98c9dd63c8be1ddfca000af528790d6dbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6c202f02809984c4861c70d30e62800", "guid": "bfdfe7dc352907fc980b868725387e989b792c09e20dd79f4687659e1a154d6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814675c9615ea335ac4c5e50ee65f998f", "guid": "bfdfe7dc352907fc980b868725387e985ec01d70abd150e48450f5829216829b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bee21d1538316457681fdcca9a4e22a8", "guid": "bfdfe7dc352907fc980b868725387e98752cad510cfdb161ec55d796415bf70e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf31097c783b08881bf86710660af35", "guid": "bfdfe7dc352907fc980b868725387e989e08a847cd344fbe4606da21f1fb489d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e856db82374f1342c18f8e52bc16afa", "guid": "bfdfe7dc352907fc980b868725387e980570c93c6891707aa7544359884adfec"}], "guid": "bfdfe7dc352907fc980b868725387e980052c51b8717f62ce2f5ff4458b396f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98580b44084421fdbd3b6d09468cbf510b", "guid": "bfdfe7dc352907fc980b868725387e98095dc869b774ef60b122ffc4340f531d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98e4b0cbb88ebf714845641dd3d28fafba"}], "guid": "bfdfe7dc352907fc980b868725387e982e51bd129cf78dc9a0dead1ca579d6ad", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea5d9082d7813f473c7af8c27edbb7da", "guid": "bfdfe7dc352907fc980b868725387e98e88453312ad1e627434f92c5923e171c"}], "guid": "bfdfe7dc352907fc980b868725387e9892a77a5c5ca2cb2880bece9778388d81", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3bf2315f331e6c08a312dfd64b37cff", "name": "PhoneNumberKit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}