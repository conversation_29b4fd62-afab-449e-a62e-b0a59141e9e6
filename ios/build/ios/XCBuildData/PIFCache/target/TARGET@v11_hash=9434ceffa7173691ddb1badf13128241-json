{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a5d3d9a2698bb8a6b97c8c72b6e9134a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8ffff8c4608b48a02b8e6dff7150e62", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5b2b56d77ffd7e98655581a64e35ec2", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef1fc6d25699796b1f97b56600d50813", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5b2b56d77ffd7e98655581a64e35ec2", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5afe3ea1143ea5558fd7a311b1ef47e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a6255dfb5b5f1579d3254ec31266a25f", "guid": "bfdfe7dc352907fc980b868725387e98d6a9faf101b28e0cb6b0f6b28fd8ed44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98979c85e6bc31a42ff403cedf72f6a554", "guid": "bfdfe7dc352907fc980b868725387e9807c5850e8ff8be54f2772533e2622778", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982f000430adb083ec7cc5dc23620bf9cb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d44d90247868ccb547a00f7ddd45f32b", "guid": "bfdfe7dc352907fc980b868725387e986ad9004b9e984f413d1893386868089a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98747234a477858316a6068e85f2dcc781", "guid": "bfdfe7dc352907fc980b868725387e98b46bf4561f1a9b5d9d2d09740bed9696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985606b057088b2d29a9c91192a91d2c0a", "guid": "bfdfe7dc352907fc980b868725387e9878c6e6e128a43d071fdc9c551f0ce741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98809c0a97277c60e731e8250c91376e08", "guid": "bfdfe7dc352907fc980b868725387e98bb3ac1db97c0946699489c1ed2cfa425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549915914418a23bdad657827d4b999f", "guid": "bfdfe7dc352907fc980b868725387e98bf120a1bd2f7ce74436530003a44a4cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cef0f788605f82dc7d9284897245dfe", "guid": "bfdfe7dc352907fc980b868725387e9830c714ca63519cd2b6fd4c3889cce027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef0d4a5d5096d6a8e802a8a969763737", "guid": "bfdfe7dc352907fc980b868725387e98586ff9735313b6e28ae527ae6fc060e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98669b893f419b84a3ba283402c22f7fd4", "guid": "bfdfe7dc352907fc980b868725387e9842d2e1922c7e76e29084a415f15e1053"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f77d8a1b71fac31f1ef82b462809dcef", "guid": "bfdfe7dc352907fc980b868725387e986d47a8ec8bc8bdc8b318eb367029c16f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce51e443e01afc1c269d18e8bf13998f", "guid": "bfdfe7dc352907fc980b868725387e986e1701285d92452dd8bf74c46786febd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c9936feaa331991219a816a67523e83", "guid": "bfdfe7dc352907fc980b868725387e98bd71f3b6061cf59cd93d2faf1b3c932b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98670dcfda94218bef36469bd569ebe18c", "guid": "bfdfe7dc352907fc980b868725387e988ca85ee09957ec545830431e29c207e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985350a88b8756c1b84bac1ffa5ab49def", "guid": "bfdfe7dc352907fc980b868725387e98fe1ba52874faefb9f87dee2c7039df14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875ac04dfd6201c66bc6f14d4e4ca14c3", "guid": "bfdfe7dc352907fc980b868725387e98697e48304338bc77be4d818d5609a886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ed3a099f8e45f02c8704950a8786cc", "guid": "bfdfe7dc352907fc980b868725387e98c3086dfcadaa23e79851b441332eef07"}], "guid": "bfdfe7dc352907fc980b868725387e987c966a3b129a240cdc79c574c521c055", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98660c51cf828305bc3a5ec4c52aa8b13a"}], "guid": "bfdfe7dc352907fc980b868725387e98fa71aa902bf434f3999bc057159ce99f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988a175a6448755246720f963908648c4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e983dbb0d5d79b94fc9af349ed668188ddd", "name": "flutter_contacts", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9844de8c75da5c0dc8b59260788428245e", "name": "flutter_contacts.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}