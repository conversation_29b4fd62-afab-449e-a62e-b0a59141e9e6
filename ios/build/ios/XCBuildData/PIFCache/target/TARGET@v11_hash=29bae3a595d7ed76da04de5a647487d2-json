{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9eaa18a3ae749c428c43ce72f0a0dd1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1636185a95aaa75e13c66c1ca5f1c50", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a4d9c21bba9116caed5d0577c854861", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b41b4117a92f0a9cb2598be4d4f13680", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a4d9c21bba9116caed5d0577c854861", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a080a2e286699631673de4d834eff0ae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b90239e930f4a2aa34ff21cf8da7d48c", "guid": "bfdfe7dc352907fc980b868725387e98b4b5bac9cd382643f1a8340a1ea185cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495ec5e2a94d4c7da63eb9a6214d8cb3", "guid": "bfdfe7dc352907fc980b868725387e98fc94f28e36eba46a54259bc04247b79e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982370b0bb5b3961b78f7561b38f18208c", "guid": "bfdfe7dc352907fc980b868725387e985867418456f8c77c9a1dc1c657d50c87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5faaca6f851c0868154779182dc17a", "guid": "bfdfe7dc352907fc980b868725387e98474b26dd73ee7d1bc32217395ce3a91c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98673a80b980a4f5286cbf6392f825b87a", "guid": "bfdfe7dc352907fc980b868725387e9822ce64b9884d37c7ea104300de8c78dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842cd913e41264501b5111f759258d409", "guid": "bfdfe7dc352907fc980b868725387e98317e05cda8a546ce6767c9e6924b4c00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985627143c3fa089a65e2a1235385bd64d", "guid": "bfdfe7dc352907fc980b868725387e985ed6f0475a487a1846de8c80dd133c65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98650f88cadbbd5e4d0baa68d62de0c5aa", "guid": "bfdfe7dc352907fc980b868725387e982f65a64d74514ef9b459bd90d0ec57a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e055e0d676d08a464049ce5a2e9a80", "guid": "bfdfe7dc352907fc980b868725387e98ff11049f23774061c8be1b40aaf8b152"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cc617abe0cd1abe75089405f2697d15", "guid": "bfdfe7dc352907fc980b868725387e9825597cfddb77c04f1fe800f511f0ea65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5dc8940f48f6123157112973adc661", "guid": "bfdfe7dc352907fc980b868725387e986e113420346ce8ba11e95a9069348c7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98025e6100fdf700fa4cedd79e830085d9", "guid": "bfdfe7dc352907fc980b868725387e989672e724d35c59e5da46d502858a2963", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044f704775e353d469c11e335b3699ae", "guid": "bfdfe7dc352907fc980b868725387e9858c67fa6886153fd4807aeeba64ee6f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98210fd38f95e963dea35b1d28085ff462", "guid": "bfdfe7dc352907fc980b868725387e98b7ddc9ab1bc8e0e4fa1df4adfe08725a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98527e26e1e68b1138a4c2a8f70fc3a680", "guid": "bfdfe7dc352907fc980b868725387e988d92000bc335c362272dbd82c2b68a86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6b1cdfab1a8555ad4793b960b460c6", "guid": "bfdfe7dc352907fc980b868725387e9816756bc1c13ef36bb8ecf9f65a57af0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851fb05aec4492ddd020c64c4165abffe", "guid": "bfdfe7dc352907fc980b868725387e98b9d14c36355f16eb478c0c41bc3b4884", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e3f505d8b4bcc490c075fb0b9c8c13", "guid": "bfdfe7dc352907fc980b868725387e980e38513b1655452a3b6b0cbacdf86b41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a03535380cfe88bc4b0f1d4082fa289e", "guid": "bfdfe7dc352907fc980b868725387e9864ddbafaf3a0e8f343aff1d7f165732b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a5c1a8ddfea89f76df881b38bcf3a4", "guid": "bfdfe7dc352907fc980b868725387e9836fce46d4825e49fa46cdaaa8c82b41d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9f01fff65ec84912f32afe28f15faf3", "guid": "bfdfe7dc352907fc980b868725387e983bbfdcfde58fe5f281862d2d6147cb98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc16fbe3e50fe6809acf736266ff646", "guid": "bfdfe7dc352907fc980b868725387e987ddf2b26778cca29b333241a6e7b8712"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4c5e89b777e48907faeb3c2f99d8b5", "guid": "bfdfe7dc352907fc980b868725387e980cd0cbdf0614f2b99838f0146cd9aa23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880c25c65ea3497d3631844f8bdb6b1cd", "guid": "bfdfe7dc352907fc980b868725387e98aa687c2303b6af2e273eae2eb5354ec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d099d79c3b9b5d297342d038bd0d1bc8", "guid": "bfdfe7dc352907fc980b868725387e9843d638eb4364d2cbc43376bc39df2aca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371ca5ef3b32e776187dce4d5ea501cd", "guid": "bfdfe7dc352907fc980b868725387e983dddf993758f539af407e907eef1c933"}], "guid": "bfdfe7dc352907fc980b868725387e980706349a184b3399228bef9192eeb2cf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98abf90f7f77e81be009d826dbb7473f16", "guid": "bfdfe7dc352907fc980b868725387e98ccc0a5232545cd24395e03b21a0d4b02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881fff6e5e7a393b2930a5307a6674736", "guid": "bfdfe7dc352907fc980b868725387e98df5a54215f5c7b56363d6bd1ce4a2f3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6cd395ec1b4ad34b8461f59b39f7d2d", "guid": "bfdfe7dc352907fc980b868725387e9860bf15609aa349206fdfafb3f1ed14e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b4bde9d51734c89338ed7e22a73011", "guid": "bfdfe7dc352907fc980b868725387e98f134e16a905f9afd033f1324357bc4b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98997b4e5c036e0b648dc5b8deb86f743e", "guid": "bfdfe7dc352907fc980b868725387e98b8554ea197a5c48f148fcc0bd383a23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0529a6f14c580258e2f7a5d95c150bf", "guid": "bfdfe7dc352907fc980b868725387e989e64252bcc2fb135550220c1d689acd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98458d410c84664ffdf8b4d6e3359f46bf", "guid": "bfdfe7dc352907fc980b868725387e9853e81565a27b2db86f7e6ea4f5b2f83f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b77909cba139d60584da24d24d5acb2f", "guid": "bfdfe7dc352907fc980b868725387e98ed28780f24e4a66c9cc741e5e3dfdbc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e24a6614ec6f51e166fdb1fdd9c57e9e", "guid": "bfdfe7dc352907fc980b868725387e98e132168453edfd6344787f6c19dc5c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f402b51142fd0b37fdcc5705f0a84aef", "guid": "bfdfe7dc352907fc980b868725387e982004aba16d19bfc41bd3afe2d7044ebc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ac5716b0709b4876b690f0bf40fe06", "guid": "bfdfe7dc352907fc980b868725387e988ce615a65f83f80b9a828bb76bae9893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986770713cb53882635d62ce1b3893af92", "guid": "bfdfe7dc352907fc980b868725387e9863b1db16992160e2f2d6fd533d9d0687"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d882a4d0e5b54755d089125c2300ba", "guid": "bfdfe7dc352907fc980b868725387e983147cebb8e8507c34d3f3130aa26bee3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f962885750eb47f86a1f5cd41e19f8", "guid": "bfdfe7dc352907fc980b868725387e980f06acc966a8ee850556af6f6ba23a6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd58d2a1e0ce21f2c9ec6c56f015178c", "guid": "bfdfe7dc352907fc980b868725387e98a7745710800434685fc9730302d77bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854693f1ea2e8b11df146c45e96610f87", "guid": "bfdfe7dc352907fc980b868725387e98d54fdb338be15abd968910c0b36aa241"}], "guid": "bfdfe7dc352907fc980b868725387e98a0bf93ada82d95e692e17400f15af0da", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e982754d84bd19d0bcf3b6d122d3385ab4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6815db8b12f96ce7ef9e16aa9f5816", "guid": "bfdfe7dc352907fc980b868725387e98d806bc8aeebf822948ce25398dc2f0f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98875e206812b734f1cf349363e49beb99", "guid": "bfdfe7dc352907fc980b868725387e9836ccbb759f40d3d2c326e283ec270814"}], "guid": "bfdfe7dc352907fc980b868725387e98fb540b217fe068a965c278fc96a449de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d883b401e91d533d53cb9add3bfaa4f8", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9833b364180b9efb25d6798442053e47c7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}