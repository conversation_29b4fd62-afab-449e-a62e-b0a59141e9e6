{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982471337fa6542cd401f86f6d5efe6eb7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d0ff57dae6eee48dc575d2004f558c59", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803f0ff43d0ebda3e49c66a1dd95276be", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98494dfea02a94ad730ac9472611ba8dc6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803f0ff43d0ebda3e49c66a1dd95276be", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983a0a1d083bc064d282180b6abaeb4df3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9836713aa7542c48049e555a28fe966bd6", "guid": "bfdfe7dc352907fc980b868725387e98e2abc3207440375701316f853344d9e0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989ea497ab570f59fbe94660f1e24bc879", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988d26759b9a8e92c196d17e9c1d308850", "guid": "bfdfe7dc352907fc980b868725387e9828fc324b7ee0dd00db671028125eafec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851341be1fcd6d4562cfa5865fe0c4f0b", "guid": "bfdfe7dc352907fc980b868725387e98c71fb63b5dde78eda845d18a8f400490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9fb090c9154a3230605b49a98a0c1c9", "guid": "bfdfe7dc352907fc980b868725387e9849cd395026b043679ccaf3f4f8f4a865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aa0b0868b2351d88dd50a09f60fc198", "guid": "bfdfe7dc352907fc980b868725387e98f6f2f2773cdf347b59ea5cc47e8d16ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1782b3c9853b4c60e41805066ebeab9", "guid": "bfdfe7dc352907fc980b868725387e98d15374eb4e69e4b1c1d40313f8b10369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477138c3280821def5f44ceea1efa707", "guid": "bfdfe7dc352907fc980b868725387e98aca2098888038d890ecd66f85407f172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d91f2edd1616cee798331f61a4ff51b5", "guid": "bfdfe7dc352907fc980b868725387e986e4e310b5694c17b0c75f0a541149bc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98923795b3a37d5b3b0e50e7703ed0d694", "guid": "bfdfe7dc352907fc980b868725387e98872db10599e96dcfc7910e4ee9cd33ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e8e9ca1920d83a3700a68eddd777ca", "guid": "bfdfe7dc352907fc980b868725387e986c6e6b6bad6e97f1b0b8c97ac1418d41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98997944ddd8c172b6b9a92fca5b7eedfc", "guid": "bfdfe7dc352907fc980b868725387e98aad55615931b9ef7cdcffd2076b18f36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f77920328ec0ca96031eb4eaeb5b635", "guid": "bfdfe7dc352907fc980b868725387e989487a34bdd1d806265b13bc78ccd6c12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811e20b61fe5d0aba8ee6f2f9837b174a", "guid": "bfdfe7dc352907fc980b868725387e9861bd82f633d9d2cbc2dda767db42d12f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e39455ce22699ca7d32a97d39c5598ef", "guid": "bfdfe7dc352907fc980b868725387e988a617c25d73a4320b2e32aa3a17a0db8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5e552459363164ea91cf18c6d17af04", "guid": "bfdfe7dc352907fc980b868725387e98cefdb0dd30349469b18d72cb37c6e2e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf912b6ddce639751adc6ac6b9aa74b", "guid": "bfdfe7dc352907fc980b868725387e983284b18ee3e9f50f754a25f6d9c2f8a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983afefee8de9119849367f3a13fb8e442", "guid": "bfdfe7dc352907fc980b868725387e98fa299d241382d4523c08bc89de95d0ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851200c3a321ad7595fc7b61cde8b5588", "guid": "bfdfe7dc352907fc980b868725387e98d92c1dbbd65e63cdfedcd4e26459c3df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d62e5c9ef878de60025bf9b749b654bc", "guid": "bfdfe7dc352907fc980b868725387e981850c7393626eb691d3f215d68975592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a2663dba898d4f6505b03975ddb78ec", "guid": "bfdfe7dc352907fc980b868725387e989c1f15ab440435600a372c3b405b7cf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae838b9500f1b0e345448b295f7378cb", "guid": "bfdfe7dc352907fc980b868725387e98e49efcfb34a7554c6282514b6c7fd9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98199428458c943559a329d4c1f2f3836a", "guid": "bfdfe7dc352907fc980b868725387e98f0fd5875ce4ce15038e2123aa4f7117b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98327ac89987107d5dc1f96bdbdfe16c4e", "guid": "bfdfe7dc352907fc980b868725387e98cbf533316795a50cd10a44912044c5b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805d68e9ed39483bbacc4e99feeb1034d", "guid": "bfdfe7dc352907fc980b868725387e9885507342fc7e8a92dc2808906ff2d89d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988042a6192de7c28a268c29f3af9d503f", "guid": "bfdfe7dc352907fc980b868725387e98c233d4eb5764b3fb390af75f747dab37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ef4efcfbc9d85616cdf6db8f44e7f2", "guid": "bfdfe7dc352907fc980b868725387e98d22f8973694dfc6e28458505433a7bb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f761cb3d9a55658002af027b07c18fb", "guid": "bfdfe7dc352907fc980b868725387e980e1d7cef35bd6dd861cfc61ead8a4f40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7f30ccf3d3d655b63deb5f6edf9f865", "guid": "bfdfe7dc352907fc980b868725387e98538b6c4fdde203a829ec657229e3b77d"}], "guid": "bfdfe7dc352907fc980b868725387e98b6bb0cffc1f243cb063a41ac127f82b9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eab614c2665412b3623d533980704485", "guid": "bfdfe7dc352907fc980b868725387e98a492c14825fb12b46cfec4ad012dcea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca1dc9714c22653cf87328d9618c0a5", "guid": "bfdfe7dc352907fc980b868725387e9818bf8975a2921526bef052472579919f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98d900417154f0ae3ba286cfb0f9c8a48c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a56ee73bf2b4a9ebe8786ad0da92c10", "guid": "bfdfe7dc352907fc980b868725387e989ec4c6164915d66f412543911d36a25c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d38f5a7d519c077fa06220df9cf84", "guid": "bfdfe7dc352907fc980b868725387e98c2d83d4ff211af4d9b846d8f2892f938"}], "guid": "bfdfe7dc352907fc980b868725387e98b92233db49547e072258ae7d4c2ab688", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98249ba72feb5695362d59fe670ac41836", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98c92c0b1b593945748dc8b05c6bb51b2d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}