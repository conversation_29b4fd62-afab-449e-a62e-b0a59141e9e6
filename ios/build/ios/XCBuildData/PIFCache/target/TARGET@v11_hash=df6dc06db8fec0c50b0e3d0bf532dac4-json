{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a513ba2854461430f89dd6ef1efd6b2c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398c7a093c4fa76bfeee96c3353db9d0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808b1c43edc267da6ad4b46a890acb396", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fe717b6aaed6e73a5de3ab18254ad58", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808b1c43edc267da6ad4b46a890acb396", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985681cb119fce67fcf802024383544abc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98161254b6b5bf61855e7a82fb698c9302", "guid": "bfdfe7dc352907fc980b868725387e98e605cdac5b3921d7a9f4185906d5bb6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1f4e3ccf66d4d21c24a3a001bb4121", "guid": "bfdfe7dc352907fc980b868725387e98c86fe8105efe2091028323d62b54d9c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2310fd783cfc12ee44d4d9cc0fbe4d5", "guid": "bfdfe7dc352907fc980b868725387e98ce840ed3523eea819c0360f418f9087c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf7e64ea03b7e65ab2e4c2c8a1092ad2", "guid": "bfdfe7dc352907fc980b868725387e98c7b5c98c578ab8ee1bb348aa99f18db5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4a905a710ddcd8648fb9deadd3e0e7", "guid": "bfdfe7dc352907fc980b868725387e98ba0c6fe325723d11cafaad20a3f6c795", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf6b314006e10c98d39e7e9f33420fb", "guid": "bfdfe7dc352907fc980b868725387e9834b83bf1f8b8bbf0e4990cd91c24b733", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e524c2aa6aeaef7af1a483f48497b5", "guid": "bfdfe7dc352907fc980b868725387e9855a4baa9ef7d11e223645babde212201", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0246586defd527951565c9013156599", "guid": "bfdfe7dc352907fc980b868725387e9837e5c796a3ee1802581bcfc66670ed48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd2a2523cfe845ad520a1d7ff1951db", "guid": "bfdfe7dc352907fc980b868725387e9892cdfca3b67a485235193b4bae2fdbb4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981b923694a98418557bf5d3dd942d4336", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98401562e96d8197f113f3389bb9fb9284", "guid": "bfdfe7dc352907fc980b868725387e98fff3a517bd80663a34364ca15e29381d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98355d0a72d1f256c0eee5c32308261c8d", "guid": "bfdfe7dc352907fc980b868725387e985ce33174070c77b2555274cbd59fbf20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835931ee265a6389af67b7ec66ca97ee5", "guid": "bfdfe7dc352907fc980b868725387e981a6cf957598cdaa2de2dd3fcc5317b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b7f85cfa9e35c921b250ad1174da3b1", "guid": "bfdfe7dc352907fc980b868725387e9823a5f2f1bae20eec9925fc8981ed0860"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2bc4704e15ae0611fd0e784a5c33601", "guid": "bfdfe7dc352907fc980b868725387e989caa11d99d28da5b2b6ec5adce810833"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e60201f2ce191b52ac97f8626a03511", "guid": "bfdfe7dc352907fc980b868725387e9836b42b05ddc3d6f34d61286d5e00e608"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc73cc6006c2d8f6aac8660622cec358", "guid": "bfdfe7dc352907fc980b868725387e98ea5b86c62139ac3206e4937b90ff6bae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980864836f673c154720e311213952bcf7", "guid": "bfdfe7dc352907fc980b868725387e98d1c2e0a932a7f5ad757a716cc0d6726a"}], "guid": "bfdfe7dc352907fc980b868725387e98878c61a6cb40e21ced80fc21ab9b6838", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9875f522cd885bb3d37ea0ddbd5143e786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6815db8b12f96ce7ef9e16aa9f5816", "guid": "bfdfe7dc352907fc980b868725387e98b51868efaf9dacb39c51dfb85e0a58b5"}], "guid": "bfdfe7dc352907fc980b868725387e98874cca97e23087a63f3eae0489ae1971", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984946a128fc563f65884a48039cd0fc0d", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98fc25ce687c1c596337223718d5ce4c70", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e9819553f06c0312349aebd106a5058b2d5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}