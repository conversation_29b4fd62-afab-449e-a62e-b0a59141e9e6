{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98562b2aac14cd27c43e9d9dc2c174cf7c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zpns/zego_zpns-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zpns/zego_zpns-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zpns/zego_zpns.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zpns", "PRODUCT_NAME": "zego_zpns", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98425ad05e6308c088ee93720e64c64e4c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981e49e9387933c4ca720a6c7ac195e216", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zpns/zego_zpns-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zpns/zego_zpns-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zpns/zego_zpns.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zpns", "PRODUCT_NAME": "zego_zpns", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b10c92a64abe9b978c5a96aefb8db763", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981e49e9387933c4ca720a6c7ac195e216", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zpns/zego_zpns-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zpns/zego_zpns-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zpns/zego_zpns.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zpns", "PRODUCT_NAME": "zego_zpns", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989ba857978dd8e2d4fefcc2e50b403baa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980962351319d48d255460f98e3d65c8b9", "guid": "bfdfe7dc352907fc980b868725387e985f91fc8416a5b21a47124fb934637ffd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c73d3bde1a19a6522bcda348b179ec8", "guid": "bfdfe7dc352907fc980b868725387e9883ae75f74d99806ef882cf40dd078fae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cac4218bfcedbf3338572c39f0e4026", "guid": "bfdfe7dc352907fc980b868725387e98aa64711b5e571d180fc00ae143621672", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822785dcc09adfb1563037a57e0463766", "guid": "bfdfe7dc352907fc980b868725387e98f4579ffc8f95a92fbfe2156273123f80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b9522c368f9b140bf2602b0455c98e", "guid": "bfdfe7dc352907fc980b868725387e9819c17097564f5dfea18afd311fd4f115", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e0c9cd04a752f6efb3b962eaa3f602", "guid": "bfdfe7dc352907fc980b868725387e986a7ba20d99f2292c4d863e71e16c5721", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe73771be4f43dd32b55ed71bde2005c", "guid": "bfdfe7dc352907fc980b868725387e9897be0bf5d7c8212e6e776c77102561c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b0a811df1a28eb3af1a1c2cdd9b2ba", "guid": "bfdfe7dc352907fc980b868725387e985c4e21080b721cd72fa151796b5796e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e93e1578d9fd23327711f512a413d9ca", "guid": "bfdfe7dc352907fc980b868725387e98dc08253dff138b94100126269a14d2a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987143b4aea2fd3d6f4fa26892a90fcbaa", "guid": "bfdfe7dc352907fc980b868725387e98787fb4211f7a94629af0d292018d74c7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9861b3fd48076ca47b7857b91d5c75c9e9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f57d50d10a094a785a7d105ebc93f19a", "guid": "bfdfe7dc352907fc980b868725387e9836d7bdaf02dcb0cb77bdf553c66e55c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2188663dc219dfc756a2c30665a508", "guid": "bfdfe7dc352907fc980b868725387e987262e5f679b1420ff2d75069ce8231d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca89eefa990152ca2ec27ab549e3dcac", "guid": "bfdfe7dc352907fc980b868725387e9818bd731af9906dd83a5bd2298b95b4cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863f826a33a4410b028b87236ca6ad350", "guid": "bfdfe7dc352907fc980b868725387e982f7e9b22a11fd612255cb259645cccfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac4a4f6b60f9ab66091f23e1e57992e3", "guid": "bfdfe7dc352907fc980b868725387e98065ee8d01b11db1fb6d716619d70ed14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad03d638984b334f831c4bb182779572", "guid": "bfdfe7dc352907fc980b868725387e9896c7fd0a5985f0122c1e1dfc40aa3597"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7a452716cda42bf651fbff544a25ee1", "guid": "bfdfe7dc352907fc980b868725387e98b0dfbf8077334efc1bf4709977ae384b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869c8aaef6e9bddaac621d9adcc8f1f59", "guid": "bfdfe7dc352907fc980b868725387e98931a6dd00fe517bd8c71efdb2babb402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c29a06ac5f8cc252c386655ac70bcba", "guid": "bfdfe7dc352907fc980b868725387e98ea58d08e444c9a7f88d8ef0b46fedb4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893451eb93355e4e9de47243f1bb20989", "guid": "bfdfe7dc352907fc980b868725387e986d37315d692cd4e37da389adcd082af0"}], "guid": "bfdfe7dc352907fc980b868725387e985cf5ce70f18f3598753d846a79ba60df", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e980c8eeab24f7755fdad647f62ad515164"}], "guid": "bfdfe7dc352907fc980b868725387e98b2f3c13554b2d0a58624de7816af6c36", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a4557f1a6ea2bd739a5325a7d5cb3b17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985ce3e9ebc659825424136caea7c58179", "name": "ZPNs"}], "guid": "bfdfe7dc352907fc980b868725387e9849329696c702c6f14df29431364d0da5", "name": "zego_zpns", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98788e4190f0e90f66b72ad1e551889001", "name": "zego_zpns.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}