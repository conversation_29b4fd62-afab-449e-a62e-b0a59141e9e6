{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1b169484f29c9a6cc88a547195ef8a7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a256a765611fc6a1896302a90ab93ca", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a256a765611fc6a1896302a90ab93ca", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804ad7eb8c245782ac1adb46eb0899502", "guid": "bfdfe7dc352907fc980b868725387e987f28047e02a7d34a46f8ada17b829c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c6475cd696c1c80ee232cbd25ad1234", "guid": "bfdfe7dc352907fc980b868725387e98f6c67c4679e7f7927d29cc6777b4854d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f91cada95f7682b24e7749f18a2659e", "guid": "bfdfe7dc352907fc980b868725387e98fcb7e61931146dcd711e99248d6ccb4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989535ef62e9a38e495e3c9857f06c0522", "guid": "bfdfe7dc352907fc980b868725387e9895fe4b33f65c965da7dc45a450744ad5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e70f54f61c3a45f0286002da7042c5bc", "guid": "bfdfe7dc352907fc980b868725387e9881f94adb811a31f238513440b73eb9ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b92ed2d4e344abc2a90d8fc58c553f6c", "guid": "bfdfe7dc352907fc980b868725387e98d2993dc352971d9dec3337f6492eedd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c3445c20371928bc0fd2139dccf1291", "guid": "bfdfe7dc352907fc980b868725387e987744d4bc1141e8c3af7814a94ab5d191", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245ac83e74508be8810e581036d6b1e4", "guid": "bfdfe7dc352907fc980b868725387e98d44ce0ad517ce468c11abe8a1a7ee842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c944cdcf91106feb2ee94b61d17caff3", "guid": "bfdfe7dc352907fc980b868725387e986dfc835f86ba61de853986e0c332c9bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837ffe1aa9cdf7164fce5daf17ae8531f", "guid": "bfdfe7dc352907fc980b868725387e98832bcfeadec19c9672e7d13bba695944", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984672421159d96a923f4b73425708405c", "guid": "bfdfe7dc352907fc980b868725387e987522b569e1888c59443fabbfd9eec3d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc2278c65966bdc554a471de4986e28d", "guid": "bfdfe7dc352907fc980b868725387e9826df2a365ab1701d04b81b250e704469", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a3347f537e98671e85b18557c3e26c9", "guid": "bfdfe7dc352907fc980b868725387e98683700ed014e2584117d9919dc247bfe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e35bab1db842818661a45b0a066b8fd", "guid": "bfdfe7dc352907fc980b868725387e98a95b46a3d3c80a8cb5734bbb7717809c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e6ee0d2664e6476f2d33ab14212d26", "guid": "bfdfe7dc352907fc980b868725387e98c814b053e6b465d54fefe875995c908b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bcc02992d5c3a27d0ea3ae904d9e7f5", "guid": "bfdfe7dc352907fc980b868725387e980e4020bf220b21c849a1247a598ef500", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd2e97f905d3525379878f7aec003dae", "guid": "bfdfe7dc352907fc980b868725387e9867e1d59733a195924e43c7221ab3b507", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984af25057a558c46f06ae775d77ebf87d", "guid": "bfdfe7dc352907fc980b868725387e9836a664f9634f39c09eb263d32eb33de8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985070eba38b9112dfe167da4f943e2094", "guid": "bfdfe7dc352907fc980b868725387e981012013b7c46227b76d2a26c3973c5f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98283db32e1d24b8a0c70ce0340131f7a2", "guid": "bfdfe7dc352907fc980b868725387e986adce0f63de9503184a2571c68ae8037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980799c98cae09f43158d97c550a5ec3ba", "guid": "bfdfe7dc352907fc980b868725387e98c97111306fd51e26ac236698d182d1fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d06619e8bec000c22f51f984aedef67a", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98342d04afcd1446d4d6a9efe6ef743a1e", "guid": "bfdfe7dc352907fc980b868725387e98c97bce089e46629de083eac4838dd800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a1f1aa767954b9d79c4a4f4ae63a91", "guid": "bfdfe7dc352907fc980b868725387e985817ad63c8e4f8db81700ac14cc618cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5aa4690909db8c3d5faba0e440227ac", "guid": "bfdfe7dc352907fc980b868725387e986e494a42d2c0843cec709c09ffdc69a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f38bff1b50150711ff27a1274ee368d", "guid": "bfdfe7dc352907fc980b868725387e9805c8314d750be278604bc8677fcf4420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb424eb990abef9561679e092cca44c", "guid": "bfdfe7dc352907fc980b868725387e988270e34481adcde5ab4b6a9daa89a011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98322b1fca5f7b125a55db5066ec8ebcb4", "guid": "bfdfe7dc352907fc980b868725387e98615ad6c8a17ee1f04050629a5a2a260a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809eb7bf58ea53f6f7c90b5f7c0197c5b", "guid": "bfdfe7dc352907fc980b868725387e98fd9db82003eb1b08fd6976b7ba6920aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ecb969c24baadd3b149ce14bc8726d5", "guid": "bfdfe7dc352907fc980b868725387e9836872f22a07d006d803b66800b0fd898"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a215abe9f8e78a5a11e9aa8d9d6ac589", "guid": "bfdfe7dc352907fc980b868725387e985cf4803afdb0ccdba56bf5e79afdbebc"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}