{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98613d4e584921ee9b48ed380f54b15a51", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988feafec0abec993c8346933bd34b170a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b4360419975d3ee8bab04293fb610559", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983246c7355382bff8d4edc7294044650a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851b4101bacb757486c3f164e66a10ad3", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f12947c0fedf4ca123d7c978e9336b6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98816c0e40b16378d1dd4faf26a59db042", "guid": "bfdfe7dc352907fc980b868725387e98b3acda13a2823dd05b2e709f67d0c74e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98557b5e8f0de58bae0ee80ee5afe94a83", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c916e55204b401587fb8b69d473cc336", "guid": "bfdfe7dc352907fc980b868725387e986452ce340f4b1bd77dc3a8bfbb3f79be"}], "guid": "bfdfe7dc352907fc980b868725387e98c8034249b0fda8c58c71b352dd52ddb9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98f265aa76ee3fb898fa445abe0cf0f5eb"}], "guid": "bfdfe7dc352907fc980b868725387e98d954b2cc544a5a3a563ff87a304e4e29", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b3f6a7dcae0e80400715705d3b70e83c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98c184ad8b3c49a0c098ef639132f1afe4", "name": "BoringSSL-GRPC"}, {"guid": "bfdfe7dc352907fc980b868725387e981c0768badcb962e4e9f1efa0f12f70a9", "name": "CryptoSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e989176bbd636a13f9582f645f1f0315a8f", "name": "FBAEMKit"}, {"guid": "bfdfe7dc352907fc980b868725387e98828dc99e68108e01848c924cebb9b2d5", "name": "FBSDKCoreKit"}, {"guid": "bfdfe7dc352907fc980b868725387e98f52a87663484c8c5c2fb376dbe85216c", "name": "FBSDKCoreKit_Basics"}, {"guid": "bfdfe7dc352907fc980b868725387e9851e8fd886951a87551ea0aff618ed4ec", "name": "FBSDKLoginKit"}, {"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98b762d5de103fb6cfc7506aa6be1d4f33", "name": "FirebaseAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e984871cb004918038f6f30024db9e06b51", "name": "FirebaseDatabase"}, {"guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e9808d6f7e039038e527371e6953faff60d", "name": "MapboxCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e984fff5b878eb76268c180101cf95e2304", "name": "MapboxCoreMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e98dfb03d8133a4c4d0ce289957deff5e40", "name": "MapboxMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e98e3be389a010182c334a5c542b48f8dc5", "name": "Masonry"}, {"guid": "bfdfe7dc352907fc980b868725387e9808993ee7e539dbc8104a52b3ab1a956e", "name": "OneSignalXCFramework"}, {"guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit"}, {"guid": "bfdfe7dc352907fc980b868725387e98da29652de71b686743df2bd56decf7ef", "name": "RecaptchaInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}, {"guid": "bfdfe7dc352907fc980b868725387e986fc9ace0c8808d101bf5c63f54197cca", "name": "<PERSON><PERSON>"}, {"guid": "bfdfe7dc352907fc980b868725387e98725973221efb8c479c179f718b1bc2aa", "name": "ZIM"}, {"guid": "bfdfe7dc352907fc980b868725387e9880d1b2aab04aef5eb91394017eba5f52", "name": "ZIPFoundation"}, {"guid": "bfdfe7dc352907fc980b868725387e985ce3e9ebc659825424136caea7c58179", "name": "ZPNs"}, {"guid": "bfdfe7dc352907fc980b868725387e989e6eef29fe3a944913e4dc39e457adc5", "name": "ZegoUIKitReport"}, {"guid": "bfdfe7dc352907fc980b868725387e98cb446d83a55866e5ca0338d00b7158fb", "name": "abseil"}, {"guid": "bfdfe7dc352907fc980b868725387e98916834ec4bb54bd12b93f5cff3b46819", "name": "audio_session"}, {"guid": "bfdfe7dc352907fc980b868725387e98d6a3e96f78013eaa4ca36aa0bb49af35", "name": "audioplayers_darwin"}, {"guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore"}, {"guid": "bfdfe7dc352907fc980b868725387e98144902882b713248a71c322fd5b2f4ee", "name": "connectivity_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e98d41ce0bf2141365ff0288286787936d9", "name": "device_info_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e98bf75f71a2aa7fdc38a7d7d70e6c200c1", "name": "file_picker"}, {"guid": "bfdfe7dc352907fc980b868725387e983788d8769c821650606514be955fca93", "name": "firebase_auth"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}, {"guid": "bfdfe7dc352907fc980b868725387e98769fa09b34e5cfc28413eaa4369dd6ba", "name": "firebase_database"}, {"guid": "bfdfe7dc352907fc980b868725387e983ef179a5373bc7706869e99d4489da64", "name": "flutter_callkit_incoming_yoer"}, {"guid": "bfdfe7dc352907fc980b868725387e983dbb0d5d79b94fc9af349ed668188ddd", "name": "flutter_contacts"}, {"guid": "bfdfe7dc352907fc980b868725387e989dac9fac004a9055ad38289b6692399f", "name": "flutter_facebook_auth"}, {"guid": "bfdfe7dc352907fc980b868725387e98c766a571e4d2610827ef9ef3b2b9d7c3", "name": "flutter_logs"}, {"guid": "bfdfe7dc352907fc980b868725387e983b5ee1860506a75929f335a2720b97b0", "name": "flutter_native_contact_picker"}, {"guid": "bfdfe7dc352907fc980b868725387e9812c371dadc76e7ca44d59f6cfbef1a62", "name": "flutter_ringtone_player"}, {"guid": "bfdfe7dc352907fc980b868725387e987cb9c58d33d05677fb576674bac57313", "name": "flutter_secure_storage"}, {"guid": "bfdfe7dc352907fc980b868725387e98ef93265391fc6313b4b0582b39a3069c", "name": "flutter_timezone"}, {"guid": "bfdfe7dc352907fc980b868725387e98c8fd51cb7d6ab4a686c4237acf9ae73e", "name": "flutter_volume_controller"}, {"guid": "bfdfe7dc352907fc980b868725387e98bccb561e0566a1524751a3c67bb26d35", "name": "flutter_web_auth"}, {"guid": "bfdfe7dc352907fc980b868725387e98839a1650b1f10605b2db52456c9e6468", "name": "fluttertoast"}, {"guid": "bfdfe7dc352907fc980b868725387e98d7de55694c750fe04d3bc1517f91a481", "name": "gRPC-C++"}, {"guid": "bfdfe7dc352907fc980b868725387e986836e78755c7a49cbac94d6710bb39cc", "name": "gRPC-Core"}, {"guid": "bfdfe7dc352907fc980b868725387e98a9264f727d973888bb984bf45da252f2", "name": "geocoding_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple"}, {"guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e98c283846ee6bbf6294e59b4712edf254e", "name": "google_sign_in_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e981f000f066404b97b12e9c4ca84d38d0f", "name": "image_picker_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio"}, {"guid": "bfdfe7dc352907fc980b868725387e98812cb744006e837a06a1ec7fcf8ca389", "name": "leveldb-library"}, {"guid": "bfdfe7dc352907fc980b868725387e989152bf3b88a1e7242d5cb4c469a589c6", "name": "location"}, {"guid": "bfdfe7dc352907fc980b868725387e984c276e032465a1ed0eb1d385ed4ab588", "name": "map_launcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98d1b046a0c587f9fad447e9b5f311f327", "name": "mapbox_maps_flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}, {"guid": "bfdfe7dc352907fc980b868725387e98445c0c37a1972f853f445373c125aa78", "name": "native_device_orientation"}, {"guid": "bfdfe7dc352907fc980b868725387e98f4438af4c3c51df17ddafa29b7aae198", "name": "onesignal_flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98a5ae7244e41cc249cf7186dbb9962ecb", "name": "package_info_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e9830037b09fee48cfce1f8562d753688c8", "name": "path_provider_foundation"}, {"guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple"}, {"guid": "bfdfe7dc352907fc980b868725387e98848ff9cf74c635f5324731538a1c853f", "name": "share_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e9828cab1f188854e0a973e6ff6905c5ffe", "name": "shared_preferences_foundation"}, {"guid": "bfdfe7dc352907fc980b868725387e9854872b53324b870c3c439eeaf7fabcb1", "name": "sign_in_with_apple"}, {"guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin"}, {"guid": "bfdfe7dc352907fc980b868725387e98903e66fa03d6d27edaa18126a82c20fd", "name": "url_launcher_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e98e89b0c2b50c07920aa58b86e182619bc", "name": "vibration"}, {"guid": "bfdfe7dc352907fc980b868725387e985ee86805101bc8fd279e03690a1048af", "name": "wakelock_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview"}, {"guid": "bfdfe7dc352907fc980b868725387e985cb10b10fd31529de5352ae05ca3017d", "name": "zego_callkit"}, {"guid": "bfdfe7dc352907fc980b868725387e98e0dabaf2081d80624f8efb13f2fad9bd", "name": "zego_express_engine"}, {"guid": "bfdfe7dc352907fc980b868725387e98123565603ef24f80536ad38d20ed3f73", "name": "zego_uikit"}, {"guid": "bfdfe7dc352907fc980b868725387e989e7cfa17b1add31202da052e00c54967", "name": "zego_uikit_prebuilt_call"}, {"guid": "bfdfe7dc352907fc980b868725387e98ddb55acd15a3305b425983327fdf97f5", "name": "zego_uikit_signaling_plugin"}, {"guid": "bfdfe7dc352907fc980b868725387e987e0ce7f7db3bf62d00283e8ca2e0829b", "name": "zego_zim"}, {"guid": "bfdfe7dc352907fc980b868725387e9849329696c702c6f14df29431364d0da5", "name": "zego_zpns"}], "guid": "bfdfe7dc352907fc980b868725387e98312b4bc59bbbe2c06c205bf4da6737f5", "name": "Pods-Runner", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98699846e06e93b50cafdb00290784c775", "name": "Pods_Runner.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}