{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981328074de89481a0cbbc9ea19cab3baa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/onesignal_flutter/onesignal_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "onesignal_flutter", "PRODUCT_NAME": "onesignal_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8fd614a39305a6e957398af90ebfa83", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9877d294102b84bd0959a057791235730b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/onesignal_flutter/onesignal_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "onesignal_flutter", "PRODUCT_NAME": "onesignal_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989520d76d660f2a422c2367458a4f19da", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9877d294102b84bd0959a057791235730b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/onesignal_flutter/onesignal_flutter-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "onesignal_flutter", "PRODUCT_NAME": "onesignal_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985a8b218f2279d0e46904e5bd0c7b677f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be6da4b0cf80f1ed84b9fe08cb6febd0", "guid": "bfdfe7dc352907fc980b868725387e98d2a0979de9b21f4d4f26b6b880003463", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab754f87480f45717af12b467e103773", "guid": "bfdfe7dc352907fc980b868725387e98a049644bf2ddffba72d583591a0861f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986309593b6b74029fbb80b5bb1970dd2d", "guid": "bfdfe7dc352907fc980b868725387e9810ff7f3b73c1a735e09fb999a171b0db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0b4623faab05d010f6fb6523e68b34", "guid": "bfdfe7dc352907fc980b868725387e98fd0209d0f8894c1631f3cf449097e2ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988907d4fffe6164561a9383f64202e75c", "guid": "bfdfe7dc352907fc980b868725387e9833a401aa2b2f6f92e658d2f64eb32270", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1845421f5c54b8decc9356427a0286e", "guid": "bfdfe7dc352907fc980b868725387e98276b670c16c16aef2528939f877da460", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5daba202f8fe22ba5fad1bb84478430", "guid": "bfdfe7dc352907fc980b868725387e98d390da9e4aefcfbccff02713fac29067", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5be2cac2b22e318da67bbad6adc4b32", "guid": "bfdfe7dc352907fc980b868725387e98bc1b2ca5291c60437bb87e282836a724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859578b8c1c6e6aca329d23425bfd590b", "guid": "bfdfe7dc352907fc980b868725387e9844341abc341e8ba29939fb71fb8de846", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abc6d3432b365fe2aac215e5a0899df5", "guid": "bfdfe7dc352907fc980b868725387e98c75504bc8faf343fcd873337d72a3d43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0b6b3ca7780795ea1d3c70f46aae4ed", "guid": "bfdfe7dc352907fc980b868725387e982f9ce6d02b6a41cb332fdc47f595cedc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d1ab47feb38e0c5732edef94818c80e5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9865da47df2c117dcc9d530f579f39aad0", "guid": "bfdfe7dc352907fc980b868725387e982259d3a7b8f5a9bd3365066f78b3849c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc0f872f64c45ec00cdafc199e277443", "guid": "bfdfe7dc352907fc980b868725387e98c112fc3362ca91ba2132d61edadf8fe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f4e959e6feb86c54d53e949aa936e56", "guid": "bfdfe7dc352907fc980b868725387e983790cd8d123a5bbbfe22195cc154dd67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7012a2320883aa51b8f76bd04f15263", "guid": "bfdfe7dc352907fc980b868725387e98b1fe4cbaa5a00f6a9cacdea0bc021cc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f399b9675745e1c4df7b985b96aefc3", "guid": "bfdfe7dc352907fc980b868725387e98b7a190260c5a9a634d48f9fb4d933367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bdbab03c1287da7b0898974146c1579", "guid": "bfdfe7dc352907fc980b868725387e98b6d8303c486958a5ca2883959aee3280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827ee4113dec0b2df9164e3f5931cade1", "guid": "bfdfe7dc352907fc980b868725387e98b5b6c9020a6ee90dcb7c440c74e38630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137cc8a42d394ccd73b84056ea914fb4", "guid": "bfdfe7dc352907fc980b868725387e98f9a206d71c4ba2460ebb3b317624c749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e5f161fd478fd5a398c3cf7df62a07", "guid": "bfdfe7dc352907fc980b868725387e984af9fedcbb14061136cf95f8c394565a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882912bd2b94038cbe90aecb2fee70fdb", "guid": "bfdfe7dc352907fc980b868725387e98609ed4722b1453628051a1f174154cb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825fa8418f55bfacb5af947a4d05bfb9b", "guid": "bfdfe7dc352907fc980b868725387e983e5f5b5af5b0298c0fe253edec93ce5a"}], "guid": "bfdfe7dc352907fc980b868725387e9867932396a40c237ff55ddd1e21d5d231", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e9807f415839ae67220202613bacd8e36cd"}], "guid": "bfdfe7dc352907fc980b868725387e98663895ae45270bfd00867ce1f5453e1f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98733c5b7f4934b273ad6455bc91dda241", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9808993ee7e539dbc8104a52b3ab1a956e", "name": "OneSignalXCFramework"}], "guid": "bfdfe7dc352907fc980b868725387e98f4438af4c3c51df17ddafa29b7aae198", "name": "onesignal_flutter", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982015f089d5d93545d9616a3341903bda", "name": "onesignal_flutter.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}