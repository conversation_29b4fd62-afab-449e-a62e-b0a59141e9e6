{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987256237728e4261dcd99b5dded95bf86", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ab9759c01c118828e6a7afe450bffd5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846596959c0fc0bb7501e430dd96613be", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed278e5f520c525a2067932639ada823", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846596959c0fc0bb7501e430dd96613be", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ec852a8d1adada10d1ac783212da0ea", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98649d4a579d63326e63f6e8f0237c1abc", "guid": "bfdfe7dc352907fc980b868725387e98886cce8d89eedd0e8bab1ebae394ce44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98179f09a99923c8ae696306406d4b2476", "guid": "bfdfe7dc352907fc980b868725387e9814d4634efa63a6d2d505baa31e1ac713"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb0a9d3d1fbd0da07a687f419982d13", "guid": "bfdfe7dc352907fc980b868725387e98320cd36a8701febd579756d6afaaf887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986608109489d7541ef700cbaaa288f212", "guid": "bfdfe7dc352907fc980b868725387e989fc70d6b14cc9ff6f9284594eda21c4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d569e4d7309035e779487afb408859", "guid": "bfdfe7dc352907fc980b868725387e987ad8ad9016d3b0417c705cf13bf56cd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feffb5c389b51656f9a797f4a6e425e7", "guid": "bfdfe7dc352907fc980b868725387e98d703dfd4c027fb2c6536f0b761e5693b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f52d745d250831428c8001d64260556a", "guid": "bfdfe7dc352907fc980b868725387e98e3a5d36fe9ddc383dd52cfb2f4aa3d2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc29a1bdbb7d5b9d9a48796ad7314fdf", "guid": "bfdfe7dc352907fc980b868725387e988c0cbdb0ce4d520f771f0a0119fc0a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851b23024d7a99b7ede535aaea41b76a4", "guid": "bfdfe7dc352907fc980b868725387e9841f34774f1d1f35fd1421e074705026a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98526d640fb79515e3310ba721c6e189b4", "guid": "bfdfe7dc352907fc980b868725387e980709a52f0b40d2f4f632a567e77f0052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf4e69983b0780295e777648b36f7687", "guid": "bfdfe7dc352907fc980b868725387e98e7e5ef36f766a85235666117b7e3af60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c7ebd9727746bc5356d0808d9bd6ca", "guid": "bfdfe7dc352907fc980b868725387e981219ec7c40082ae325734559e6ff1972", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bef2603217486eb21c9310e4056fb62", "guid": "bfdfe7dc352907fc980b868725387e988f15e339179ba61cee0efcba36dc94d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca7d6c673c8a6cffe8bbb74b99c85ba", "guid": "bfdfe7dc352907fc980b868725387e98c613f6e9f1268a6fd9760f88f3ba1cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851653106228e91b688112d36813a8afe", "guid": "bfdfe7dc352907fc980b868725387e98cef3f95e150d8d212f3fd2bc94dbbc73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e023fe0820dee6564f7646f26abe3130", "guid": "bfdfe7dc352907fc980b868725387e98c03b9eab66a77826f26ccfe61cd18596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980424924ef0502d2f7220c28004519be8", "guid": "bfdfe7dc352907fc980b868725387e98005c9eec2b5c8702ffcaf744ee071c9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879552cf5e5cc486325ecff471a0bcc2b", "guid": "bfdfe7dc352907fc980b868725387e98bb76d4700128219548c4ec6ddd809bdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98add18a29172909230c345205bc8e648c", "guid": "bfdfe7dc352907fc980b868725387e986927c359f2dda2d0de174559b9012d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b108f0e8973caa4c45409add0d838f16", "guid": "bfdfe7dc352907fc980b868725387e981e07ec96bdb3b4f1c8571c17df42f556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832dd0498df89050722d03706b2125a62", "guid": "bfdfe7dc352907fc980b868725387e98e8a59315c0e4f60820a2913515601152", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eb20bd9a2b67fa02b9b54ba98ca07a7", "guid": "bfdfe7dc352907fc980b868725387e989370a0471632ea106ad7ef12faf7eb58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809eaf495686395d403010dbaea21d095", "guid": "bfdfe7dc352907fc980b868725387e985a8665f65a5b1496a289fc9a37fd5d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae80b86f3952533f1da693cf6704a7f", "guid": "bfdfe7dc352907fc980b868725387e985ffe850fe17544bc1ca5e12098182a72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb9871d62a629cf4b4baf1b02653aee", "guid": "bfdfe7dc352907fc980b868725387e98103c9274d36cf279ca06210c7d6fc9d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c89580f9a72001a8eacd16f73040918", "guid": "bfdfe7dc352907fc980b868725387e9849661bc209a55c852f76889cf6a1e997", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870dc180d551b54e3a43be31bac4a082e", "guid": "bfdfe7dc352907fc980b868725387e980cec98cb2e90c63aa654a612ef32167e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb558ae482ebbe19c89a14cdf9e6032", "guid": "bfdfe7dc352907fc980b868725387e984db1948fead7015391334ac8735be24a"}], "guid": "bfdfe7dc352907fc980b868725387e987f5b8c6929a27c0c9bd2e06b911bd5b4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e012f4107b93e309915cbed5315b6150", "guid": "bfdfe7dc352907fc980b868725387e989fd85f031f44e7e867837755a95d792e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd8c524977771fcba62e04cb263f4157", "guid": "bfdfe7dc352907fc980b868725387e983a357ebf5ac631d9351567fa4c1a590a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb5292179b592937db7ce9e1cadb5dc3", "guid": "bfdfe7dc352907fc980b868725387e983ff901e1c348f871af84eaa18946dc86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f332f24303f75bb572560e1fca9776c", "guid": "bfdfe7dc352907fc980b868725387e986e0959b19781b54bbcbee026cba50b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ae4f619829aae7f37f35f2c397d8ca", "guid": "bfdfe7dc352907fc980b868725387e9840c005f94a80219ea5597c3482850da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e559151116dcdc85efc7d617b4cc166", "guid": "bfdfe7dc352907fc980b868725387e98425fb0d289c9745ca8ac6723d38ade98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829520603bbd20253c5249cf95baf11b3", "guid": "bfdfe7dc352907fc980b868725387e9847bfe48de35e2a18da782a45e7538de1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2b59c05ea3cb88f098fec82678baca", "guid": "bfdfe7dc352907fc980b868725387e9801fe482d894d5861f713698ed9ee48fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cf01c8e4bccaf1819f8977ee09144c6", "guid": "bfdfe7dc352907fc980b868725387e989761f53b0f553eb48fefe5e27e6df52a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583a48b46deabc67d0a9a795d634e932", "guid": "bfdfe7dc352907fc980b868725387e985e004db72a88aabda259dd96fe2d878e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b58f5a1dcfa801f05ef1ef27466e67d2", "guid": "bfdfe7dc352907fc980b868725387e987403a31bf817ee8bcfa02bd720133be0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986827753b93ddc466563a92f83876ced4", "guid": "bfdfe7dc352907fc980b868725387e983e4e4a667d359ad7936ffb1a019c396e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b02c8f10a94735d1022f89e77b63c4b", "guid": "bfdfe7dc352907fc980b868725387e98dc10bd3e500f94a88b8237eb3d5976eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98602af3538fc1019413929d61131d9318", "guid": "bfdfe7dc352907fc980b868725387e98d0d3e46f3d082a79fde397aaae6ff8cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b18e035e9029c963158e2c28001658", "guid": "bfdfe7dc352907fc980b868725387e98915f338ff896f522a21633c22b745224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffc6d97eaa92dd71f73edc1f1da0d9e2", "guid": "bfdfe7dc352907fc980b868725387e98f6e22be9228fb66854e2d978db3d0fb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982958ce6c68b46d60774cdaac412f8704", "guid": "bfdfe7dc352907fc980b868725387e98ae74b6a1ad2caa4d0bed9d8659c899db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db2b32d965ba489385b4b8f3436ba32b", "guid": "bfdfe7dc352907fc980b868725387e9847a9e3ab0f288ebfbc84fcc9dd22948b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f3405f0d5e70f21f58dfc82bf97023f", "guid": "bfdfe7dc352907fc980b868725387e983247400d15e10e10db86e4c31c9710e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6d0f26649e81740a1528d8c7372a168", "guid": "bfdfe7dc352907fc980b868725387e98b0e3693309aa6dd9fddf8e4e0c1ef477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cecc99e152a4f990ca52262d343d4a0", "guid": "bfdfe7dc352907fc980b868725387e98b58454db49475740727c589fbd08bd53"}], "guid": "bfdfe7dc352907fc980b868725387e984f5e94e465f5903eadcd32566152e573", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988e22a1f21471242681d7e1125856594d", "guid": "bfdfe7dc352907fc980b868725387e98d5751d5d5edfe95cbeeb7320d75636f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6d673d7c58ed75fa6e6a09b2721508d", "guid": "bfdfe7dc352907fc980b868725387e98606f654508a766c82daac06f2cf31545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e989ba13964eec132954b26eb3135d7fe0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6673472af704aac2a29e63ef8fe542", "guid": "bfdfe7dc352907fc980b868725387e98a86e502fca8e7bbfc825b02ec2da6001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6815db8b12f96ce7ef9e16aa9f5816", "guid": "bfdfe7dc352907fc980b868725387e984b2fdd52b6577de327fc05ea8cc3e6eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d38f5a7d519c077fa06220df9cf84", "guid": "bfdfe7dc352907fc980b868725387e9829688bd5d8608456cc667396d5869b23"}], "guid": "bfdfe7dc352907fc980b868725387e98b2fa6105a1ff6ac5133924a8b9d0316f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98edbfd520c3bab10d90ee1126f6a977b9", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e987f0db34de032e064343027920ce15adb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}