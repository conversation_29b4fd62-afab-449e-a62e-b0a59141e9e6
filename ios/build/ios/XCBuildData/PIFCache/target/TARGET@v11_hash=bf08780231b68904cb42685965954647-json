{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fce5d3c8259e2c1cfe1c2b25f4d71b77", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f852a8f57782372e1849b26411643cbf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c82a8f2a41904001e960cff4eaa3872", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98114c0e87e390a936ef9bba587b12a553", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c82a8f2a41904001e960cff4eaa3872", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9898da30df817aa8a560d0acd1ecaff874", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982b42bfce8c6c1f5863a925363e308796", "guid": "bfdfe7dc352907fc980b868725387e98b54d3f5cabc1fb6ff4206038e5c246a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988121485ff6222b5304fb472e26fe0abb", "guid": "bfdfe7dc352907fc980b868725387e989f16af05a2ee9427b6b89ef101b14031", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98100809d2ed358db29c01e26c5b324c60", "guid": "bfdfe7dc352907fc980b868725387e9879b775b5c00e7ce4bf87b94ad3893bbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98311f087e6af013086bf9830a46794a92", "guid": "bfdfe7dc352907fc980b868725387e98984808a3fdcee6d24a9703742396e621"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff4b438632c3245ec855ed705b1172e", "guid": "bfdfe7dc352907fc980b868725387e98c338576023b36eb2ab45ac6c5b728d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895245f2112814ef62aadd9a4de94fd59", "guid": "bfdfe7dc352907fc980b868725387e9831ac136e7b1688aa644812fad5146284"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986907aa2957db07b968c2e055ed70535b", "guid": "bfdfe7dc352907fc980b868725387e981df462ffdc709a837b6055f69b501219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f20d63cbb1cf5abcd2ef5bd0bdcbf0", "guid": "bfdfe7dc352907fc980b868725387e982890dfeb0fa8108d0711a0593b5be8a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98237e49d47f2e3a19f08bc42242b4e3b6", "guid": "bfdfe7dc352907fc980b868725387e98cee2fe34b0b32b96629f0a3e0ac752ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0349109de60bec1c21b053c31de90e9", "guid": "bfdfe7dc352907fc980b868725387e985e75770cd05e8cf15575ee2eca246e5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98136e75bedf06fcc321e10aec55b5cbc3", "guid": "bfdfe7dc352907fc980b868725387e986088c447f1ccf271b0a7e618516a1529", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6cfac83d20599d6651c21f471cae0e4", "guid": "bfdfe7dc352907fc980b868725387e980068b32e1869a04970716fbbb2b89e74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b0b7b7937cd5a12e0f33411f23337a5", "guid": "bfdfe7dc352907fc980b868725387e98c5df88d6bf55bb6072b8460524884378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986084e73d0615f954dc91fffcec53324c", "guid": "bfdfe7dc352907fc980b868725387e9858cd463b8db93aa343750cf37399c661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fcf269016458c0502eb7ce0028471e7", "guid": "bfdfe7dc352907fc980b868725387e9816b2dc9a3d230689595c1571fce3579b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f931242cfe338548a11de3eb7b05c0a", "guid": "bfdfe7dc352907fc980b868725387e9887e6da0417ee8b22b0a0cb23277b991e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988750adb21e9daa9a7ae0ccee1c3cd25a", "guid": "bfdfe7dc352907fc980b868725387e9813aa94f00ac4762ecd56dbc860ff2467"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ab91214f851468c785ff3e555303ae", "guid": "bfdfe7dc352907fc980b868725387e98c4689046873e8232388919106473deb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a71982001d4f7f7a86498d8fd45d64f8", "guid": "bfdfe7dc352907fc980b868725387e98bb71002031b325a577ee8dcc632d8650", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98008f40c3e925ee6366a116763c2aec91", "guid": "bfdfe7dc352907fc980b868725387e98e4480c8c8be3e20dc060b2c95790149c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982518b2c5c93f3f51ba0c086493800f93", "guid": "bfdfe7dc352907fc980b868725387e9854d622965da44890473c922471dd640b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d15ffb7e7a72ed2fad0338565c2ddf70", "guid": "bfdfe7dc352907fc980b868725387e98a6417502db59ea3b44d30e3c2a760264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e9e0d41f9a629e631f6fd06aa4480a", "guid": "bfdfe7dc352907fc980b868725387e98d7e98da8d896e6be3799c2b2a071a34c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987fbdcba7b347c69bb228f435a1603e42", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850e3591f78049ee93c1d40c46fdd705e", "guid": "bfdfe7dc352907fc980b868725387e980995f9c6f0e12bcaee6f9e301613e060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98853fc399b7826c0ef4418e783b4d5d8e", "guid": "bfdfe7dc352907fc980b868725387e984a5293f81cb6105b3d88d9946a8c2e48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d5da1084669a046a898e4a4723143c", "guid": "bfdfe7dc352907fc980b868725387e98e5279a0cca73674c5c551db9f8eda629"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f32b1e3131cea0d89e6aaf57120ac2f", "guid": "bfdfe7dc352907fc980b868725387e98b7e4c9c7e4324772ca43bf690efb5ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cea3dd8f55dc1f0aae2757357868e11d", "guid": "bfdfe7dc352907fc980b868725387e98ab0b751a61139bbee3f6c61c2b041ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899508f3574bac9690ae2d863f9295fc5", "guid": "bfdfe7dc352907fc980b868725387e9851dbbe6534d3493682b6905df86ffdc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4f7d3d50f269184410e3634f3bc9003", "guid": "bfdfe7dc352907fc980b868725387e988f0aa86379bc1a2d3b589b26eaab38c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3cd2ad80c22de433ba85f2fb00b8397", "guid": "bfdfe7dc352907fc980b868725387e98131f0ebb8213ad7b1dd18edf88ae96ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb91bb78622dc722a73a34abd64bea3", "guid": "bfdfe7dc352907fc980b868725387e9845530b46172b158b7ee84c7c74dd9f93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df42949d9aa0d61f2b2e054a96efe421", "guid": "bfdfe7dc352907fc980b868725387e981bf8a136f0c629d340c92e483e91341d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869ff01ef7977a7b48488505fd5f23858", "guid": "bfdfe7dc352907fc980b868725387e9888fbf020158fe8a7f54ed3d6a712493a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e79122532ecf03a9fed8fb2931ea0c8d", "guid": "bfdfe7dc352907fc980b868725387e98e0252017bb51fdf856f483f387308d23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b9866e1f172bf15905774ad9b4416b3", "guid": "bfdfe7dc352907fc980b868725387e98027a55051e9f85b70223cce4004c52f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cda699a552e44d5627aa633642f448d1", "guid": "bfdfe7dc352907fc980b868725387e989b633dc11a626e19102597b309b6aa6d"}], "guid": "bfdfe7dc352907fc980b868725387e98d4081706f90081d3ee12667612bb85c1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e981cc89baf0d973f3ee6c2644a8e1cc209"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16d38f5a7d519c077fa06220df9cf84", "guid": "bfdfe7dc352907fc980b868725387e9870c7220fd64790e81fb43004d249e1e9"}], "guid": "bfdfe7dc352907fc980b868725387e98bd1d691bd2fcbc85101ed50529a1c655", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986fd1b22fa427cd184b1aced3bff245cb", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e984254ff3b2709f2e016a62528fe435b6c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}