{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98083f6b1c641a49a06eda2b49193001ee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zim/zego_zim-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zim/zego_zim-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zim/zego_zim.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zim", "PRODUCT_NAME": "zego_zim", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffaa0dd19a9a3cc1910cfb9b8679f9db", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9e66a703ef02882411ecd348f6bf84a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zim/zego_zim-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zim/zego_zim-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zim/zego_zim.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zim", "PRODUCT_NAME": "zego_zim", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d7bdf8a5130a549b39ca5073905352c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9e66a703ef02882411ecd348f6bf84a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zim/zego_zim-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zim/zego_zim-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zim/zego_zim.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zim", "PRODUCT_NAME": "zego_zim", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b471a110821808524cf85b68dd7eaaa0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd9247bf8494f19705da17179649f676", "guid": "bfdfe7dc352907fc980b868725387e98f8422d0023ea72e30cc1a0d6845da979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804affe8766df7554081d3a6f15d3f296", "guid": "bfdfe7dc352907fc980b868725387e98a7c33908a7279808de96b84fdc97407b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7f17dbb3bad9c3dd1d621d31a265ba", "guid": "bfdfe7dc352907fc980b868725387e98f4274eeacb45d1b2a1e256ba2ce974fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b8fedaa8cb7a3a6b14f1dac5d01c6b6", "guid": "bfdfe7dc352907fc980b868725387e9865cae82c21f134e0bc1854c3f75c2421", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988649798c0deb76516828f853db6c4dda", "guid": "bfdfe7dc352907fc980b868725387e984d3a932baee9839fc0a7634ea964e8be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69ed709939fb8e18fa12700df0fc6e8", "guid": "bfdfe7dc352907fc980b868725387e985f2e5644cf99c9c409a8b6f647b0a28c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a749214dc5b6ee222160df99effe5bff", "guid": "bfdfe7dc352907fc980b868725387e987a7ca84f14f943dab5f1458470bd0561", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811e3ce514221dbec37f326b98b5b0558", "guid": "bfdfe7dc352907fc980b868725387e987e1cd7de760d66adc72a4fb0785bbbb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814fe2d2fff7057a79d5f5102b06e5670", "guid": "bfdfe7dc352907fc980b868725387e98019265443b684695d9af10a1dbe333df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98581828c0082f285d2bef57b5f187e367", "guid": "bfdfe7dc352907fc980b868725387e98246ae38858e923e955df602b2f396fe9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98466b3bae266f68da339aaac6f178ec21", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987a9dcafcc3326ee3155aa1dffd13a6f6", "guid": "bfdfe7dc352907fc980b868725387e98c4832c4b57d183d7150c636b77f936e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbdc2ffa9acd2fe26d172f5dad22b69e", "guid": "bfdfe7dc352907fc980b868725387e98da2eb4f44bef83675a9f0345bbcf1f74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fd400e53aa9103b219e3e05ce8f968b", "guid": "bfdfe7dc352907fc980b868725387e98f33e3ea3fd8188e0ba7e59be94a25b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dceef4fd21235ef9ee518ce956ceb64", "guid": "bfdfe7dc352907fc980b868725387e98fd05b7a571cbf628318ceb3b997938fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807ee0e82472f2322461b747b221aac83", "guid": "bfdfe7dc352907fc980b868725387e98ca69f521aa29a9360dd434aa54ca69ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c07081edd34d2bb4db5dc71bec60e10", "guid": "bfdfe7dc352907fc980b868725387e98b28b919e3e08490a95f986a5c9a5634a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832819fed16358aa3ca11d23fec571ad2", "guid": "bfdfe7dc352907fc980b868725387e983b4f82d5a96f8a371c344c2bebe7c2cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881f32707a3d98158080eed602ea35040", "guid": "bfdfe7dc352907fc980b868725387e9858db54184ead18308b3b519b5f763fde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caf509dd50b187115ac4dcea9c28309d", "guid": "bfdfe7dc352907fc980b868725387e98a25eb753b4c35be49d70c6515c3c2cf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b58c6d49512bbf1df4e631233cd03770", "guid": "bfdfe7dc352907fc980b868725387e98bc2f0a604e25e0454a55932905eaa0a9"}], "guid": "bfdfe7dc352907fc980b868725387e98224297091bc465911a9843f4ed7d0e49", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98459502863f612728a4c53d0e644743e4"}], "guid": "bfdfe7dc352907fc980b868725387e98f1b8aefbe4d1b0edf51640ebbffaadb4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98eb9c34cb05f25a98a3666536e3c8aeac", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98725973221efb8c479c179f718b1bc2aa", "name": "ZIM"}], "guid": "bfdfe7dc352907fc980b868725387e987e0ce7f7db3bf62d00283e8ca2e0829b", "name": "zego_zim", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98daf582ea233f74674b611f2e53cb66c3", "name": "zego_zim.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}