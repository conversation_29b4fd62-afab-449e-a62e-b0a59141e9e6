{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d1845b07825d67280b144afa0603782", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9864dece58fe8898e7f0b46391fe2d091f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987f413dfff1c63ac797ca9a872daf37fa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b08ec94c515cc2b895c3ae9b4c03834", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987f413dfff1c63ac797ca9a872daf37fa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982b03ce4519745554a82e5cffe58f25e2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d3a5138d60c92711edc0ca0afa84cda2", "guid": "bfdfe7dc352907fc980b868725387e98cdfbb149f5de654f8816f7b5c2a6ed9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fad9915665dcf2eb19ee69a7df79d9c3", "guid": "bfdfe7dc352907fc980b868725387e988db1cfd8786de6440cdbbcba2332ac6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a32126817cf95132a36d77a129558b2", "guid": "bfdfe7dc352907fc980b868725387e9873b8c9133ca9f3bf6271615389e68976", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863a035f7deb985185c19bef5381d270a", "guid": "bfdfe7dc352907fc980b868725387e98f30ddee80a79d1ee17e6825ac04be0c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d6bcd17e54e30f81a2dfdd2b07f210", "guid": "bfdfe7dc352907fc980b868725387e98bc430f4f3c6fa4b6c32248dcb7f1e3c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ac7d7fccd4580ae93bec7606c879ad1", "guid": "bfdfe7dc352907fc980b868725387e98c0e7e9bc3481dd41ecd09036f9d917ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d7c5e72792132f0819616e09a1ac0ca", "guid": "bfdfe7dc352907fc980b868725387e9878dab8d4135588ef740182d0018fe0fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f561c717f7d97f6fa8aed2e0930f369d", "guid": "bfdfe7dc352907fc980b868725387e98ea3cd69529df7b7d8dd353973ed88eea", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987c543487347cac13b6462fae62598e7f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b5aa2b492cf5683176199d5208e775fc", "guid": "bfdfe7dc352907fc980b868725387e98095a5298775dc9366e4bffc279fcdb47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98411735f9396578878d7a8c81a0c316e3", "guid": "bfdfe7dc352907fc980b868725387e98b4ac4172febac1884a3307d475bac12d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d56c2c2e89d95edd4c7ff55b50f870a", "guid": "bfdfe7dc352907fc980b868725387e98bca48271d050687ede839ba747e02ab3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b99d174484b28da6189c9bd73151d37", "guid": "bfdfe7dc352907fc980b868725387e98af0bc255b5f26aca10ea531d0fb71f4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8df4c4d0bcebdbee5d638060560208a", "guid": "bfdfe7dc352907fc980b868725387e9812246b867359da4b149c09f90f5f34dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a58b69336ed722ac7d317c6ee6444e11", "guid": "bfdfe7dc352907fc980b868725387e98e0ab939fbafbd88342244f2b4a6d7f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845ed1c362e4effa840cb1afef1fd069e", "guid": "bfdfe7dc352907fc980b868725387e98f24716c5dc027942ebf92f04a1315e28"}], "guid": "bfdfe7dc352907fc980b868725387e98ce67561b68c83c2e24888c94da999914", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98cf1fddec899afc6c9825c5eb5ec44493"}], "guid": "bfdfe7dc352907fc980b868725387e98434353ef3b38c3699582ee30b73fe6a8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9872ef11792920b3966776ea469c5092df", "targetReference": "bfdfe7dc352907fc980b868725387e98082dc85da1fc941e5234c7cc1f11b27d"}], "guid": "bfdfe7dc352907fc980b868725387e9811e9e8a5f23273fd9234f4740a75ccb9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98082dc85da1fc941e5234c7cc1f11b27d", "name": "image_picker_ios-image_picker_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981f000f066404b97b12e9c4ca84d38d0f", "name": "image_picker_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988e06e8c3685b7c12032d8059f412f4cb", "name": "image_picker_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}