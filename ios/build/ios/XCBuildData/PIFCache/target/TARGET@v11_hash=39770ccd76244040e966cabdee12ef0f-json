{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98baabe2847bdb6b6eb305b0fc25858c45", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zim/zego_zim-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zim/zego_zim-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zim/zego_zim.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zim", "PRODUCT_NAME": "zego_zim", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffaa0dd19a9a3cc1910cfb9b8679f9db", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d89cd0b4b5821da249eb966d842e00ec", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zim/zego_zim-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zim/zego_zim-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zim/zego_zim.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zim", "PRODUCT_NAME": "zego_zim", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d7bdf8a5130a549b39ca5073905352c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d89cd0b4b5821da249eb966d842e00ec", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/zego_zim/zego_zim-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/zego_zim/zego_zim-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/zego_zim/zego_zim.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "zego_zim", "PRODUCT_NAME": "zego_zim", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b471a110821808524cf85b68dd7eaaa0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988dad527d31439c330665dfe8e2de9a08", "guid": "bfdfe7dc352907fc980b868725387e984c228bb2b1cf8939fee2f5fa0ed1a06d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838eb58aba3e5f3728e7f9721a591501f", "guid": "bfdfe7dc352907fc980b868725387e98bf6052d21b1d959f108323457e91eb04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab9a11c5383c8c21a4dc721853a4e92b", "guid": "bfdfe7dc352907fc980b868725387e98aec970283074ae841356bc13df54731e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b3c7a0572d1b9502b12712905be0f0b", "guid": "bfdfe7dc352907fc980b868725387e98676737e33c82932880dc2dbe8cc8c4e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de729dc719ac8c607d91a1dc52086fd", "guid": "bfdfe7dc352907fc980b868725387e98fa7ce8779f1071df9865f6e7270e6fa3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ccc34c573a8f299b4a79b22b3fba8b", "guid": "bfdfe7dc352907fc980b868725387e985f2e5644cf99c9c409a8b6f647b0a28c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cd9473731eb0a166a6b7b58811def9b", "guid": "bfdfe7dc352907fc980b868725387e98689039f47c51c79fe7108e4b892abb67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c70db6194dc5a573c7cce9c38a4df8", "guid": "bfdfe7dc352907fc980b868725387e9843b3c7341f65a90da86c8439fb50857f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893bb680971844d7ac895857e3f4dfd1d", "guid": "bfdfe7dc352907fc980b868725387e987488212be4b39015d3b8b703f047cef1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7c13c9b115b5e874630a8620d96c8d", "guid": "bfdfe7dc352907fc980b868725387e98bebc21b54ffdd52df3ee947b22de85b5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98466b3bae266f68da339aaac6f178ec21", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f3b3ae487f0bf3fc69993759ce4dd421", "guid": "bfdfe7dc352907fc980b868725387e98f68b53cb1c5f8d6b3777813e3551b38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9af93a880fd5ce72ace63cd511129c", "guid": "bfdfe7dc352907fc980b868725387e98a27bd3e809dd9bc5d7f23a6752db804f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1ea3e9eb685a81d323c741efc4282e3", "guid": "bfdfe7dc352907fc980b868725387e98c665212de5c25357fb3941be4c206751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90775d732345f8697cb071f5288db77", "guid": "bfdfe7dc352907fc980b868725387e98be22f738df7305e474b455682faa2eb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e1cb0a1e09acd176472539950c0b274", "guid": "bfdfe7dc352907fc980b868725387e985de1015350bfd1e93f9c044cdab65939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ac733e737ad021973aae6b43e79230c", "guid": "bfdfe7dc352907fc980b868725387e98b28b919e3e08490a95f986a5c9a5634a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b56d692b14de947762abc15e4e640b", "guid": "bfdfe7dc352907fc980b868725387e98b11df76226e314f4aa86ccc134150090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986796be30ce24b1ff8934b05673d2478d", "guid": "bfdfe7dc352907fc980b868725387e98cbb67bf418c535706cae2fa3e10d7bd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7af6265acfa600c60f453dc557a5c48", "guid": "bfdfe7dc352907fc980b868725387e983be2fbdca707f994f5f929f4d830ebd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7bf71914f4d015aabc44403ce958d52", "guid": "bfdfe7dc352907fc980b868725387e984b072dc8c15f32deca0037fc52e27dd5"}], "guid": "bfdfe7dc352907fc980b868725387e98224297091bc465911a9843f4ed7d0e49", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98459502863f612728a4c53d0e644743e4"}], "guid": "bfdfe7dc352907fc980b868725387e98f1b8aefbe4d1b0edf51640ebbffaadb4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98eb9c34cb05f25a98a3666536e3c8aeac", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98725973221efb8c479c179f718b1bc2aa", "name": "ZIM"}], "guid": "bfdfe7dc352907fc980b868725387e987e0ce7f7db3bf62d00283e8ca2e0829b", "name": "zego_zim", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98daf582ea233f74674b611f2e53cb66c3", "name": "zego_zim.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}