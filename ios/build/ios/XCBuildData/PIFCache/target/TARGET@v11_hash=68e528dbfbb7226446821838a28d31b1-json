{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98af505a975f8570360606a980e63af7cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a706d5c44b8e01395b0635143e35e976", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a706d5c44b8e01395b0635143e35e976", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sumeet/work/tools/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983ce365cea70f1494b18ebaf5b3bf41e9", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873cdfd6ead5ed4110ef7f45f5670aa01", "guid": "bfdfe7dc352907fc980b868725387e9822b0e8a2a7ee1039d9d028807bed7d8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988718d099507346e83bb37ac68d633b7f", "guid": "bfdfe7dc352907fc980b868725387e98aff039dda3b416fce2880ef1a06e572a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e4eb3530fdb365ae7e703c02f0739c7", "guid": "bfdfe7dc352907fc980b868725387e98485ed126f4b202d4028410d7a9d52671", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876177fdf700488ebe6f58ed9511cb710", "guid": "bfdfe7dc352907fc980b868725387e98918f57b720847384dc5f52cb1b892228", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ebf50e17d17d1e338a77ee0d7327966", "guid": "bfdfe7dc352907fc980b868725387e98bdd95bf1da080d18140257e575d587b4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3bb7d43e2c1c5dca30b9ab497936167", "guid": "bfdfe7dc352907fc980b868725387e98fcd09a0bf78b41bb10484989b52ef40d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c496b89451700b7c72e9fe43862fb677", "guid": "bfdfe7dc352907fc980b868725387e9865286d3b415f7282a314da9a57ba5f2b", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc7272145d1bd883b39c10706c009bb1", "guid": "bfdfe7dc352907fc980b868725387e98dde87ab678f55277d0b82837ec394ca2", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e10469eac6f4b691e41c8f30f539b86", "guid": "bfdfe7dc352907fc980b868725387e980c4dd0e0c3f3372009b8702406317938", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989229e16af28ac0432acbeb710f67ef95", "guid": "bfdfe7dc352907fc980b868725387e9880be6e8e20a60c74d9fd6196ab4a0fdc", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41786d32dfe1dabb775ea12f97f7792", "guid": "bfdfe7dc352907fc980b868725387e98b764654ae1f689fc99cf5998ed88c5b8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb77b95be8e2a47065963ded59db8749", "guid": "bfdfe7dc352907fc980b868725387e981ca03b2bbcc29477c87b804b7c867a34", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98900cba05b14c838c27a00e67996bea64", "guid": "bfdfe7dc352907fc980b868725387e984881429c95848ef5c238970eae7503f0", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980706e88983ec5c5f42fbe69c12f9be99", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dc7564275a304a23f6e77b339902db8", "guid": "bfdfe7dc352907fc980b868725387e98525316ae28bc2c4599fdd81984ad96d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a0017e338fe1e69d80468f769c5c307", "guid": "bfdfe7dc352907fc980b868725387e984496bd0068c1970bea663552ec8b19ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98272108ee3b06c8e0a9c5998364241245", "guid": "bfdfe7dc352907fc980b868725387e984f5df78a5210ae6b2aa478ad03b2e183"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6bd7029f1fe3c8f2167c48c56a72687", "guid": "bfdfe7dc352907fc980b868725387e982bb181b8a38746a303fd0a05d955f9c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e1bbf6a7501f4221b98469c94f1ec6e", "guid": "bfdfe7dc352907fc980b868725387e98d8cccd06e3e182eda8c31ae84351fa70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea09dc8f14e7d1dcd8f5e2a417497561", "guid": "bfdfe7dc352907fc980b868725387e9825aa15668a24552d7221bf9799034523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894934d878c3dd41bc0d857555567f49f", "guid": "bfdfe7dc352907fc980b868725387e98fff563a2ebfd48d56cbb8b6792ae563a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd7eca7baf3ebfbe4f39546eceda08c", "guid": "bfdfe7dc352907fc980b868725387e981851f826f246e5727e4aeb757286f1c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d90283b3dcc01a22901fbba1c1c7d536", "guid": "bfdfe7dc352907fc980b868725387e986b2003664d94f4e610c000b05462f1c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f92dadabb0df572a4b7b9c47788f8d80", "guid": "bfdfe7dc352907fc980b868725387e98b5a20bde5339a16972a5b14fd2a53f7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817072fec21a5a82196b827d8f384ce15", "guid": "bfdfe7dc352907fc980b868725387e983b3338c95584a29a997e8d7e565be27d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988323c42615efe6e1ab9d16e3f73e5cfa", "guid": "bfdfe7dc352907fc980b868725387e9879f75c76465ee8790744b653065e04d7"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}