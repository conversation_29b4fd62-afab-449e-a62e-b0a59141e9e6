{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d450cdab4ba8cd85e345feb55897373a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98764bf1b0d886d23cf9d2a1f9fd7475f7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3abe07f2c2649ef22aaef8e7c4870a6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acbecd6f0dde9e7ecfe2292446ae4418", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3abe07f2c2649ef22aaef8e7c4870a6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98166c76e4c74b3674c869203a2660ff28", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac371fb01b8cb04ed9c5573b63796777", "guid": "bfdfe7dc352907fc980b868725387e980ee0c26367f337c4929292e16b951b62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703f7f02f3caa04d5935311bdab4e383", "guid": "bfdfe7dc352907fc980b868725387e987a23e293379545e9afaf83ca608c2f51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b014e7e24f989f7def872d563fc6b91f", "guid": "bfdfe7dc352907fc980b868725387e98b9f1c1b37ef421c7eae75aa8d3c4a188", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfd4c10f9802e95a8cd2749979392688", "guid": "bfdfe7dc352907fc980b868725387e98dc952cdb39e2432d1cf2d9d6aee31640", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d9c156b2948391193b725876e65c630", "guid": "bfdfe7dc352907fc980b868725387e98d3676c660e049d0650abbe666a44586a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1abf312c91b41bad9bdb3c917b00729", "guid": "bfdfe7dc352907fc980b868725387e981e8f9050f70b22bb678276199ed7230a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850ee27b1ddf5e740c5118b7ada17f841", "guid": "bfdfe7dc352907fc980b868725387e98bf39599481516f82192394859dd86bdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896de63b54b3618b07146ec124f0664ea", "guid": "bfdfe7dc352907fc980b868725387e98d3eb07b1ddd27bedfda0f2a377950dc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987779986506792517e93acaa58a43a6c6", "guid": "bfdfe7dc352907fc980b868725387e98042b5b8e4c04236c91b2f39cd75a35ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d6f4af1d6f5f6c024503a2756cce3c6", "guid": "bfdfe7dc352907fc980b868725387e987f4b0956423c04b6f9027b1aabcbdcd1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851b0898b08debf790bd4ebdd749c4085", "guid": "bfdfe7dc352907fc980b868725387e98a13cd54a85ce952cb4ee07ccb1c64c2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4aa3fc89c965174f19f0e8ccb6e4dd8", "guid": "bfdfe7dc352907fc980b868725387e986b765ccc1877bda41bca48301a2ab6bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e59e44984f3f31a8fb47cad3e77908", "guid": "bfdfe7dc352907fc980b868725387e981802007fd9ea3d61d58f99e2ca910a5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d15324f17549018923310a977ed8b0", "guid": "bfdfe7dc352907fc980b868725387e987cf70931d7f5b310263e881de4d509dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837605a3f36de4aaf5fffa4238d943d2e", "guid": "bfdfe7dc352907fc980b868725387e98409bd672aff4d3c919568894101c9887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408f22c0ab6c5a96621138fc64912f5d", "guid": "bfdfe7dc352907fc980b868725387e98cd4b0bab894e9a77ae1ed45ffc604450", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98505fa6253932b0b466e88835b503e9d3", "guid": "bfdfe7dc352907fc980b868725387e98163aa18722b348927312358b64fbd273", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fb5a32827b6da8e00651b64a6c79a4a", "guid": "bfdfe7dc352907fc980b868725387e985650d7019121961be1f32e5c8e79e59f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e1b745ed75c27ce5bed38372904b1eb", "guid": "bfdfe7dc352907fc980b868725387e9896764a68c53c550c856ee0495ce28219", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b8c3337ebd3449ca670f7c20b2f0f92", "guid": "bfdfe7dc352907fc980b868725387e9892bea98bd771a28fe39ec62b8d164fc8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b20d392e47490a75c87ad9179c2a9734", "guid": "bfdfe7dc352907fc980b868725387e98006682bbe7656ec55df761c5e52050c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98725b408134eacec3c2405eafbbeaf5d5", "guid": "bfdfe7dc352907fc980b868725387e98b24a1d67a6aba6c6fb4a65084793ecee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b5b47e6454cc3d63726fc8584dde945", "guid": "bfdfe7dc352907fc980b868725387e98571560c2ee6e9032b4f650c9a5e7898c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897adda7b692955eb84b2b9ddaefa0e1d", "guid": "bfdfe7dc352907fc980b868725387e98189deb9b626ec96e6315d1dda653b6aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823f057db9d1d6f35b3d5370dd3a52562", "guid": "bfdfe7dc352907fc980b868725387e98e76e7350e114846a04c281415387250d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847959165461f8eaee26e9711db5e660f", "guid": "bfdfe7dc352907fc980b868725387e983b89300ed9843ad1959c7c409f9ea800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ca28585339a38d310285eba42f04b6", "guid": "bfdfe7dc352907fc980b868725387e98216feff0dea618eda3513a7002cb6fe3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f38a04dece2cfc795ef3957280937c7", "guid": "bfdfe7dc352907fc980b868725387e98d35c6197cab42ac1676f520d25d7f5cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98258e8c1fd0eac83008e07ab8420f0c92", "guid": "bfdfe7dc352907fc980b868725387e98273d0a5540d704f8fbe020409809a21c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5aa5ad2f69b44cc1fef80b0a5cba406", "guid": "bfdfe7dc352907fc980b868725387e98bf74527a8ceba1349a8727a922528b52", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985e5039b20b0cae4748b69499fb1cdfd1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984698b14576f53ab864515d408e2fc49c", "guid": "bfdfe7dc352907fc980b868725387e98d1ba39cca8374e52c7491fa1807fd6ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868b2ca4b32e7225e513ed812dec21162", "guid": "bfdfe7dc352907fc980b868725387e98ba2b0e748f0d7b226c05378ca8cded32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ffe9661df0c0ec62e78e64647ed10cf", "guid": "bfdfe7dc352907fc980b868725387e98414dc06a1e2e4bf8eff3e4052ac41716"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c160ada775d9e81568810b6661e47c9", "guid": "bfdfe7dc352907fc980b868725387e98d8c87ebb3ae1010a98a5c72dbc46b26c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837f6300e6e5bbdb40502c7622ec4dce6", "guid": "bfdfe7dc352907fc980b868725387e98a92ecca852c3de84c2033796bc155f29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840ac196291bdf408487cebcd8f25a8af", "guid": "bfdfe7dc352907fc980b868725387e98cd0881431746e7370be8e77b180f1902"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adef21c167ffbea1ef216992f5d02ff3", "guid": "bfdfe7dc352907fc980b868725387e98217589e2598242f8053b6ee50380b20f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c23e1cd83e57f7d5fdb9830f48d6ed", "guid": "bfdfe7dc352907fc980b868725387e981befc63acdf0d1794b7dd1f1e2970155"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f8726dec8b336dced65acf5cc45a1b", "guid": "bfdfe7dc352907fc980b868725387e988225bce30b24d5fd670434c54312beb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875d4464e0a60b359012e2953ef7ceefd", "guid": "bfdfe7dc352907fc980b868725387e98d4f7b1d76d98c9f179384e957bf14000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d45614fb2642c785e98329c086f48a16", "guid": "bfdfe7dc352907fc980b868725387e98355d6b9643d9683694c5b018cb804114"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a752752784cabaf65433c02f1e9cae85", "guid": "bfdfe7dc352907fc980b868725387e989893eedb6e1395d457c5bf2d9d271622"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d0d97254130353c5c65275d830ff89", "guid": "bfdfe7dc352907fc980b868725387e98b5d2856e58e34ffe50acff93b1c04481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981378f839df0b0b6e499c350f556330f8", "guid": "bfdfe7dc352907fc980b868725387e98cb34b3ba090bfa8b94143f01102573da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf7647ab13a2f2c9faa119411a0a9cfb", "guid": "bfdfe7dc352907fc980b868725387e984a097fd20652e45ab8dad5642c8998dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc6684d7ff91804524f5eda7f70554e", "guid": "bfdfe7dc352907fc980b868725387e981f0a47868a65303c2964efce067b7d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98148f07742bace2852fbc91365db1d9de", "guid": "bfdfe7dc352907fc980b868725387e98d4998a24ffcc1a38ad7c8303e964dd51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488a7a84bad36455aecb12079579e64c", "guid": "bfdfe7dc352907fc980b868725387e9803449ca86830de3f57abd3149dad4401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814a88cfa6f030a7f0ece3a306468d146", "guid": "bfdfe7dc352907fc980b868725387e983365acb165bf25dfebe5833035185a58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888773cf0b766f0e2b9f35ed5df9898ca", "guid": "bfdfe7dc352907fc980b868725387e9889c1ad7bc22ee7c8d0ad8c639d3dd2ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558c742cc2ed6ea9ede540cd974eac83", "guid": "bfdfe7dc352907fc980b868725387e98aa7e21dca2147f3a5738964bfed2223c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984072410cf28379e3f9bb878694e39436", "guid": "bfdfe7dc352907fc980b868725387e985ee618e14eda49d1090af592a5174e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f19ffbd7374f75762a2d647a4ac196a6", "guid": "bfdfe7dc352907fc980b868725387e984b0977f52776252f987373e568dbbc14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ed73001d23587b5a07b94e0b6968b5", "guid": "bfdfe7dc352907fc980b868725387e988575a0f9aefa851cb38665876b40ae97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15a044dada3647c2c69c2ba6a291054", "guid": "bfdfe7dc352907fc980b868725387e98fb8b364bb2dfe8aef100754f9b69a045"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ff226320d842baded044187debc467", "guid": "bfdfe7dc352907fc980b868725387e98618a5aa7fe31280e30d2ef2b1a2b1d22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eca83b676654a44320a3973c2e211bd", "guid": "bfdfe7dc352907fc980b868725387e981efc8ae201af2b2dfc2a9616b9b2558b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d82e5f161fc90259c15a18b5b0ef4630", "guid": "bfdfe7dc352907fc980b868725387e98a6183ac8df609aaff7e6fa72029dd338"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c6fb827e93b8599e360a5dbe31c81e4", "guid": "bfdfe7dc352907fc980b868725387e987a2b1463525e7595092256c42cb3d9df"}], "guid": "bfdfe7dc352907fc980b868725387e983a05ae78dbd90cefc78af097775cd09f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e99237a14967e2c12392462d37c95628", "guid": "bfdfe7dc352907fc980b868725387e98cb33067c3c27636c20d471a7f2ab30de"}], "guid": "bfdfe7dc352907fc980b868725387e98435b71ea30429aab201384c6cda38ef8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9875b812b75d570c8c556cae1bf9e375e6", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98868825dafdf3fd8a68c87ad966f753e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}