name: rooo_driver
description: Rooo App

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+3

environment:
  sdk: 3.6.2


dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.5



  pinput: 5.0.0
  # TOAST
  flutter_sliding_toast: 1.4.2 

  # IMAGE
  cached_network_image: 3.4.0
  

  # BASE
  intl: ^0.18.0
  google_fonts: 6.2.1
  # html: ^0.15.1
  shared_preferences: 2.3.1
    

  # UI
  sliding_up_panel: 2.0.0+1 
  dotted_line: 3.2.2
  lottie: 3.1.2
  flutter_rating_bar: 4.0.1
  flutter_staggered_animations: 1.1.1 
  # bottom_picker: ^2.0.1
  country_code_picker: 3.0.0
  flutter_native_contact_picker: 0.0.9
  # timeline_tile: ^2.0.0
  flutter_vector_icons: ^2.0.0
  flutter_switch: ^0.3.2
  syncfusion_flutter_charts: ^20.4.44
  accordion: ^2.6.0
  uuid: 4.5.1

  #OTP TEXTFIELD
  # otp_text_field: ^1.1.3

  # MAP
  #TODO: remove any
  geolocator: any
  geocoding: 3.0.0
  # google_maps_flutter: 2.10.0
  flutter_polyline_points: 2.1.0
  #TODO: remove any
  # ultra_map_place_picker: 0.4.0
  # location: ^4.4.0

  # IMAGES
  image_picker: 0.8.7+4
  file_picker: 8.1.2
  # date_time_picker: ^2.1.0

  # CONNECTIVITY
  package_info_plus: 8.1.2 


  # CONNECTIVITY
  http: 1.2.2
  connectivity_plus: 6.1.1
  url_launcher: 6.3.0
  webview_flutter: 4.8.0
  chucker_flutter: 1.8.3

  firebase_core: 3.6.0
  firebase_auth: 5.3.1
  # paginate_firestore: 1.0.3+1
  cloud_firestore: 5.4.0

  # STATE MANAGEMENT
  mobx: ^2.1.3
  flutter_mobx: ^2.0.6+5

  # Payment gateway
  # razorpay_flutter: ^1.3.4
  # flutter_stripe: ^3.0.0
  # flutter_paystack: ^1.0.7
  # flutter_braintree: ^2.3.1
  # flutterwave_standard: ^1.0.6
  # flutter_paytabs_bridge: ^2.3.2
  # my_fatoorah: ^3.2.0
  # paytm: ^3.0.1


  # PUSH NOTIFICATION
  onesignal_flutter: 5.2.6

  # Social Login
  google_sign_in: 6.2.1 

  # MQTT
  mqtt_client: 9.8.1


    #zego
  zego_uikit_prebuilt_call: 4.16.27
  zego_uikit_signaling_plugin: 2.8.11
  zego_express_engine: 3.20.0
  zego_zim: 2.20.0

  # DOWNLOAD INVOICE PDF
  pdf: 3.11.0
  path_provider: 2.1.4
  flutter_animated_button: ^2.0.3
  audioplayers: 6.4.0 
  share_plus: 10.1.2 
  flutter_contacts: ^1.1.7+1
  intl_phone_field: ^3.2.0
  # maps_launcher: 2.2.1
  flutter_logs: 2.1.12 
  map_launcher: ^3.1.0
  sign_in_with_apple: 6.1.1 
  flutter_bloc: ^8.1.3
  shimmer: ^3.0.0
  flutter_spinkit: ^5.2.1
  flex_color_scheme: ^7.3.1
  slider_button: ^2.1.0
  flutter_launcher_icons: ^0.14.1
  
 
  # geofence_service: ^5.0.0

  path: any
  # vector_math: any
  # flutter_svg: any
  # background_location_tracker:  1.4.3
  # latlong2: ^0.9.1
  timer_count_down: ^2.2.2
  # flutter_timer_countdown: ^1.0.7
  # animated_marker: ^0.0.5
  fluttertoast: ^8.2.6
  flutter_localizations:
    sdk: flutter
  polyline_animation_v1: ^0.0.6
  video_player_android: ^2.4.11
  location: ^7.0.0
  permission_handler: ^11.3.1
  swipe_to: ^1.0.6
  flutter_slidable: ^3.1.1
  firebase_pagination: ^4.0.1
  flutter_facebook_auth: ^6.2.0
  flutter_web_auth: ^0.6.0
  aad_oauth: 1.0.1
  # chucker_flutter: ^1.8.3
  action_slider: ^0.7.0
  tutorial_coach_mark: ^1.2.12
  custom_info_window: ^1.0.1
  wakelock_plus: ^1.2.8
  flutter_timezone: ^3.0.1
  mapbox_maps_flutter: 2.5.0
  # flutter_local_notifications: ^17.2.4

  # image_cropper: ^8.0.2
  google_maps_flutter: any
  vector_math: any
  html: any
flutter_launcher_icons:
  image_path: "images/icons/app_logo.png"
  # image_path_ios: "images/icons/app_logo.png"
  android: true
  adaptive_icon_foreground: "images/icons/app_logo.png"
  adaptive_icon_background: "#ffffff"
  ios: true
  remove_alpha_ios: true
  min_sdk_android: 21

  flutter_localizations:
    sdk: flutter


  # dio: ^5.1.1
  # retrofit: ^4.0.1
  # json_annotation: ^4.8.0
  # riverpod: ^2.3.5

dev_dependencies:
  # flutter_test:
  #   sdk: flutter

  # mobx_codegen: ^2.1.1
  # retrofit_generator: ^6.0.0+2
  # json_serializable: ^6.6.1
  #flutter packages pub run build_runner build --delete-conflicting-outputs


dependency_overrides:
  # firebase_core_platform_interface: ^4.5.2
  intl: ^0.18.0
  ffi: 2.0.1
  get: ^4.6.6
  web: 1.0.0
  js: 0.7.1
  vibration: 3.1.3
  # firebase_database_platform_interface: 0.2.6+5
  # open_file: 3.2.1
  # cloud_firestore: ^4.3.1
  flutter_logs:
    git:
      url: https://github.com/umair13adil/flutter_logs.git
      ref: master

flutter:
  generate: true
  uses-material-design: true

  assets:
    - images/
    - images/icons/
    - images/icons/accounts/light/
    - images/icons/accounts/dark/
    - images/icons/settings/light/
    - images/icons/settings/dark/

    - images/flag/
    - images/car_images_box/
    - audio/
    - assets/audio/online.mp3
    - assets/audio/offline.mp3
    - assets/audio/new_ride.mp3 
    - assets/audio/error.mp3
    - assets/audio/silence.mp3
    - assets/lottie/


    



