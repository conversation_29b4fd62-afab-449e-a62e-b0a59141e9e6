# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.



analyzer:
 errors:
   must_be_immutable: error
  #  prefer_const_constructors: error
  #  prefer_final_fields: error
  #  unused_field: error
   prefer_const_constructors_in_immutables: error
   depend_on_referenced_packages: error
   avoid_unnecessary_containers: error
   sized_box_for_whitespace: error
  #  avoid_print: error
  #  dead_code: error
  #  unused_element: error
   empty_statements: error
  #  unused_local_variable: error
  #  unused_import: error
   sort_child_properties_last: error
  #  annotate_overrides: error
  #  todo: warning
   must_call_super: error
   use_build_context_synchronously: error
  #  always_declare_return_types: error
  #  curly_braces_in_flow_control_structures: error
   non_constant_identifier_names: ignore
  #  avoid_init_to_null: error
  #  file_names: error
  #  constant_identifier_names: error
  #  unnecessary_null_comparison: error
  #  unnecessary_string_interpolations: error
  #  prefer_interpolation_to_compose_strings: error
  #  use_key_in_widget_constructors: error
  #  prefer_if_null_operators: error
  #  avoid_returning_null_for_void: error
  #  override_on_non_overriding_member: error
   duplicate_import: error
  #  unrelated_type_equality_checks: error
  #  prefer_const_literals_to_create_immutables: error
  #  unchecked_use_of_nullable_value: error
  #  dead_null_aware_expression: error
  #  no_leading_underscores_for_local_identifiers: error
   prefer_is_empty: error
  #  body_might_complete_normally_nullable: error
  #  unnecessary_import: error
  #  unignorable_ignore: error
  #  iterable_contains_unrelated_type: error
  #  unnecessary_const: error
  #  unnecessary_non_null_assertion: error
linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule
    

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
