{"project_info": {"project_number": "972284281345", "project_id": "rooo-2c8b1", "storage_bucket": "rooo-2c8b1.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:972284281345:android:ef6c31c0e61ddfe7da7600", "android_client_info": {"package_name": "app.rooo.driver"}}, "oauth_client": [{"client_id": "972284281345-4r8ddcbnn95tvu7e4fq8lk80b3najb9a.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "app.rooo.driver", "certificate_hash": "2a034b99575c68d10f5d5a868e2c7f4e64100008"}}, {"client_id": "972284281345-ua60ubmi2genbs9pnkvmb4osqkr2bpt2.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "app.rooo.driver", "certificate_hash": "26e5e9d0754754654092355dd2afaa68f0699e17"}}, {"client_id": "972284281345-qkajstri499diuan93vuiv46ibf982uj.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDY0eYHMkXAq4JdpL1XWf1B3hU7jZqPNFE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "972284281345-c48h98moq31gkff6u48pr6cb1jj5h9vk.apps.googleusercontent.com", "client_type": 3}, {"client_id": "972284281345-csbm4ti46p9t2vmr2id07giekub8v368.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "app.rooo.driver"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:972284281345:android:1d4f32995bb9196cda7600", "android_client_info": {"package_name": "app.rooo.rider"}}, "oauth_client": [{"client_id": "972284281345-dfhgldvssn4sf5b405g3fmo4uajj4vao.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "app.rooo.rider", "certificate_hash": "26e5e9d0754754654092355dd2afaa68f0699e17"}}, {"client_id": "972284281345-mmga817kltarnkrcu3a54t04in6oovjr.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "app.rooo.rider", "certificate_hash": "e9f727de8eae256d72a425fc855127b7dd972d43"}}, {"client_id": "972284281345-qkajstri499diuan93vuiv46ibf982uj.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDY0eYHMkXAq4JdpL1XWf1B3hU7jZqPNFE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "972284281345-c48h98moq31gkff6u48pr6cb1jj5h9vk.apps.googleusercontent.com", "client_type": 3}, {"client_id": "972284281345-csbm4ti46p9t2vmr2id07giekub8v368.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "app.rooo.driver"}}]}}}], "configuration_version": "1"}