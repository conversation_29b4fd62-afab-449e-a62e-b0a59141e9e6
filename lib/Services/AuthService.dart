import 'dart:io';

import 'package:rooo_driver/features/documents/screens/document_screen.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/utils/Extensions/StringExtensions.dart';
import 'package:http/http.dart';

import '../main.dart';
import '../model/LoginResponse.dart';
import '../network/RestApis.dart';
import '../utils/Constants.dart';
import '../utils/Extensions/app_common.dart';

class AuthServices {
  Future<void> updateUserData(UserData user) async {
    userService.updateDocument({
      'player_id': sharedPref.getString(PLAYER_ID),
      'updatedAt': Timestamp.now(),
    }, user.uid);
  }

  Future<void> signUpWithEmailPassword(
    context, {
    String? name,
    String? email,
    String? password,
    String? mobileNumber,
    String? fName,
    String? lName,
    String? userName,
    bool socialLoginName = false,
    String? userType,
    String? uID,
    bool isOtp = false,
    UserDetail? userDetail,
    required Iterable<int> serviceIds,
    bool? nightDrivingPreference,
    String? securityNumber,
    String? age,
    String? gender,
    String? referralCode,
    File? file,
    bool isExist = true,
  }) async {
    UserCredential? userCredential = await roooFirebaseAuth
        .createUserWithEmailAndPassword(email: email!, password: password!);
    if (userCredential.user != null) {
      try {
        await FirebaseAuth.instance
            .signInWithEmailAndPassword(email: email, password: password)
            .then((value) {})
            .onError((error, stackTrace) {});
      } catch (e) {
        print(e.toString());
      }
      User currentUser = userCredential.user!;

      UserData userModel = UserData();

      /// Create user
      userModel.uid = currentUser.uid.validate();
      userModel.email = email;
      userModel.referralCode = referralCode;

      userModel.contactNumber = mobileNumber.validate();


      userModel.userType = userType.validate();
      userModel.displayName = fName.validate() + " " + lName.validate();
      userModel.firstName = fName.validate();
      userModel.lastName = lName.validate();
      userModel.createdAt = Timestamp.now().toDate().toString();
      userModel.updatedAt = Timestamp.now().toDate().toString();
      userModel.playerId = sharedPref.getString(PLAYER_ID).validate();
      sharedPref.setString(UID, userCredential.user!.uid.validate());

      // userModel.userDetail = UserDetail(
      //   nightDrivingPreference: nightDrivingPreference,
      //   age: int.parse(age!),
      //   socialSecurityNumber: securityNumber,
      // );

      await userService
          .addDocumentWithCustomId(currentUser.uid, userModel.toJson())
          .then((value) async {
        MultipartRequest multiPartRequest =
            await getMultiPartRequest('driver-signup');
        multiPartRequest.fields['username'] = userName!;
        multiPartRequest.fields['first_name'] = fName!;
        multiPartRequest.fields['last_name'] = lName!;
        multiPartRequest.fields['email'] = email;
        multiPartRequest.fields['user_type'] = 'driver';
        multiPartRequest.fields['contact_number'] = mobileNumber!;
        multiPartRequest.fields['password'] = password;
        multiPartRequest.fields['player_id'] =
            sharedPref.getString(PLAYER_ID).validate();
        multiPartRequest.fields['uid'] = userModel.uid!;
        multiPartRequest.fields['gender'] = gender!;
        multiPartRequest.fields["referral_code"] = referralCode!;

        String servicesData = "";
        for (var element in serviceIds) {
          servicesData += "," + element.toString();
        }
        servicesData = servicesData.substring(1);

        multiPartRequest.fields['service_ids'] = servicesData;

        // multiPartRequest.fields['user_detail'] = jsonEncode(UserDetail(
        //   age: int.parse(age),
        //   nightDrivingPreference: nightDrivingPreference,
        //   socialSecurityNumber: securityNumber,
        // ).toJson());

        if (file != null) {
          multiPartRequest.files
              .add(await MultipartFile.fromPath('profile_image', file.path));
        }

        // Map req = {
        //   'first_name': fName,
        //   'last_name': lName,
        //   'username': userName,
        //   'email': email,
        //   "user_type": "driver",
        //   "contact_number": mobileNumber,
        //   'password': password,
        //   "player_id": sharedPref.getString(PLAYER_ID).validate(),
        //   "uid": userModel.uid,
        //   "gender": gender,
        //   if (socialLoginName) 'login_type': 'mobile',
        //   'service_ids': serviceIds,
        //   'age': age,
        //   'socialSecurityNumber': securityNumber,
        //   'nightDrivingPreference': nightDrivingPreference,
        //   // "user_detail": {
        //   //   'car_model': userDetail!.carModel.validate(),
        //   //   'car_color': userDetail.carColor.validate(),
        //   //   'car_plate_number': userDetail.carPlateNumber.validate(),
        //   //   'car_production_year': userDetail.carProductionYear.validate(),
        //   // },
        //   //updated service_id to service_ids as a Set<int>
        // };

        if (!isExist) {
          updateProfileUid();
          if (sharedPref.getInt(IS_Verified_Driver) == 1) {


            GlobalMethods.pushAndRemoveAll(context: context, screen:  RideScreen(), screenIdentifier: ScreenIdentifier. InitialScreen);
            // launchScreen(context, InitialScreen(), isNewTask: true);
          } else {
                        GlobalMethods.pushAndRemoveAll(context: context, screen:  DocumentScreen(canGoToDashboard: true), screenIdentifier: ScreenIdentifier. DocumentScreen);

            // launchScreen(context, DocumentScreen(canGoToDashboard: true),
            //     pageRouteAnimation: PageRouteAnimation.Slide, isNewTask: true);
          }
        } else {
          // await signUpApi(req).then((value) {
          print('nct-> sending reg mul ');
          await sendMultiPartRequest(
            multiPartRequest,
          ).onError((e, _) {
            print('nct-> sending reg mul error 3' + e.toString());
            GlobalMethods.toast(errorMessage);
          }).then((data) async {
            print('nct-> reg mul done ');
            print('nct-> ' + data.toString());

            if (data != null) {
              if (data['status'] == false) {
                GlobalMethods.toast(data['message']);
                await userService
                    .removeDocument(userModel.uid!)
                    .then((value) async {
                  AuthServices authService = AuthServices();

                  await authService
                      .deleteUserFirebase()
                      .then((value) async {})
                      .catchError((error) {});
                }).catchError((error) {});
                return;
              }
              try {
                var loginResponse = LoginResponse.fromJson(data);

                await sharedPref.setString(
                    TOKEN, loginResponse.data!.apiToken.validate());
                await sharedPref.setString(
                    USER_TYPE, loginResponse.data!.userType.validate());
                await sharedPref.setString(
                    FIRST_NAME, loginResponse.data!.firstName.validate());
                await sharedPref.setString(
                    LAST_NAME, loginResponse.data!.lastName.validate());
                await sharedPref.setString(CONTACT_NUMBER,
                    loginResponse.data!.contactNumber.validate());
                await sharedPref.setString(
                    USER_EMAIL, loginResponse.data!.email.validate());
              
              
                await sharedPref.setInt(USER_ID, loginResponse.data!.id ?? 0);
                await sharedPref.setString(USER_PROFILE_PHOTO,
                    loginResponse.data!.profileImage.validate());
                await sharedPref.setString(
                    GENDER, loginResponse.data!.gender.validate());
                if (loginResponse.data!.isOnline != null)
                  await sharedPref.setInt(
                      IS_ONLINE, loginResponse.data!.isOnline ?? 0);
                await sharedPref.setInt(IS_Verified_Driver,
                    loginResponse.data!.isVerifiedDriver ?? 0);
                if (loginResponse.data!.uid != null)
                  await sharedPref.setString(
                      UID, loginResponse.data!.uid.validate());
                await sharedPref.setString(
                    LOGIN_TYPE, loginResponse.data!.email.validate());

                await appStore.setLoggedIn(true);
                await appStore
                    .setUserEmail(loginResponse.data!.email.validate());
                await appStore.setUserProfile(
                    loginResponse.data!.profileImage.validate());

                if (sharedPref.getInt(IS_Verified_Driver) == 1) {

                  GlobalMethods.pushAndRemoveAll(context: context, screen: RideScreen(), screenIdentifier: ScreenIdentifier.InitialScreen);
                  // launchScreen(context, InitialScreen(), isNewTask: true);
                } else {
                                    GlobalMethods.pushAndRemoveAll(context: context, screen:  DocumentScreen(canGoToDashboard: true), screenIdentifier: ScreenIdentifier.DocumentScreen);

                  // launchScreen(context, DocumentScreen(canGoToDashboard: true),
                  //     pageRouteAnimation: PageRouteAnimation.Slide,
                  //     isNewTask: true);
                }
              } catch (e) {
                print('nct-> sending reg mul error' + e.toString());

                GlobalMethods.toast(data['message']);
              }
            } else {
              print('nct-> sending reg mul error 2');

              GlobalMethods.toast(errorMessage);
            }
          }).catchError((error) {
            print('nct-> sending reg mul error 4' + "Server error");

            GlobalMethods.toast("Server error");
            log('${"Server error"}');
          });
        }

        appStore.setLoading(false);
      }).catchError((e) {
        appStore.setLoading(false);
        GlobalMethods.toast('${e.toString()}');
        log('${e.toString()}');
      });
    } else {
      throw "errorSomethingWentWrong";
    }
  }

  Future<void> signInWithEmailPassword(context,
      {required String email, required String password}) async {
    await roooFirebaseAuth
        .signInWithEmailAndPassword(email: email, password: password)
        .then((value) async {
      appStore.setLoading(true);
      final User user = value.user!;
      UserData userModel = await userService.getUser(email: user.email);
      await updateUserData(userModel);

      appStore.setLoading(true);
      //Login Details to SharedPreferences
      sharedPref.setString(UID, userModel.uid.validate());
      sharedPref.setString(USER_EMAIL, userModel.email.validate());
      sharedPref.setBool(IS_LOGGED_IN, true);

      //Login Details to AppStore
      appStore.setUserEmail(userModel.email.validate());
      appStore.setUId(userModel.uid.validate());

      //
    }).catchError((e) {
      GlobalMethods.toast(e.toString());
      log(e.toString());
    });
  }

  Future<void> loginFromFirebaseUser(User currentUser,
      {LoginResponse? loginDetail,
      String? fullName,
      String? fName,
      String? lName}) async {
    UserData userModel = UserData();

    if (await userService.isUserExist(loginDetail!.data!.email)) {
      ///Return user data
      await userService.userByEmail(loginDetail.data!.email).then((user) async {
        userModel = user;
        appStore.setUserEmail(userModel.email.validate());
        appStore.setUId(userModel.uid.validate());

        await updateUserData(user);
      }).catchError((e) {
        log(e);
        throw e;
      });
    } else {
      /// Create user
      userModel.uid = currentUser.uid.validate();
      userModel.id = loginDetail.data!.id;
      userModel.email = loginDetail.data!.email.validate();
      userModel.contactNumber = loginDetail.data!.contactNumber.validate();
      userModel.email = loginDetail.data!.email.validate();
      userModel.contactNumber = loginDetail.data!.contactNumber.validate();
      userModel.profileImage = loginDetail.data!.profileImage.validate();
      userModel.playerId = sharedPref.getString(PLAYER_ID);

      sharedPref.setString(UID, currentUser.uid.validate());
      log(sharedPref.getString(UID)!);
      sharedPref.setString(USER_EMAIL, userModel.email.validate());
      sharedPref.setBool(IS_LOGGED_IN, true);

      log(userModel.toJson());

      await userService
          .addDocumentWithCustomId(currentUser.uid, userModel.toJson())
          .then((value) {
        //
      }).catchError((e) {
        throw e;
      });
    }
  }

  // Future<void> loginWithOTP(BuildContext context, String phoneNumber) async {
  //   return await _auth.verifyPhoneNumber(
  //     phoneNumber: phoneNumber,
  //     verificationCompleted: (PhoneAuthCredential credential) async {},
  //     verificationFailed: (FirebaseAuthException e) {
  //       if (e.code == 'invalid-phone-number') {
  //         GlobalMethods.toast('The provided phone number is not valid.');
  //         throw 'The provided phone number is not valid.';
  //       } else {
  //         GlobalMethods.toast(e.toString());
  //         throw e.toString();
  //       }
  //     },
  //     codeSent: (String verificationId, int? resendToken) async {
  //       Navigator.pop(context);
  //       appStore.setLoading(false);
  //       await showDialog(
  //         context: context,
  //         builder: (context) => AlertDialog(
  //             content: OTPDialog(
  //                 verificationId: verificationId,
  //                 isCodeSent: true,
  //                 phoneNumber: phoneNumber)),
  //         barrierDismissible: false,
  //       );
  //     },
  //     codeAutoRetrievalTimeout: (String verificationId) {
  //       //
  //     },
  //   );
  // }

  Future deleteUserFirebase() async {
    if (FirebaseAuth.instance.currentUser != null) {
      FirebaseAuth.instance.currentUser!.delete();
      await FirebaseAuth.instance.signOut();
    }
  }
}