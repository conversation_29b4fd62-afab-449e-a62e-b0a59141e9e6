import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';


abstract class BaseService {
  CollectionReference? messageRef;

  BaseService({this.messageRef});

  Future<DocumentReference> addDocument(Map data) async {
    var doc = await messageRef!.add(data);
    doc.update({'uid': doc.id});
    return doc;
  }

  Future<DocumentReference> addDocumentWithCustomId(String id, Map<String, dynamic> data) async {
    var doc = messageRef!.doc(id);

    return await doc.set(data).then((value) {
      log('Added: $data');

      return doc;
    }).catchError((e) {
      log(e);
      throw e;
    });
  }

  Future<void> updateDocument(Map<String, dynamic> data, String? id) => messageRef!.doc(id).update(data);

  Future<void> removeDocument(String id) => messageRef!.doc(id).delete();

  Future<bool> isUserExist(String? email) async {
    Query query = messageRef!.limit(1).where('email', isEqualTo: email);
    var res = await query.get();

    // ignore: unnecessary_null_comparison
    if (res.docs != null) {
      return res.docs.length == 1;
    } else {
      return false;
    }
  }

  Future<Iterable> getList() async {
    var res = await messageRef!.get();
    Iterable it = res.docs;
    return it;
  }
}
