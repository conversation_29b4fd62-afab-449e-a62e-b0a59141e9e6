import 'dart:io';

import 'package:rooo_driver/utils/Extensions/app_common.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart';
import 'package:rooo_driver/utils/Extensions/StringExtensions.dart';
import '../main.dart';
import '../global/models/UserDetailModel.dart';
import '../utils/Constants.dart';
import 'BaseServices.dart';

class UserService extends BaseService {
  FirebaseFirestore fireStore = FirebaseFirestore.instance;
  // FirebaseStorage _storage = FirebaseStorage.instance;

  UserService() {
    messageRef = fireStore.collection(USER_COLLECTION);
  }

  Future<void> updateUserInfo(Map data, String id, {File? profileImage}) async {
    if (profileImage != null) {
      String fileName = basename(profileImage.path);
      // Reference storageRef =
      //     _storage.ref().child("$USER_PROFILE_PHOTO/$fileName");
      // UploadTask uploadTask = storageRef.putFile(profileImage);
      // await uploadTask.then((e) async {
      //   await e.ref.getDownloadURL().then((value) {
      //     sharedPref.setString(USER_PROFILE_PHOTO, value);
      //     data.putIfAbsent("photoUrl", () => value);
      //   });
      // });
    }

    return messageRef!.doc(id).update(data as Map<String, Object?>);
  }

  Future<void> updateUserStatus(Map data, String id) async {
    return messageRef!.doc(id).update(data as Map<String, Object?>);
  }

  Future<UserData> getUser({String? email}) {
    return messageRef!.where("email", isEqualTo: email).limit(1).get().then((value) {
      if (value.docs.length == 1) {
        return UserData.fromJson(
            value.docs.first.data() as Map<String, dynamic>);
      } else {
        throw 'User Not found';
      }
    });
  }

  Stream<List<UserData>> users({String? searchText}) {
    return messageRef!
        .where('caseSearch',
            arrayContains: searchText.validate().isEmpty
                ? null
                : searchText!.toLowerCase())
        .snapshots()
        .map((x) {
      return x.docs.map((y) {
        return UserData.fromJson(y.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  Future<UserData> userByEmail(String? email) async {
    return await messageRef!
        .where('email', isEqualTo: email)
        .limit(1)
        .get()
        .then((value) {
      if (value.docs.isNotEmpty) {
        return UserData.fromJson(
            value.docs.first.data() as Map<String, dynamic>);
      } else {
        throw 'No User Found';
      }
    });
  }

  Stream<UserData> singleUser(String? id, {String? searchText}) {
    return messageRef!.where('uid', isEqualTo: id).limit(1).snapshots().map((event) {
      return UserData.fromJson(event.docs.first.data() as Map<String, dynamic>);
    });
  }

  Future<UserData> userByMobileNumber(String? phone) async {
    log("Phone $phone");
    return await messageRef!
        .where('phoneNumber', isEqualTo: phone)
        .limit(1)
        .get()
        .then((value) {
      if (value.docs.isNotEmpty) {
        return UserData.fromJson(
            value.docs.first.data() as Map<String, dynamic>);
      } else {
        throw "No user found";
      }
    });
  }
}
