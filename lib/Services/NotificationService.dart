
import 'package:rooo_driver/global/export/app_export.dart';


class NotificationService {
  Future<void> sendPushNotifications(
      {required String title,
      required String content,
      required String rideId,
      required String playerId,
      // required String driverPlayerId,
      required String riderUid,
      // required String driverUid,

      required String profileImage,
      required String riderName,
      String? id,
      String? image,
      String? receiverPlayerId}) async {
    log(receiverPlayerId!);
    Map req = {
      'headings': {
        'en': title,
      },
      'contents': {
        'en': content,
      },
      'data': {
        'type': 'chat_msg',
        "ride_id": rideId,
        "uid": riderUid,
        "player_id": playerId,
        "name": riderName,
        "profile_image": profileImage,
      },

      'big_picture': image.validate().isNotEmpty ? image.validate() : '',
      'large_icon': image.validate().isNotEmpty ? image.validate() : '',
      //   'small_icon': mAppIconUrl,
      'app_id': AppCred.oneSignalAppIdRider,
      'include_subscription_ids': [receiverPlayerId],
      'android_group': "ROOO Rider",
          "target_channel": "push",
      // "isAndroid": true,
      // "content_available": true,
    };
    var header = {
      HttpHeaders.authorizationHeader: 'Basic ${AppCred.oneSignalRestKeyRider}',
      HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    };
    // GlobalMethods.succesToast(navigatorKey.currentContext!, receiverPlayerId);

    Response res = await post(
      Uri.parse('https://api.onesignal.com/notifications'),
      body: jsonEncode(req),
      headers: header,
    );

    log(res.body);

    if (res.statusCode.isEven) {
    } else {
      throw 'Something Went Wrong';
    }
  }
}
