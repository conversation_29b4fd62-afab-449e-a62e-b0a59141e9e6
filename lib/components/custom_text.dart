import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class CustomText extends StatefulWidget {
  final bool? underline;
  final String data;
  final FontWeight? fontweight;
  final double? size;
  final Color? color;
  final TextOverflow? textOverflow;
  final int? maxline;
  final TextAlign? textAlign;
  final double? lineHeight;
  final TextStyle? textStyle;

  const CustomText({
    Key? key,
    this.underline,
    required this.data,
    this.fontweight,
    this.size,
    this.color,
    this.textOverflow,
    this.maxline,
    this.textAlign,
    this.lineHeight,
    this.textStyle,
  }) : super(key: key);

  @override
  State<CustomText> createState() => _CustomTextState();
}

class _CustomTextState extends State<CustomText> {
  bool _showAll = false;
  bool _showLessMore = false;
  String _textToShow = '';
  final int _wordsLimit = 35;

  @override
  void initState() {
    if (RegExp(r'[\w-]+').allMatches(widget.data).length - _wordsLimit > 5) {
      _showLessMore = true;
      List<String> words = widget.data.split(' ');

      for (var i = 0; i < _wordsLimit; i++) {
        _textToShow += '${words[i]} ';
      }
      _textToShow = _textToShow.trim();
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (_showLessMore) {
      return RichText(
        text: TextSpan(
            text: _showAll ? widget.data : '$_textToShow...',
            style: TextStyle(
              fontSize: widget.size,
              fontWeight: widget.fontweight,
            ),
            children: [
              TextSpan(
                text: _showAll ? ' Show less' : ' Show more',
                style: const TextStyle(color: Colors.red),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    setState(() {
                      _showAll = !_showAll;
                    });
                  },
              ),
            ]),
      );
    }

    return Text(
      widget.data,
      style: widget.textStyle ??
          TextStyle(
            decoration: widget.underline == null
                ? TextDecoration.none
                : widget.underline!
                    ? TextDecoration.underline
                    : TextDecoration.none,
            color: widget.color,
            fontSize: widget.size,
            fontWeight: widget.fontweight,
            overflow: widget.textOverflow,
            height: widget.lineHeight,
          ),
      maxLines: widget.maxline,
      textAlign: widget.textAlign,
    );
  }
}
