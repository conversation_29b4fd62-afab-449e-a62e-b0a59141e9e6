import 'package:flutter/material.dart';

import '../utils/Common.dart';

class CircularIconWidget extends StatefulWidget {
  final Color backgroundColor;
  final Color iconColor;

  final double size;
  final dynamic Function()? onTap;
  final IconData icon;

  const CircularIconWidget(
      {super.key,
      required this.backgroundColor,
      required this.size,
      this.onTap,
      required this.icon,
      required this.iconColor});

  @override
  State<CircularIconWidget> createState() => _CircularIconWidgetState();
}

class _CircularIconWidgetState extends State<CircularIconWidget> {
  @override
  Widget build(BuildContext context) {
    return inkWellWidget(
      onTap: widget.onTap,
      child: Container(
        width: widget.size,
        height: widget.size,
        child: Icon(
          widget.icon,
          color: widget.iconColor,
          size: widget.size,
        ),
        decoration: BoxDecoration(
            color: widget.backgroundColor, shape: BoxShape.circle),
      ),
    );
  }
}
