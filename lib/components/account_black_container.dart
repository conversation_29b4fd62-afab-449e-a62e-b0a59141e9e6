import 'package:rooo_driver/components/custom_icon.dart';

// ignore: unused_import
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';

class AccountBlackContainer extends StatefulWidget {
  final String title;
  final Function() onTap;
  final String? address;
  const AccountBlackContainer(
      {super.key, required this.title, required this.onTap, this.address});

  @override
  State<AccountBlackContainer> createState() => _AccountBlackContainerState();
}

class _AccountBlackContainerState extends State<AccountBlackContainer> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    widget.address == null
                        ? Icon(
                            Icons.notifications,
                            color: IS_DARK_MODE_ON
                                ? Colors.grey
                                : const Color.fromARGB(255, 120, 117, 117),
                          )
                        : CustomIcon(
                            height: 20,
                            width: 20,
                            adress: IS_DARK_MODE_ON
                                ? "images/icons/accounts/dark/" +
                                    widget.address!
                                : "images/icons/accounts/light/" +
                                    widget.address!),
                    width10,
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
                Icon(Icons.keyboard_arrow_right_outlined, color: Colors.grey)
              ],
            ),
          ),
          Divider(
            thickness: 1,
          )
        ],
      ),
    );
  }
}
