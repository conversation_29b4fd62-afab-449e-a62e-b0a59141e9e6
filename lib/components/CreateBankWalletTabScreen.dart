// import 'package:rooo_driver/screens/BankInfoScreen.dart';
// import 'package:rooo_driver/screens/MyWalletScreen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';

// import '../utils/Constants.dart';

// class CreateBankWalletTabScreen extends StatefulWidget {
//   final String status;
//   CreateBankWalletTabScreen({required this.status});

//   @override
//   CreateBankWalletTabScreenState createState() =>
//       CreateBankWalletTabScreenState();
// }

// class CreateBankWalletTabScreenState extends State<CreateBankWalletTabScreen> {
//   ScrollController scrollController = ScrollController();

//   int currentPage = 1;
//   int totalPage = 1;

//   @override
//   void initState() {
//     super.initState();

//     // init();

//     // scrollController.addListener(() {
//     //   if (scrollController.position.pixels ==
//     //       scrollController.position.maxScrollExtent) {
//     //     if (currentPage < totalPage) {
//     //       appStore.setLoading(true);
//     //       currentPage++;
//     //       setState(() {});

//     //       init();
//     //     }
//     //   }
//     // });
//     // afterBuildCreated(() => appStore.setLoading(true));
//   }

//   void init() async {
//     // await getRiderRequestList(
//     //         page: currentPage,
//     //         status: widget.status,
//     //         driverId: sharedPref.getInt(USER_ID))
//     //     .then((value) {
//     //   appStore.setLoading(false);

//     //   currentPage = value.pagination!.currentPage!;
//     //   totalPage = value.pagination!.totalPages!;
//     //   if (currentPage == 1) {
//     //     riderData.clear();
//     //   }
//     //   riderData.addAll(value.data!);
//     //   setState(() {});
//     // }).catchError((error) {
//     //   appStore.setLoading(false);
//     //   log("Server error");
//     // });
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Observer(builder: (context) {
//       return Stack(
//         children: [
//           widget.status == BANK_INFO ? BankInfoScreen() : MyWalletScreen(),

//           // Visibility(
//           //   visible: appStore.isLoading,
//           //   child: loaderWidget(),
//           // ),
//           // if (riderData.isEmpty)
//           //   appStore.isLoading ? SizedBox() : emptyWidget(),
//         ],
//       );
//     });
//   }
// }
