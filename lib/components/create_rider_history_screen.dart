// import 'package:rooo_driver/components/custom_text.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/model/CurrentRequestModel.dart';
// import 'package:rooo_driver/network/RestApis.dart';
// import 'package:rooo_driver/screens/ride_history_details_screen.dart';
// import 'package:rooo_driver/utils/Common.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';

// class RideHistoryCompleted extends StatefulWidget {
//   final String? status;
//   const RideHistoryCompleted({super.key, required this.status,});

//   @override
//   State<RideHistoryCompleted> createState() => _RideHistoryCompletedState();
// }

// class _RideHistoryCompletedState extends State<RideHistoryCompleted> {
//   List<OnRideRequest> ride_history_list = [];
//   int currentPage = 1;
//   int totalPage = 1;
//    String? api_message;
   

//   void getRideHistoryCompletedList() async {
//     setState(() {
//       appStore.setLoading(true);
//     });
//     await getRideHistoryApi(status: widget.status.toString()).then((value) {
//       setState(() {
//         appStore.setLoading(false);
//       });
//       if (value.data != null) {
//         ride_history_list = value.data!;
//         api_message=value.message;
//       }

//       currentPage = value.pagination!.currentPage!;
//       print(
//           "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa");
//       print(ride_history_list.toString());
//       setState(() {});
//     }).catchError((error) {
//       setState(() {
//         appStore.setLoading(false);
//       });
//       log("Server error" +
//           "qweeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee");
//     });
//   }

//   @override
//   void initState() {
//     //  : implement initState
//     super.initState();
//     getRideHistoryCompletedList();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [



        
//         Padding(
//           padding: screenPadding,
//           child: ListView.separated(
//               shrinkWrap: true,
//               itemBuilder: (context, index) {
//                 return InkWell(
//                   onTap: () {
//                     launchScreen(
//                         context,
//                         RideHistoryDetailScreen(
//                             id: ride_history_list[index].id ?? 0));
//                   },
//                   child: Card(
//                     child: Container(
//                       padding: screenPadding,
//                       child: Column(
//                         children: [
//                           // Row(
//                           //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           //   children: [
//                           //     Expanded(child: CustomText(data: "Ride id")),
//                           //     Expanded(
//                           //       child: CustomText(
//                           //           data:
//                           //               ride_history_list[index].id.toString()),
//                           //     ),
//                           //   ],
//                           // ),
//                           // Divider(
//                           //   thickness: 1,
//                           // ),
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Expanded(
//                                   child: CustomText(data: language.PickupLocationTxt)),
//                               Expanded(
//                                 child: CustomText(
//                                     data: ride_history_list[index]
//                                         .startAddress
//                                         .toString()),
//                               ),
//                             ],
//                           ),
//                           Divider(
//                             thickness: 1,
//                           ),
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Expanded(child: CustomText(data: language.destinationLocation)),
//                               Expanded(
//                                 child: CustomText(
//                                     data: ride_history_list[index]
//                                         .endAddress
//                                         .toString()),
//                               ),
//                             ],
//                           ),
//                           Divider(
//                             thickness: 1,
//                           ),
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Expanded(child: CustomText(data: language.TimeTxt)),
//                               Expanded(
//                                 child: CustomText(
//                                     data: ride_history_list[index]
//                                         .datetime
//                                         .toString()),
//                               ),
//                             ],
//                           ),
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Expanded(child: CustomText(data: language.statusTxt)),
//                               Expanded(
//                                 child: Container(
//                                   alignment: Alignment.center,
//                                   padding: EdgeInsets.all(5),
//                                   color: widget.status == "cancelled"
//                                       ? Colors.red
//                                       : Colors.green,
//                                   child: CustomText(
//                                       color: Colors.white,
//                                       data: widget.status.toString()),
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 );
//               },
//               separatorBuilder: (context, index) => height10,
//               itemCount: ride_history_list.length),
//         ),
//        Observer(
//             builder: (context) {
//               return Visibility(
//                 visible: !appStore.isLoading&&ride_history_list.isEmpty,
//                 child: emptyWidget(message: api_message.toString()),
//               );
//             },
//           ),
//              Observer(
//             builder: (context) {
//               return Visibility(
//                 visible: appStore.isLoading,
//                 child: loaderWidget(),
//               );
//             },
//           )
//       ],
//     );
//   }
// }
