// import 'package:rooo_driver/features/earnings/model/earnings_model_responce.dart';
// import 'package:rooo_driver/global/constants/app_enums.dart';
// import 'package:rooo_driver/global/constants/constants.dart';
// import 'package:rooo_driver/global/export/app_export.dart';
// import 'package:rooo_driver/global/widgets/app_button.dart';
// import 'package:rooo_driver/screens/BankAndWalletScreen.dart';
// import 'package:rooo_driver/utils/Extensions/AppButtonWidget.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';
// import 'package:syncfusion_flutter_charts/charts.dart';

// import '../main.dart';
// import '../model/EarningListModelWeek.dart';
// import '../network/RestApis.dart';

// import '../utils/Common.dart';
// import '../utils/Constants.dart';
// import '../utils/Extensions/app_common.dart';

// class EarningWeekWidget extends StatefulWidget {
//   @override
//   EarningWeekWidgetState createState() => EarningWeekWidgetState();
// }

// class EarningWeekWidgetState extends State<EarningWeekWidget> {
//   EarningListModelWeek? earningListModelWeek;
//   List<WeekReport> weekReport = [];
//   num totalRide = 0;
//   num totalReferrals = 0;
//   num TotalEarnings = 0;

//   @override
//   void initState() {
//     super.initState();
//     init();
//   }

//   void init() async {
//     appStore.setLoading(true);
//     Map req = {
//       "type": "week",
//     };
//     await earningList(req: req).then((value) {
//       appStore.setLoading(false);

//       totalRide = value.total_ride_fare ?? 0.0;
//       totalReferrals = value.total_offer_referral ?? 0.0;
//       TotalEarnings = value.total_earnings ?? 0.0;

//       // weekReport.addAll(value.weekReport!);
//       setState(() {});
//     }).catchError((error) {
//       appStore.setLoading(false);

//       log("Server error");
//     });
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Observer(
//       builder: (_) {
//         return Stack(
//           children: [
//             SingleChildScrollView(
//               padding: screenPadding,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Container(
//                     padding: EdgeInsets.all(16),
//                     height: 350,
//                     child: SfCartesianChart(
//                       title: ChartTitle(
//                           text: language.WeeklyOrderCountTxt,
//                           textStyle: boldTextStyle()),
//                       tooltipBehavior: TooltipBehavior(enable: true),
//                       series: <ChartSeries>[
//                         StackedColumnSeries<WeekReport, String>(
//                           enableTooltip: true,
//                           markerSettings: MarkerSettings(isVisible: true),
//                           dataSource: weekReport,
//                           xValueMapper: (WeekReport exp, _) => exp.day,
//                           yValueMapper: (WeekReport exp, _) => exp.amount,
//                         ),
//                       ],
//                       primaryXAxis: CategoryAxis(isVisible: true),
//                     ),
//                     decoration: BoxDecoration(
//                       boxShadow: [
//                         BoxShadow(
//                             color: Colors.black12,
//                             blurRadius: 10.0,
//                             spreadRadius: 0),
//                       ],
//                       color: Colors.white,
//                       borderRadius: BorderRadius.circular(defaultRadius),
//                     ),
//                   ),
//                   SizedBox(height: 16),
//                   SizedBox(height: 16),
//                   earningText(title: language.totalRide, amount: totalRide),
//                   SizedBox(height: 16),
//                   earningText(
//                       title: language.totalReferralsTxt,
//                       amount: totalReferrals),
//                   SizedBox(height: 16),
//                   Divider(),
//                   earningText(
//                       title: language.totalEarning, amount: TotalEarnings),
//                   height20,
//                   height20,
//                   Align(
//                       alignment: Alignment.center,
//                       child: AppButton(
//                           text: language.cashOutTxt,
//                           onPressed: () {
//                             GlobalMethods.pushScreen(
//                                 context: context,
//                                 screen:  BankAndWalletScreen(
//                                   isWalletScreen: true,
//                                 ),
//                                 screenIdentifier: ScreenIdentifier.BankAndWalletScreen);
//                             // launchScreen(
//                             //     context,
//                             //     BankAndWalletScreen(
//                             //       isWalletScreen: true,
//                             //     ));
//                           })
//                       // AppButtonWidget(
//                       //   shapeBorder: RoundedRectangleBorder(
//                       //       borderRadius: BorderRadius.circular(10)),
//                       //   padding: EdgeInsets.symmetric(horizontal: 40),
//                       //   text: language.cashOutTxt,
//                       //   onTap: () {
//                       //     launchScreen(
//                       //         context,
//                       //         BankAndWalletScreen(
//                       //           isWalletScreen: true,
//                       //         ));
//                       //   },
//                       // ),
//                       )
//                 ],
//               ),
//             ),
//             Visibility(
//               visible: appStore.isLoading,
//               child: loaderWidget(),
//             )
//           ],
//         );
//       },
//     );
//   }
// }
