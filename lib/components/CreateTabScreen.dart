// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
// import 'package:rooo_driver/global/constants/app_enums.dart';
// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/model/rooo_care/rooo_care_list_model.dart';
// import 'package:rooo_driver/screens/roooCareDetailScreen.dart';
// import 'package:rooo_driver/screens/rooo_care_add_screen.dart';

// import '../main.dart';
// import '../network/RestApis.dart';
// import '../utils/Common.dart';
// import '../utils/Constants.dart';
// import '../utils/Extensions/app_common.dart';

// class CreateTabScreen extends StatefulWidget {
//   final String? status;
//   final bool isCareScreen;

//   CreateTabScreen({this.status, required this.isCareScreen});

//   @override
//   CreateTabScreenState createState() => CreateTabScreenState();
// }

// class CreateTabScreenState extends State<CreateTabScreen> {
//   ScrollController scrollController = ScrollController();
//   String? api_message = "";
//   int currentPage = 1;
//   int totalPage = 1;
//   List<RoooCareListModel> rooo_care_pending_list = [];
//   List<RoooCareListModel> rooo_care_completed_list = [];

//   List<String> rooo_care_status = [COMPLETED, CLOSED];

//   @override
//   void initState() {
//     super.initState();
//     if (widget.isCareScreen) {
//       rooo_care_status = [PENDING, CLOSED];
//       initFunction();

//       scrollController.addListener(() {
//         if (scrollController.position.pixels ==
//             scrollController.position.maxScrollExtent) {
//           if (currentPage < totalPage) {
//             appStore.setLoading(true);
//             currentPage++;
//             setState(() {});

//             initFunction();
//           }
//         }
//       });
//       afterBuildCreated(() => appStore.setLoading(true));
//     }
//   }

//   getRoooCarePendingList() async {
//     await getRoooCarePendingListApi(
//       page: currentPage,
//       // status: widget.status,
//       // driverId: sharedPref.getInt(USER_ID)
//     ).then((value) {
//       appStore.setLoading(false);

//       currentPage = value.pagination!.currentPage!;
//       totalPage = value.pagination!.totalPages!;
//       api_message = value.message;
//       if (currentPage == 1) {
//         rooo_care_pending_list.clear();
//       }
//       rooo_care_pending_list.addAll(value.data!);
//       setState(() {});
//     }).catchError((error) {
//       appStore.setLoading(false);
//       log("Server error");
//     });
//   }

//   getRoooCareCompletedList() async {
//     await getRoooCareCompletedListApi(
//       page: currentPage,
//       // status: widget.status,
//       // driverId: sharedPref.getInt(USER_ID)
//     ).then((value) {
//       appStore.setLoading(false);

//       currentPage = value.pagination!.currentPage!;
//       totalPage = value.pagination!.totalPages!;
//       if (currentPage == 1) {
//         rooo_care_completed_list.clear();
//       }
//       rooo_care_completed_list.addAll(value.data!);
//       setState(() {});
//     }).catchError((error) {
//       appStore.setLoading(false);
//       log("Server error" + "=>");
//       GlobalMethods.errorToast(context, "Server error");
//     });
//   }

//   void initFunction() async {
//     getRoooCarePendingList();

//     getRoooCareCompletedList();
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Observer(builder: (context) {
//       return Stack(
//         children: [
//           AnimationLimiter(
//             child: ListView.builder(
//                 itemCount: widget.status == PENDING
//                     ? rooo_care_pending_list.length
//                     : rooo_care_completed_list.length,
//                 controller: scrollController,
//                 padding:
//                     EdgeInsets.only(top: 8, bottom: 8, left: 16, right: 16),
//                 itemBuilder: (_, index) {
//                   RoooCareListModel data = rooo_care_pending_list[index];
//                   return AnimationConfiguration.staggeredList(
//                     delay: Duration(milliseconds: 200),
//                     position: index,
//                     duration: Duration(milliseconds: 375),
//                     child: SlideAnimation(
//                       child: IntrinsicHeight(
//                         child: inkWellWidget(
//                           onTap: () {
//                             // if (data.status != CANCELED) {
//                             //   launchScreen(
//                             //       context, RideDetailScreen(orderId: data.id!),
//                             //       pageRouteAnimation:
//                             //           PageRouteAnimation.SlideBottomTop);
//                             // }
//                           },
//                           child: Card(
//                             color: Colors.white.withOpacity(0.5),
//                             child: InkWell(
//                               onTap: () {
//                                 GlobalMethods.pushScreen(
//                                     context: context,
//                                     screen:  RoooCareDetailScreen(
//                                       title: data.subject,
//                                       details: data.message.toString()),
//                                     screenIdentifier: ScreenIdentifier.RoooCareDetailScreen);
//                                 // launchScreen(
//                                 //   context,
//                                 //   RoooCareDetailScreen(
//                                 //       title: data.subject,
//                                 //       details: data.message.toString()),
//                                 // );
//                               },
//                               child: Container(
//                                 margin: EdgeInsets.only(
//                                   bottom: 8,
//                                 ),
//                                 padding: EdgeInsets.all(
//                                   20,
//                                 ),
//                                 decoration: BoxDecoration(
//                                     // // color: Colors.white,
//                                     // boxShadow: [
//                                     //   BoxShadow(
//                                     //     color: Colors.white.withOpacity(0.1),
//                                     //     blurRadius: 10,
//                                     //     spreadRadius: 0,
//                                     //     offset: Offset(
//                                     //       0.0,
//                                     //       0.0,
//                                     //     ),
//                                     //   ),
//                                     // ],
//                                     ),
//                                 child: Text(
//                                   data.subject,
//                                   style: TextStyle(
//                                       // color: Colors.white,
//                                       ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   );
//                 }),
//           ),
//           widget.isCareScreen
//               ? Positioned(
//                   bottom: 40,
//                   right: 40,
//                   child: FloatingActionButton(
//                     onPressed: () async {
//                       bool result = await Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => RoooCareAddScreen()));

//                       if (result) {
//                         getRoooCarePendingList();
//                         getRoooCareCompletedList();
//                       }
//                     },
//                     child: Icon(Icons.add),
//                   ),
//                 )
//               : SizedBox(),
//           Visibility(
//             visible: appStore.isLoading,
//             child: loaderWidget(),
//           ),
//           if (rooo_care_pending_list.isEmpty)
//             appStore.isLoading
//                 ? SizedBox()
//                 : emptyWidget(message: api_message.toString()),
//         ],
//       );
//     });
//   }
// }
