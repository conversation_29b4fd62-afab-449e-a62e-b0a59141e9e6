import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model.dart';
import 'package:rooo_driver/screens/referrals/refferal_offer_detail_screen.dart';

class ReferralOfferCard extends StatefulWidget {
  final RefferalOfferModel refferalOfferData;

  const ReferralOfferCard({super.key, required this.refferalOfferData});

  @override
  State<ReferralOfferCard> createState() => _ReferralOfferCardState();
}

class _ReferralOfferCardState extends State<ReferralOfferCard> {
  @override
  Widget build(BuildContext context) {
    final primaryColor = AppColors.primaryColor(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColors.whiteColor(context),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.card_giftcard,
                      color: primaryColor,
                      size: 24,
                    ),
                  ),
                  width10,
                  Expanded(
                    child: CustomText(
                      data: widget.refferalOfferData.title ?? "",
                      size: 18,
                      fontweight: FontWeight.w600,
                      color: AppColors.blackColor(context),
                    ),
                  ),
                ],
              ),
              height15,
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: primaryColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: CustomText(
                  data: widget.refferalOfferData.sub_title.toString(),
                  size: 15,
                  color: AppColors.blackColor(context),
                ),
              ),
              height20,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: ReferralOfferDetailScreen(
                              description: widget.refferalOfferData.details ?? "",
                              title: widget.refferalOfferData.details ?? ""),
                          screenIdentifier: ScreenIdentifier.RefferalOfferDetailScreen);
                    },
                    style: TextButton.styleFrom(
                      backgroundColor: primaryColor.withOpacity(0.08),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    icon: Icon(
                      Icons.info_outline,
                      color: primaryColor,
                      size: 20,
                    ),
                    label: CustomText(
                      data: language.termsApplyTxt,
                      size: 14,
                      color: primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
