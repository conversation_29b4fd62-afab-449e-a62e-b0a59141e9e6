import 'package:flutter/material.dart';

class CustomIcon extends StatefulWidget {
  final double height;
  final double width;
  final String adress;

  const CustomIcon(
      {super.key,
      required this.height,
      required this.width,
      required this.adress});

  @override
  State<CustomIcon> createState() => _CustomIconState();
}

class _CustomIconState extends State<CustomIcon> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      width: widget.width,
      child: Image.asset(
        widget.adress,
      ),
    );
  }
}
