// import 'package:country_code_picker/country_code_picker.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';
// import 'package:google_sign_in/google_sign_in.dart';
// import 'package:rooo_rider/screens/RiderDashBoardScreen.dart';
// import 'package:rooo_rider/screens/verify_otp_screen.dart';
// import 'package:rooo_rider/utils/Extensions/StringExtensions.dart';

// import '../../main.dart';
// import '../model/UserDetailModel.dart';
// import '../network/RestApis.dart';
// import '../service/AuthService1.dart';
// import '../utils/Colors.dart';
// import '../utils/Common.dart';
// import '../utils/Constants.dart';
// import '../utils/Extensions/AppButtonWidget.dart';
// import '../utils/Extensions/app_common.dart';
// import '../utils/Extensions/app_textfield.dart';
// import 'RegisterScreen.dart';

// class NewLoginScreen extends StatefulWidget {
//   @override
//   NewLoginScreenState createState() => NewLoginScreenState();
// }

// class NewLoginScreenState extends State<NewLoginScreen> {
//   AuthServices authService = AuthServices();
//   TextEditingController emailController = TextEditingController();
//   TextEditingController passController = TextEditingController();

//   FocusNode emailFocus = FocusNode();
//   FocusNode passFocus = FocusNode();

//   bool mIsCheck = false;
//   bool isAcceptedTc = false;
//   String? privacyPolicy;
//   String? termsCondition;
//   String selectedCountryCode = defaultCountryCode;

//   final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

//   String _email = '';
//   String _phone = '';

//   String _name = '';

//   @override
//   void initState() {
//     super.initState();
//     init();
//   }

//   void init() async {
//     appSetting();
//     if (sharedPref.getString(PLAYER_ID).validate().isEmpty) {
//       await saveOneSignalPlayerId().then((value) {
//         //
//       });
//     }
//     mIsCheck = sharedPref.getBool(REMEMBER_ME) ?? false;
//     if (mIsCheck) {
//       emailController.text = sharedPref.getString(USER_EMAIL).validate();
//       passController.text = sharedPref.getString(USER_PASSWORD).validate();
//     }
//   }

//   // Future<void> logIn() async {
//   //   hideKeyboard(context);

//   //   if (formKey.currentState!.validate()) {
//   //     formKey.currentState!.save();
//   //     if (isAcceptedTc) {
//   //       appStore.setLoading(true);

//   //       Map req = {
//   //         'email': emailController.text.trim(),
//   //         'password': passController.text.trim(),
//   //         "player_id": sharedPref.getString(PLAYER_ID).validate(),
//   //         'user_type': 'driver',
//   //       };
//   //       if (mIsCheck) {
//   //         await sharedPref.setBool(REMEMBER_ME, mIsCheck);
//   //         await sharedPref.setString(USER_EMAIL, emailController.text);
//   //         await sharedPref.setString(USER_PASSWORD, passController.text);
//   //       }

//   //       await logInApi(req).then((value) async {
//   //         _userModel = value.data!;

//   //         _auth
//   //             .signInWithEmailAndPassword(
//   //                 email: emailController.text, password: passController.text)
//   //             .then((value) {
//   //           if (sharedPref.getInt(IS_Verified_Driver) == 1) {
//   //             launchScreen(context, DriverDashboardScreen(),
//   //                 isNewTask: true,
//   //                 pageRouteAnimation: PageRouteAnimation.Slide);
//   //           } else {
//   //             launchScreen(context, VerifyDeliveryPersonScreen(isShow: true),
//   //                 isNewTask: true,
//   //                 pageRouteAnimation: PageRouteAnimation.Slide);
//   //           }
//   //           appStore.isLoading = false;
//   //         }).catchError((e) {
//   //           if (e.toString().contains('user-not-found')) {
//   //             authService.signUpWithEmailPassword(
//   //               context,
//   //               name: _userModel.firstName,
//   //               mobileNumber: _userModel.contactNumber,
//   //               email: emailController.text,
//   //               fName: _userModel.firstName,
//   //               lName: _userModel.lastName,
//   //               userName: _userModel.username,
//   //               password: passController.text,
//   //               userType: 'driver',
//   //               isExist: false,
//   //               gender: _userModel.gender,
//   //               serviceIds: _userModel.serviceIds,
//   //               userDetail: UserDetail(
//   //                 carModel: '',
//   //                 carColor: '',
//   //                 carPlateNumber: '',
//   //                 carProductionYear: '',
//   //               ),
//   //             );
//   //           } else {
//   //             if (sharedPref.getInt(IS_Verified_Driver) == 1) {
//   //               launchScreen(context, DriverDashboardScreen(),
//   //                   isNewTask: true,
//   //                   pageRouteAnimation: PageRouteAnimation.Slide);
//   //             } else {
//   //               launchScreen(context, VerifyDeliveryPersonScreen(isShow: true),
//   //                   isNewTask: true,
//   //                   pageRouteAnimation: PageRouteAnimation.Slide);
//   //             }
//   //           }
//   //           //GlobalMethods.infoToast(context,  e.toString());
//   //           log('${e.toString()}');
//   //           log(e.toString());
//   //         });
//   //       }).catchError((error) {
//   //         appStore.setLoading(false);

//   //         GlobalMethods.infoToast(context,  "Server error");
//   //         log('${"Server error"}');
//   //       });
//   //     } else {
//   //      GlobalMethods.infoToast(context,  language.pleaseAcceptTermsOfServicePrivacyPolicy);
//   //     }
//   //   }
//   // }

//   Future<void> appSetting() async {
//     await getAppSettingApi().then((value) {
//       if (value.privacyPolicyModel!.value != null)
//         privacyPolicy = value.privacyPolicyModel!.value;
//       if (value.termsCondition!.value != null)
//         termsCondition = value.termsCondition!.value;
//     }).catchError((error) {
//       log("Server error");
//     });
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   void _checkUser({
//     String source = 'phone',
//     String? phone,
//     String? name,
//     String? email,
//   }) {
//     hideKeyboard(context);

//     if ((source == 'phone' && _formKey.currentState!.validate()) ||
//         source != 'phone') {
//       appStore.setLoading(true);
//       isUserExists(selectedCountryCode + ' ' + _phone, _email)
//           .then((value) async {
//         if (value['status']) {
//           launchScreen(
//             context,
//             VerifyOTPScreen(
//                 countryCode: selectedCountryCode,
//                 phone: _phone,
//                 user: null,
//                 otpVerificationKey: value['data']['key'] ?? ''),
//             pageRouteAnimation: PageRouteAnimation.Slide,
//           );

//           appStore.setLoading(false);

//           //   //verify otp
//           //   if (source == 'phone') {
//           //     launchScreen(
//           //       context,
//           //       VerifyOTPScreen(
//           //           countryCode: selectedCountryCode,
//           //           phone: _phone,
//           //           user: null,
//           //           otpVerificationKey: value['key'] ?? ''),
//           //       pageRouteAnimation: PageRouteAnimation.Slide,
//           //     );
//           //   } else {
//           //     launchScreen(
//           //       context,
//           //       RegisterScreen(
//           //         name: name,
//           //         phone: phone,
//           //         email: email,
//           //       ),
//           //       pageRouteAnimation: PageRouteAnimation.Slide,
//           //     );
//           //   }
//           //   appStore.setLoading(false);
//           // } else {

//           // var user = UserData.fromJson(value['data']);

//           // if (source == 'phone') {
//           //   launchScreen(
//           //     context,
//           //     VerifyOTPScreen(
//           //         countryCode: selectedCountryCode,
//           //         phone: _phone,
//           //         user: user,
//           //         otpVerificationKey: value['key'] ?? ''),
//           //     pageRouteAnimation: PageRouteAnimation.Slide,
//           //   );
//           // } else {
//           //   await sharedPref.setString(TOKEN, user.apiToken.validate());
//           //   await sharedPref.setString(USER_TYPE, user.userType.validate());
//           //   await sharedPref.setString(FIRST_NAME, user.firstName.validate());
//           //   await sharedPref.setString(LAST_NAME, user.lastName.validate());
//           //   await sharedPref.setString(
//           //       CONTACT_NUMBER, user.contactNumber.validate());
//           //   await sharedPref.setString(USER_EMAIL, user.email.validate());
//           //   await sharedPref.setString(USER_NAME, user.username.validate());
//           //   await sharedPref.setString(ADDRESS, user.address.validate());
//           //   await sharedPref.setInt(USER_ID, user.id ?? 0);
//           //   await sharedPref.setString(
//           //       USER_PROFILE_PHOTO, user.profileImage.validate());
//           //   await sharedPref.setString(GENDER, user.gender.validate());
//           //   await sharedPref.setString(LOGIN_TYPE, user.loginType.validate());
//           //   await appStore.setLoggedIn(true);
//           //   await appStore.setUserEmail(user.email.validate());
//           //   await sharedPref.setString(UID, user.uid.validate());
//           //   await appStore.setUserProfile(user.profileImage.validate());

//           //   launchScreen(
//           //     context,
//           //     RiderDashBoardScreen(),
//           //     isNewTask: true,
//           //     pageRouteAnimation: PageRouteAnimation.Slide,
//           //   );
//           // }
//           // }
//         } else {
//           GlobalMethods.infoToast(context,  value['message']);
//           setState(() {
//             appStore.setLoading(false);
//           });
//         }
//       }).onError((error, stackTrace) {
//         setState(() {
//           appStore.setLoading(false);
//         });
//         GlobalMethods.infoToast(context,  
//           language.errorMsg,
//         );
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         backgroundColor: getPrimaryScaffoldColor(),
//         body: GestureDetector(
//           onTap: () {
//             hideKeyboard(context);
//           },
//           child: Stack(
//             children: [
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Flexible(
//                     child: Container(
//              
//                       child: Center(
//                           child: Image.asset(
//                         'images/ic_driver_white.png',
//                         width: MediaQuery.sizeOf(context).width - 100,
//                       )),
//                     ),
//                   ),
//                   Expanded(
//                     child: SingleChildScrollView(
//                       child: Padding(
//                         padding: const EdgeInsets.only(
//                           top: screenPadding,
//                           left: screenPadding,
//                           right: screenPadding,
//                         ),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Text(
//                               language.enterYourMobileNumber,
//                               style: TextStyle(
//                                 fontWeight: FontWeight.bold,
//     
//                               ),
//                             ),
//                             height3,
//                             Form(
//                               key: _formKey,
//                               child: AppTextField(
//                                 onChanged: (p0) {
//                                   _phone = p0.trim();
//                                 },
//                                 maxLength: 12,
//                                 textFieldType: TextFieldType.PHONE,
//                                 nextFocus: passFocus,
//                                 decoration: inputDecoration(
//                                   context,

//                                   // label: language.phoneNumber,
//                                   prefixIcon: IntrinsicHeight(
//                                     child: Row(
//                                       mainAxisSize: MainAxisSize.min,
//                                       children: [
//                                         CountryCodePicker(
//                                           padding: EdgeInsets.zero,
//                                           initialSelection: defaultCountryCode,
//                                           showCountryOnly: false,
//                                           dialogSize: Size(
//                                               MediaQuery.of(context)
//                                                       .size
//                                                       .width -
//                                                   60,
//                                               MediaQuery.of(context)
//                                                       .size
//                                                       .height *
//                                                   0.6),
//                                           showFlag: true,
//                                           showFlagDialog: true,
//                                           showOnlyCountryWhenClosed: false,
//                                           alignLeft: false,
//                                           textStyle: TextStyle(
//                 
//                                           ),
//                                           dialogBackgroundColor:
//                                               Theme.of(context).cardColor,
//                                           barrierColor: Colors.black12,
//                                           dialogTextStyle: primaryTextStyle(),
//                                           searchDecoration: InputDecoration(
//                                             iconColor:
//                                                 Theme.of(context).dividerColor,
//                                             enabledBorder: UnderlineInputBorder(
//                                                 borderSide: BorderSide(
//                                                     color: Theme.of(context)
//                                                         .dividerColor)),
//                                             focusedBorder: UnderlineInputBorder(
//                                                 borderSide: BorderSide(
//                                                     )),
//                                           ),
//                                           searchStyle: primaryTextStyle(),
//                                           onInit: (c) {
//                                             selectedCountryCode = c!.dialCode!;
//                                           },
//                                           onChanged: (c) {
//                                             selectedCountryCode = c.dialCode!;
//                                           },
//                                         ),
//                                         VerticalDivider(
//                                             color:
//                                                 Colors.grey.withOpacity(0.5)),
//                                       ],
//                                     ),
//                                   ),
//                                 ),
//                                 validator: (value) {
//                                   if (value!.trim().isEmpty)
//                                     return language.thisFieldRequired;
//                                   if (value.trim().length != 10 &&
//                                       value.trim().length != 12)
//                                     return language.contactLength;
//                                   return null;
//                                 },
//                                 onEditingComplete: () {},
//                               ),
//                             ),
//                             height10,
//                             SizedBox(
//                               height: 42,
//                               child: AppButtonWidget(
//                                 padding: EdgeInsets.zero,
//                                 width: MediaQuery.sizeOf(context).width,
//                                 text: language.continueText,
//                                 textStyle: TextStyle(
//                                   fontSize: 14,
//                                   color: getPrimaryButtonTextColor(),
//                                 ),
//                                 onTap: _checkUser,
//                               ),
//                             ),
//                             height10,
//                             Center(
//                               child: Text(
//                                 '--' + language.orText + ' --',
//                                 style: TextStyle(
//       
//                                 ),
//                               ),
//                             ),
//                             height10,
//                             inkWellWidget(
//                               onTap: () async {
//                                 try {
//                                   // Trigger the authentication flow
//                                   final GoogleSignInAccount? googleUser =
//                                       await GoogleSignIn().signIn();

//                                   // // Obtain the auth details from the request
//                                   // final GoogleSignInAuthentication? googleAuth =
//                                   //     await googleUser?.authentication;

//                                   // // Create a new credential
//                                   // final credential =
//                                   //     GoogleAuthProvider.credential(
//                                   //   accessToken: googleAuth?.accessToken,
//                                   //   idToken: googleAuth?.idToken,
//                                   // );
//                                   _email = googleUser!.email;

//                                   launchScreen(
//                                       context,
//                                       RegisterScreen(
//                                         email: googleUser.email,
//                                         isFromPhone: false,
//                                         name: googleUser.displayName,
//                                         socialLogin: false,
//                                       ));

//                                   // _checkUser(
//                                   //     source: 'google',
//                                   //     name: googleUser.displayName,
//                                   //     phone: null,
//                                   //     email: googleUser.email);
//                                 } catch (e) {
//                                  GlobalMethods.infoToast(context,  language.errorMsg);
//                                 }

//                                 // Once signed in, return the UserCredential
//                                 // return await FirebaseAuth.instance
//                                 //     .signInWithCredential(credential)
//                                 //     .then((value) {
//                                 //   log('done');
//                                 // }).onError((error, stackTrace) {
//                                 //   log(error);
//                                 // });

//                                 // launchScreen(context, LoginScreen(),
//                                 //     pageRouteAnimation: PageRouteAnimation.Slide);
//                               },
//                               child: Container(
//                                 padding: EdgeInsets.all(7),
//                                 width: MediaQuery.sizeOf(context).width,
//                                 decoration: BoxDecoration(
//                                     borderRadius: BorderRadius.circular(
//                                       10,
//                                     ),
//                                     border:
//                                         Border.all()),
//                                 child: Row(
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     Image.asset('images/ic_google.png',
//                                         fit: BoxFit.cover,
//                                         height: 25,
//                                         width: 25),
//                                     width4,
//                                     Text(
//                                       language.signInWithGoogle,
//                                       style: TextStyle(
//             
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ),
//                             height10,
//                             inkWellWidget(
//                               onTap: () async {
//                                 final LoginResult result = await FacebookAuth
//                                     .instance
//                                     .login(); // by default we request the email and the public profile
// // or FacebookAuth.i.login()
//                                 if (result.status == LoginStatus.success) {
//                                   final userData =
//                                       await FacebookAuth.instance.getUserData();

//                                   _email = userData['email'];
//                                   _checkUser(
//                                       source: 'facebook',
//                                       name: userData['name'],
//                                       phone: null,
//                                       email: userData['email']);

//                                   print('ms->object' + userData['name']);
//                                   print('ms->object' + userData['email']);
//                                   print('ms->object' + userData.toString());
// // or FacebookAuth.i.getUserData()
//                                 } else {
//                                   GlobalMethods.infoToast(context,  errorSomethingWentWrong);
//                                 }
//                               },
//                               child: Container(
//                                 padding: EdgeInsets.all(7),
//                                 width: MediaQuery.sizeOf(context).width,
//                                 decoration: BoxDecoration(
//                                     borderRadius: BorderRadius.circular(
//                                       10,
//                                     ),
//                                     border:
//                                         Border.all()),
//                                 child: Row(
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     Image.asset('images/ic_facebook.png',
//                                         fit: BoxFit.cover,
//                                         height: 25,
//                                         width: 25),
//                                     width4,
//                                     Text(
//                                       language.signInWithFacebook,
//                                       style: TextStyle(
//             
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//               Observer(
//                 builder: (context) {
//                   return Visibility(
//                     visible: appStore.isLoading,
//                     child: loaderWidget(),
//                   );
//                 },
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }