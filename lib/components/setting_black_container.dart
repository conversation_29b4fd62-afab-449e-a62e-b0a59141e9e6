import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';

class SettingsBlackContainer extends StatelessWidget {
  final String title;
  final Function() onTap;
  // final String address;
  final Icon icon;
  const SettingsBlackContainer(
      {super.key,
      required this.title,
      required this.onTap,
       required this.icon});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                   icon ,
                    width10,
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
                Icon(Icons.keyboard_arrow_right_outlined, color: Colors.grey)
              ],
            ),
          ),
          Divider(
            thickness: 1,
          )
        ],
      ),
    );
  }
}
