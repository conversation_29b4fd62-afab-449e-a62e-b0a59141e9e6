import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/model/FAQ.dart';
import 'package:flutter/material.dart';

class BlogCard extends StatelessWidget {
  final FAQ blog;

  const BlogCard(FAQ this.blog);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Card(
          color: Colors.white,
          surfaceTintColor: Colors.white,
          child: Column(
            children: [
              // SizedBox(height: 8),
              // SizedBox(height: 8),
              // SizedBox(height: 8),
              CachedNetworkImage(
                imageUrl: blog.imageURL!,
                placeholder: (context, url) => Container(
                  width: double.infinity,
                  height: 200,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  width: double.infinity,
                  height: 200,
                  child: Center(
                    child: Icon(
                      Icons.error,
                    ),
                  ),
                ),
                imageBuilder: (context, imageProvider) => Container(
                  width: double.infinity,
                  height: 200,
                  // height: 200,
                  // width:200,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(blog.title,
                    style: TextStyle(
                        fontWeight: FontWeight.w900,
                        fontSize: 18,
                        color: Colors.black)),
              ),
              SizedBox(height: 4),
              // Text('Now booking your Rooo is easy!', style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14)),
              SizedBox(height: 8),
              SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }
}
