import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';
import 'package:rooo_driver/global/widgets/ios_padding.dart';
import 'package:rooo_driver/screens/BankAndWalletScreen.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

import '../main.dart';
import '../network/RestApis.dart';
import '../utils/Common.dart';
import '../utils/Extensions/app_common.dart';

class EarningReportWidget extends StatefulWidget {
  @override
  EarningReportWidgetState createState() => EarningReportWidgetState();
}

class EarningReportWidgetState extends State<EarningReportWidget> {
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();

  DateTime? fromDate, toDate;
  num? totalRide;
  num? totalReferrals;
  num? TotalEarnings;
  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    if (fromDateController.text.isNotEmpty &&
        toDateController.text.isNotEmpty) {
      appStore.setLoading(true);
      Map req = {
        "type": "report",
        "from_date": fromDateController.text.toString(),
        "to_date": toDateController.text.toString(),
      };
      await earningList(req: req).then((value) {
        appStore.setLoading(false);
        totalRide = value.totalRideFare ?? 0.0;
        totalReferrals = value.totalOfferReferral ?? 0.0;
        TotalEarnings = value.totalEarnings ?? 0.0;

        setState(() {});
      }).catchError((error) {
        appStore.setLoading(false);

        log(error.toString());
      });
    } else {
      GlobalMethods.infoToast(context, language.pleaseSelectFromDateAndToDate);
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) {
        return Stack(
          children: [
            SingleChildScrollView(
              padding: screenPadding,
              child: Column(
                children: [
                  Row(
                    children: [
                      SizedBox(
                        width: 50,
                        child: Text(language.from, style: primaryTextStyle()),
                      ),
                      SizedBox(width: 16),

                      Flexible(
                        child: AppTextField(
                          onTap: () async {
                            DateTime? FromPickedDate = await showDatePicker(
                              builder: IS_DARK_MODE_ON
                                  ? (context, child) {
                                      return Theme(
                                        data: Theme.of(context).copyWith(
                                          dialogBackgroundColor: Colors.white,
                                          colorScheme: ColorScheme.light(
                                            primary: Colors
                                                .black, // header background color
                                            onPrimary: Colors
                                                .white, // header text color
                                            onSurface:
                                                Colors.black, // body text color
                                          ),
                                          textButtonTheme: TextButtonThemeData(
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors
                                                  .black, // button text color
                                            ),
                                          ),
                                        ),
                                        child: child!,
                                      );
                                    }
                                  : null,
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(2010),
                              lastDate: DateTime.now(),
                            );
                            fromDate =
                                DateTime.parse(FromPickedDate.toString());
                            fromDateController.text =
                                dateToInfoString(FromPickedDate!)
                                    .toString()
                                    .substring(0, 10);
                            setState(() {});
                          },
                          controller: fromDateController,
                          textFieldType: TextFieldType.OTHER,
                          errorThisFieldRequired: language.thisFieldRequired,
                          decoration: inputDecoration(context,
                              hintTextStyle: TextStyle(color: Colors.black),
                              hint: language.fromDate,
                              suffixIcon: Icon(
                                Icons.calendar_today,
                                color: Theme.of(context).brightness == Brightness.dark ?   Colors.white : Colors.black,
                              )),
                        ),
                      ),

                      // Expanded(
                      //   child: DateTimePicker(

                      //     controller: fromDateController,
                      //     type: DateTimePickerType.date,
                      //     lastDate: DateTime.now(),
                      //     firstDate: DateTime(2010),
                      //     onChanged: (value) {
                      //       fromDate = DateTime.parse(value);
                      //       fromDateController.text = value;
                      //       setState(() {});
                      //     },
                      //     decoration: inputDecoration(context,
                      //              fillColor: colorSelector(darkModeColor: Colors.grey, lightmodeColor: extraLightGreyColor),
                      //       hintTextStyle: TextStyle(color: Colors.black),
                      //     hint: language.fromDate, suffixIcon: Icon(Icons.calendar_today,color: Colors.black,)),
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Row(
                    children: [
                      SizedBox(
                        width: 50,
                        child: Text(language.to, style: primaryTextStyle()),
                      ),
                      SizedBox(width: 16),

                      Flexible(
                        child: AppTextField(
                          onTap: () async {
                            DateTime? toPickedDate = await showDatePicker(
                              builder: IS_DARK_MODE_ON
                                  ? (context, child) {
                                      return Theme(
                                        data: Theme.of(context).copyWith(
                                          hintColor: Colors.black,
                                          dialogBackgroundColor: Colors.white,
                                          colorScheme: ColorScheme.light(
                                            primary: Colors
                                                .black, // header background color
                                            onPrimary: Colors
                                                .white, // header text color
                                            onSurface:
                                                Colors.black, // body text color
                                          ),
                                          textButtonTheme: TextButtonThemeData(
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors
                                                  .black, // button text color
                                            ),
                                          ),
                                        ),
                                        child: child!,
                                      );
                                    }
                                  : null,
                              context: context,
                              lastDate: DateTime.now(),
                              firstDate: fromDate ?? DateTime.now(),
                              initialDate: fromDate ?? DateTime.now(),
                            );
                            toDate = DateTime.parse(toPickedDate.toString());
                            toDateController.text =
                                dateToInfoString(toPickedDate!)
                                    .toString()
                                    .substring(0, 10);
                            // DateFormat("dd-MM-yyyy").parse(toPickedDate.toString()).toString();
                            // toPickedDate.toString().substring(0, 10);
                            setState(() {});
                          },
                          controller: toDateController,
                          textFieldType: TextFieldType.OTHER,
                          errorThisFieldRequired: language.thisFieldRequired,
                          decoration: inputDecoration(context,
                              hintTextStyle: TextStyle(color: Colors.black),
                              hint: language.toDate,
                              suffixIcon: Icon(
                                Icons.calendar_today,
                                color: Colors.black,
                              )),
                        ),
                      ),
                      // Expanded(
                      //   child: DateTimePicker(
                      //     controller: toDateController,
                      //     type: DateTimePickerType.date,
                      // lastDate: DateTime.now(),
                      // firstDate: fromDate ?? DateTime.now(),
                      //     onChanged: (value) {
                      //       toDate = DateTime.parse(value);
                      //       toDateController.text = value;
                      //       setState(() {});
                      //     },
                      //     decoration: inputDecoration(context,
                      //         hint: language.toDate,
                      //         fillColor: colorSelector(
                      //             darkModeColor: Colors.grey,
                      //             lightmodeColor: extraLightGreyColor),
                      //         hintTextStyle: TextStyle(color: Colors.black),
                      //         suffixIcon: Icon(
                      //           Icons.calendar_today,
                      //           color: Colors.black,
                      //         )),
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(height: 16),

                  AppButton(
                      text: language.confirm,
                      onPressed: () async {
                        init();
                      }),

                  // AppButtonWidget(
                  //   width: MediaQuery.of(context).size.width,
                  //   textStyle: boldTextStyle(),
                  //   text: language.confirm,
                  //   onTap: () async {
                  //     init();
                  //   },
                  // ),

                  (totalRide == null ||
                          totalReferrals == null ||
                          TotalEarnings == null)
                      ? SizedBox()
                      : Column(
                          children: [
                            height20,
                            if (fromDateController.text.isNotEmpty &&
                                toDateController.text.isNotEmpty)
                              Text(
                                  '${fromDateController.text}   to  ${toDateController.text}',
                                  style: boldTextStyle()),
                            SizedBox(height: 16),
                            earningText(
                                title: language.totalRide, amount: totalRide),
                            SizedBox(height: 16),
                            earningText(
                                title: language.totalReferralsTxt,
                                amount: totalReferrals),
                            SizedBox(height: 16),
                            Divider(),
                            earningText(
                                title: language.totalEarning,
                                amount: TotalEarnings),
                            Divider(),
                            SizedBox(height: 16),
                          ],
                        ),
                  SizedBox(height: 16),

                  // Align(
                  //   alignment: Alignment.bottomCenter,
                  //   child: AppButtonWidget(
                  //     shapeBorder: RoundedRectangleBorder(
                  //         borderRadius: BorderRadius.circular(10)),
                  //     padding: EdgeInsets.symmetric(horizontal: 40),
                  //     text: language.cashOutTxt,
                  //     onTap: () {
                  //       launchScreen(
                  //           context,
                  //           BankAndWalletScreen(
                  //             isWalletScreen: true,
                  //           ));
                  //     },
                  //   ),
                  // )
                ],
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: IosPadding(
                  child: AppButton(
                      text: language.cashOutTxt,
                      onPressed: () async {
                        GlobalMethods.pushScreen(
                            context: context,
                            screen: BankAndWalletScreen(
                              isWalletScreen: true,
                            ),
                            screenIdentifier:
                                ScreenIdentifier.BankAndWalletScreen);
                        // launchScreen(
                        //     context,
                        //     BankAndWalletScreen(
                        //       isWalletScreen: true,
                        //     ));
                      })

                  //  AppButtonWidget(
                  //   shapeBorder: RoundedRectangleBorder(
                  //       borderRadius: BorderRadius.circular(10)),
                  //   padding: EdgeInsets.symmetric(horizontal: 60),
                  //   text: language.cashOutTxt,
                  //   onTap: () {
                  //     launchScreen(
                  //         context,
                  //         BankAndWalletScreen(
                  //           isWalletScreen: true,
                  //         ));
                  //   },
                  // ),
                  ),
            ),
            Visibility(
              visible: appStore.isLoading,
              child: loaderWidget(),
            )
          ],
        );
      },
    );
  }
}
