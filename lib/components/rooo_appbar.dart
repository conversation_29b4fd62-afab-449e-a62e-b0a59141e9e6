
import 'package:flutter/material.dart';

class RoooAppbar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actionIconList;
  final Icon? leadingIcon;
  final bool isDarkOverlay;
  const RoooAppbar(
      {super.key,
      required this.title,
      this.actionIconList,
      this.leadingIcon,
      this.isDarkOverlay = true});

  @override
  State<RoooAppbar> createState() => _RoooAppbarState();

  @override
  //  : implement preferredSize
  Size get preferredSize => const Size.fromHeight(60);
}

class _RoooAppbarState extends State<RoooAppbar> {
  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 5,
      // iconTheme:  IconThemeData(color: Colors.white),
      title: Text(
        widget.title,
        style: TextStyle(
            fontSize: 20, fontWeight: FontWeight.bold, color: Colors.white),
      ),
      actions: widget.actionIconList??null,
      leading: widget.leadingIcon??null,
      // systemOverlayStyle: Platform.isIOS ? SystemUiOverlayStyle.light : null,
    );
  }
}
