// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:google_map_marker_animation/widgets/animarker.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:rooo_driver/utils/Common.dart';

// class GoogleMapWidget extends StatelessWidget {
//   final LatLng state;
//   // final ValueNotifier<LatLng>? value;
//   final bool is_polyline_created;
//   final Completer<GoogleMapController> completer;
//   final Set<Polyline> polyLines;
//   final LatLng initialCameraPosition;

//   final Map<MarkerId, Marker> markers;
//   const GoogleMapWidget(
//       {super.key,
//       required this.state,
//       // this.value,
//       required this.markers,
//       required this.polyLines,
//       required this.completer,
//       required this.is_polyline_created,
//       required this.initialCameraPosition});

//   @override
//   Widget build(BuildContext context) {
//     onMapCreated(GoogleMapController controller) {
//       completer.complete(controller);
//     }


    

//     Future<void> animateMap({
//       required Completer<GoogleMapController> completer,
//       double? bearing,
//       double? zoom = null,
//     }) async {
//       final GoogleMapController controller = await completer.future;
//       controller.animateCamera(
//         CameraUpdate.newCameraPosition(
//           CameraPosition(
//               target: LatLng(state.latitude,state.longitude),
//               zoom: zoom ?? await controller.getZoomLevel()),
//           // CameraPosition(target: driverLocation!, zoom: await controller.getZoomLevel(), bearing: driverMarkerRotation),
//         ),
//       );
//     }

//     // animateMap(completer: completer);

//     return Stack(
//       alignment: Alignment.topCenter,
//       children: [
//         Animarker(
          
//           // shouldAnimateCamera: true,
//           useRotation: false,
//           duration: Duration(milliseconds: 1000),
//           mapId: completer.future.then((value) => value.mapId),
//           markers: markers.values.toSet(),
//           child: GoogleMap(
//             zoomControlsEnabled: false,
//             compassEnabled: false,
//             myLocationEnabled: false,
//             onMapCreated: onMapCreated,
//             initialCameraPosition:
//                 CameraPosition(target: initialCameraPosition, zoom: 15),
//             mapType: MapType.normal,
//             polylines: polyLines,
//           ),
//         ),
//         state == LatLng(0, 0) ? SizedBox() : LinearProgressIndicator()
//       ],
//     );
//   }
// }
