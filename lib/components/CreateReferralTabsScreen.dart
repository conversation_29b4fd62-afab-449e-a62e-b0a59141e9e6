import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

import '../main.dart';
import '../utils/Common.dart';
import '../utils/Constants.dart';

class CreateReferralTabScreen extends StatefulWidget {
  final String status;
  CreateReferralTabScreen({required this.status});

  @override
  CreateReferralTabScreenState createState() => CreateReferralTabScreenState();
}

class CreateReferralTabScreenState extends State<CreateReferralTabScreen> {
  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;
  List<String> riderStatus = [REFERRAL_STATUS, REFERRAL_OFFER];

  @override
  void initState() {
    super.initState();

    // init();

    // scrollController.addListener(() {
    //   if (scrollController.position.pixels ==
    //       scrollController.position.maxScrollExtent) {
    //     if (currentPage < totalPage) {
    //       appStore.setLoading(true);
    //       currentPage++;
    //       setState(() {});

    //       init();
    //     }
    //   }
    // });
    // afterBuildCreated(() => appStore.setLoading(true));
  }

  void init() async {
    // await getRiderRequestList(
    //         page: currentPage,
    //         status: widget.status,
    //         driverId: sharedPref.getInt(USER_ID))
    //     .then((value) {
    //   appStore.setLoading(false);

    //   currentPage = value.pagination!.currentPage!;
    //   totalPage = value.pagination!.totalPages!;
    //   if (currentPage == 1) {
    //     riderData.clear();
    //   }
    //   riderData.addAll(value.data!);
    //   setState(() {});
    // }).catchError((error) {
    //   appStore.setLoading(false);
    //   log("Server error");
    // });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Observer(builder: (context) {
      return Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(9),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  language.completedTxt,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(language.completed),
                          Text(
                            '8',
                            style: TextStyle(fontSize: 22),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 40,
                      child: VerticalDivider(
                        thickness: 3,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("You made"),
                          Text(
                            "\$158",
                            style: TextStyle(fontSize: 22),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                const Divider(
                  thickness: 3,
                ),
                // Text('User A' +hit the goal),
                Text('User A hit the goal'),
                Text('You were paid \$100 on Feb 10 2023'),
                const Divider(
                  thickness: 3,
                ),
                Text('User A hit the goal'),
                Text('You were paid \$100 on Feb 10 2023'),
                const Divider(
                  thickness: 3,
                ),
              ],
            ),
          ),

          Visibility(
            visible: appStore.isLoading,
            child: loaderWidget(),
          ),
          // if (riderData.isEmpty)
          //   appStore.isLoading ? SizedBox() : emptyWidget(),
        ],
      );
    });
  }
}
