// import 'package:flutter/material.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

// class AnimatedListViewBuilder extends StatelessWidget {
//   final Widget child;
//   final int itemCount;
//   const     AnimatedListViewBuilder(
//       {super.key, required this.child, required this.itemCount});

//   @override
//   Widget build(BuildContext context) {
//     return AnimationLimiter(
//       child: ListView.builder(
//         itemCount: itemCount,
//         itemBuilder: (BuildContext context, int index) {

//           return AnimationConfiguration.staggeredList(
//             position: index,
//             duration: const Duration(milliseconds: 375),
//             child: SlideAnimation(
//               verticalOffset: 50.0,
//               child: FadeInAnimation(
//                 child: child.,
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
