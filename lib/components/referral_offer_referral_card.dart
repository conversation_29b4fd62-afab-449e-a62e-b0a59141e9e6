import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model.dart';
import 'package:rooo_driver/screens/referrals/refferal_offer_detail_screen.dart';
import 'package:share_plus/share_plus.dart';

class ReferralOfferReferralCard extends StatefulWidget {
  final RefferalOfferModel refferalOfferData;

  const ReferralOfferReferralCard({super.key, required this.refferalOfferData});

  @override
  State<ReferralOfferReferralCard> createState() =>
      _ReferralOfferReferralCardState();
}

class _ReferralOfferReferralCardState extends State<ReferralOfferReferralCard> {
  @override
  Widget build(BuildContext context) {
    final primaryColor = AppColors.primaryColor(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColors.whiteColor(context),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.card_giftcard,
                      color: primaryColor,
                      size: 24,
                    ),
                  ),
                  width10,
                  Expanded(
                    child: CustomText(
                      data: widget.refferalOfferData.title ?? "",
                      size: 18,
                      fontweight: FontWeight.w600,
                      color: AppColors.blackColor(context),
                    ),
                  ),
                ],
              ),
              height15,
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: primaryColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: CustomText(
                  data: widget.refferalOfferData.sub_title.toString(),
                  size: 15,
                  color: AppColors.blackColor(context),
                ),
              ),
              height20,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: ReferralOfferDetailScreen(
                              description: widget.refferalOfferData.details ?? "",
                              title: widget.refferalOfferData.details ?? ""),
                          screenIdentifier:
                              ScreenIdentifier.RefferalOfferDetailScreen);
                    },
                    style: TextButton.styleFrom(
                      backgroundColor: primaryColor.withOpacity(0.08),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    icon: Icon(
                      Icons.info_outline,
                      color: primaryColor,
                      size: 20,
                    ),
                    label: CustomText(
                      data: language.TermsTxt,
                      size: 14,
                      color: primaryColor,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () async {
                      Share.share(
                          widget.refferalOfferData.complete_heading ?? "");
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: AppColors.whiteColor(context),
                      elevation: 1,
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    icon: Icon(Icons.share, size: 18),
                    label: CustomText(
                      data: language.ShareYourLinkTxt,
                      size: 14,
                      color: AppColors.whiteColor(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
