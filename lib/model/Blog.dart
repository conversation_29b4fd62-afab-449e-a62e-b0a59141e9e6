class Blog {
  String title;
  String subTitle;
  String imgUrl;
  int id;

  Blog({required this.title, required this.subTitle, required this.imgUrl, required this.id});

  factory Blog.fromJson(Map<String, dynamic> json) {
    return Blog(
        title: json['title'],
        subTitle: json['subTitle'],
        imgUrl: json['imgUrl'],
        id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['subTitle'] = this.subTitle;
    data['imgUrl'] = this.imgUrl;
    data['id'] = this.id;
    return data;
  }
}