class ChatMessageModel {
  String? id;
  String? senderFirestoreId;
  String? receiverFirestoreId;
  int? rideId;
  String? photoUrl;
  String? messageType;
  bool? isMe;
  bool? isMessageRead;
  String? message;
  int? createdAt;

  ChatMessageModel({
    this.id,
    this.senderFirestoreId,
    this.receiverFirestoreId,
    this.createdAt,
    this.message,
    this.isMessageRead,
    this.photoUrl,
    this.messageType,
    this.rideId,
  });

  factory ChatMessageModel.fromJson(Map<String, dynamic> json) {
    return ChatMessageModel(
      id: json['id'],
      senderFirestoreId: json['senderFirestoreId'],
      receiverFirestoreId: json['receiverFirestoreId'],
      message: json['message'],
      isMessageRead: json['isMessageRead'],
      photoUrl: json['photoUrl'],
      messageType: json['messageType'],
      rideId: json['rideId'],
      createdAt: json['createdAt'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createdAt'] = this.createdAt;
    data['message'] = this.message;
    data['senderFirestoreId'] = this.senderFirestoreId;
    data['isMessageRead'] = this.isMessageRead;
    data['receiverFirestoreId'] = this.receiverFirestoreId;
    data['photoUrl'] = this.photoUrl;
    data['messageType'] = this.messageType;
    data['rideId'] = this.rideId;
    return data;
  }
}
