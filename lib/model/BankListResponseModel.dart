import 'package:rooo_driver/model/BankListModel.dart';

class BankListResponseModel {
  List<BankListModel>? data;

  BankListResponseModel({
    this.data,
  });

  factory BankListResponseModel.fromJson(Map<String, dynamic> json) {
    return BankListResponseModel(
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => BankListModel.fromJson(e))
                .toList()
            : null);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();

    datas["data"] = this.data;
    return datas;
  }
}
