import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/model/ComplaintModel.dart';
import 'package:rooo_driver/model/DriverRatting.dart';


class RideHistoryDetailsModel {
  OnRideRequest? data;
  List<RideHistory>? rideHistory;
  DriverRatting? riderRating;
  DriverRatting? driverRating;
  ComplaintModel? complaint;
  Payment? payment;

  RideHistoryDetailsModel(
      {this.data,
      this.rideHistory,
      this.riderRating,
      this.driverRating,
      this.complaint,
      this.payment});

  RideHistoryDetailsModel.fromJson(Map<String, dynamic> json) {
    data =
        json['data'] != null ? new OnRideRequest.fromJson(json['data']) : null;
    if (json['ride_history'] != null) {
      rideHistory = <RideHistory>[];
      json['ride_history'].forEach((v) {
        rideHistory!.add(new RideHistory.fromJson(v));
      });
    }
    riderRating = json['rider_rating'] != null
        ? new DriverRatting.fromJson(json['rider_rating'])
        : null;
    driverRating = json['driverRating'] != null
        ? new DriverRatting.fromJson(json['driverRating'])
        : null;

    complaint = json['complaint'] != null
        ? ComplaintModel.fromJson(json['complaint'])
        : null;
    payment =
        json['payment'] != null ? new Payment.fromJson(json['payment']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (this.rideHistory != null) {
      data['ride_history'] = this.rideHistory!.map((v) => v.toJson()).toList();
    }
    data['rider_rating'] = this.riderRating;
    data['driver_rating'] = this.driverRating;
    data['complaint'] = this.complaint;
    if (this.payment != null) {
      data['payment'] = this.payment!.toJson();
    }
    return data;
  }
}

class Data {
  int? id;
  int? riderId;
  int? serviceId;
  String? datetime;
  int? isSchedule;
  int? rideAttempt;
  Null otp;
  Null reachedOtp;
  double? totalAmount;
  double? subtotal;
  double? extraChargesAmount;
  Null driverId;
  Null driverName;
  String? riderName;
  Null driverEmail;
  String? riderEmail;
  Null driverContactNumber;
  String? riderContactNumber;
  String? driverProfileImage;
  String? riderProfileImage;
  String? startLatitude;
  String? startLongitude;
  String? startAddress;
  String? endLatitude;
  String? endLongitude;
  String? endAddress;
  String? distanceUnit;
  Null startTime;
  Null endTime;
  Null riderequestInDriverId;
  double? distance;
  Null duration;
  int? seatCount;
  Null reason;
  String? status;
  Null tips;
  int? baseFare;
  int? minimumFare;
  int? perDistance;
  double? perDistanceCharge;
  int? perMinuteDrive;
  int? perMinuteDriveCharge;
  Null perMinuteWaiting;
  Null waitingTime;
  int? waitingTimeLimit;
  Null perMinuteWaitingCharge;
  Null cancelationCharges;
  Null cancelBy;
  int? paymentId;
  String? paymentType;
  String? paymentStatus;
  int? couponDiscount;
  Null couponCode;
  Null couponData;
  int? isRiderRated;
  int? isDriverRated;
  Null maxTimeForFindDriverForRideRequest;
  String? createdAt;
  String? updatedAt;
  int? regionId;
  int? isRideForOther;
  Null otherRiderData;
  Null waitingTimeStart;
  String? estimatedArrivalTime;

  Data(
      {this.id,
      this.riderId,
      this.serviceId,
      this.datetime,
      this.isSchedule,
      this.rideAttempt,
      this.otp,
      this.reachedOtp,
      this.totalAmount,
      this.subtotal,
      this.extraChargesAmount,
      this.driverId,
      this.driverName,
      this.riderName,
      this.driverEmail,
      this.riderEmail,
      this.driverContactNumber,
      this.riderContactNumber,
      this.driverProfileImage,
      this.riderProfileImage,
      this.startLatitude,
      this.startLongitude,
      this.startAddress,
      this.endLatitude,
      this.endLongitude,
      this.endAddress,
      this.distanceUnit,
      this.startTime,
      this.endTime,
      this.riderequestInDriverId,
      this.distance,
      this.duration,
      this.seatCount,
      this.reason,
      this.status,
      this.tips,
      this.baseFare,
      this.minimumFare,
      this.perDistance,
      this.perDistanceCharge,
      this.perMinuteDrive,
      this.perMinuteDriveCharge,
      this.perMinuteWaiting,
      this.waitingTime,
      this.waitingTimeLimit,
      this.perMinuteWaitingCharge,
      this.cancelationCharges,
      this.cancelBy,
      this.paymentId,
      this.paymentType,
      this.paymentStatus,
      this.couponDiscount,
      this.couponCode,
      this.couponData,
      this.isRiderRated,
      this.isDriverRated,
      this.maxTimeForFindDriverForRideRequest,
      this.createdAt,
      this.updatedAt,
      this.regionId,
      this.isRideForOther,
      this.otherRiderData,
      this.waitingTimeStart,
      this.estimatedArrivalTime});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    riderId = json['rider_id'];
    serviceId = json['service_id'];
    datetime = json['datetime'];
    isSchedule = json['is_schedule'];
    rideAttempt = json['ride_attempt'];
    otp = json['otp'];
    reachedOtp = json['reached_otp'];
    totalAmount = json['total_amount'];
    subtotal = json['subtotal'];
    extraChargesAmount = json['extra_charges_amount'];
    driverId = json['driver_id'];
    driverName = json['driver_name'];
    riderName = json['rider_name'];
    driverEmail = json['driver_email'];
    riderEmail = json['rider_email'];
    driverContactNumber = json['driver_contact_number'];
    riderContactNumber = json['rider_contact_number'];
    driverProfileImage = json['driver_profile_image'];
    riderProfileImage = json['rider_profile_image'];
    startLatitude = json['start_latitude'];
    startLongitude = json['start_longitude'];
    startAddress = json['start_address'];
    endLatitude = json['end_latitude'];
    endLongitude = json['end_longitude'];
    endAddress = json['end_address'];
    distanceUnit = json['distance_unit'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    riderequestInDriverId = json['riderequest_in_driver_id'];
    distance = json['distance'];
    duration = json['duration'];
    seatCount = json['seat_count'];
    reason = json['reason'];
    status = json['status'];
    tips = json['tips'];
    baseFare = json['base_fare'];
    minimumFare = json['minimum_fare'];
    perDistance = json['per_distance'];
    perDistanceCharge = json['per_distance_charge'];
    perMinuteDrive = json['per_minute_drive'];
    perMinuteDriveCharge = json['per_minute_drive_charge'];
    perMinuteWaiting = json['per_minute_waiting'];
    waitingTime = json['waiting_time'];
    waitingTimeLimit = json['waiting_time_limit'];
    perMinuteWaitingCharge = json['per_minute_waiting_charge'];
    cancelationCharges = json['cancelation_charges'];
    cancelBy = json['cancel_by'];
    paymentId = json['payment_id'];
    paymentType = json['payment_type'];
    paymentStatus = json['payment_status'];
    couponDiscount = json['coupon_discount'];
    couponCode = json['coupon_code'];
    couponData = json['coupon_data'];
    isRiderRated = json['is_rider_rated'];
    isDriverRated = json['is_driver_rated'];
    maxTimeForFindDriverForRideRequest =
        json['max_time_for_find_driver_for_ride_request'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    regionId = json['region_id'];
    isRideForOther = json['is_ride_for_other'];
    otherRiderData = json['other_rider_data'];
    waitingTimeStart = json['waiting_time_start'];
    estimatedArrivalTime = json['estimatedArrivalTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['rider_id'] = this.riderId;
    data['service_id'] = this.serviceId;
    data['datetime'] = this.datetime;
    data['is_schedule'] = this.isSchedule;
    data['ride_attempt'] = this.rideAttempt;
    data['otp'] = this.otp;
    data['reached_otp'] = this.reachedOtp;
    data['total_amount'] = this.totalAmount;
    data['subtotal'] = this.subtotal;
    data['extra_charges_amount'] = this.extraChargesAmount;
    data['driver_id'] = this.driverId;
    data['driver_name'] = this.driverName;
    data['rider_name'] = this.riderName;
    data['driver_email'] = this.driverEmail;
    data['rider_email'] = this.riderEmail;
    data['driver_contact_number'] = this.driverContactNumber;
    data['rider_contact_number'] = this.riderContactNumber;
    data['driver_profile_image'] = this.driverProfileImage;
    data['rider_profile_image'] = this.riderProfileImage;
    data['start_latitude'] = this.startLatitude;
    data['start_longitude'] = this.startLongitude;
    data['start_address'] = this.startAddress;
    data['end_latitude'] = this.endLatitude;
    data['end_longitude'] = this.endLongitude;
    data['end_address'] = this.endAddress;
    data['distance_unit'] = this.distanceUnit;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    data['riderequest_in_driver_id'] = this.riderequestInDriverId;
    data['distance'] = this.distance;
    data['duration'] = this.duration;
    data['seat_count'] = this.seatCount;
    data['reason'] = this.reason;
    data['status'] = this.status;
    data['tips'] = this.tips;
    data['base_fare'] = this.baseFare;
    data['minimum_fare'] = this.minimumFare;
    data['per_distance'] = this.perDistance;
    data['per_distance_charge'] = this.perDistanceCharge;
    data['per_minute_drive'] = this.perMinuteDrive;
    data['per_minute_drive_charge'] = this.perMinuteDriveCharge;
    data['per_minute_waiting'] = this.perMinuteWaiting;
    data['waiting_time'] = this.waitingTime;
    data['waiting_time_limit'] = this.waitingTimeLimit;
    data['per_minute_waiting_charge'] = this.perMinuteWaitingCharge;
    data['cancelation_charges'] = this.cancelationCharges;
    data['cancel_by'] = this.cancelBy;
    data['payment_id'] = this.paymentId;
    data['payment_type'] = this.paymentType;
    data['payment_status'] = this.paymentStatus;
    data['coupon_discount'] = this.couponDiscount;
    data['coupon_code'] = this.couponCode;
    data['coupon_data'] = this.couponData;
    data['is_rider_rated'] = this.isRiderRated;
    data['is_driver_rated'] = this.isDriverRated;
    data['max_time_for_find_driver_for_ride_request'] =
        this.maxTimeForFindDriverForRideRequest;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['region_id'] = this.regionId;
    data['is_ride_for_other'] = this.isRideForOther;
    data['other_rider_data'] = this.otherRiderData;
    data['waiting_time_start'] = this.waitingTimeStart;
    data['estimatedArrivalTime'] = this.estimatedArrivalTime;
    return data;
  }
}

class RideHistory {
  int? id;
  int? rideRequestId;
  String? datetime;
  String? historyType;
  String? historyMessage;
  HistoryData? historyData;
  String? createdAt;
  String? updatedAt;

  RideHistory(
      {this.id,
      this.rideRequestId,
      this.datetime,
      this.historyType,
      this.historyMessage,
      this.historyData,
      this.createdAt,
      this.updatedAt});

  RideHistory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    rideRequestId = json['ride_request_id'];
    datetime = json['datetime'];
    historyType = json['history_type'];
    historyMessage = json['history_message'];
    historyData = json['history_data'] != null
        ? new HistoryData.fromJson(json['history_data'])
        : null;
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['ride_request_id'] = this.rideRequestId;
    data['datetime'] = this.datetime;
    data['history_type'] = this.historyType;
    data['history_message'] = this.historyMessage;
    if (this.historyData != null) {
      data['history_data'] = this.historyData!.toJson();
    }
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class HistoryData {
  int? riderId;
  String? riderName;

  HistoryData({this.riderId, this.riderName});

  HistoryData.fromJson(Map<String, dynamic> json) {
    riderId = json['rider_id'];
    riderName = json['rider_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rider_id'] = this.riderId;
    data['rider_name'] = this.riderName;
    return data;
  }
}

class Payment {
  int? id;
  int? riderId;
  int? rideRequestId;
  String? datetime;
  num? totalAmount;
  num? adminCommission;
  Null receivedBy;
  int? driverFee;
  int? driverTips;
  num? driverCommission;
  int? fleetCommission;
  String? paymentType;
  Null txnId;
  String? paymentStatus;
  Null transactionDetail;
  String? createdAt;
  String? updatedAt;
  String? transactionType;

  Payment(
      {this.id,
      this.riderId,
      this.rideRequestId,
      this.datetime,
      this.totalAmount,
      this.adminCommission,
      this.receivedBy,
      this.driverFee,
      this.driverTips,
      this.driverCommission,
      this.fleetCommission,
      this.paymentType,
      this.txnId,
      this.paymentStatus,
      this.transactionDetail,
      this.createdAt,
      this.updatedAt,
      this.transactionType});

  Payment.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    riderId = json['rider_id'];
    rideRequestId = json['ride_request_id'];
    datetime = json['datetime'];
    totalAmount = json['total_amount'];
    adminCommission = json['admin_commission'];
    receivedBy = json['received_by'];
    driverFee = json['driver_fee'];
    driverTips = json['driver_tips'];
    driverCommission = json['driver_commission'];
    fleetCommission = json['fleet_commission'];
    paymentType = json['payment_type'];
    txnId = json['txn_id'];
    paymentStatus = json['payment_status'];
    transactionDetail = json['transaction_detail'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    transactionType = json['transaction_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['rider_id'] = this.riderId;
    data['ride_request_id'] = this.rideRequestId;
    data['datetime'] = this.datetime;
    data['total_amount'] = this.totalAmount;
    data['admin_commission'] = this.adminCommission;
    data['received_by'] = this.receivedBy;
    data['driver_fee'] = this.driverFee;
    data['driver_tips'] = this.driverTips;
    data['driver_commission'] = this.driverCommission;
    data['fleet_commission'] = this.fleetCommission;
    data['payment_type'] = this.paymentType;
    data['txn_id'] = this.txnId;
    data['payment_status'] = this.paymentStatus;
    data['transaction_detail'] = this.transactionDetail;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['transaction_type'] = this.transactionType;
    return data;
  }
}
