class StatusMessageModel {
  String message;
  bool status;


  StatusMessageModel({required  this.message,required   this.status});

  factory StatusMessageModel.fromJson(Map<String, dynamic> json) {
    return StatusMessageModel(message: json["message"],
    status: json["status"]
  
    );
  }}

class ChatCountModel {
    int rideId;
    int chatCount;
  ChatCountModel({
    required this.rideId,
    required this.chatCount,
  });
  }
