class BankListModel {
  int? id;
  String? name;
  String? address;
  String? status;
  int? region_id;

  BankListModel({
    this.id,
    this.address,
    this.name,
    this.status,
    this.region_id,
  });

  factory BankListModel.fromJson(Map<String, dynamic> json) {
    return BankListModel(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      status: json['status'],
      region_id: json['region_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'status': status,
      'region_id': region_id,
    };
  }
}
