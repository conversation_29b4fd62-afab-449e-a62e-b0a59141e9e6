import 'package:rooo_driver/model/PaginationModel.dart';

class WithDrawListModel {
  List<WithDrawModel>? data;
  PaginationModel? pagination;
  WalletBalance? wallet_balance;
  String? message;

  WithDrawListModel(
      {this.data, this.pagination, this.wallet_balance, this.message});

  factory WithDrawListModel.fromJson(Map<String, dynamic> json) {
    return WithDrawListModel(
        data: json['data'] != null
            ? (json['data'] as List)
                .map((i) => WithDrawModel.fromJson(i))
                .toList()
            : null,
        pagination: json['pagination'] != null
            ? PaginationModel.fromJson(json['pagination'])
            : null,
        wallet_balance: json['wallet_balance'] != null
            ? WalletBalance.fromJson(json['wallet_balance'])
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.pagination != null) {
      data['pagination'] = this.pagination!.toJson();
    }
    if (this.wallet_balance != null) {
      data['wallet_balance'] = this.wallet_balance!.toJson();
    }

    data["message"] = this.message;
    return data;
  }
}

class WithDrawModel {
  num? amount;
  String? created_at;
  String? currency;
  int? id;
  int? status;
  String? updated_at;
  String? user_display_name;
  int? user_id;
  bool?instant_pay;

  WithDrawModel({
    this.amount,
    this.created_at,
    this.currency,
    this.id,
    this.status,
    this.updated_at,
    this.user_display_name,
    this.user_id,
    this.instant_pay,
  });

  factory WithDrawModel.fromJson(Map<String, dynamic> json) {
    return WithDrawModel(
      amount: json['amount'],
      created_at: json['created_at'],
      currency: json['currency'],
      id: json['id'],
      status: json['status'],
      updated_at: json['updated_at'],
      user_display_name: json['user_display_name'],
      user_id: json['user_id'],
            instant_pay: json['instant_pay'],

    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['amount'] = this.amount;
    data['created_at'] = this.created_at;
    data['currency'] = this.currency;
    data['id'] = this.id;
    data['status'] = this.status;
    data['updated_at'] = this.updated_at;
    data['user_display_name'] = this.user_display_name;
    data['user_id'] = this.user_id;

    data['instant_pay'] = this.instant_pay;
    return data;
  }
}

class WalletBalance {
  num? collectedCash;
  String? createdAt;
  String? currency;
  int? id;
  num? manualReceived;
  num? onlineReceived;
  num? totalAmount;
  num? totalWithdrawn;
  String? updatedAt;
  int? userId;

  WalletBalance({
    this.collectedCash,
    this.createdAt,
    this.currency,
    this.id,
    this.manualReceived,
    this.onlineReceived,
    this.totalAmount,
    this.totalWithdrawn,
    this.updatedAt,
    this.userId,
  });

  factory WalletBalance.fromJson(Map<String, dynamic> json) {
    return WalletBalance(
      collectedCash: json['collected_cash'],
      createdAt: json['created_at'],
      currency: json['currency'],
      id: json['id'],
      manualReceived: json['manual_received'],
      onlineReceived: json['online_received'],
      totalAmount: json['total_amount'],
      totalWithdrawn: json['total_withdrawn'],
      updatedAt: json['updated_at'],
      userId: json['user_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['collected_cash'] = this.collectedCash;
    data['created_at'] = this.createdAt;
    data['currency'] = this.currency;
    data['id'] = this.id;
    data['manual_received'] = this.manualReceived;
    data['online_received'] = this.onlineReceived;
    data['total_amount'] = this.totalAmount;
    data['total_withdrawn'] = this.totalWithdrawn;
    data['updated_at'] = this.updatedAt;
    data['user_id'] = this.userId;
    return data;
  }
}
