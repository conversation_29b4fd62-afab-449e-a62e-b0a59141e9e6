import 'package:rooo_driver/model/PaginationModel.dart';

class DocumentListModelResponse {
  List<NewDocumentListModel>? data;
  PaginationModel? pagination;

  DocumentListModelResponse({this.data, this.pagination});

  factory DocumentListModelResponse.fromJson(Map<String, dynamic> json) {
    return DocumentListModelResponse(
      data: json['data'] != null
          ? (json['data'] as List)
              .map((i) => NewDocumentListModel.fromJson(i))
              .toList()
          : null,
      pagination: json['pagination'] != null
          ? PaginationModel.fromJson(json['pagination'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.pagination != null) {
      data['pagination'] = this.pagination!.toJson();
    }
    return data;
  }
}

class NewDocumentListModel {
  List<NewDocumentListModel>? children;

  String? name;
  String? imageURL;
  String? description;
  int? status;
  int? is_required;
  int? has_expiry_date;
  int? parent_id;
  String? createdAt;
  int? documentId;
  int? id;
  String? updatedAt;

  NewDocumentListModel({
    this.children,
    this.name,
    this.imageURL,
    this.description,
    this.status,
    this.is_required,
    this.parent_id,
    this.createdAt,
    this.documentId,
    this.id,
    this.updatedAt,
    this.has_expiry_date,
  });

  factory NewDocumentListModel.fromJson(Map<String, dynamic> json) {
    return NewDocumentListModel(
      createdAt: json['created_at'],
      documentId: json['document_id'],
      id: json['id'],
      name: json['name'],
      imageURL: json['image_url'],
      description: json['description'],
      status: json['status'],
      updatedAt: json['updated_at'],
      is_required: json['is_required'],
      has_expiry_date: json['has_expiry_date'],
      parent_id: json['parent_id'],
      children: json['children'] != null
          ? (json['children'] as List)
              .map((i) => NewDocumentListModel.fromJson(i))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['created_at'] = this.createdAt;
    data['document_id'] = this.documentId;
    data['id'] = this.id;
    data['updated_at'] = this.updatedAt;
    data['name'] = this.name;
    data['image_url'] = this.imageURL;
    data['description'] = this.description;
    data['status'] = this.status;
    data['is_required'] = this.is_required;
    data['has_expiry_date'] = this.has_expiry_date;
    data['parent_id'] = this.parent_id;
    if (this.children != null) {
      data['data'] = this.children!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
