import 'SettingModel.dart';

class AppSettingModel {
  ProvinceModel? region;
  SettingModel? settingModel;
  List<RideSetting>? rideSetting;
  List<WalletSetting>? walletSetting;
  CurrencySetting? currencySetting;
  PrivacyPolicyModel? privacyPolicyModel;
  PrivacyPolicyModel? termsCondition;
  num? allCommonGeofencing;
  /* in meters */
  num? arrivedGeofencingLimit;

  AppSettingModel(
      {this.region,
      this.rideSetting,
      this.walletSetting,
      this.currencySetting,
      this.settingModel,
      this.privacyPolicyModel,
      this.allCommonGeofencing,
      this.termsCondition});

  AppSettingModel.fromJson(Map<String, dynamic> json) {
    region = json['region'] != null
        ? new ProvinceModel.fromJson(json['region'])
        : null;
    allCommonGeofencing =
        json['geofencing'] != null ? json['geofencing'] : null;
    arrivedGeofencingLimit = json['driver_arrived_radius'] ?? 100;
    settingModel = json['app_setting'] != null
        ? new SettingModel.fromJson(json['app_setting'])
        : null;
    if (json['ride_setting'] != null) {
      rideSetting = <RideSetting>[];
      json['ride_setting'].forEach((v) {
        rideSetting!.add(new RideSetting.fromJson(v));
      });
    }
    if (json['Wallet_setting'] != null) {
      walletSetting = <WalletSetting>[];
      json['Wallet_setting'].forEach((v) {
        walletSetting!.add(new WalletSetting.fromJson(v));
      });
    }
    currencySetting = json['currency_setting'] != null
        ? new CurrencySetting.fromJson(json['currency_setting'])
        : null;
    privacyPolicyModel = json['privacy_policy'] != null
        ? new PrivacyPolicyModel.fromJson(json['privacy_policy'])
        : null;
    termsCondition = json['terms_condition'] != null
        ? new PrivacyPolicyModel.fromJson(json['terms_condition'])
        : null;
  }
}

class ProvinceModel {
  int id;
  String provinceName;
  int regionId;
  String regionName;

  ProvinceModel({
    required this.id,
    required this.provinceName,
    required this.regionId,
    required this.regionName,
  });
  factory ProvinceModel.fromJson(Map<String, dynamic> json) {
    return ProvinceModel(
      id: json['id'],
      provinceName: json['state'],
      regionId: json['region_id'],
      regionName: json['region_name'],
    );
  }
}

class ProvinceModelResponse {
  bool status;
  String message;
  List<ProvinceModel>? data;

  ProvinceModelResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory ProvinceModelResponse.fromJson(Map<String, dynamic> json) {
    return ProvinceModelResponse(
      status: json['status'],
      message: json['message'],
      data: (json['data'] as List?)
          ?.map((e) => ProvinceModel.fromJson(e))
          .toList(),
    );
  }
}

class WalletSetting {
  int? id;
  String? key;
  String? type;
  String? value;

  WalletSetting({this.id, this.key, this.type, this.value});

  WalletSetting.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    key = json['key'];
    type = json['type'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['key'] = this.key;
    data['type'] = this.type;
    data['value'] = this.value;
    return data;
  }
}

class RideSetting {
  int? id;
  String? key;
  String? type;
  String? value;

  RideSetting({this.id, this.key, this.type, this.value});

  RideSetting.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    key = json['key'];
    type = json['type'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['key'] = this.key;
    data['type'] = this.type;
    data['value'] = this.value;
    return data;
  }
}

class CurrencySetting {
  String? name;
  String? code;
  String? symbol;
  String? position;

  CurrencySetting({this.name, this.code, this.position, this.symbol});

  CurrencySetting.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    code = json['code'];
    position = json['position'];
    symbol = json['symbol'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['code'] = this.code;
    data['position'] = this.position;
    data['symbol'] = this.symbol;
    return data;
  }
}

class PrivacyPolicyModel {
  int? id;
  String? key;
  String? type;
  String? value;

  PrivacyPolicyModel({this.id, this.key, this.type, this.value});

  factory PrivacyPolicyModel.fromJson(Map<String, dynamic> json) {
    return PrivacyPolicyModel(
      id: json['id'],
      key: json['key'],
      type: json['type'],
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['key'] = this.key;
    data['type'] = this.type;
    data['value'] = this.value;
    return data;
  }
}
