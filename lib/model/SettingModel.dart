class NotifyAdminWaitingTime {
  int normalRideTime;
  int scheduledRideTime;

  NotifyAdminWaitingTime({
    this.normalRideTime = 300,
    this.scheduledRideTime = 300,
  });

  factory NotifyAdminWaitingTime.fromJson(Map<String, dynamic> json) {
    return NotifyAdminWaitingTime(
      normalRideTime: json['notify_admin_time_for_normal_rides'] != null
          ? int.tryParse(json['notify_admin_time_for_normal_rides'].toString()) ?? 300
          : 300,
      scheduledRideTime: json['notify_admin_time_for_scheduled_rides'] != null
          ? int.tryParse(json['notify_admin_time_for_scheduled_rides'].toString()) ?? 300
          : 300,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['notify_admin_time_for_normal_rides'] = this.normalRideTime;
    data['notify_admin_time_for_scheduled_rides'] = this.scheduledRideTime;
    return data;
  }
}

class SettingModel {
  String? contactEmail;
  String? contactNumber;
  String? createdAt;
  String? facebookUrl;
  int? id;
  String? instagramUrl;
  List<String>? languageOption;
  String? linkedinUrl;
  String? helpSupportUrl;

  String? siteCopyright;
  String? siteDescription;
  String? siteEmail;
  String? siteFavicon;
  String? siteLogo;
  String? siteName;
  String? twitterUrl;
  String? updatedAt;
  NotifyAdminWaitingTime? notifyAdminWaitingTime;

  SettingModel({
    this.contactEmail,
    this.contactNumber,
    this.createdAt,
    this.facebookUrl,
    this.id,
    this.instagramUrl,
    this.languageOption,
    this.linkedinUrl,
    this.siteCopyright,
    this.siteDescription,
    this.siteEmail,
    this.siteFavicon,
    this.siteLogo,
    this.siteName,
    this.twitterUrl,
    this.updatedAt,
    this.helpSupportUrl,
    this.notifyAdminWaitingTime,
  });

  factory SettingModel.fromJson(Map<String, dynamic> json) {
    return SettingModel(
      contactEmail: json['contact_email'],
      contactNumber: json['contact_number'],
      createdAt: json['created_at'],
      facebookUrl: json['facebook_url'],
      id: json['id'],
      instagramUrl: json['instagram_url'],
      languageOption: json['language_option'] != null ? new List<String>.from(json['language_option']) : null,
      linkedinUrl: json['linkedin_url'],
      siteCopyright: json['site_copyright'],
      siteDescription: json['site_description'],
      siteEmail: json['site_email'],
      siteFavicon: json['site_favicon'],
      siteLogo: json['site_logo'],
      siteName: json['site_name'],
      twitterUrl: json['twitter_url'],
      updatedAt: json['updated_at'],
      helpSupportUrl: json['help_support_url'],
      notifyAdminWaitingTime: json['notify_admin_waiting_time'] != null
          ? NotifyAdminWaitingTime.fromJson(json['notify_admin_waiting_time'])
          : NotifyAdminWaitingTime(),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['contact_email'] = this.contactEmail;
    data['contact_number'] = this.contactNumber;
    data['created_at'] = this.createdAt;
    data['facebook_url'] = this.facebookUrl;
    data['id'] = this.id;
    data['instagram_url'] = this.instagramUrl;
    data['linkedin_url'] = this.linkedinUrl;
    data['site_copyright'] = this.siteCopyright;
    data['site_description'] = this.siteDescription;
    data['site_email'] = this.siteEmail;
    data['site_favicon'] = this.siteFavicon;
    data['site_logo'] = this.siteLogo;
    data['site_name'] = this.siteName;
    data['twitter_url'] = this.twitterUrl;
    data['updated_at'] = this.updatedAt;
    data['help_support_url'] = this.helpSupportUrl;
    if (this.notifyAdminWaitingTime != null) {
      data['notify_admin_waiting_time'] = this.notifyAdminWaitingTime!.toJson();
    }
    if (this.languageOption != null) {
      data['language_option'] = this.languageOption;
    }

    return data;
  }
}
