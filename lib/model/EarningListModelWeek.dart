
class RideRequestDetailsModel {
  final int? id;
  final String? startAddress;
  final String? endAddress;
  final Payment? payment;

  RideRequestDetailsModel({
    this.id,
    this.startAddress,
    this.endAddress,
    this.payment,
  });

  factory RideRequestDetailsModel.fromJson(Map<String, dynamic> json) {
    return RideRequestDetailsModel(
      id: json['id'],
      startAddress: json['start_address'],
      endAddress: json['end_address'],
      payment:
          json['payment'] != null ? Payment.fromJson(json['payment']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_address': startAddress,
      'end_address': endAddress,
      'payment': payment?.toJson(),
    };
  }
}

class Payment {
  final int? id;
  final int? rideRequestId;
  final num? driverCommission;

  Payment({
    this.id,
    this.rideRequestId,
    this.driverCommission,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'],
      rideRequestId: json['ride_request_id'],
      driverCommission: json['driver_commission'] != null
          ? (json['driver_commission'] as num)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ride_request_id': rideRequestId,
      'driver_commission': driverCommission,
    };
  }
}

class RideReportModel {
  String? todayDate;
  String? fromDate;
  String? toDate;
  List<WeekReport>? weekReport;
  List<RideRequestDetailsModel>?
      todayRideRequestDetails; // Nullable list for today ride details

  int? totalRideCount;
  num? totalRideFare;
  RideDetailsReportModel? rideDetails;
  int? totalOfferReferral;
  num? totalEarnings;
  num? todayEarnings;

  RideReportModel({
    this.todayDate,
    this.fromDate,
    this.todayRideRequestDetails,
    this.toDate,
    this.weekReport,
    this.totalRideCount,
    this.totalRideFare,
    this.rideDetails,
    this.totalOfferReferral,
    this.totalEarnings,
    this.todayEarnings,
  });

  factory RideReportModel.fromJson(Map<String, dynamic> json) {
    return RideReportModel(
      todayDate: json['today_date'] as String?,
      fromDate: json['from_date'] as String?,
      toDate: json['to_date'] as String?,
      weekReport: (json['week_report'] as List?)
          ?.map((i) => WeekReport.fromJson(i as Map<String, dynamic>))
          .toList(),
      todayRideRequestDetails: (json['today_ride_request_details'] as List?)
          ?.map((i) =>
              RideRequestDetailsModel.fromJson(i as Map<String, dynamic>))
          .toList(),
      totalRideCount: json['today_ride_request'] as int?,
      totalRideFare: json['total_ride_fare'] as num?,
      rideDetails: json['ride_details'] != null
          ? RideDetailsReportModel.fromJson(
              json['ride_details'] as Map<String, dynamic>)
          : null,
      totalOfferReferral: json['total_offer_referral'] as int?,
      totalEarnings: json['total_earnings'] as num?,
      todayEarnings: json['today_earnings'] as num?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'today_date': todayDate,
      'from_date': fromDate,
      'to_date': toDate,
      'week_report': weekReport?.map((day) => day.toJson()).toList(),
      'today_ride_request_details':
          todayRideRequestDetails?.map((day) => day.toJson()).toList(),
      'total_ride_count': totalRideCount,
      'total_ride_fare': totalRideFare,
      'ride_details': rideDetails?.toJson(),
      'total_offer_referral': totalOfferReferral,
      'total_earnings': totalEarnings,
    };
  }
}

class WeekReport {
  String? day;
  double? amount;
  String? date;

  WeekReport({
    this.day,
    this.amount,
    this.date,
  });

  factory WeekReport.fromJson(Map<String, dynamic> json) {
    return WeekReport(
      day: json['day'],
      amount: (json['amount'] as num?)?.toDouble(),
      date: json['date'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'amount': amount,
      'date': date,
    };
  }
}

class RideDetailsReportModel {
  List<DayRideInfo>? days;

  RideDetailsReportModel({
    this.days,
  });

  factory RideDetailsReportModel.fromJson(Map<String, dynamic> json) {
    return RideDetailsReportModel(
      days:
          (json['days'] as List?)?.map((i) => DayRideInfo.fromJson(i)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'days': days?.map((day) => day.toJson()).toList(),
    };
  }
}

class DayRideInfo {
  String? name;
  List<Ride>? rideInformation;

  DayRideInfo({
    this.name,
    this.rideInformation,
  });

  factory DayRideInfo.fromJson(Map<String, dynamic> json) {
    return DayRideInfo(
      name: json['name'],
      rideInformation: (json['ride_information'] as List?)
          ?.map((i) => Ride.fromJson(i))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'ride_information':
          rideInformation?.map((ride) => ride.toJson()).toList(),
    };
  }
}

class Ride {
  int? id;
  String? startAddress;
  String? endAddress;
  num? commission;
  String? datetime;

  Ride({
    this.id,
    this.startAddress,
    this.endAddress,
    this.commission,
    this.datetime,
  });

  factory Ride.fromJson(Map<String, dynamic> json) {
    return Ride(
      id: json['id'],
      startAddress: json['start_address'],
      endAddress: json['end_address'],
      commission: (json['commission'] as num?),
      datetime: json['datetime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_address': startAddress,
      'end_address': endAddress,
      'commission': commission,
      'datetime': datetime,
    };
  }
}
