





class ReviewResponseModel {
  List<ReviewModel>? data;

  ReviewResponseModel({
    required this.data,
  });

  factory ReviewResponseModel.fromJson(Map<String, dynamic> json) {
    return ReviewResponseModel(
  
      data:
          json['data'] != null ? (json['data'] as List).map((i) => ReviewModel.fromJson(i)).toList() : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();

     if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }


    return data;
  }
}




class ReviewModel {
  num rating;
  List  messages;

  ReviewModel({
    required this.rating,
   required  this.messages,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      rating: json["rating"],
      messages:
          json['messages'] != null ? (json['messages'] as List) : [],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();

    data['ride_history'] = this.messages;
  
    data['rating'] = this.rating;

    return data;
  }
}
