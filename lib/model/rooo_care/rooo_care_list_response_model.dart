import 'package:rooo_driver/model/PaginationModel.dart';
import 'package:rooo_driver/model/rooo_care/rooo_care_list_model.dart';

class RoooCareResponseModel {
  PaginationModel? pagination;
  List<RoooCareListModel>? data;
  String? message;

  RoooCareResponseModel({this.data, this.pagination, this.message});

  factory RoooCareResponseModel.fromJson(Map<String, dynamic> json) {
    return RoooCareResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => RoooCareListModel.fromJson(e))
                .toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["message"] = this.message;

    return datas;
  }
}
