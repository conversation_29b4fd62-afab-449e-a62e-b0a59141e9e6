class RoooCareListModel {
  int id;
  String subject;
  String ?message;
  int?status;
  int?driver_id;

  

  RoooCareListModel({
    required this.id,
    this.message,
    required this.subject,
    this.status,
    this.driver_id
  });

  factory RoooCareListModel.fromJson(Map<String, dynamic> json) {
    return RoooCareListModel(
      id: json['id'],
      subject: json['subject'],
      message: json['message'],
      status: json['status'],
      driver_id:json['driver_id']
    );
  }
}