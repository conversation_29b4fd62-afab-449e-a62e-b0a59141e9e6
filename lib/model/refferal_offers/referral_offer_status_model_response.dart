import 'package:rooo_driver/model/PaginationModel.dart';
import 'package:rooo_driver/model/refferal_offers/referral_offer_status_model.dart';

class RefferalOfferModelStatusResponse {
  PaginationModel? pagination;
  List<RefferalOfferStatusModel>? data;
  int ?offer_completed;
  int ?offer_completed_new;
  String ?you_made_offer;
  String ? you_made;
  List<ReferralModel>? referrals;


  RefferalOfferModelStatusResponse({
    this
    .referrals,
    this.data,this.you_made_offer,this.offer_completed_new, this.pagination, this.offer_completed, this.you_made});

  factory RefferalOfferModelStatusResponse.fromJson(Map<String, dynamic> json) {
    return RefferalOfferModelStatusResponse(
        // pagination: json["pagination"] != null
        //     ? PaginationModel.fromJson(json["pagination"])
            // : null,
        data: json["data"] != null
            ? (json["data"] as List).map((e) => RefferalOfferStatusModel.fromJson(e)).toList()
            : null,
               referrals: json["referrals"] != null
            ? (json["referrals"] as List).map((e) => ReferralModel.fromJson(e)).toList()
            : null,
            offer_completed: json["offer_completed"],
                        offer_completed_new: json["offer_completed_new"],
                        you_made_offer:json["you_made_offer"] ,

          you_made: json["you_made"]
    );
          
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
        datas["referrals"] = this.referrals;

    datas["offer_completed"] = this.offer_completed;
    datas["you_made"] = this.you_made;
        datas["offer_completed_new"] = this.offer_completed_new;

    datas["you_made_offer"] = this.you_made_offer;

    return datas;
  }
}




class ReferralModel {
  int id;
  int userId;
  int referBy;
  DateTime createdAt;
  DateTime updatedAt;
  String userName;
  String amount;

  ReferralModel({
    required this.id,
    required this.amount,
    required this.userId,
    required this.referBy,
    required this.createdAt,
    required this.updatedAt,
    required this.userName,
  });

  // Create a Referral object from a JSON map
  factory ReferralModel.fromJson(Map<String, dynamic> json) {
    return ReferralModel(
      id: json['id'],
            amount: json['amount'],

      userId: json['user_id'],
      referBy: json['refer_by'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      userName: json['user_name'],
    );
  }

  // Convert a Referral object to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      "amount":amount,
      'user_id': userId,
      'refer_by': referBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'user_name': userName,
    };
  }
}

