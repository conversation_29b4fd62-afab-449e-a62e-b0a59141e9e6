import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/model/FAQ.dart';

class ScRidesBlogsModel {
  num? rating;
  List<OnRideRequest>? scheduledRides;
  List<FAQ>? blogs;
  int? region_id;
  int? schedule_ride_time;
  int? advance_booking_limit;
  num? today_earning;
  int? ride_accept_decline_duration_for_driver_in_second;
  String? account_delete_instructions_for_driver;
  String? about_us_instruction_driver;

  ScRidesBlogsModel(
      {this.blogs,
      this.rating,
      this.about_us_instruction_driver,
      this.scheduledRides,
      this.region_id,
      this.schedule_ride_time,
      this.advance_booking_limit,
      this.account_delete_instructions_for_driver,
      this.today_earning,
      this.ride_accept_decline_duration_for_driver_in_second});

  factory ScRidesBlogsModel.fromJson(Map<String, dynamic> json) {
    return ScRidesBlogsModel(
      scheduledRides: json["scheduledRides"] != null
          ? (json["scheduledRides"] as List)
              .map((e) => OnRideRequest.fromJson(e))
              .toList()
          : null,
      blogs: json["blogs"] != null
          ? (json["blogs"] as List).map((e) => FAQ.fromJson(e)).toList()
          : null,
      region_id: json["region_id"],
      schedule_ride_time: json["schedule_ride_time"],
      advance_booking_limit: json["advance_booking_limit"],
      today_earning: json["today_earning"],
      account_delete_instructions_for_driver:
          json["account_delete_instructions_for_driver"],
      about_us_instruction_driver: json["about_us_instruction_driver"],
      rating: json["rating"],
      ride_accept_decline_duration_for_driver_in_second:
          json["ride_accept_decline_duration_for_driver_in_second"],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["scheduledRides"] = this.scheduledRides;
    datas["blogs"] = this.blogs;
    datas["rating"] = this.rating;

    datas["region_id"] = this.region_id;
    datas["schedule_ride_time"] = this.schedule_ride_time;
    datas["advance_booking_limit"] = this.advance_booking_limit;
    datas["today_earning"] = this.today_earning;
    datas["about_us_instruction_driver"] = this.about_us_instruction_driver;
    datas["account_delete_instructions_for_driver"] =
        this.account_delete_instructions_for_driver;
    datas["ride_accept_decline_duration_for_driver_in_second"] =
        this.ride_accept_decline_duration_for_driver_in_second;
    return datas;
  }
}

class HomePageModel {
  ScRidesBlogsModel? response;

  HomePageModel({this.response});

  factory HomePageModel.fromJson(Map<String, dynamic> json) {
    return HomePageModel(
      response: json["response"] != null
          ? ScRidesBlogsModel.fromJson(json["response"])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["response"] = this.response;
    return datas;
  }
}
