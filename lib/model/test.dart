import 'package:accordion/accordion.dart';
import 'package:flutter/material.dart';

class TestCard extends StatefulWidget {
  const TestCard({super.key});

  @override
  State<TestCard> createState() => _TestCardState();
}

class _TestCardState extends State<TestCard> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          Accordion(
              header: ListTile(
                title: Text("header"),
              ),
              children: [
                AccordionSection(header: Text("header"), content: Text("data")),
                AccordionSection(
                    header: Text("header2"),
                    content: Column(
                      children: [
                        Accordion(
                            header: ListTile(
                              title: Text("header"),
                            ),
                            children: [
                              AccordionSection(
                                  header: Text("header"), content: Text("data"))
                            ]),
                        Accordion(
                            header: ListTile(
                              title: Text("header"),
                            ),
                            children: [
                              AccordionSection(
                                  header: Text("header"), content: Text("data"))
                            ]),
                        Accordion(
                            header: ListTile(
                              title: Text("header"),
                            ),
                            children: [
                              AccordionSection(
                                  header: Text("header"), content: Text("data"))
                            ]),
                      ],
                    ))
              ])
        ],
      ),
    );
  }
}

var response = {
  "pagination": {
    "total_items": 7,
    "per_page": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "data": [
    {
      "id": 1,
      "parent_id": null,
      "children": [
        {
          "id": 8,
          "parent_id": 1,
          "children": [],
          "name": "Front",
          "imageURL": {},
          "description": null,
          "status": null,
          "is_required": null,
          "has_expiry_date": null,
          "created_at": null,
          "updated_at": null
        },
        {
          "id": 9,
          "parent_id": 1,
          "children": [],
          "name": "Back",
          "imageURL": {},
          "description": null,
          "status": null,
          "is_required": null,
          "has_expiry_date": null,
          "created_at": null,
          "updated_at": null
        }
      ],
      "name": "Drivers Abstract",
      "imageURL":
          "https:\/\/www.rooo.com\/public\/images\/public\/drivers-abstract-ontario.png",
      "description": "test",
      "status": 1,
      "is_required": 1,
      "has_expiry_date": 0,
      "created_at": "2023-04-02T23:09:10.000000Z",
      "updated_at": "2023-07-29T05:31:25.000000Z"
    },
    {
      "id": 2,
      "parent_id": null,
      "children": [],
      "name": "Driver Abstarct Back",
      "imageURL":
          "https:\/\/www.rooo.com\/public\/images\/public\/drivers-abstract-ontario.png",
      "description": null,
      "status": 1,
      "is_required": 0,
      "has_expiry_date": 0,
      "created_at": "2023-08-09T08:35:41.000000Z",
      "updated_at": "2023-08-09T08:35:41.000000Z"
    },
    {
      "id": 3,
      "parent_id": null,
      "children": [],
      "name": "Passport",
      "imageURL":
          "https:\/\/www.rooo.com\/public\/images\/public\/passport-for-canada.png",
      "description": "test",
      "status": 2,
      "is_required": 1,
      "has_expiry_date": 0,
      "created_at": "2023-04-02T23:09:10.000000Z",
      "updated_at": "2023-08-09T09:58:30.000000Z"
    },
    {
      "id": 4,
      "parent_id": null,
      "children": [],
      "name": "Police background check",
      "imageURL":
          "https:\/\/www.rooo.com\/public\/images\/public\/police-background-check-canada.png",
      "description": "test",
      "status": 3,
      "is_required": 1,
      "has_expiry_date": 0,
      "created_at": "2023-04-02T23:09:10.000000Z",
      "updated_at": "2023-08-09T09:59:10.000000Z"
    },
    {
      "id": 5,
      "parent_id": null,
      "children": [],
      "name": "PR card",
      "imageURL":
          "https:\/\/www.rooo.com\/public\/images\/public\/pr-card-for-canada.png",
      "description": "test",
      "status": 1,
      "is_required": 1,
      "has_expiry_date": 0,
      "created_at": "2023-04-02T23:09:10.000000Z",
      "updated_at": "2023-08-10T11:15:38.000000Z"
    },
    {
      "id": 6,
      "parent_id": null,
      "children": [],
      "name": "Social Security Number",
      "imageURL":
          "https:\/\/www.rooo.com\/public\/images\/public\/social-security-number-canada.png",
      "description": "test",
      "status": 1,
      "is_required": 1,
      "has_expiry_date": 0,
      "created_at": "2023-04-02T23:09:10.000000Z",
      "updated_at": "2023-04-17T11:01:20.000000Z"
    },
    {
      "id": 7,
      "parent_id": null,
      "children": [],
      "name": "Work Permit",
      "imageURL":
          "https:\/\/www.rooo.com\/public\/images\/public\/work-permit-for-canada-from-india.png",
      "description": "test",
      "status": 1,
      "is_required": 1,
      "has_expiry_date": 0,
      "created_at": "2023-04-02T23:09:10.000000Z",
      "updated_at": "2023-04-17T11:01:20.000000Z"
    }
  ]
};
