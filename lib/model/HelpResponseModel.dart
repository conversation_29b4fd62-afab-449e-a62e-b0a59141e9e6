// import 'package:rooo_driver/model/FAQ.dart';
// import 'package:rooo_driver/model/PaginationModel.dart';

// class HelpResponseModel {
//   PaginationModel? pagination;
//   List<FAQ>? data;
//   String ? message;

//   HelpResponseModel({this.data, this.pagination, this.message});

//   factory HelpResponseModel.fromJson(Map<String, dynamic> json) {
//     return HelpResponseModel(
//         pagination: json["pagination"] != null
//             ? PaginationModel.fromJson(json["pagination"])
//             : null,
//         data: json["data"] != null
//             ? (json["data"] as List).map((e) => FAQ.fromJson(e)).toList()
//             : null,
            
//             message:json["message"]
//             );
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> datas = new Map<String, dynamic>();
//     datas["pagination"] = this.pagination;
//     datas["data"] = this.data;
//     datas["message"]=this.message;
//     return datas;
//   }
// }
