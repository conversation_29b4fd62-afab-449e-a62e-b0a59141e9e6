class UpdateDriverProfileResponseModel {
  String  ?message;

  UpdateDriverProfileResponseModel({ this.message,});

  factory UpdateDriverProfileResponseModel.fromJson(Map<String, dynamic> json) {
    return UpdateDriverProfileResponseModel(message: json["message"],
    
  
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
  datas["message"]=this.message;
    return datas;
  }
}