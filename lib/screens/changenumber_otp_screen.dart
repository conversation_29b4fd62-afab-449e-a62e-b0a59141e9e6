// import 'dart:io';

// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/global/constants/constants.dart';
// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/global/widgets/app_button.dart';
// import 'package:rooo_driver/global/widgets/ios_padding.dart';
// import 'package:rooo_driver/utils/Extensions/ConformationDialog.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';
// import 'package:intl_phone_field/intl_phone_field.dart';
// import 'package:pinput/pinput.dart';
// import '../network/RestApis.dart';
// import '../utils/Extensions/AppButtonWidget.dart';
// import '../utils/Extensions/app_common.dart';

// import '../../main.dart';
// import '../../utils/Common.dart';
// import '../../utils/Constants.dart';

// class ChangeMobileNumberScreen extends StatefulWidget {
//   final String mobileNumber;
//   ChangeMobileNumberScreen({
//     required this.mobileNumber,
//   });

//   @override
//   ChangeMobileNumberScreenState createState() =>
//       ChangeMobileNumberScreenState();
// }

// class ChangeMobileNumberScreenState extends State<ChangeMobileNumberScreen> {
//   GlobalKey<FormState> _formKey = GlobalKey<FormState>();

//   TextEditingController codeController = TextEditingController();

//   FocusNode codeFocus = FocusNode();
//   FocusNode mobileFocus = FocusNode();

//   String mobileNumber = '';
//   String countryCode = '';

//   bool isSendOTP = true;

//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   void dispose() {
//     appStore.setLoading(false);
//     if (Platform.isIOS)
//       SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
//     super.dispose();
//   }

//   void sendOTPToMobile() {
//     hideKeyboard(context);
//     if (mobileNumber.isEmpty) {
//      GlobalMethods.infoToast(context,  language.enterYourMobileNumber);
//       return;
//     } else if (countryCode + mobileNumber.trim() == widget.mobileNumber) {
//       GlobalMethods.infoToast(context,  
//           language.NumberAlreadyExistPleaseEnterOrUpdateNewNumbertxt);
//       return;
//     }

//     if (_formKey.currentState!.validate()) {
//       _formKey.currentState!.save();

//       setState(() {
//         appStore.setLoading(true);
//       });
//       sendMobileVerificationCode(mobile: countryCode + mobileNumber)
//           .then((value) {
//         setState(() {
//           appStore.setLoading(false);
//         });
//         if (value == null || value['status'] == null) {
//          GlobalMethods.infoToast(context,  language.errorMsg);
//         } else if (value['status'] == false) {
//           GlobalMethods.infoToast(context,  value['message']);
//         } else if (value['status'] == true) {
//           setState(() {
//             isSendOTP = false;
//           });
//          GlobalMethods.infoToast(context,  language.enterOTP);
//         }
//       }).onError((error, stackTrace) {
//         setState(() {
//           appStore.setLoading(false);
//         });
//         GlobalMethods.infoToast(context,  
//           language.errorMsg,
//         );
//         // handleError(error, stackTrace);
//       });
//     }
//   }

//   Future<void> changeMobile() async {
//     hideKeyboard(context);
//     if (_formKey.currentState!.validate()) {
//       _formKey.currentState!.save();
//       if (codeController.text.trim().length != 6) {
//        GlobalMethods.infoToast(context,  language.enterOTP);
//       } else {
//         setState(() {
//           appStore.setLoading(true);
//         });
//         await changeRiderMobile(
//           verificationCode: codeController.text.trim(),
//           mobile: countryCode + mobileNumber,
//         ).then((value) {
//           setState(() {
//             appStore.setLoading(false);
//           });
//           if (value == null) {
//             setState(() {
//               isSendOTP = false;
//             });
//            GlobalMethods.infoToast(context,  language.errorMsg);
//           } else if (value['status'] == true) {
//             //show message
//             showInfoDialog(
//               context,
//               title: value['message'] ?? '',
//               barrierDismissible: false,
//               dialogType: DialogType.ACCEPT,
//               text: language.done,
//               onAccept: (p0) {
//                 Navigator.of(context).pop();
//                 Navigator.of(context).pop();
//                 Navigator.of(context).pop();
//               },
//             );
//           } else if (value['status'] == false) {
//             GlobalMethods.infoToast(context,  value['message']);

//             codeController.clear();
//           } else {
//             GlobalMethods.infoToast(context,  
//               language.errorMsg,
//             );
//           }
//         }).onError((error, stackTrace) {
//           setState(() {
//             appStore.setLoading(false);
//           });
//          GlobalMethods.infoToast(context,  language.errorMsg);
//           // handleError(error, stackTrace);
//         });
//       }
//     }
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         hideKeyboard(context);
//       },
//       child: Scaffold(
//         appBar: RoooAppbar(
//           title: language.ChangeMobileTxt,
//           isDarkOverlay: false,
//         ),
//         body: Padding(
//           padding: screenPadding,
//           child: Stack(
//             children: [
//               Form(
//                 key: _formKey,
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     SizedBox(
//                       height: 80,
//                       child: Theme(
//                         data: ThemeData(
//                           disabledColor: Colors.black87,
//                         ),
//                         child: IntlPhoneField(
//                           validator: (p0) {
//                             if (int.tryParse((p0?.number) ?? '') == null) {
//                               return language.InvalidMobileNumberTxt;
//                             }
//                             return null;
//                           },
//                           decoration: InputDecoration(
//                             disabledBorder: OutlineInputBorder(
//                               borderSide: BorderSide(
//                                 color: Colors.grey[400]!,
//                               ),
//                             ),
//                             labelText: language.MobileNumberTxt,
//                             border: OutlineInputBorder(
//                               borderSide: BorderSide(),
//                             ),
//                           ),
//                           initialCountryCode: 'CA',
//                           onChanged: (phone) {
//                             if (isSendOTP == false) {
//                               setState(() {
//                                 isSendOTP = true;
//                               });
//                             }
//                             countryCode = phone.countryCode;
//                             mobileNumber = phone.number.trim();
//                           },
//                           onCountryChanged: (value) {
//                             countryCode = value.code;
//                           },
//                         ),
//                       ),
//                     ),
//                     isSendOTP
//                         ? const SizedBox()
//                         : Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               SizedBox(height: 20),
//                               Text(
//                                 language.verifyOTPHeading,
//                                 style: TextStyle(),
//                               ),
//                               height10,
//                               Pinput(
//                                 controller: codeController,
//                                 length: 6,
//                                 onCompleted: (pin) {
//                                   // _otp = pin;
//                                 },
//                               ),
//                             ],
//                           ),
//                   ],
//                 ),
//               ),
//               Observer(
//                 builder: (_) {
//                   return Visibility(
//                     visible: appStore.isLoading,
//                     child: loaderWidget(),
//                   );
//                 },
//               ),
//             ],
//           ),
//         ),
//         bottomNavigationBar: IosPadding(
//             child: AppButton(
//                 text: isSendOTP ? language.sendOTP : language.ChangeMobileTxt,
//                 onPressed: () {
//                   isSendOTP ? sendOTPToMobile : changeMobile;
//                 })
//             //  AppButtonWidget(
//             //   text: isSendOTP ? language.sendOTP : language.ChangeMobileTxt,
//             //   textStyle: boldTextStyle(color: Colors.white),
//             //   onTap: isSendOTP ? sendOTPToMobile : changeMobile,
//             // ),
//             ),
//       ),
//     );
//   }
// }
