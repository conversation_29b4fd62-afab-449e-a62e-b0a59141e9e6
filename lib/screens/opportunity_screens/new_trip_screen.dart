// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:rooo_driver/components/ScheduledRideCard.dart';
// import 'package:rooo_driver/components/custom_text.dart';
// import 'package:rooo_driver/global/constants/app_enums.dart';
// import 'package:rooo_driver/global/constants/constants.dart';
// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/global/models/on_ride_request_model.dart';
// import 'package:rooo_driver/global/models/ride_model.dart';
// import 'package:rooo_driver/global/widgets/empty_widget.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/network/RestApis.dart';
// import 'package:rooo_driver/screens/opportunity_details_screen.dart';
// import 'package:rooo_driver/utils/Common.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';

// class NewTripScreen extends StatefulWidget {
//   @override
//   NewTripScreenState createState() => NewTripScreenState();
// }

// class NewTripScreenState extends State<NewTripScreen> {
//   List<OnRideRequest> _opprtunityList = [];
//   int currentPage = 1;
//   int totalPage = 1;
//   String imageUrl = '';
//   String? api_message;

//   @override
//   void initState() {
//     getOpportunityListfn();
//     super.initState();
//   }

//   @override
//   void dispose() {
//     appStore.setLoading(false);
//     super.dispose();
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   void getOpportunityListfn() async {
//     setState(() {
//       appStore.setLoading(true);
//     });

//     await getOpportunityListApi(page: currentPage, request: "new")
//         .then((value) {
//       appStore.setLoading(false);

//       if (value.data != null) {
//         _opprtunityList = value.data!;
//         imageUrl = value.banner_image ?? "";
//       }
//       api_message = value.messasge;

//       currentPage = value.pagination!.currentPage!;

//       print(_opprtunityList.toString());
//       setState(() {});
//     }).catchError((error) {
//       appStore.setLoading(false);
//       setState(() {});
//     });
//   }

//   Future<void> rideRequestAccept(
//       {bool deCline = false, required int id}) async {
//     setState(() {
//       appStore.setLoading(true);
//     });

//     Map req = {
//       "id": id,
//       "driver_id": sharedPref.getInt(USER_ID),
//       "is_accept": "1",
//     };

//     await rideRequestResPondSchedule(request: req).then((value) async {
//      GlobalMethods.infoToast(context,  language.RideAcceptedTxt);

//       getOpportunityListfn();
//     }).catchError((error) {
//       appStore.setLoading(false);
//       log("Server error");
//     });
//   }

// // Future<void> rideRequestAccept(
// //       {bool deCline = false, required int id}) async {
// //     setState(() {
// //       appStore.setLoading(true);
// //     });

// //     Map req = {
// //       "id": id,
// //       "driver_id": sharedPref.getInt(USER_ID),
// //       "is_accept": "1",
// //     };

// //     await rideRequestResPondSchedule(request: req).then((value) async {
// //       GlobalMethods.infoToast(context,  "ride Accepted");

// //       getOpportunityListfn();
// //     }).catchError((error) {
// //       appStore.setLoading(false);
// //       log("Server error");
// //     });
// //   }

//   // Future<void> getData() async {
//   //       appStore.setLoading(true);

//   //   var result = await getOpportunityData();
//   //   if (result != null) {
//   //           appStore.setLoading(false);

//   //     setState(() {

//   //     });
//   //     appStore.setLoading(false);
//   //   }
//   // }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         _opprtunityList.isEmpty
//             ? RooEmptyWidegt(title: api_message.toString())
//             : ListView(
//                 children: [
//                   height10,
//                   Container(
//                     padding: EdgeInsets.all(2),
//                     child: CachedNetworkImage(
//                       imageUrl: imageUrl,
//                       placeholder: (context, url) => Container(
//                         height: MediaQuery.of(context).size.height * .30,
//                         child: Center(
//                           child: CircularProgressIndicator(),
//                         ),
//                       ),
//                       errorWidget: (context, url, error) => Container(
//                         height: MediaQuery.of(context).size.height * .30,
//                         child: Center(
//                           child: Icon(
//                             Icons.error,
//                           ),
//                         ),
//                       ),
//                       imageBuilder: (context, imageProvider) => Container(
//                         height: MediaQuery.of(context).size.height * .30,
//                         decoration: BoxDecoration(
//                           image: DecorationImage(
//                             image: imageProvider,
//                             fit: BoxFit.cover,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                   height10,
//                   _opprtunityList.isEmpty
//                       ? CustomText(
//                           data:
//                               "${language.SorryYouDontHaveAnyOpportunitiesTxt},\n ${language.NewOpportunitiesAreComingTxt}, \n ${language.stayTunedTxt}.",
//                           textAlign: TextAlign.center,
//                         )
//                       : ListView.separated(
//                           physics: NeverScrollableScrollPhysics(),
//                           shrinkWrap: true,
//                           padding: EdgeInsets.all(screenPaddingValue),
//                           itemBuilder: (context, index) {
//                             OnRideRequest data = _opprtunityList[index];
//                             return InkWell(
//                               onTap: () {


//                                 GlobalMethods.pushScreen(context: context, screen:     OpportunityDetailScreen(
//                                       opportunityRequest:
//                                           _opprtunityList[index],
//                                     ), screenIdentifier: ScreenIdentifier.OpportunityDetailScreen);
//                                 // launchScreen(
//                                 //     context,
//                                 //     OpportunityDetailScreen(
//                                 //       opportunityRequest:
//                                 //           _opprtunityList[index],
//                                 //     ));
//                               },
//                               child: OpportunityRideCard(
//                                 text: language.accept,
//                                 datetime: _opprtunityList[index].datetime,
//                                 endAddress: _opprtunityList[index]
//                                     .endAddress
//                                     .toString(),
//                                 startAddress: _opprtunityList[index]
//                                     .startAddress
//                                     .toString(),
//                                 isButton: true,
//                                 onPressed: () {
//                                   rideRequestAccept(id: data.id!);
//                                 },
//                               ),
//                             );

//                             // FAQCard(
//                             //   onTap: () {
//                             //     launchScreen(
//                             //       context,
//                             //       FaqDetailsScreen(
//                             //         id: item.id,
//                             //         title: item.title,

//                             //       ),
//                             //     );
//                             //   },
//                             //   faq: item,
//                             // );
//                           },
//                           separatorBuilder: (context, index) => SizedBox(
//                             height: 10,
//                           ),
//                           itemCount: _opprtunityList.length,
//                         ),
//                 ],
//               ),
//         Observer(
//           builder: (context) {
//             return Visibility(
//               visible: appStore.isLoading,
//               child: loaderWidget(),
//             );
//           },
//         )
//       ],
//     );
//   }
// }
