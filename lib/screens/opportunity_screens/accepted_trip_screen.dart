// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:rooo_driver/components/ScheduledRideCard.dart';
// import 'package:rooo_driver/components/custom_text.dart';
// import 'package:rooo_driver/features/initial/screen/initial_screen.dart';
// import 'package:rooo_driver/global/constants/app_enums.dart';
// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/global/models/on_ride_request_model.dart';
// import 'package:rooo_driver/global/models/ride_model.dart';
// import 'package:rooo_driver/global/widgets/app_button.dart';
// import 'package:rooo_driver/global/widgets/empty_widget.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/network/RestApis.dart';
// import 'package:rooo_driver/screens/dashboard/screens/oldinitial_screen.dart';
// import 'package:rooo_driver/screens/opportunity_details_screen.dart';
// import 'package:rooo_driver/utils/Common.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/AppButtonWidget.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';

// import '../../global/constants/constants.dart';

// class AcceptedTripScreen extends StatefulWidget {
//   @override
//   AcceptedTripScreenState createState() => AcceptedTripScreenState();
// }

// class AcceptedTripScreenState extends State<AcceptedTripScreen> {
//   List<OnRideRequest> _opprtunityList = [];
//   int currentPage = 1;
//   int totalPage = 1;
//   String imageUrl = '';
//   String? api_message;

//   bool currentlyRiding = true;
//   @override
//   void initState() {
//     getOpportunityListfn();

//     getCurrentRequest();
//     super.initState();
//   }

//   @override
//   void dispose() {
//     appStore.setLoading(false);
//     super.dispose();
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   void getOpportunityListfn() async {
//     setState(() {
//       appStore.setLoading(true);
//     });

//     await getOpportunityListApi(page: currentPage, request: "accepted")
//         .then((value) {
//       appStore.setLoading(false);

//       if (value.data != null) {
//         _opprtunityList = value.data!;
//         imageUrl = value.banner_image ?? "";
//       }
//       api_message = value.messasge;

//       currentPage = value.pagination!.currentPage!;

//       print(_opprtunityList.toString());
//       setState(() {});
//     }).catchError((error) {
//       appStore.setLoading(false);
//       setState(() {});
//     });
//   }

//   Future<void> getCurrentRequest() async {
//     setState(() {
//       appStore.setLoading(true);
//     });

//     await getCurrentRideRequest().then((value) async {
//       appStore.setLoading(false);
//       if (value.onRideRequest == null) {
//         currentlyRiding = false;
//       } else {
//         currentlyRiding = true;
//       }
//     });
//   }

//   Future<void> startScheduleRide({String? status, required int id}) async {
//     appStore.setLoading(true);
//     Map req = {
//       "id": id,
//       "status": ARRIVING,
//     };
//     await rideRequestUpdate(request: req, rideId: id).then((value) async {
//       appStore.setLoading(false);

//       GlobalMethods.pushAndRemoveAll(
//           context: context,
//           screen: InitialScreen(),
//           screenIdentifier: ScreenIdentifier.InitialScreen);
//       // launchScreen(context, InitialScreen(), isNewTask: true);
//     }).catchError((error) {
//       appStore.setLoading(false);
//       GlobalMethods.infoToast(context, error);
//       // log("Server error" + "---->");
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         _opprtunityList.isEmpty
//             ? RooEmptyWidegt(title: api_message.toString())
//             : ListView(
//                 children: [
//                   height10,
//                   Container(
//                     padding: EdgeInsets.all(2),
//                     child: CachedNetworkImage(
//                       imageUrl: imageUrl,
//                       placeholder: (context, url) => Container(
//                         height: MediaQuery.of(context).size.height * .30,
//                         child: Center(
//                           child: CircularProgressIndicator(),
//                         ),
//                       ),
//                       errorWidget: (context, url, error) => Container(
//                         height: MediaQuery.of(context).size.height * .30,
//                         child: Center(
//                           child: Icon(
//                             Icons.error,
//                           ),
//                         ),
//                       ),
//                       imageBuilder: (context, imageProvider) => Container(
//                         height: MediaQuery.of(context).size.height * .30,
//                         decoration: BoxDecoration(
//                           image: DecorationImage(
//                             image: imageProvider,
//                             fit: BoxFit.cover,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                   height10,
//                   _opprtunityList.isEmpty
//                       ? CustomText(
//                           data:
//                               "${language.SorryYouDontHaveAnyOpportunitiesTxt},\n ${language.NewOpportunitiesAreComingTxt}, \n ${language.stayTunedTxt}.",
//                           textAlign: TextAlign.center,
//                         )
//                       : ListView.separated(
//                           physics: NeverScrollableScrollPhysics(),
//                           shrinkWrap: true,
//                           padding: EdgeInsets.all(screenPaddingValue),
//                           itemBuilder: (context, index) {
//                             OnRideRequest data = _opprtunityList[index];
//                             return InkWell(
//                               onTap: () {

//                                 GlobalMethods.pushScreen(context: context, screen:  OpportunityDetailScreen(
//                                       opportunityRequest:
//                                           _opprtunityList[index],
//                                     ), screenIdentifier: ScreenIdentifier.OpportunityDetailScreen);
//                                 // launchScreen(
//                                 //     context,
//                                 //     OpportunityDetailScreen(
//                                 //       opportunityRequest:
//                                 //           _opprtunityList[index],
//                                 //     ));
//                               },
//                               child: Card(
//                                 color: Colors.white,
//                                 child: Padding(
//                                   padding: const EdgeInsets.all(8.0),
//                                   child: Column(
//                                     children: [
//                                       OpportunityRideCard(
//                                         datetime:
//                                             _opprtunityList[index].datetime,
//                                         endAddress: _opprtunityList[index]
//                                             .endAddress
//                                             .toString(),
//                                         startAddress: _opprtunityList[index]
//                                             .startAddress
//                                             .toString(),
//                                         isButton: false,
//                                         onPressed: () {},
//                                       ),
//                                       height20,
//                                       AppButton(
//                                           text: currentlyRiding
//                                               ? "Please complete this ride first"
//                                               : "Start Ride",
//                                           onPressed: () {
//                                             if (currentlyRiding) {
//                                               GlobalMethods.infoToast(context,
//                                                   "You have already have a ride, please complete this ride first");
//                                             } else {
//                                               startScheduleRide(id: data.id!);
//                                             }
//                                           })
//                                       // AppButtonWidget(
//                                       //   width: double.infinity,
//                                       //   text: currentlyRiding
//                                       //       ? "Please complete this ride first"
//                                       //       : "Start Ride",
//                                       //   onTap: () {
//                                       //     if (currentlyRiding) {
//                                       //       GlobalMethods.infoToast(context,
//                                       //           "You have already have a ride, please complete this ride first");
//                                       //     } else {
//                                       //       startScheduleRide(id: data.id!);
//                                       //     }
//                                       //   },
//                                       // )
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                             );

//                             // FAQCard(
//                             //   onTap: () {
//                             //     launchScreen(
//                             //       context,
//                             //       FaqDetailsScreen(
//                             //         id: item.id,
//                             //         title: item.title,

//                             //       ),
//                             //     );
//                             //   },
//                             //   faq: item,
//                             // );
//                           },
//                           separatorBuilder: (context, index) => SizedBox(
//                             height: 10,
//                           ),
//                           itemCount: _opprtunityList.length,
//                         ),
//                 ],
//               ),
//         Observer(
//           builder: (context) {
//             return Visibility(
//               visible: appStore.isLoading,
//               child: loaderWidget(),
//             );
//           },
//         )
//       ],
//     );
//   }
// }
