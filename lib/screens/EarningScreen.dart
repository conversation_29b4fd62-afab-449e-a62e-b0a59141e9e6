
// import 'package:flutter/material.dart';
// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/utils/Common.dart';

// import '../components/EarningReportWidget.dart';
// import '../components/EarningTodayWidget.dart';
// import '../components/EarningWeekWidget.dart';
// import '../main.dart';
// import '../model/EarningListModelWeek.dart';

// class EarningScreen extends StatefulWidget {
//   @override
//   EarningScreenState createState() => EarningScreenState();
// }

// class EarningScreenState extends State<EarningScreen> with AutomaticKeepAliveClientMixin {
//   EarningModel? earningListModelWeek;
//   List<WeekReport> weekReport = [];

//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   @override
//   Widget build(BuildContext context) {
//     super.build(context);
//     return DefaultTabController(
//       length: 3,
//       child: Scaffold(
//         appBar: RoooAppbar(
//           title: language.earning,
//         ),
//         body: Column(
//           children: [


//             tabContainer(tabs: [
//                   language.today,
//                   language.weekly,
//                   language.report,
//                   ]),
           
//             Expanded(
//               child: TabBarView(
//                 children: [
//                   EarningTodayWidget(),
//                   EarningWeekWidget(),
//                   EarningReportWidget(),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
  
//   @override
//   // TODO: implement wantKeepAlive
//   bool get wantKeepAlive => true;
// }
