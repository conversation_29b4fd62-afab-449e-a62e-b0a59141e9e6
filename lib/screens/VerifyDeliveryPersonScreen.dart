// import 'dart:io';

// import 'package:accordion/accordion.dart';
// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/model/DocumentListModel.dart';
// import 'package:rooo_driver/model/DriverDocumentList.dart';
// import 'package:rooo_driver/screens/dashboard/screens/initial_screen.dart';
// import 'package:rooo_driver/screens/dashboard/screens/no_ride_screen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';
// import 'package:http/http.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/utils/Colors.dart';
// import 'package:rooo_driver/utils/Extensions/AppButtonWidget.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:image_picker/image_picker.dart';

// import '../network/NetworkUtils.dart';
// import '../network/RestApis.dart';
// import '../utils/Common.dart';
// import '../utils/Constants.dart';
// import '../utils/Extensions/ConformationDialog.dart';

// class VerifyDeliveryPersonScreen extends StatefulWidget {
//   final bool isShow;

//   VerifyDeliveryPersonScreen({this.isShow = false});

//   @override
//   VerifyDeliveryPersonScreenState createState() =>
//       VerifyDeliveryPersonScreenState();
// }

// class VerifyDeliveryPersonScreenState
//     extends State<VerifyDeliveryPersonScreen> {
//   String? required_document_uploaded;
//   DateTime selectedDate = DateTime.now();
//   List<DocumentModel> documentList = [];

//   List<DriverDocumentModel> driverDocumentList = [];
//   XFile? filePickerResult;

//   List<int> uploadedDocList = [];
//   List<String> eAttachments = [];
//   File? imageFiles;
//   int docId = 0;

//   int? isExpire;

//   Future<void> selectDate(BuildContext context) async {
//     final DateTime? picked = await showDatePicker(
//       context: context,
//       initialDate: selectedDate,
//       firstDate: DateTime.now(),
//       lastDate: DateTime(2040),
//     );
//     if (picked != null && picked != selectedDate) {
//       setState(() {
//         selectedDate = picked;
//       });
//     }
//   }

//   @override
//   void initState() {
//     // logOutSuccess();

//     super.initState();
//     init();
//   }

//   void init() async {
//     afterBuildCreated(() async {
//       appStore.setLoading(true);
//       await getDocument();
//       await DriverDocument();
//     });
//   }

//   ///Driver Document List
//   Future<void> getDocument() async {
//     appStore.setLoading(true);
//     await getDocumentList().then((value) {
//       documentList.addAll(value.data!);
//       appStore.setLoading(false);
//       setState(() {});
//     }).catchError((error) {
//       appStore.setLoading(false);
//       GlobalMethods.infoToast(context,  "Server error");
//       log("Server error");
//     });
//   }

//   ///Document List
//   Future<void> DriverDocument() async {
//     appStore.setLoading(true);
//     await getDriverDocumentList().then((value) {
//       driverDocumentList.clear();
//       driverDocumentList.addAll(value.data!);
//       uploadedDocList.clear();
//       driverDocumentList.forEach((element) {
//         uploadedDocList.add(element.documentId!);
//       });
//       appStore.setLoading(false);

//       setState(() {});
//     }).catchError((error) {
//       setState(() {
//         appStore.setLoading(false);
//       });
//       log("Server error");
//       GlobalMethods.infoToast(context,  "Server error");
//     });
//   }

//   /// Add Documents
//   addDocument(int? docId, int? isExpire,
//       {int? updateId, DateTime? dateTime}) async {
//     MultipartRequest multiPartRequest = await getMultiPartRequest(
//         updateId == null
//             ? 'driver-document-save'
//             : 'driver-document-update/$updateId');
//     multiPartRequest.fields['driver_id'] =
//         sharedPref.getInt(USER_ID).toString();
//     multiPartRequest.fields['document_id'] = docId.toString();
//     multiPartRequest.fields['is_verified'] = '0';
//     if (isExpire != null)
//       multiPartRequest.fields['expire_date'] = dateTime.toString();
//     if (imageFiles != null) {
//       multiPartRequest.files.add(
//           await MultipartFile.fromPath("driver_document", imageFiles!.path));
//     }
//     multiPartRequest.headers.addAll(buildHeaderTokens());
//     appStore.setLoading(true);
//     sendMultiPartRequest(
//       multiPartRequest,
//       onSuccess: (data) async {
//         await DriverDocument();
//       },
//       onError: (error) {
//         GlobalMethods.infoToast(context,  "Server error", print: true);
//         appStore.setLoading(false);
//       },
//     ).catchError((e) {
//       appStore.setLoading(false);
//       GlobalMethods.infoToast(context,  e.toString());
//     });
//   }

//   /// SelectImage
//   getMultipleFile(int? docId, int? isExpire,
//       {int? updateId, DateTime? dateTime}) async {
//     filePickerResult = await ImagePicker()
//         .pickImage(source: ImageSource.camera, imageQuality: 50
//             // preferredCameraDevice: CameraDevice.rear,
//             // type: FileType.custom,
//             // allowedExtensions: ['jpg', 'png', 'jpeg', 'pdf']
//             );

//     if (filePickerResult != null) {
//       showConfirmDialogCustom(
//         context,
//         title: language.uploadFileConfirmationMsg,
//         onAccept: (BuildContext context) {
//           setState(() {
//             imageFiles = File(filePickerResult!.path);

//             eAttachments = [];
//           });
//           addDocument(docId, isExpire,
//               dateTime: selectedDate, updateId: updateId);
//         },
//         positiveText: language.yes,
//         negativeText: language.no,
//          
//       );
//       if (isExpire == 1) selectDate(context);
//     } else {}
//   }

//   /// Delete Documents
//   deleteDoc(int? id) {
//     setState(() {
//       appStore.setLoading(true);
//     });
//     deleteDeliveryDoc(id!).then((value) async {
//       GlobalMethods.infoToast(context,  value.message, print: true);

//       await DriverDocument();

//       setState(() {
//         appStore.setLoading(false);
//       });
//     }).catchError((e) {
//       setState(() {
//         appStore.setLoading(false);
//       });

//       GlobalMethods.infoToast(context,  e.toString());
//     });
//   }

//   Future<void> getDetailAPi() async {
//     // launchScreen(context, DriverDashboardScreen(),
//     //     isNewTask: true, pageRouteAnimation: PageRouteAnimation.Slide);

//     appStore.setLoading(true);
//     await getUserDetail(userId: sharedPref.getInt(USER_ID)).then((value) {
//       appStore.setLoading(false);

//       sharedPref.setInt(
//           IS_Verified_Driver, (value.data?.isVerifiedDriver ?? 0));
//       // if (value.data!.isDocumentRequired != 0) {
//       // showConfirmDialogCustom(context,
//       //      
//       //     dialogType: DialogType.CONFIRMATION,
//       //     title: "Please Upload and verify Documents for further options",
//       //     positiveText: "Ok",
//       //     negativeText: language.cancel, onAccept: (v) async {
//       //   appStore.setLoading(true);
//       //   await Future.delayed(Duration(milliseconds: 500));
//       // await
//       launchScreen(context, InitialScreen(), isNewTask: true);
//       // }
//       // )
//       ;
//       //GlobalMethods.infoToast(context,  language.someRequiredDocumentAreNotUploaded);
//       // }
//       // else {
//       //   if (sharedPref.getInt(IS_Verified_Driver) == 1) {
//       //     launchScreen(context, DriverDashboardScreen(),
//       //         isNewTask: true, pageRouteAnimation: PageRouteAnimation.Slide);
//       //   } else {
//       //     GlobalMethods.infoToast(context,  '${language.userNotApproveMsg}');
//       //   }
//       // }
//     }).catchError((error) {
//       appStore.setLoading(false);

//       log("Server error");
//     });
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   List<dynamic> getChildDocs(int getIndex) {
//     List<dynamic> childData = [];

//     if (documentList[getIndex].children!.isNotEmpty) {
//       for (int index = 0;
//           index < documentList[getIndex].children!.length;
//           index++) {
//         int itemIndex = driverDocumentList.indexWhere((element) =>
//             element.documentId == documentList[getIndex].children![index].id);

//         DriverDocumentModel? driverDocument;
//         if (itemIndex >= 0) {
//           driverDocument = driverDocumentList[itemIndex];
//         }

//         bool isRejected = driverDocument?.isVerified==2 ;
//         // childData.add(Divider());

//         childData.add(Container(
//           child: Column(
//             children: [
//               Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [                                                  
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                             documentList[getIndex].children![index].name ?? ''),
//                         Text(driverDocument?.rejectionText ?? ''),
//                       ],
//                     ),
//                   ),
//                   const SizedBox(
//                     width: 4,
//                   ),
//                   InkWell(
//                     onTap: () {
//                       showDialog(
//                         context: context,
//                         builder: (context) {
//                           return AlertDialog(
//                             title: Text(
//                               documentList[getIndex].children![index].name!,
//                             ),
//                             content: Column(
//                               mainAxisSize: MainAxisSize.min,
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Divider(),
//                                 Text(
//                                   documentList[getIndex]
//                                           .children![index]
//                                           .description ??
//                                       language.notAvailable,
//                                 ),
//                               ],
//                             ),
//                             actions: [
//                               AppButtonWidget(
//     
//                                 onTap: () {
//                                   Navigator.of(context).pop();
//                                 },
//                                 child: Text(
//                                   language.OkTxt,
//                                   style: TextStyle(color: Colors.white),
//                                 ),
//                               )
//                             ],
//                           );
//                         },
//                       );
//                     },
//                     child: Container(
//                       padding: EdgeInsets.all(
//                         2,
//                       ),
//                       decoration: BoxDecoration(
//                         shape: BoxShape.circle,
//                
//                       ),
//                       child: Icon(
//                         Icons.question_mark_rounded,
//                         size: 14,
//                 
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//               const SizedBox(
//                 height: 6,
//               ),
//               Container(
//                 padding: EdgeInsets.all(8),
//                 decoration: BoxDecoration(
//             
//                     border: Border.all(color: borderColor),
//                     borderRadius: BorderRadius.circular(defaultRadius)),
//                 child: driverDocument == null
//                     ? InkWell(
//                         onTap: () {
//                           if (documentList[getIndex]
//                                   .children![index]
//                                   .hasExpiryDate ==
//                               1) {
//                             getMultipleFile(
//                               documentList[getIndex].children![index].id,
//                               documentList[getIndex]
//                                           .children![index]
//                                           .hasExpiryDate ==
//                                       0
//                                   ? null
//                                   : 1,
//                               dateTime: selectedDate,
//                             );
//                           } else {
//                             getMultipleFile(
//                               documentList[getIndex].children![index].id,
//                               documentList[getIndex]
//                                           .children![index]
//                                           .hasExpiryDate ==
//                                       0
//                                   ? null
//                                   : 1,
//                             );
//                           }
//                         },
//                         child: ClipRRect(
//                           borderRadius: BorderRadius.circular(defaultRadius),
//                           child:

//                               // SizedBox(
//                               //   height: 200,
//                               //   width:
//                               //       MediaQuery.of(context).size.width,
//                               //   child: FittedBox(
//                               //     child: Icon(
//                               //       Icons.image,
//                               //     ),
//                               //   ),
//                               // ),

//                               Stack(
//                             children: [
//                               commonCachedNetworkImage(
//                                   documentList[getIndex]
//                                           .children![index]
//                                           .imageURL ??
//                                       '',
//                                   height: 200,
//                                   width: MediaQuery.of(context).size.width,
//                                   fit: BoxFit.cover,
//                                   showLoading: true),
//                               Container(
//                                 height: 200,
//                                 color: Colors.black.withOpacity(
//                                   0.15,
//                                 ),
//                               ),
//                               Positioned(
//                                 bottom: 10,
//                                 left: 0,
//                                 right: 0,
//                                 child: Center(
//                                   child: Container(
//                                     height: 40,
//                                     width: 100,
//                                     decoration: BoxDecoration(
//                                       borderRadius: BorderRadius.circular(
//                                         30,
//                                       ),
//           
//                                     ),
//                                     child: Center(
//                                       child: Text(
//                                         language.TakePhoto,
//                                         style: TextStyle(
//                                           fontSize: 10,
//                                           color: Colors.white,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               )
//                             ],
//                           ),
//                         ),
//                       )
//                     : Column(
//                         children: [
//                           ClipRRect(
//                             borderRadius: BorderRadius.circular(defaultRadius),
//                             child: commonCachedNetworkImage(
//                               driverDocument.driverDocument!,
//                               height: 200,
//                               width: MediaQuery.of(context).size.width,
//                               fit: BoxFit.cover,
//                               showLoading: true,
//                             ),
//                           ),
//                           SizedBox(height: 16),
//                           Row(
//                             children: [
//                               driverDocument.expireDate != null
//                                   ? Text(language.expireDate,
//                                       style: boldTextStyle())
//                                   : Text(''),
//                               SizedBox(width: 8),
//                               driverDocument.expireDate != null
//                                   ? Expanded(
//                                       child: Text(
//                                           driverDocument.expireDate.toString(),
//                                           style: primaryTextStyle()))
//                                   : Expanded(
//                                       child: Text(''),
//                                     ),
//                               Visibility(
//                                 visible: driverDocument.isVerified == 0 ||
//                                     documentList[index].hasExpiryDate == 1 ||
//                                     isRejected,
//                                 child: inkWellWidget(
//                                   onTap: () {
//                                     if (documentList[getIndex]
//                                             .children![index]
//                                             .hasExpiryDate ==
//                                         1) {
//                                       getMultipleFile(
//                                           documentList[getIndex]
//                                               .children![index]
//                                               .id,
//                                           documentList[getIndex]
//                                                       .children![index]
//                                                       .hasExpiryDate ==
//                                                   0
//                                               ? null
//                                               : 1,
//                                           dateTime: selectedDate,
//                                           updateId: driverDocument!.id);
//                                     } else {
//                                       getMultipleFile(
//                                           documentList[getIndex]
//                                               .children![index]
//                                               .id,
//                                           documentList[getIndex]
//                                                       .children![index]
//                                                       .hasExpiryDate ==
//                                                   0
//                                               ? null
//                                               : 1,
//                                           updateId: driverDocument!.id);
//                                     }
//                                   },
//                                   child: Container(
//                                     height: 25,
//                                     width: 25,
//                                     decoration: BoxDecoration(
//                                       .withOpacity(0.2),
//                                       borderRadius: BorderRadius.circular(4),
//                                       border:
//                                           Border.all(),
//                                     ),
//                                     child: Icon(Icons.edit,
//              size: 14),
//                                   ),
//                                 ),
//                               ),
//                               SizedBox(width: 16),
//                               Visibility(
//                                 visible: driverDocument.isVerified == 0 ||
//                                     documentList[index].hasExpiryDate == 1,
//                                 child: inkWellWidget(
//                                   onTap: () async {
//                                     showConfirmDialogCustom(
//                                       context,
//                                       title: language
//                                           .areYouSureYouWantToDeleteThisDocument,
//                                       onAccept: (BuildContext context) async {
//                                         await deleteDoc(driverDocument!.id);
//                                       },
//                                       positiveText: language.yes,
//                                       negativeText: language.no,
//                                        
//                                     );
//                                   },
//                                   child: Container(
//                                     height: 25,
//                                     width: 25,
//                                     decoration: BoxDecoration(
//                                       color: Colors.red.withOpacity(0.2),
//                                       borderRadius: BorderRadius.circular(4),
//                                       border: Border.all(color: Colors.red),
//                                     ),
//                                     child: Icon(Icons.delete,
//                                         color: Colors.red, size: 14),
//                                   ),
//                                 ),
//                               ),
//                               SizedBox(width: 16),
//                               Visibility(
//                                 visible: driverDocument.isVerified == 1,
//                                 child: Icon(Icons.verified_user,
//                                     color: Colors.green),
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: 8),
//                         ],
//                       ),
//               ),
//             ],
//           ),
//         ));
//       }
//     }
//     return childData;
//   }

//   Widget _getNestedDocsView(int parentDocIndex, dynamic mainChildren) {
//     List<AccordionSection> children = [];

//     for (int childIndex = 0;
//         childIndex < (documentList[parentDocIndex].children ?? []).length;
//         childIndex++) {
//       int itemIndex = driverDocumentList.indexWhere((element) =>
//           element.documentId ==
//           documentList[parentDocIndex].children![childIndex].id);

//       DriverDocumentModel? driverDocument;
//       if (itemIndex >= 0) {
//         driverDocument = driverDocumentList[itemIndex];
//       }

//       bool isVerified = driverDocument?.isVerified == 1;
//       bool isRejected = driverDocument?.isVerified==2;

//       children.add(AccordionSection(
//         contentBackgroundColor: getBlackColor(),
//         paddingBetweenClosedSections: 20,
//         headerBorderRadius: 0,
//         headerPadding: const EdgeInsets.symmetric(
//           horizontal: 10,
//           vertical: 20,
//         ),
//         flipRightIconIfOpen: false,
//         contentBorderColor: Colors.grey[300],
//         contentBorderWidth: 1,
//         rightIcon: (driverDocument != null)
//             ? Align(
//                 alignment: Alignment.centerRight,
//                 child: SizedBox(
//                   height: 20,
//                   child: CircleAvatar(
//                     backgroundColor: isRejected
//                         ? Colors.red
//                         : isVerified
//                             ? Color.fromARGB(255, 9, 152, 69).withOpacity(.8)
//                             : Colors.green[200],
//                     child: Icon(
//                       isRejected ? Icons.close : Icons.done_rounded,
//                       color: Colors.white,
//                       size: 12,
//                     ),
//                   ),
//                 ),
//               )
//             : Icon(Icons.arrow_drop_down_sharp),
//         headerBackgroundColor: isVerified
//             ? getDocumentVerifiedColor()
//             : getDocumentNotVerifiedColor(),
//         header: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//                 driverDocument == null
//                     ? language.NotUploadedTxt
//                     : isVerified
//                         ? language.completedTxt
//                         : isRejected
//                             ? language.rejectedTxt
//                             : language.pendingTxt,
//                 style: TextStyle(
//                     fontWeight: FontWeight.w700,
//                     fontSize: 12,
//                     color: isVerified
//                         ? Colors.black
//                         : IS_DARK_MODE_ON
//                             ? Colors.white
//                             : Colors.black)),
//             Text(
//               documentList[parentDocIndex].children![childIndex].name!,
//               style: TextStyle(
//                   color: isVerified
//                       ? Colors.black
//                       : IS_DARK_MODE_ON
//                           ? Colors.white
//                           : Colors.black,
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold),
//             ),
//             documentList[parentDocIndex].children![childIndex].isRequired == 1
//                 ? Text(
//                     language.requiredTxt,
//                     style: TextStyle(
//                         color: Colors.red,
//                         fontSize: 14,
//                         fontWeight: FontWeight.w500),
//                   )
//                 : SizedBox()
//           ],
//         ),
//         content: mainChildren[childIndex],
//       ));
//     }

//     return Accordion(
//       scaleWhenAnimating: false,
//       flipRightIconIfOpen: false,
//       disableScrolling: true,
//       maxOpenSections: children.length,
//       children: [
//         ...children,
//       ],
//     );
//   }

//   bool _areAllNestedDocsVerified(int parentDocIndex) {
//     // get all child docs
//     List children = documentList[parentDocIndex].children!;
//     int requiredChildDocs = documentList[parentDocIndex].totalRequiredDocs ?? 1;

//     int verifiedChildDocs = 0;
//     int itemIndex = -1;

//     DriverDocumentModel? driverDocument;

//     for (var doc in children) {
//       itemIndex = driverDocumentList
//           .indexWhere((element) => element.documentId == doc.id);

//       if (itemIndex >= 0) {
//         driverDocument = driverDocumentList[itemIndex];
//         if ((driverDocument.isVerified ?? 0) == 1) {
//           verifiedChildDocs++;
//         }
//       }
//     }
//     return requiredChildDocs <= verifiedChildDocs;
//   }

//   bool _childDocsAreUploaded(int parentDocIndex) {
//     // get all child docs
//     List children = documentList[parentDocIndex].children!;

//     int requiredChildDocs = documentList[parentDocIndex].totalRequiredDocs ?? 1;

//     int uploadedChildDocs = 0;

//     int itemIndex = -1;

//     for (var doc in children) {
//       itemIndex = driverDocumentList.indexWhere((element) {
//         return element.documentId == doc.id &&
//             element.parent_id == documentList[parentDocIndex].id;
//       });

//       if (itemIndex != -1) {
//         uploadedChildDocs++;
//       }
//     }
//     return requiredChildDocs <= uploadedChildDocs;
//   }

//   List<dynamic> _getView() {
//     List<dynamic> list = [];
//     List<dynamic> childs = [];
//     for (int index = 0; index < documentList.length; index++) {
//       childs = [];
//       childs = getChildDocs(index);

//       int itemIndex = driverDocumentList.indexWhere(
//           (element) => element.documentId == documentList[index].id);

//       DriverDocumentModel? driverDocument;
//       if (itemIndex >= 0) {
//         driverDocument = driverDocumentList[itemIndex];
//       }

//       bool isVerified = childs.isEmpty
//           ? driverDocument?.isVerified == 1
//           : _areAllNestedDocsVerified(index);
//       bool isRejected = driverDocument?.isVerified==2;

//       bool areAllUploaded = _childDocsAreUploaded(index);

//       list.add(
//         AccordionSection(
//           contentBackgroundColor: getBlackColor(),
//           paddingBetweenClosedSections: 20,
//           headerBorderRadius: 0,
//           headerPadding: const EdgeInsets.symmetric(
//             horizontal: 10,
//             vertical: 20,
//           ),
//           flipRightIconIfOpen: false,
//           contentBorderColor: Colors.grey[300],
//           contentBorderWidth: 1,
//           rightIcon: (driverDocument != null ||
//                   (childs.isNotEmpty && _childDocsAreUploaded(index)))
//               ? Align(
//                   alignment: Alignment.centerRight,
//                   child: SizedBox(
//                     height: 20,
//                     child: CircleAvatar(
//                       backgroundColor: isRejected
//                           ? Colors.red
//                           : isVerified
//                               ? Color.fromARGB(255, 9, 152, 69).withOpacity(.8)
//                               : Colors.green[200],
//                       child: Icon(
//                         isRejected ? Icons.close : Icons.done_rounded,
//                         color: Colors.white,
//                         size: 12,
//                       ),
//                     ),
//                   ),
//                 )
//               : Icon(Icons.arrow_drop_down_sharp),
//           headerBackgroundColor: isVerified
//               ? getDocumentVerifiedColor()
//               : getDocumentNotVerifiedColor(),
//           header: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                   childs.isEmpty
//                       ? (driverDocument == null
//                           ? language.NotUploadedTxt
//                           : isVerified
//                               ? language.completedTxt
//                               : isRejected
//                                   ? language.rejectedTxt
//                                   : language.pendingTxt)
//                       : !areAllUploaded
//                           ? language.NotUploadedTxt
//                           : isVerified
//                               ? language.completedTxt
//                               : isRejected
//                                   ? language.rejectedTxt
//                                   : language.pendingTxt,
//                   style: TextStyle(
//                       fontWeight: FontWeight.w700,
//                       fontSize: 12,
//                       color: isVerified
//                           ? Colors.black
//                           : IS_DARK_MODE_ON
//                               ? Colors.white
//                               : Colors.black)),

//               Text(
//                 documentList[index].name!,
//                 style: TextStyle(
//                     color: isVerified
//                         ? Colors.black
//                         : IS_DARK_MODE_ON
//                             ? Colors.white
//                             : Colors.black,
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold),
//               ),
//               documentList[index].description != null
//                   ? Text(
//                       documentList[index].description.toString(),
//                       style: TextStyle(
//                  
//                           fontSize: 14,
//                           fontWeight: FontWeight.w500),
//                     )
//                   : SizedBox(),
//               //can ber change, dont delete it, important
// //                !(driverDocument != null ||
// //                   (childs.isNotEmpty && _childDocsAreUploaded(index)))&&documentList[index].children!.isNotEmpty?

// //                  Text(
// //                   "required Total "+documentList[index].isRequired.toString()+" document"
// // ,
// //                 style: TextStyle(
// //            
// //                     fontSize: 14,
// //                     fontWeight: FontWeight.w500),
// //               ):SizedBox(),
//             ],
//           ),
//           content: Column(
//             children: [
//               if (childs.isEmpty)
//                 Container(
//                   child: Column(
//                     children: [
//                       Row(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Expanded(
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Text(documentList[index].name ?? ''),
//                                 Text(driverDocument?.rejectionText ?? ''),
//                               ],
//                             ),
//                           ),
//                           const SizedBox(
//                             width: 4,
//                           ),
//                           InkWell(
//                             onTap: () {
//                               showDialog(
//                                 context: context,
//                                 builder: (context) {
//                                   return AlertDialog(
//                                     title: Text(
//                                       documentList[index].name!,
//                                     ),
//                                     content: Column(
//                                       mainAxisSize: MainAxisSize.min,
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         Divider(),
//                                         Text(
//                                           documentList[index].description ??
//                                               language.notAvailable,
//                                         ),
//                                       ],
//                                     ),
//                                     actions: [
//                                       AppButtonWidget(
//             
//                                         onTap: () {
//                                           Navigator.of(context).pop();
//                                         },
//                                         child: Text(
//                                           language.OkTxt,
//                                           style: TextStyle(color: Colors.white),
//                                         ),
//                                       )
//                                     ],
//                                   );
//                                 },
//                               );
//                             },
//                             child: Container(
//                               padding: EdgeInsets.all(
//                                 2,
//                               ),
//                               decoration: BoxDecoration(
//                                 shape: BoxShape.circle,
//     
//                               ),
//                               child: Icon(
//                                 Icons.question_mark_rounded,
//                                 size: 14,
//      
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                       const SizedBox(
//                         height: 6,
//                       ),
//                       Container(
//                         padding: EdgeInsets.all(8),
//                         decoration: BoxDecoration(
//  
//                             border: Border.all(color: borderColor),
//                             borderRadius: BorderRadius.circular(defaultRadius)),
//                         child: driverDocument == null
//                             ? InkWell(
//                                 onTap: () {
//                                   if (documentList[index].hasExpiryDate == 1) {
//                                     getMultipleFile(
//                                       documentList[index].id,
//                                       documentList[index].hasExpiryDate == 0
//                                           ? null
//                                           : 1,
//                                       dateTime: selectedDate,
//                                     );
//                                   } else {
//                                     getMultipleFile(
//                                       documentList[index].id,
//                                       documentList[index].hasExpiryDate == 0
//                                           ? null
//                                           : 1,
//                                     );
//                                   }
//                                 },
//                                 child: ClipRRect(
//                                   borderRadius:
//                                       BorderRadius.circular(defaultRadius),
//                                   child:

//                                       // SizedBox(
//                                       //   height: 200,
//                                       //   width:
//                                       //       MediaQuery.of(context).size.width,
//                                       //   child: FittedBox(
//                                       //     child: Icon(
//                                       //       Icons.image,
//                                       //     ),
//                                       //   ),
//                                       // ),

//                                       Stack(
//                                     children: [
//                                       commonCachedNetworkImage(
//                                           documentList[index].imageURL ?? '',
//                                           height: 200,
//                                           width:
//                                               MediaQuery.of(context).size.width,
//                                           fit: BoxFit.cover,
//                                           showLoading: true),
//                                       Container(
//                                         height: 200,
//                                         color: Colors.black.withOpacity(
//                                           0.15,
//                                         ),
//                                       ),
//                                       Positioned(
//                                         bottom: 10,
//                                         left: 0,
//                                         right: 0,
//                                         child: Center(
//                                           child: Container(
//                                             height: 40,
//                                             width: 100,
//                                             decoration: BoxDecoration(
//                                               borderRadius:
//                                                   BorderRadius.circular(
//                                                 30,
//                                               ),
//                   
//                                             ),
//                                             child: Center(
//                                               child: Text(
//                                                 language.TakePhoto,
//                                                 style: TextStyle(
//                                                   fontSize: 10,
//                                                   color: Colors.white,
//                                                 ),
//                                               ),
//                                             ),
//                                           ),
//                                         ),
//                                       )
//                                     ],
//                                   ),
//                                 ),
//                               )
//                             : Column(
//                                 children: [
//                                   ClipRRect(
//                                     borderRadius:
//                                         BorderRadius.circular(defaultRadius),
//                                     child: commonCachedNetworkImage(
//                                       driverDocument.driverDocument!,
//                                       height: 200,
//                                       width: MediaQuery.of(context).size.width,
//                                       fit: BoxFit.cover,
//                                       showLoading: true,
//                                     ),
//                                   ),
//                                   SizedBox(height: 16),
//                                   Row(
//                                     children: [
//                                       driverDocument.expireDate != null
//                                           ? Text(language.expireDate,
//                                               style: boldTextStyle())
//                                           : Text(''),
//                                       SizedBox(width: 8),
//                                       driverDocument.expireDate != null
//                                           ? Expanded(
//                                               child: Text(
//                                                   driverDocument.expireDate
//                                                       .toString(),
//                                                   style: primaryTextStyle()))
//                                           : Expanded(
//                                               child: Text(''),
//                                             ),
//                                       Visibility(
//                                         visible: driverDocument.isVerified ==
//                                                 0 ||
//                                             documentList[index].hasExpiryDate ==
//                                                 1,
//                                         child: inkWellWidget(
//                                           onTap: () {
//                                             if (documentList[index]
//                                                     .hasExpiryDate ==
//                                                 1) {
//                                               getMultipleFile(
//                                                   documentList[index].id,
//                                                   documentList[index]
//                                                               .hasExpiryDate ==
//                                                           0
//                                                       ? null
//                                                       : 1,
//                                                   dateTime: selectedDate,
//                                                   updateId: driverDocument!.id);
//                                             } else {
//                                               getMultipleFile(
//                                                   documentList[index].id,
//                                                   documentList[index]
//                                                               .hasExpiryDate ==
//                                                           0
//                                                       ? null
//                                                       : 1,
//                                                   updateId: driverDocument!.id);
//                                             }
//                                           },
//                                           child: Container(
//                                             height: 25,
//                                             width: 25,
//                                             decoration: BoxDecoration(
//                                               
//                                                   .withOpacity(0.2),
//                                               borderRadius:
//                                                   BorderRadius.circular(4),
//                                               border: Border.all(
//                                                   ),
//                                             ),
//                                             child: Icon(Icons.edit,
//                     
//                                                 size: 14),
//                                           ),
//                                         ),
//                                       ),
//                                       SizedBox(width: 16),
//                                       Visibility(
//                                         visible: driverDocument.isVerified ==
//                                                 0 ||
//                                             documentList[index].hasExpiryDate ==
//                                                 1 ||
//                                             isRejected,
//                                         child: inkWellWidget(
//                                           onTap: () async {
//                                             showConfirmDialogCustom(
//                                               context,
//                                               title: language
//                                                   .areYouSureYouWantToDeleteThisDocument,
//                                               onAccept:
//                                                   (BuildContext context) async {
//                                                 await deleteDoc(
//                                                     driverDocument!.id);
//                                               },
//                                               positiveText: language.yes,
//                                               negativeText: language.no,
//                                                
//                                             );
//                                           },
//                                           child: Container(
//                                             height: 25,
//                                             width: 25,
//                                             decoration: BoxDecoration(
//                                               color:
//                                                   Colors.red.withOpacity(0.2),
//                                               borderRadius:
//                                                   BorderRadius.circular(4),
//                                               border:
//                                                   Border.all(color: Colors.red),
//                                             ),
//                                             child: Icon(Icons.delete,
//                                                 color: Colors.red, size: 14),
//                                           ),
//                                         ),
//                                       ),
//                                       SizedBox(width: 16),
//                                       Visibility(
//                                         visible: driverDocument.isVerified == 1,
//                                         child: Icon(Icons.verified_user,
//                                             color: Colors.green),
//                                       ),
//                                     ],
//                                   ),
//                                   SizedBox(height: 8),
//                                 ],
//                               ),
//                       ),
//                     ],
//                   ),
//                 ),
//               if (childs.isNotEmpty)
//                 //nested accordian
//                 _getNestedDocsView(index, childs)
//             ],
//           ),
//         ),
//       );
//     }
//     return list;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         
//         appBar: RoooAppbar(
//           title: language.document,
//           actionIconList: widget.isShow
//               ? [
//                   Padding(
//                     padding: EdgeInsets.only(
//                       top: 10,
//                       bottom: 10,
//                       right: 10,
//                     ),
//                     child: AppButtonWidget(
//                       padding: EdgeInsets.zero,
//                       text: language.logOut,
//                       color: Colors.white,
//                       textStyle: TextStyle(
//                         color: Colors.black,
//                         fontSize: 13,
//                       ),
//                       onTap: () async {
//                         await showConfirmDialogCustom(context,
//                              
//                             dialogType: DialogType.CONFIRMATION,
//                             title: language.areYouSureYouWantToLogoutThisApp,
//                             positiveText: language.yes,
//                             negativeText: language.no, onAccept: (v) async {
//                           appStore.setLoading(true);
//                           await Future.delayed(Duration(milliseconds: 500));
//                           await logout();
//                           appStore.setLoading(false);
//                         });
//                       },
//                     ),
//                   )
//                 ]
//               : null,
//         ),
//         body: Observer(builder: (context) {
//           return Stack(
//             children: [
//               SingleChildScrollView(
//                   padding: EdgeInsets.all(16),
//                   child: Accordion(
//                     scaleWhenAnimating: false,
//                     flipRightIconIfOpen: false,
//                     disableScrolling: true,
//                     maxOpenSections: documentList.length,
//                     children: [
//                       ..._getView(),
//                     ],
//                   )),
//               Visibility(
//                 visible: appStore.isLoading,
//                 child: loaderWidget(),
//               ),
//             ],
//           );
//         }),
//         bottomNavigationBar:
//             // driverDocumentList.isNotEmpty?
//             Visibility(
//           visible: widget.isShow,
//           child: Padding(
//             padding: EdgeInsets.all(16),
//             child: AppButtonWidget(
//      
//               textStyle: boldTextStyle(color: Colors.white),
//               text: language.goDashBoard,
//               onTap: () {
//                 getDetailAPi();
//               },
//             ),
//           ),
//         )
//         // : SizedBox(),
//         );
//   }
// }
