// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_polyline_points/flutter_polyline_points.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';

// class PolylineTestingScreen extends StatefulWidget {
//   const PolylineTestingScreen({super.key});

//   @override
//   State<PolylineTestingScreen> createState() => _PolylineTestingScreenState();
// }

// class _PolylineTestingScreenState extends State<PolylineTestingScreen>
//     with SingleTickerProviderStateMixin {
//   Set<Polyline> _polyLines = Set<Polyline>();
//   PolylinePoints polylinePoints = PolylinePoints();
//   List<LatLng> polylineCoordinates = [];
//   List<LatLng> initial = [
    
//   ];
//   List<LatLng> middle = [];
//   List<LatLng> secondmiddle = [];
//   List<LatLng> finallist = [];

//   LatLng initiallatlng = LatLng(31.022016899861917, 75.78458795743093);
//   LatLng middlelatlng = LatLng(31.224020, 75.770798);
//   LatLng finallatlang = LatLng(31.3260, 75.5762);

//   late AnimationController animatiocontroller;
//   late Animation polylineCoordinatesaimation;

//   Future<void> setPolyLines() async {
//     var result = await polylinePoints.getRouteBetweenCoordinates(
//         googleMapAPIKey,
//         PointLatLng(31.022016899861917, 75.78458795743093),
//         PointLatLng(31.224020, 75.770798));
//     if (result.points.isNotEmpty) {
//       setState(() {
        
//       });
//       // polylineCoordinates = [];

//       // polylineCoordinates.add(LatLng(31.224020, 75.770798));
//       // polylineCoordinates.add(LatLng(31.02201689986191, 75.78458795743093));

//       _polyLines.add(
//         Polyline(
//           width: 5,
//           polylineId: PolylineId('poly'),
//           color: Color.fromARGB(255, 40, 122, 198),
//           points: polylineCoordinatesaimation.value,
//         ),
//       );
//     }
//   }
  

//   void initState() {
//       animatiocontroller =
//         AnimationController(vsync: this, duration: Duration(seconds: 20));
// initial=[initiallatlng,finallatlang];

// middle=[middlelatlng,finallatlang];

//     animatiocontroller.addListener(() {
//       setState(() {});
//     });

//     super.initState();
  


//             // setPolyLines();


//   }

//   @override
//   Widget build(BuildContext context) {
    
//                 setPolyLines();

//     return Scaffold(
//       appBar: AppBar(
//         title: Text("testing"),
//       ),
//       body: Stack(
//         children: [
//           GoogleMap(
//             myLocationEnabled: true,
//             polylines: _polyLines,
//             initialCameraPosition: CameraPosition(
//                 target: LatLng(31.022016899861917, 75.78458795743093),
//                 zoom: 17.0),
//           )
//         ],
//       ),
//     );
//   }
// }
