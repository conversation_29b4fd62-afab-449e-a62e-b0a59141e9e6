import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';

class OpportunityDetailScreen extends StatefulWidget {
  final OnRideRequest opportunityRequest;
  const OpportunityDetailScreen({super.key, required this.opportunityRequest});

  @override
  State<OpportunityDetailScreen> createState() =>
      _OpportunityDetailScreenState();
}

class _OpportunityDetailScreenState extends State<OpportunityDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: RoooAppbar(title: language.opportunityTxt),
        body: Padding(
          padding: screenPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(child: CustomText(data: language.from)),
                  Expanded(
                      child: CustomText(
                          data: widget.opportunityRequest.startAddress
                              .toString())),
                ],
              ),
              Align(
                alignment: Alignment.center,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [],
              ),
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.only(top: 8, bottom: 8),
                margin: EdgeInsets.only(top: 8, bottom: 8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white),
                  borderRadius: BorderRadius.circular(defaultRadius),
                  boxShadow: [
                    BoxShadow(
                        color: Colors.grey.withOpacity(0.4),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: Offset(0.0, 0.0)),
                  ],
                ),
                child: Container(
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(child: CustomText(data: language.from)),
                          Expanded(
                              child: CustomText(
                                  data: widget.opportunityRequest.startAddress
                                      .toString())),
                        ],
                      ),
                      Divider(
                        thickness: 1,
                      ),
                      Row(
                        children: [
                          Expanded(child: CustomText(data: language.to)),
                          Expanded(
                              child: CustomText(
                                  data: widget.opportunityRequest.endAddress
                                      .toString())),
                        ],
                      ),
                      Divider(
                        thickness: 1,
                      ),
                      Row(
                        children: [
                          Expanded(child: CustomText(data: language.TimeTxt)),
                          Expanded(
                              child: CustomText(
                                  data: widget.opportunityRequest.datetime
                                      .toString())),
                        ],
                      ),
                      Divider(
                        thickness: 1,
                      ),

                      Row(
                        children: [
                          Expanded(child: CustomText(data: language.distance)),
                          Expanded(
                              child: CustomText(
                                  data: widget.opportunityRequest.distance
                                      .toString())),
                        ],
                      ),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Row(
                      //       children: [
                      //         Icon(Ionicons.calendar,
                      //     size: 16),
                      //         SizedBox(width: 8),
                      //         Padding(
                      //             padding: EdgeInsets.only(top: 2),
                      //             child: Text(
                      //                 '${printDate(widget.opportunityRequest.datetime.toString())}',
                      //                 style: TextStyle(
                      //
                      //                     fontSize: 14))),
                      //       ],
                      //     ),

                      //     // Text('${language.lblRide} #${data.id}',
                      //     //     style: IS_DARKMODE
                      //     //         ? TextStyle(
                      //     //             color: Colors.black, fontSize: 14)
                      //     //         : boldTextStyle(size: 14)),
                      //   ],
                      // ),
                      // Divider( height: 24, thickness: 1),
                      // Row(
                      //   children: [
                      //     Column(
                      //       children: [
                      //         Icon(Icons.near_me,
                      //             color: Colors.green, size: 18),
                      //         SizedBox(height: 2),
                      //         SizedBox(
                      //           height: 34,
                      //           child: DottedLine(
                      //             direction: Axis.vertical,
                      //             lineLength: double.infinity,
                      //             lineThickness: 1,
                      //             dashLength: 2,
                      //
                      //           ),
                      //         ),
                      //         SizedBox(height: 2),
                      //         Icon(Icons.location_on,
                      //             color: Colors.red, size: 18),
                      //       ],
                      //     ),
                      //     SizedBox(width: 16),
                      //     Expanded(
                      //       child: Column(
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         children: [
                      //           SizedBox(height: 2),
                      //           Text(
                      //             widget.opportunityRequest.startAddress
                      //                 .toString(),
                      //             style: primaryTextStyle(
                      //         size: 14),
                      //             maxLines: 2,
                      //           ),
                      //           SizedBox(height: 22),
                      //           Text(
                      //               widget.opportunityRequest.endAddress
                      //                   .toString(),
                      //               style: primaryTextStyle(
                      //           size: 14),
                      //               maxLines: 2),
                      //         ],
                      //       ),
                      //     ),
                      //   ],
                      // ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
