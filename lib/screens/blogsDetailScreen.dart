import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:webview_flutter/webview_flutter.dart';

class BlogsDetailScreen extends StatefulWidget {
  final int id;
  final String title;
  final String? appBarTitle;
  const BlogsDetailScreen(
      {super.key, required this.id, required this.title, this.appBarTitle});

  @override
  State<BlogsDetailScreen> createState() => _BlogsDetailScreenState();
}

class _BlogsDetailScreenState extends State<BlogsDetailScreen> {
  // late WebViewController webViewController;

  // OpportunityModel details = OpportunityModel(id: 5000, title: "", imageURL: "");

  String htmldata = '';

  bool webpageLoaded = false;

  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    appStore.setLoading(true);
    controller.setBackgroundColor(Colors.white);
    getBlogDetailData(id: widget.id, controller: controller);

    controller.setNavigationDelegate(NavigationDelegate(
      onPageFinished: (url) {
        Future.delayed(Duration(seconds: 3), () {
          setState(() {
            appStore.setLoading(false);
          });
                  if (Theme.of(context).brightness == Brightness.dark) {
          controller.runJavaScript('''
            document.body.style.backgroundColor = '#121212';
            document.body.style.color = '#FFFFFF';
            document.querySelectorAll('*').forEach(element => {
              if (element.tagName === 'A') {
                element.style.color = '#90CAF9';
              }
              if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3' || 
                  element.tagName === 'H4' || element.tagName === 'H5' || element.tagName === 'H6') {
                element.style.color = '#FFFFFF';
              }
              if (element.tagName === 'P' || element.tagName === 'SPAN' || element.tagName === 'DIV') {
                element.style.color = '#E0E0E0';
              }
            });
          ''');
        }
        });
      },
    ));

    super.initState();
  }

  @override
  void dispose() {
    appStore.setLoading(false);
    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  void loadHtml(WebViewController controller) {
    // webViewController = controller;
    controller.loadHtmlString(htmldata);
  }

  Future getBlogDetailData(
      {required int id, required WebViewController controller}) async {
    getBlogsDetailsApi(id).then((value) {
      if (value != null) {
        controller.loadHtmlString(value);
      }
    }).onError((error, stackTrace) {
      GlobalMethods.infoToast(context, "Server error");
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: RoooAppbar(title: widget.title),
      body: Stack(
        children: [
          Column(
            children: [
              // CustomText(
              //   color: Colors.black,
              //   data: widget.title,
              //   size: 15,
              //   fontweight: FontWeight.bold,
              // ),
              Expanded(
                child: WebViewWidget(
                  controller: controller,
                ),
              ),
            ],
          ),
          Visibility(
            visible: appStore.isLoading,
            child: Observer(
              builder: (context) => loaderWidget(color: Colors.black),
            ),
          )
        ],
      ),
    );
  }
}
