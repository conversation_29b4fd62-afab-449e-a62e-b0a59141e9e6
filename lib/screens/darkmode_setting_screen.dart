import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
// ignore: unused_import
import 'package:flutter_mobx/flutter_mobx.dart';

class DarkModeSettingsScreen extends StatefulWidget {
  const DarkModeSettingsScreen({super.key});

  @override
  State<DarkModeSettingsScreen> createState() => _DarkModeSettingsScreenState();
}

class _DarkModeSettingsScreenState extends State<DarkModeSettingsScreen> {
  DarkLightTimingMode? selectedMode = DarkLightTimingMode.Empty;
  String local_saved_dark_mode_setting = DARKMODE_ALWAYS_ON;

  @override
  void initState() {
    //  : implement initState
    super.initState();
    init().then((value) {
      setState(() {});
    });
  }

  Future init() async {
  

    if (await sharedPref.getString(IS_DARK_MODE_SETTING) != null) {
      local_saved_dark_mode_setting =
          await sharedPref.getString(IS_DARK_MODE_SETTING).toString();

      if (local_saved_dark_mode_setting == DARKMODE_AUTOMATIC) {
        selectedMode = DarkLightTimingMode.Automatic;
      } else if (local_saved_dark_mode_setting == DARKMODE_ALWAYS_OFF) {
        selectedMode = DarkLightTimingMode.AlwaysOff;
      } else if (local_saved_dark_mode_setting == DARKMODE_ALWAYS_ON) {
        selectedMode = DarkLightTimingMode.AlwaysOn;
      } else if (local_saved_dark_mode_setting == DARKMODE_PHONESETTING) {
        selectedMode = DarkLightTimingMode.PhoneSetting;
      }
    } else {
      selectedMode = DarkLightTimingMode.Automatic;
    }
  }

  @override
  Widget build(BuildContext context) {
    //  : implement build

    return WillPopScope(
      onWillPop: () {
        return Future.value(!appStore.isLoading);
      },
      child: Scaffold(
        appBar: RoooAppbar(title: language.nightModeTxt),
        body: Stack(
          children: [
            Padding(
                padding: screenPadding,
                child: Column(
                  children: <Widget>[
                    ListTile(
                      onTap: () async {
                        setState(() {
                          selectedMode = DarkLightTimingMode.Automatic;
                          IS_DARK_MODE_ON = isDarkMode() ? true : false;
                        });
                        afterChangingMode(selected_mode: DARKMODE_AUTOMATIC);
                      },
                      title: Text(
                        "${language.automaticText} (${language.timeOfDayTxt})",
                        style: TextStyle(),
                      ),
                      trailing: selectedMode == DarkLightTimingMode.Automatic
                          ? Icon(
                              Icons.check,
                              color: Colors.blue,
                            )
                          : SizedBox(),
                    ),
                    Divider(
                      thickness: 1,
                    ),
                    ListTile(
                      onTap: () async {
                        setState(() {
                          selectedMode = DarkLightTimingMode.AlwaysOn;
                          IS_DARK_MODE_ON = true;
                        });
                        afterChangingMode(selected_mode: DARKMODE_ALWAYS_ON);
                      },
                      title: Text(
                        language.alwaysOnTxt,
                        style: TextStyle(),
                      ),
                      trailing: selectedMode == DarkLightTimingMode.AlwaysOn
                          ? Icon(
                              Icons.check,
                              color: Colors.blue,
                            )
                          : SizedBox(),
                    ),
                    Divider(
                      thickness: 1,
                    ),
                    ListTile(
                      onTap: () async {
                        setState(() {
                          selectedMode = DarkLightTimingMode.AlwaysOff;
                          IS_DARK_MODE_ON = false;
                        });
                        afterChangingMode(selected_mode: DARKMODE_ALWAYS_OFF);
                      },
                      title: Text(
                        language.AlwaysOffTxt,
                        style: TextStyle(),
                      ),
                      trailing: selectedMode == DarkLightTimingMode.AlwaysOff
                          ? Icon(
                              Icons.check,
                              color: Colors.blue,
                            )
                          : SizedBox(),
                    ),
                    Divider(
                      thickness: 1,
                    ),
                    ListTile(
                      onTap: () async {
                        setState(() {
                          selectedMode = DarkLightTimingMode.PhoneSetting;
                          IS_DARK_MODE_ON = false;
                        });
                        afterChangingMode(selected_mode: DARKMODE_PHONESETTING);
                      },
                      title: Text(
                        language.UsePhoneSettingsTxt,
                        style: TextStyle(),
                      ),
                      trailing: selectedMode == DarkLightTimingMode.PhoneSetting
                          ? Icon(
                              Icons.check,
                              color: Colors.blue,
                            )
                          : SizedBox(),
                    ),
                  ],
                )),
            Observer(builder: (context) {
              return appStore.isLoading ? loaderWidget() : SizedBox();
            })
          ],
        ),
      ),
    );
  }

  afterChangingMode({required String selected_mode}) async {
    setState(() {
      appStore.setLoading(true);
    });
    Future.delayed(Duration(milliseconds: 200)).then((value) async {
      await sharedPref.setBool(IS_DARK_MODE, IS_DARK_MODE_ON);
      await sharedPref.setString(IS_DARK_MODE_SETTING, selected_mode);

    }).then((value) {
      appStore.setLoading(false);

      GlobalMethods.pushAndRemoveAll(
          context: context,
          screen: RideScreen(),
          screenIdentifier: ScreenIdentifier.InitialScreen);
      // launchScreen(context, InitialScreen(), isNewTask: true);
    });
  }
}
