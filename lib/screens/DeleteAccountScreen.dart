import 'package:rooo_driver/components/rooo_appbar.dart';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';

import '../Services/AuthService.dart';
import '../main.dart';
import '../network/RestApis.dart';
import '../utils/Common.dart';
import '../utils/Constants.dart';

import '../utils/Extensions/app_common.dart';

class DeleteAccountScreen extends StatefulWidget {
  @override
  DeleteAccountScreenState createState() => DeleteAccountScreenState();
}

class DeleteAccountScreenState extends State<DeleteAccountScreen> {
  AuthServices authService = AuthServices();

  AccountDeletionStatus? accountStatus;

  @override
  void initState() {
    appStore.setLoading(true);
    super.initState();
    init();
  }

  void init() async {
    var response = await checkAccountDeletionStatus();
    if (!response.status) {
      GlobalMethods.errorToast(context, response.message);
    } else {
      accountStatus = response.data!;
      setState(() {
        appStore.setLoading(false);
      });
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: language.deleteAccount,
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
              padding: EdgeInsets.all(16), child: _getChild()),
          Observer(builder: (context) {
            return Visibility(
              visible: appStore.isLoading,
              child: loaderWidget(),
            );
          }),
        ],
      ),
    );
  }

  Future deleteAccount(BuildContext context) async {
    appStore.setLoading(true);
    await deleteUser().then((value) async {
      setState(() {
        appStore.setLoading(false);
      });

      GlobalMethods.showInfoDialogNew(
          barrierDismissible: false,
          context: context,
          onClick: () {
            Navigator.pop(context);
            Navigator.pop(context);
          },
          title: value.message.toString());
    }).catchError((error) {
      appStore.setLoading(false);
      GlobalMethods.errorToast(context, "Server error");
    });
  }

  Widget _getChild() {
    if (accountStatus == null) {
      return Center(
        child: CircularProgressIndicator(),
      );
    } else if (accountStatus!.isAlreadyRequested || accountStatus!.isRejected) {
      Color cardColor;
      if (accountStatus!.isAlreadyRequested) {
        cardColor = Colors.yellow[100]!;
      } else {
        cardColor = Colors.red[100]!;
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            color: cardColor,
            elevation: 2,
            margin: EdgeInsets.symmetric(vertical: 8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(accountStatus!.message,
                  style: TextStyle(
                      color: Colors.black)), // Ensuring text is readable
            ),
          ),
          if (accountStatus!.isRejected) ...[
            SizedBox(height: 24),
            Text("Want to request agian?", style: TextStyle()),
            SizedBox(height: 10),
            Center(
              child: AppButton(
                text: language.deleteAccount,
                onPressed: () async {
                  await deleteAccount(context);
                },
              ),
            ),
          ]
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(delete_account_instruction_text, style: primaryTextStyle()),
          SizedBox(height: 24),
          Center(
              child: AppButton(
                  text: language.deleteAccount,
                  onPressed: () async {
                    await GlobalMethods.showConfirmationDialog(
                        context: context,
                        positiveText: "Yes",
                        negativeText: "No",
                        onPositiveAction: () async {
                          await deleteAccount(context);
                        },
                        title: language.areYouSureYouWantDeleteAccount);
                  }))
        ],
      );
    }
  }
}

class AccountDeletionStatus {
  bool isAlreadyRequested;
  bool isRejected;
  String message;
  AccountDeletionStatus(
      {required this.isAlreadyRequested,
      required this.isRejected,
      required this.message});
  factory AccountDeletionStatus.fromMap(Map<String, dynamic> json) {
    return AccountDeletionStatus(
      isAlreadyRequested: json['is_already_requested'],
      isRejected: json['is_rejected'],
      message: json['message'],
    );
  }
}

class AccountDeletionResponse {
  bool status;
  String message;
  AccountDeletionStatus? data;
  AccountDeletionResponse({
    required this.status,
    required this.message,
    required this.data,
  });
  factory AccountDeletionResponse.fromMap(Map<String, dynamic> json) =>
      AccountDeletionResponse(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? null
            : AccountDeletionStatus.fromMap(json["data"]),
      );
}
