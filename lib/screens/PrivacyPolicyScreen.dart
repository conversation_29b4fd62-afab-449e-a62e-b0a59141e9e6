
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../main.dart';
import '../network/RestApis.dart';
import '../utils/Common.dart';
import '../utils/Extensions/app_common.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  @override
  PrivacyPolicyScreenState createState() => PrivacyPolicyScreenState();
}

class PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  String? url;

  bool webPageLoaded = false;

  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    await getPrivacyPolicy().then((value) {
      if (value['data']['link'] != null) {
        setState(() {
          url = value['data']['link'];
        });
        controller.setNavigationDelegate(NavigationDelegate(
          onPageFinished: (url) {
            setState(() {
              webPageLoaded = true;
            });
                  if (Theme.of(context).brightness == Brightness.dark) {
          controller.runJavaScript('''
            document.body.style.backgroundColor = '#121212';
            document.body.style.color = '#FFFFFF';
            document.querySelectorAll('*').forEach(element => {
              if (element.tagName === 'A') {
                element.style.color = '#90CAF9';
              }
              if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3' || 
                  element.tagName === 'H4' || element.tagName === 'H5' || element.tagName === 'H6') {
                element.style.color = '#FFFFFF';
              }
              if (element.tagName === 'P' || element.tagName === 'SPAN' || element.tagName === 'DIV') {
                element.style.color = '#E0E0E0';
              }
            });
          ''');
        }
          },
        ));
        controller.loadRequest(Uri.parse(url!));
      }
    }).catchError((error) {
      log("Server error");
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: getDrawerBackgroundColor(),
      appBar: RoooAppbar(
        title: language.privacyPolicy,
      ),
      body: SizedBox(
          height: MediaQuery.sizeOf(context).height,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Stack(
              children: [
                (url == null)
                    ? const SizedBox()
                    : WebViewWidget(
                        controller: controller,
                      ),
                webPageLoaded ? const SizedBox() : loaderWidget(),
              ],
            ),
          ),
        ),
    );
  }

  getDrawerBackgroundColor() {}
}
