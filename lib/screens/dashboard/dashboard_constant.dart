import 'dart:async';

import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

ValueNotifier<int> isOnline = ValueNotifier(-1);


RideModel current_ride = RideModel();
//  UserData current_rider=UserData();

late int new_ride_countdown;
 int arrived_waiting_time_countdown=60;

Timer? device_location_tracking_timer;
Timer? driver_status_at_server_timer;
// late TrackingCubit trackingCubit;

late Position driver_current_position;
