import 'package:rooo_driver/global/export/app_export.dart';

class Chat<PERSON>ounter extends StatelessWidget {
  final OnRideRequest onRideRequest;
  const ChatCounter({super.key, required this.onRideRequest});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: GlobalState.chat_count,
      builder: (context, value, child) {
        return Visibility(
          visible: GlobalState.chat_count.value
                  .any((chat) => chat.rideId == onRideRequest.id) &&
              GlobalState.chat_count.value
                      .firstWhere(
                          (element) => element.rideId == onRideRequest.id)
                      .chatCount >
                  0,
          child: Container(
              padding: EdgeInsets.all(5),
              decoration:
                  BoxDecoration(color: Colors.black, shape: BoxShape.circle),
              child: Text(
                GlobalState.chat_count.value
                    .firstWhere(
                      (element) => element.rideId == onRideRequest.id,
                      orElse: () => ChatCountModel(rideId: -1, chatCount: 0),
                    )
                    .chatCount
                    .toString(),
                style: AppTextStyles.text(color: Colors.white),
              )),
        );
      },
    );
  }
}
