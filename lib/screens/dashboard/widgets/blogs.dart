import 'package:rooo_driver/components/dashboard/blog_card.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/FAQ.dart';
import 'package:rooo_driver/screens/blogsDetailScreen.dart';

class Blogs extends StatelessWidget {
  final List<FAQ> blogs;
  const Blogs({super.key, required this.blogs});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 350,
      width: 350,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        
          // physics: NeverScrollableScrollPhysics(),
          // controller: scrollController,
          shrinkWrap: true,
          itemCount: blogs.length,
          itemBuilder: (context, index) {
            final item = blogs[index];
      
            // Return the widget for each item in the list
            return InkWell(
                onTap: () {
      
      
                  GlobalMethods.pushScreen(context: context, screen:     BlogsDetailScreen(
                        title: item.title,
                        id: item.id,
                      ), screenIdentifier: ScreenIdentifier.BlogsDetailScreen);
                  // launchScreen(
                  //     context,
                  //     BlogsDetailScreen(
                  //       title: item.title,
                  //       id: item.id,
                  //     ));
                },
                child: BlogCard(item));
          }),
    );
  }
}
