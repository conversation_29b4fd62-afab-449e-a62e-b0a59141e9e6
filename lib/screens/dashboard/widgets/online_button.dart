import 'package:action_slider/action_slider.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class OnlineButton extends StatefulWidget {
  final GlobalKey<State<StatefulWidget>> tutorial_key;
  final dynamic blocState;
  final void Function() onPress;
  const OnlineButton(
      {super.key,
      required this.blocState,
      required this.onPress,
      required this.tutorial_key});

  @override
  State<OnlineButton> createState() => _OnlineButtonState();
}

class _OnlineButtonState extends State<OnlineButton> {
  @override
  Widget build(BuildContext context) {
    if (widget.blocState is RideFlowLoadingState) {
      return LinearProgressIndicator(
        color: AppColors.greenColor,
      );
    }
    return SizedBox(
      height: 60,
      child: ActionSlider.standard(
        key: widget.tutorial_key,
        actionThreshold: .7,
        backgroundColor: AppColors.primaryMustardColr,
        toggleColor: AppColors.greenColor,
        icon: Icon(
          Icons.double_arrow,
          color: Colors.white,
        ),
        foregroundBorderRadius: BorderRadius.only(
            topRight: Radius.circular(5), bottomRight: Radius.circular(5)),
        borderWidth: 0,
        backgroundBorderRadius: BorderRadius.circular(0),
        sliderBehavior: SliderBehavior.stretch,
        child: Center(
          child: Text(
            'Go Online',
            style: AppTextStyles.header(),
          ),
        ),
        action: (controller) async {
          widget.onPress();
          if (widget.blocState is OnlineState) {
            controller.reset();
          }
        },
      ),
    );
  }
}
