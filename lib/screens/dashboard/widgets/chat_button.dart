import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/screens/ChatScreen.dart';
import 'package:rooo_driver/screens/dashboard/widgets/chat_count.dart';

class ChatButton extends StatelessWidget {
  final OnRideRequest onRideRequest;
  final void Function()? onRiderPlayerIdMissing;
  const ChatButton({
    super.key,
    required this.onRideRequest,
    this.onRiderPlayerIdMissing,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (onRiderPlayerIdMissing != null &&
            onRideRequest.riderPlayerId == null) {
          onRiderPlayerIdMissing!();
        } else {
          List<ChatCountModel> list = GlobalState.chat_count.value
              .where((element) => element.rideId == onRideRequest.id)
              .toList();

          if (list.isNotEmpty) {
            list[0].chatCount - 1;
          }
         
          GlobalState.chat_count.notifyListeners();
          

          bool? result = await GlobalMethods.pushScreen(
              context: context,
              screen: ChatScreen(
                receiverFirestoreId: onRideRequest.riderFirestoreId??"",
                rideId: onRideRequest.id!,
                uid: onRideRequest.riderUid!,
                // driverUid: onRideRequest.driverUid!,
                // driverName: onRideRequest!.driverName!,
                // driverPlayerId: GlobalState.playerId,
                playerId: onRideRequest.riderPlayerId!,
                riderProfileImage: onRideRequest.riderProfileImage!,
                riderName: onRideRequest.riderName!,
              ),
              screenIdentifier: ScreenIdentifier.chatScreen);
         

          if (result == null) {
            GlobalState.chat_count.value
                    .firstWhere(
                        (element) => element.rideId == onRideRequest.id!)
                    .chatCount -
                1;
            GlobalState.chat_count.notifyListeners();
            ;
          }

        }
      },
      child: Column(
        children: [
          Stack(
            alignment: Alignment.topRight,
            children: [
              Container(
                padding: screenPadding * .8,
                decoration: BoxDecoration(
                    color: AppColors.primaryColor(context),
                    shape: BoxShape.circle),
                child: Icon(
                  Icons.chat,
                  size: 22,
                ),
              ),
              ChatCounter(onRideRequest: onRideRequest),
            ],
          ),
          // Text(language.chat, style: secondaryTextStyle()),
        ],
      ),
    );
  }
}
