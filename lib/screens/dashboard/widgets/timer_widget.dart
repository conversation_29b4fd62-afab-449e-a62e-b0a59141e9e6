
import 'package:rooo_driver/global/export/app_export.dart';

class TimerWidget extends StatelessWidget {

  final ValueNotifier<int> value;
  const TimerWidget({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: AppColors.whiteColor(context),
          borderRadius: BorderRadius.circular(defaultRadius)),
      padding: EdgeInsets.all(6),

      child: ValueListenableBuilder(
        valueListenable:value,
        builder: (context, value, child) {
          return Text(
            value.toString(),
            style: boldTextStyle(color: AppColors.blackColor(context)),
          );
        },
      ),
      // child: SlideCountdown(
      //   streamDuration:
      //       _streamDuration,
      //   onChanged: (value) {
      //     if (value.inSeconds ==
      //         0) {
      //       timeOutNewRide(
      //           new_ride_id: widget
      //               .current_ride
      //               .rideModel!
      //               .id!);
      //     }
      //   },
      //   // onDone: () {

      //   // },
      //   // duration: Duration(
      //   //     seconds:
      //   //         new_ride_countdown),
      // )

      //     ValueListenableBuilder<
      //         int>(
      //   valueListenable:
      //       new_ride_countdown_timer_value,
      //   builder:
      //       (context, state, _) {
      //     if (state == -1) {
      //       return SizedBox(
      //         height: 20,
      //         width: 20,
      //         child:
      //             CircularProgressIndicator(
      //           color:
      //               getBlackColor(),
      //         ),
      //       );
      //     } else {
      //       return Countdown(
      //         controller: count_controller,
      //         seconds: new_ride_countdown_timer_value.value*1000,
      //         build: (BuildContext
      //                     context,
      //                 double
      //                     time) =>
      //             Text(time
      //                 .toString()),
      //         interval: Duration(
      //             milliseconds:
      //                 100),
      //         onFinished: () {
      //         timeOutNewRide(new_ride_id: widget.current_ride.rideModel!.id!);

      //         },
      //       );

      //       //  Text(
      //       //   new_ride_countdown_timer_value
      //       //       .value
      //       //       .toString(),
      //       //   style: boldTextStyle(
      //       //       color:
      //       //           getBlackColor()),
      //       // );
      //     }
      //   },
      // )
      // child: Text(
      //     new_ride_countdown_value
      //         .toString(),
      //     style: boldTextStyle(
      //         color:
      //             getWhiteColor())),
    );
  }
}
