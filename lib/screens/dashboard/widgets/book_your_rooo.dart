// import 'package:rooo_driver/components/custom_text.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/screens/book_your_rooo_steps_screen.dart';
// import 'package:rooo_driver/utils/Common.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:flutter/material.dart';

// class BookYourRooo extends StatelessWidget {
//   const BookYourRooo({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: () {
//         launchScreen(context, BookYourRoooStepsScreen());
//       },
//       child: Padding(
//         padding: const EdgeInsets.all(15),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             commonCachedNetworkImage(
//               'https://www.staffingattiffanies.com/wp-content/uploads/2021/01/driver-opening-car-door-for-young-businesswoman-768x512.jpg',
//             ),
//             height15,
//             CustomText(
//               data: language.BookYourRoooIn3EasyStepsTxt,
//               fontweight: FontWeight.bold,
//               size: 20,
//             ),
//             height5,
//             CustomText(
//               data: language.NowBookingYourRoooIsEasyTxt,
//               size: 18,
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }
