import 'package:rooo_driver/components/AlertScreen.dart';
import 'package:flutter/material.dart';

class SOSbutton extends StatefulWidget {
  final int ride_id;
  final int region_id;

  final GlobalKey<State<StatefulWidget>> tutorial_key;

  const SOSbutton(
      {super.key,
      required this.ride_id,
      required this.region_id,
      required this.tutorial_key});

  @override
  State<SOSbutton> createState() => _SOSbuttonState();
}

class _SOSbuttonState extends State<SOSbutton> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(5),
      child: ElevatedButton(
          key: widget.tutorial_key,
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) {
                return Center(
                  child: SizedBox(
                    height: MediaQuery.sizeOf(context).height*0.66,
                    width: MediaQuery.sizeOf(context).width*0.66,
                    child: AlertScreen(
                        rideId: widget.ride_id, regionId: widget.region_id),
                  ),
                );
              },
            );
          },
          child: Align(
              child: Icon(
            Icons.security_outlined,
            color: Colors.white,
          )
              // Text(
              //   language.sos,
              //   textAlign: TextAlign.center,
              //   style: TextStyle(
              //     fontSize: 20,
              //     color: Colors.white,
              //   ),
              // ),
              ),
          style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(1000)),
              minimumSize: Size(30, 30),
              elevation: 4,
              padding: EdgeInsets.all(8),
              backgroundColor: Colors.black // <-- Button color
              )),
    );
  }
}
