import 'package:lottie/lottie.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class OfflineBox extends StatelessWidget {
  final GlobalKey<State<StatefulWidget>> tutorial_key;
  final PanelController controller;
  const OfflineBox({super.key, required this.controller, required this.tutorial_key});

  @override
  Widget build(BuildContext context) {
    ValueNotifier<bool> isOpen = ValueNotifier(false);

    return inkWellWidget(
      onTap: () {
        if (isOpen.value == false) {
          isOpen.value = true;

          controller.open();
        } else {
          isOpen.value = false;

          controller.close();
        }
      },

      child: Container(
        key: tutorial_key,
          alignment: Alignment.center,
          color: Colors.black,
          height: 60,
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
        ValueListenableBuilder<bool>(
                  valueListenable: isOpen,
                  builder: (context, value, child) {
                    return Transform.flip(
                        flipY: value,
                        child: Lottie.asset(
                            "assets/lottie/upward_arrow.json"));
                  }),
              Text(
                "You are offline",
                style: AppTextStyles.title(color: Colors.red),
              ),
      
              ValueListenableBuilder<bool>(
                  valueListenable: isOpen,
                  builder: (context, value, child) {
                    return Transform.flip(
                        flipY: value,
                        child: Lottie.asset(
                            "assets/lottie/upward_arrow.json"));
                  })
      
              // Spacer(),
            ],
          )
      
         
          ),
    );
  }
}
