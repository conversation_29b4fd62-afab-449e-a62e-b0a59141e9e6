import 'package:lottie/lottie.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class OnlineBox extends StatelessWidget {
  final PanelController controller;

  const OnlineBox({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    ValueNotifier<bool> isOpen = ValueNotifier(false);

    return Column(
      children: [
        InkWell(
          onTap: () {
            if (isOpen.value == false) {
              isOpen.value = true;

              controller.open();
            } else {
              isOpen.value = false;

              controller.close();
            }
          },
          child: Container(
              alignment: Alignment.center,
              color: Colors.black,
              height: 60,
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ValueListenableBuilder<bool>(
                      valueListenable: isOpen,
                      builder: (context, value, child) {
                        return Transform.flip(
                            flipY: value,
                            child: Lottie.asset(
                                "assets/lottie/upward_arrow.json"));
                      }),
                  Text(
                    "You are online",
                    style: AppTextStyles.title(color: AppColors.greenColor),
                  ),
                  ValueListenableBuilder<bool>(
                      valueListenable: isOpen,
                      builder: (context, value, child) {
                        return Transform.flip(
                            flipY: value,
                            child: Lottie.asset(
                                "assets/lottie/upward_arrow.json"));
                      }),
                  // Spacer(),
                ],
              )),
        ),
     
      ],
    );
    
  }
}
