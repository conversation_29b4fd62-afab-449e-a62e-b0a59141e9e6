import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:flutter/material.dart';

class CurrentLocationButton extends StatelessWidget {
  final void Function()? animate_map;
  const CurrentLocationButton({super.key, required this.animate_map});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ElevatedButton(
        onPressed: animate_map,
        child: Icon(
          Icons.gps_fixed,
          color: AppColors.blackColor(context),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.whiteColor(context),
          shape: CircleBorder(),
          elevation: 4,
          padding: EdgeInsets.all(8),
          // <-- Button color
        ),
      ),
    );
  }
}
