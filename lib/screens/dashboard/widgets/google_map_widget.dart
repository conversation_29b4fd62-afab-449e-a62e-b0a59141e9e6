// import 'dart:async';

// import 'package:custom_info_window/custom_info_window.dart';
// import 'package:flutter/material.dart';
// // import 'package:google_map_marker_animation/widgets/animarker.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:rooo_driver/global/export/app_export.dart';

// class GoogleMapWidget extends StatelessWidget {
//   final CustomInfoWindowController? customInfoWindowController;
//   final bool state;
//   final bool is_polyline_created;
//   final Completer<GoogleMapController> completer;
//   final Set<Polyline> polyLines;
//   final LatLng initialCameraPosition;
//   final void Function(LatLng)? onTap;
//   final void Function(CameraPosition)? onCameraMove;

//   final Map<MarkerId, Marker> animatedMarkers;
//   final Set<Marker> staticMarker;

//   const GoogleMapWidget(
//       {super.key,
//       required this.state,
//       required this.animatedMarkers,
//       required this.polyLines,
//       required this.completer,
//       required this.is_polyline_created,
//       required this.initialCameraPosition,
//       required this.staticMarker,
//       this.onTap,
//       this.onCameraMove,
//       this.customInfoWindowController});

//   @override
//   Widget build(BuildContext context) {
//     onMapCreated(GoogleMapController controller) {
//       if (customInfoWindowController != null) {
//         customInfoWindowController!.googleMapController = controller;
//       }

//       completer.complete(controller);
//     }

//     return Stack(
//       alignment: Alignment.topCenter,
//       children: [
//         // Animarker(
//         //   shouldAnimateCamera: false,
//         //   useRotation: false,
//         //   duration: Duration(milliseconds: 1000),
//         //   mapId: completer.future.then((value) => value.mapId),
//         //   markers: animatedMarkers.values.toSet(),
//         //   child: GoogleMap(
//         //     onTap: onTap,
//         //     onCameraMove: onCameraMove,
//         //     markers: staticMarker,
//         //     zoomControlsEnabled: false,
//         //     compassEnabled: false,
//         //     myLocationEnabled: false,
//         //     onMapCreated: onMapCreated,
//         //     initialCameraPosition:
//         //         CameraPosition(target: initialCameraPosition, zoom: 15),
//         //     mapType: MapType.normal,
//         //     polylines: polyLines,
//         //   ),
//         // ),
//         state ? LinearProgressIndicator() : SizedBox()
//       ],
//     );
//   }
// }
