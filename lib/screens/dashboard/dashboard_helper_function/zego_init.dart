// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';
// import 'package:zego_uikit_signaling_plugin/zego_uikit_signaling_plugin.dart';

// class Zegoinit {
//   zegoInitialization() {
//     ZegoUIKitPrebuiltCallInvitationService()
//         .init(
//           notificationConfig: ZegoCallInvitationNotificationConfig(
//               androidNotificationConfig: ZegoCallAndroidNotificationConfig(
//             channelName: 'Call Invitation',
//             channelID: 'Call Invitation',
//             sound: 'zego_in.mp3',
//           )),
//           appID: APP_ID,
//           appSign: APP_SIGN,
//           userID:
//               // "abc",
//               sharedPref.getInt(USER_ID)?.toString() ?? '',
//           userName:
//               // "abc",
//               sharedPref.getString(FIRST_NAME) ?? '',
//           plugins: [
//             ZegoUIKitSignalingPlugin(),
//           ],
//           ringtoneConfig: ZegoCallRingtoneConfig(
//             incomingCallPath: "audio/call_in.mp3",
//             outgoingCallPath: "audio/call_out.mp3",
//           ),
//         )
//         .then((value) {})
//         .onError((error, stackTrace) {});

//     ZIMAppConfig appConfig = ZIMAppConfig();
//     appConfig.appID = APP_ID;
//     appConfig.appSign = APP_SIGN;

//     ZIM.create(appConfig);

//     // userID must be within 32 bytes, and can only contain letters, numbers, and the following special characters: '~', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '`', ';', '’', ',', '.', '<', '>', '/', '\'.
// // userName must be within 64 bytes, no special characters limited.
//     ZIMLoginConfig loginInfo = ZIMLoginConfig();
//     loginInfo.userName = sharedPref.getString(FIRST_NAME)?.toString() ?? '';
//     ZIMUserInfo userInfo = ZIMUserInfo();
//     userInfo.userID =
//         // "abc";
//         sharedPref.getInt(USER_ID)?.toString() ?? "";
//     //Fill in a String type value.
//     userInfo.userName =
//         // "abc";
//         sharedPref.getString(FIRST_NAME) ?? ''; //Fill in a String type value.

//     ZIM.getInstance()?.login(userInfo.userID, loginInfo).then((value) {
//       //This will be triggered when login successful.
//     }).catchError((onError) {
//       switch (onError.runtimeType) {
//         //This will be triggered when login failed.
//         case Exception:
//           log(onError.code); //Return the error code when login failed.
//           log(onError.message!); // Return the error indo when login failed.
//           break;
//         default:
//       }
//     });
//   }
// }
