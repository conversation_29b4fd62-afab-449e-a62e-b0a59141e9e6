import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:rooo_driver/global/models/current_ride_response_model.dart';
import 'package:rooo_driver/screens/dashboard/dashboard_constant.dart';
import 'package:geocoding/geocoding.dart';
import 'package:http/http.dart' as http;

import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/model/LoginResponse.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/model/homepage_response_model.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart';

class Drepo {
  Future<HomePageModel> getHomePageDataApi() async {
    return HomePageModel.fromJson(await handleResponse(
        await buildHttpResponse('get-driver-dashboard',
            method: HttpMethod.GET)));
  }

  Future<LoginResponse> updateDriverOnlineUpdateStatusApi(
      int status, bool driverStatusChangeCase) async {
    Map request;

    request = {
      "status": "active",
    };

    if (driverStatusChangeCase) {
      request = {
        "status": "active",
        "is_online": status,
      };
    }

    return LoginResponse.fromJson(await handleResponse(await buildHttpResponse(
        'update-user-status',
        method: HttpMethod.POST,
        request: request)));
  }

  Future<UserDetailModel> getUserDetailApi({int? userId}) async {
    return UserDetailModel.fromJson(await handleResponse(
        await buildHttpResponse('user-detail?id=$userId',
            method: HttpMethod.GET)));
  }

  // Future<CurrentRequestModel> getCurrentRideRequestApi() async {
  //   return CurrentRequestModel.fromJson(await handleResponse(
  //       await buildHttpResponse('current-riderequest',
  //           method: HttpMethod.GET)));
  // }
  Future<CurrentRideResponseModel> getCurrentRideRequest() async {
    return CurrentRideResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('current-riderequest',
            method: HttpMethod.GET)));
  }

  Future<StatusMessageModel> acceptDeclineNewrideApi({required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(await buildHttpResponse(
        'riderequest-respond',
        method: HttpMethod.POST,
        request: request)));
  }

  Future<Position> getPositionPosition() async {
    return await Geolocator.getCurrentPosition();
  }

  Future<LoginResponse> updateDriverPositionApi(Map request) async {
    return LoginResponse.fromJson(await handleResponse(await buildHttpResponse(
        'update-user-status',
        method: HttpMethod.POST,
        request: request)));
  }

  Future<StatusMessageModel> change_ride_status_api(
      {required Map request, int? rideId}) async {
    return StatusMessageModel.fromJson(await handleResponse(await buildHttpResponse(
        'riderequest-update/$rideId',
        method: HttpMethod.POST,
        request: request)));
  }

  Future notify_admin_api({required int rideId}) async {
    Map request = {"rideId": rideId};
    return await handleResponse(await buildHttpResponse(
        'arrived-admin-notification',
        method: HttpMethod.POST,
        request: request));
  }

  Future<LoginResponse> inprogress_waiting_time_api(Map request) async {
    return LoginResponse.fromJson(await handleResponse(await buildHttpResponse(
        'ride-waiting-time',
        method: HttpMethod.POST,
        request: request)));
  }

  Future<Map<String, dynamic>> upload4imagesApi({
    required int ride_request_id,
    required String type,
    required File front_image,
    required File right_image,
    required File back_image,
    required File left_image,
  }) async {
    MultipartRequest multiPartRequest =
        await getMultiPartRequest('save-riderequest-photo');

    multiPartRequest.fields['ride_request_id'] = ride_request_id.toString();
    multiPartRequest.fields['type'] = type;

    multiPartRequest.files
        .add(await MultipartFile.fromPath('left_image', left_image.path));

    multiPartRequest.files
        .add(await MultipartFile.fromPath('front_image', front_image.path));

    multiPartRequest.files
        .add(await MultipartFile.fromPath('right_image', right_image.path));

    multiPartRequest.files
        .add(await MultipartFile.fromPath('back_image', back_image.path));

    multiPartRequest.headers.addAll(buildHeaderTokens());

    log('nct-> uploading');

    var t = await multiPartRequest.send();

    var h = await http.Response.fromStream(t);
    return jsonDecode(h.body);
  }

  Future<StatusMessageModel> complete_ride_api({required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(await buildHttpResponse(
        'complete-riderequest',
        method: HttpMethod.POST,
        request: request)));
  }

  Future<String> get_user_address() async {
    List<Placemark> placemarks = await placemarkFromCoordinates(
        driver_current_position.latitude, driver_current_position.longitude);
    Placemark place = placemarks[0];
    return '${place.street},${place.subLocality},${place.thoroughfare},${place.locality}';
  }
}
