// import 'package:rooo_driver/model/RideHistoryDetailsModel.dart';
// import 'package:rooo_driver/global/models/UserDetailModel.dart';
// import 'package:rooo_driver/global/models/ride_model.dart';

// class CurrentRideModel {
//   int? id;
//   String? displayName;
//   String? email;
//   String? username;
//   String? userType;
//   String? profileImage;
//   String? status;
//   String? latitude;
//   String? longitude;
//   OnRideRequest? ride;
//   UserData? rider;
//   Payment? payment;
//   int? waiting_for_rider_time;

//   CurrentRideModel({
//     this.waiting_for_rider_time,
//     this.id,
//     this.displayName,
//     this.email,
//     this.username,
//     this.userType,
//     this.profileImage,
//     this.status,
//     this.latitude,
//     this.longitude,
//     this.ride,
//     this.rider,
//     this.payment,
//   });
// }
