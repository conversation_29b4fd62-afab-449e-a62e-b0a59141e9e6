// import 'dart:async';

// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:rooo_driver/screens/dashboard/dashboard_constant.dart';
// import 'package:rooo_driver/screens/dashboard/dashboard_repo.dart';

// abstract class TrackingState  {}

// class TrackingInitialState extends TrackingState {}
// class TrackingErrorState extends TrackingState {}



// class DriverUpdatedState extends TrackingState {
//   final Position? position;

//   DriverUpdatedState({required this.position});
// }

// class TrackingCubit extends Cubit<TrackingState> {
//   Drepo drepo = Drepo();

//   TrackingCubit() : super(TrackingInitialState());

//   Future start_device_tracking() async {
//     // GlobalMethods.infoToast(context,  "start Device tracking");

//     await get_device_current_location();

//     if (device_location_tracking_timer == null) {
//       device_location_tracking_timer =
//           Timer.periodic(Duration(seconds: 5), (timer) async {
//         await get_device_current_location();
//       });
//     }
//   }

//   Future get_device_current_location() async {
//     await drepo.getPositionPosition().then((value) {
//       // driver_current_position=null;  
//       driver_current_position = value;
//       // GlobalMethods.infoToast(context,  "Updated new location");

//     emit(
//         DriverUpdatedState(position: driver_current_position!));
//     });
//   }
// }
