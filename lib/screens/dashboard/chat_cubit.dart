// import 'package:flutter_bloc/flutter_bloc.dart';

// abstract class ChatState{

// }
// class ChatInitState extends ChatState{

// }
// class ChatIncrementState extends ChatState{
//   final int count;

//   ChatIncrementState({required this.count});
  
// }







// class ChatCubit extends Cubit<ChatState> {
//   ChatCubit() : super(ChatInitState());

//   chat_increment({required int count}){
//     emit(ChatIncrementState(count: count));
//   }
// }