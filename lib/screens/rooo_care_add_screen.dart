// import 'package:rooo_driver/components/CreateTabScreen.dart';
// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/components/custom_text.dart';
// import 'package:rooo_driver/global/constants/constants.dart';
// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/global/widgets/app_button.dart';
// import 'package:rooo_driver/global/widgets/ios_padding.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/network/RestApis.dart';
// import 'package:rooo_driver/utils/Common.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/AppButtonWidget.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:rooo_driver/utils/Extensions/app_textfield.dart';
// import 'package:flutter/material.dart';

// class RoooCareAddScreen extends StatefulWidget {
//   final String? message;
//   final String? subject;
//   final int? id;
//   const RoooCareAddScreen({super.key, this.message, this.subject, this.id});

//   @override
//   State<RoooCareAddScreen> createState() => _RoooCareAddScreenState();
// }

// class _RoooCareAddScreenState extends State<RoooCareAddScreen> {
//   roooCareAdd() {
//     if (title_controller.text == "" || message_controller.text == "") {
//       GlobalMethods.infoToast(context,  "Please enter valid details");
//     } else {
//       Map<String, dynamic> req = {
//         "subject": title_controller.text,
//         "message": message_controller.text
//       };

//       roooCareAddApi(req: req
//               // status: widget.status,
//               // driverId: sharedPref.getInt(USER_ID)
//               )
//           .then((value) {
//         GlobalMethods.infoToast(context,  value.message.toString());
//         title_controller.text = "";
//         message_controller.text = '';
//         Navigator.pop(context, true);
//         appStore.setLoading(false);

//         setState(() {});
//       }).catchError((error) {
//         appStore.setLoading(false);
//         log("Server error");
//       });
//     }
//   }

//   roooCareUpdate(int id) {
//     if (title_controller.text == "" || message_controller.text == "") {
//      GlobalMethods.infoToast(context,  language.pleaseEnterMessage);
//     } else {
//       Map<String, dynamic> req = {
//         "subject": title_controller.text,
//         "message": message_controller.text
//       };

//       roooCareUpdateApi(req: req, id: id
//               // status: widget.status,
//               // driverId: sharedPref.getInt(USER_ID)
//               )
//           .then((value) {
//         GlobalMethods.infoToast(context,  value.message.toString());
//         title_controller.text = "";
//         message_controller.text = '';
//         launchScreen(
//             context,
//             CreateTabScreen(
//               isCareScreen: true,
//             ),
//             isNewTask: true);
//         appStore.setLoading(false);

//         setState(() {});
//       }).catchError((error) {
//         appStore.setLoading(false);
//         log("Server error");
//       });
//     }
//   }

//   final title_controller = TextEditingController();
//   final message_controller = TextEditingController();

//   @override
//   void initState() {
//     title_controller.text = widget.subject ?? "";
//     message_controller.text = widget.message ?? "";

//     //  : implement initState
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: RoooAppbar(title: language.AddTxt),
//       bottomNavigationBar: IosPadding(
//           child: AppButton(
//               text: "Send",
//               onPressed: () {
//                 roooCareAdd();
//               })

//           // AppButtonWidget(
//           //   text: "Send",
//           //   onTap: () {
//           //     roooCareAdd();
//           //   },
//           // ),
//           ),
//       body: Padding(
//         padding: screenPadding,
//         child: Column(
//           children: [
//             AppTextField(
//               textFieldType: TextFieldType.NAME,
//               controller: title_controller,
//               decoration:
//                   inputDecoration(context, hint: language.pleaseEnterSubject),
//             ),
//             height10,
//             AppTextField(
//               minLines: 4,
//               textFieldType: TextFieldType.NAME,
//               controller: message_controller,
//               maxLines: 6,
//               decoration: inputDecoration(context, hint: language.messageTxt),
//             ),
//             height20,
//             CustomText(
//                 data:
//           ],
//         ),
//       ),
//     );
//   }
// }
