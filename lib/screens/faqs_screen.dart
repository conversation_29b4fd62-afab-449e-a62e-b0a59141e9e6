
// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/components/faq_card.dart';
// import 'package:rooo_driver/features/help/models/help_model.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/model/FAQ.dart';
// import 'package:rooo_driver/screens/faqDetailsScreen.dart';
// import 'package:rooo_driver/utils/Colors.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_mobx/flutter_mobx.dart';

// import '../network/RestApis.dart';
// import '../utils/Common.dart';
// import '../utils/Extensions/app_common.dart';

// class FAQsScreen extends StatefulWidget {
//   @override
//   FAQsScreenState createState() => FAQsScreenState();
// }

// class FAQsScreenState extends State<FAQsScreen> {
//   List<FAQ> _faqs = [];
//   String ? api_message;

//   @override
//   void initState() {
//     appStore.setLoading(true);
//     getData();
//     super.initState();
//   }

//   @override
//   void dispose() {
//     appStore.setLoading(false);
//     super.dispose();
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   Future<void> getData() async {
//     var result = await getFaqListApi();
//     setState(() {
//       _faqs = result.data??[];
//       api_message=result.message;
//     });
//     appStore.setLoading(false);
//     }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         
//       appBar: RoooAppbar(
//         title: language.faqsTxt,
//       ),
//       body: Stack(
//         children: [
//           ListView.separated(
//             padding: EdgeInsets.all(screenPaddingValue),
//             itemBuilder: (context, index) {
//               WebviewDataModel item = _faqs[index];
//               return WebViewCard(
//                 onTap: () {
//                   launchScreen(
//                     context,
//                     FaqDetailsScreen(
//                       description: item.description??"",
//                       title: item.title,
               
//                     ),
//                   );
//                 },
//                 data: item,
//               );
//             },
//             separatorBuilder: (context, index) => SizedBox(
//               height: 10,
//             ),
//             itemCount: _faqs.length,
//           ),
//        Observer(
//             builder: (context) {
//               return Visibility(
//                 visible: appStore.isLoading,
//                 child: loaderWidget(),
//               );
//             },
//           ), Observer(
//             builder: (context) {
//               return Visibility(
//                 visible: _faqs.isEmpty&&!appStore.isLoading,
//                 child: emptyWidget(message: api_message.toString()),
//               );
//             },
//           )
//         ],
//       ),
//     );
//   }
// }
