
// import 'package:file_picker/file_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_pagination/firebase_pagination.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/global/widgets/ios_padding.dart';

import '../Services/ChatMessagesService.dart';
import '../Services/UserServices.dart';
import '../model/ChatMessageModel.dart';
import '../model/FileModel.dart';
import 'ChatItemWidget.dart';

class ChatScreen extends StatefulWidget {
  // final UserData? userData;
  // final int rideId;
  final int rideId;
  final String uid;
    final String receiverFirestoreId;

  // final String driverUid;
  // final String driverPlayerId;
  final String playerId;

  final String riderName;
  

  // final String driverName;
  final String riderProfileImage;

  ChatScreen(
      {required this.rideId,
      required this.uid,
            required this.receiverFirestoreId,

      // required this.driverUid,
      // required this.driverName,
      // required this.driverPlayerId,
      required this.playerId,
      required this.riderName,
      required this.riderProfileImage}
      // this.userData,
      // required this.rideId,
      );

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {

 String driverFirestoreId=""; 

  var messageCont = TextEditingController();
  var messageFocus = FocusNode();
  bool isMe = false;

  @override
  void initState()  {
    GlobalState.chatRideId = widget.rideId;
    super.initState();
    init();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    sharedPref.remove("CURRENT_SCREEN");
    GlobalState.chatRideId = null;
    super.dispose();
  }

  // UserData sender = UserData(
  //   username: sharedPref.getString(USER_NAME),
  //   uid: widget.driverUid,
  //   playerId: sharedPref.getString(PLAYER_ID),
  // );

  init() async {
    // id = widget.driverUid;
       driverFirestoreId=await sharedPref.getString(FIRESTORE_ID)??"";

    mIsEnterKey = sharedPref.getBool(IS_ENTER_KEY).validate();
    mSelectedImage = sharedPref.getString(SELECTED_WALLPAPER).validate();

    chatMessageService = ChatMessageService();
    chatMessageService.setUnReadStatusToTrue(
        senderFirestoreId: driverFirestoreId,
        receiverFirestore: widget.receiverFirestoreId,
        rideId: widget.rideId // senderId: sender.uid!,
        // receiverId: GlobalState.current_ride!.value.rider!.uid!,
        // rideId: GlobalState.current_ride!.value.onRideRequest!.id!,
        );
  var list=  GlobalState.chat_count.value
        .where((element) => element.rideId == widget.rideId).toList();
  

        if(list.isNotEmpty){
          list[0].chatCount=0;
        }

    GlobalState.chat_count.notifyListeners();
    ;

    setState(() {});
  }

  sendMessage({FilePickerResult? result}) async {
    if (result == null) {
      if (messageCont.text.trim().isEmpty) {
        messageFocus.requestFocus();
        return;
      }
    }
    ChatMessageModel data = ChatMessageModel();
    data.receiverFirestoreId = widget.receiverFirestoreId;
    data.senderFirestoreId = driverFirestoreId;
    data.message = messageCont.text;
    data.isMessageRead = false;
    data.createdAt = DateTime.now().millisecondsSinceEpoch;
    data.rideId = widget.rideId;

    // if (GlobalState.current_ride!.value.rider!.uid ==
    //     sharedPref.getString(UID)) {
    //   //
    // }
    if (result != null) {
      if (result.files.single.path!.isNotEmpty) {
        data.messageType = MessageType.IMAGE.name;
      } else {
        data.messageType = MessageType.TEXT.name;
      }
    } else {
      data.messageType = MessageType.TEXT.name;
    }

    notificationService
        .sendPushNotifications(
          receiverPlayerId: widget.playerId,
          title: sharedPref.getString(FIRST_NAME)!,
          content: messageCont.text,
          rideId: widget.rideId.toString(),
          playerId: widget.playerId,
          riderUid: widget.uid,
          profileImage: widget.riderProfileImage,
          riderName: widget.riderName,
        )
        .catchError(log);
    messageCont.clear();
    setState(() {});
    return await chatMessageService.addMessage(data).then((value) async {
      if (result != null) {
        FileModel fileModel = FileModel();
        fileModel.id = value.id;
        fileModel.file = File(result.files.single.path!);
        fileList.add(fileModel);

        setState(() {});
      }

      // await chatMessageService
      //     .addMessageToDb(
      //   value,
      //   data,
      //   sender,
      //   widget.userData,
      // )
      //     .then((value) {
      //   //
      // });

      // userService.fireStore
      //     .collection(USER_COLLECTION)
      //     .doc(sharedPref.getInt(USER_ID).toString())
      //     .collection(CONTACT_COLLECTION)
      //     .doc(widget.uid)
      //     .update({
      //   'lastMessageTime': DateTime.now().millisecondsSinceEpoch
      // }).catchError((e) {
      //   log(e);
      // });
      // userService.fireStore
      //     .collection(USER_COLLECTION)
      //     .doc(widget.uid)
      //     .collection(CONTACT_COLLECTION)
      //     .doc(sharedPref.getInt(USER_ID).toString())
      //     .update({
      //   'lastMessageTime': DateTime.now().millisecondsSinceEpoch
      // }).catchError((e) {
      //   log(e);
      // });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 2,
        automaticallyImplyLeading: false,
        title: StreamBuilder<UserData>(
          stream: UserService().singleUser(widget.uid),
          builder: (context, snap) {
            if (snap.hasData) {
              return Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      child: Icon(Icons.arrow_back, color: Colors.white),
                    ),
                  ),
                  SizedBox(width: 10),
                  CircleAvatar(
                      backgroundImage: NetworkImage(widget.riderProfileImage),
                      minRadius: 20),
                  SizedBox(width: 10),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: Text(widget.riderName,
                        style: TextStyle(color: Colors.white)),
                  ),
                ],
              );
            }
            return snapWidgetHelper(snap, loadingWidget: Offstage());
          },
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(bottom: 120),
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: FirestorePagination(
                reverse: true,
                isLive: true,
                padding: EdgeInsets.only(left: 8, top: 8, right: 8, bottom: 0),
                physics: BouncingScrollPhysics(),
                query: chatMessageService.chatMessagesWithPagination(
                  currentUserId: driverFirestoreId,
                  receiverUserId: widget.receiverFirestoreId,
                  rideId: widget.rideId,
                  // receiverUserId: GlobalState.current_ride!.value.rider!.uid!,
                  // rideId: GlobalState.current_ride!.value.onRideRequest!.id!,
                ),
                limit: PER_PAGE_CHAT_COUNT,
                shrinkWrap: true,
                onEmpty: Offstage(),
                viewType: ViewType.list,
                itemBuilder: (context, snap, index) {
                  ChatMessageModel data = ChatMessageModel.fromJson(
                      snap[index].data() as Map<String, dynamic>);
                  data.isMe = data.senderFirestoreId == driverFirestoreId;
                  return ChatItemWidget(data: data);
                },
              ),
            ),
            Positioned(
              bottom: 16,
              left: 10,
              right: 10,
              child: IosPadding(
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.whiteColor(context),
                    borderRadius: radius(),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: messageCont,
                          cursorColor: AppColors.blackColor(context),
                          focusNode: messageFocus,
                          textCapitalization: TextCapitalization.sentences,
                          keyboardType: TextInputType.multiline,
                          minLines: 1,
                          textInputAction: mIsEnterKey
                              ? TextInputAction.send
                              : TextInputAction.newline,
                          onSubmitted: (s) {
                            sendMessage();
                          },
                          maxLines: 5,
                        ),
                      ),
                      width20,
                      inkWellWidget(
                        child: Icon(Icons.send,
                            color: AppColors.blackColor(context), size: 25),
                        onTap: () {
                          sendMessage();
                        },
                      )
                    ],
                  ),
                  width: MediaQuery.of(context).size.width,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
