import 'package:rooo_driver/components/ReferralOfferCard.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';

class ReferralOfferScreen extends StatefulWidget {
  const ReferralOfferScreen({super.key});

  State<ReferralOfferScreen> createState() => _ReferralOfferScreenState();
}

class _ReferralOfferScreenState extends State<ReferralOfferScreen> {
  List<RefferalOfferModel> _refferalDataList = [];
  // final FlutterContactPicker _contactPicker = new FlutterContactPicker();

  void getReferralOffersData() async {
    await getReferralOffersApi(offer_type: "offer").then((value) {
      appStore.setLoading(false);

      _refferalDataList = value.data ?? [];

      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  @override
  void initState() {
    getReferralOffersData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        itemBuilder: (context, index) {
          return ReferralOfferCard(refferalOfferData: _refferalDataList[index]);
        },
        separatorBuilder: (context, index) {
          return height10;
        },
        itemCount: _refferalDataList.length);
  }
}
