import 'package:flutter_native_contact_picker/model/contact.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';
import 'package:rooo_driver/global/widgets/empty_widget.dart';
import 'package:rooo_driver/global/widgets/ios_padding.dart';
import 'package:rooo_driver/components/referral_offer_referral_card.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart' as contacts;
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ReferralOfferReferralScreen extends StatefulWidget {
  const ReferralOfferReferralScreen({super.key});

  State<ReferralOfferReferralScreen> createState() =>
      _ReferralOfferReferralScreenState();
}

class _ReferralOfferReferralScreenState
    extends State<ReferralOfferReferralScreen> {
  List<RefferalOfferModel> _refferalDataList = [];
  // final FlutterContactPicker _contactPicker = new FlutterContactPicker();

  final FlutterNativeContactPicker _contactPicker =
      new FlutterNativeContactPicker();
  // ignore: unused_field
  contacts.Contact? _contact;

  String api_message = '';

  void getReferralOffersData() async {
    appStore.setLoading(true);

    await getReferralOffersApi(offer_type: "referrals").then((value) {
      appStore.setLoading(false);

      _refferalDataList = value.data ?? [];

      api_message = value.message.toString();

      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  @override
  void initState() {
    getReferralOffersData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Observer(builder: (_) {
        return Stack(
          alignment: Alignment.center,
          children: [
            height20,
            SingleChildScrollView(
              child: ListView.separated(
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return ReferralOfferReferralCard(
                        refferalOfferData: _refferalDataList[index]);
                  },
                  separatorBuilder: (context, index) {
                    return const Divider(
                      thickness: 3,
                    );
                  },
                  itemCount: _refferalDataList.length),
            ),
            Observer(
              builder: (context) {
                return Visibility(
                  visible: appStore.isLoading,
                  child: loaderWidget(),
                );
              },
            ),
            Observer(
              builder: (context) {
                return Visibility(
                  visible: _refferalDataList.isEmpty && !appStore.isLoading,
                  child: RooEmptyWidegt(title: api_message.toString()),
                );
              },
            )
          ],
        );
      }),
    );
  }
}
