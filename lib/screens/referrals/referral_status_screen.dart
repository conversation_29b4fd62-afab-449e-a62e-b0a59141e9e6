// ignore: unused_import
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/model/refferal_offers/referral_offer_status_model_response.dart';
import 'package:rooo_driver/network/RestApis.dart';

import 'package:rooo_driver/utils/Extensions/app_common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/export/app_export.dart';

import '../../../main.dart';
import '../../../utils/Common.dart';


class ReferralStatusScreen extends StatefulWidget {
  ReferralStatusScreen();

  @override
  ReferralStatusScreenState createState() => ReferralStatusScreenState();
}

class ReferralStatusScreenState extends State<ReferralStatusScreen> {
  // ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;

  RefferalOfferModelStatusResponse? _refferalDataList;

  // List<String> riderStatus = [REFERRAL_STATUS, REFERRAL_OFFER];

  @override
  void initState() {
    super.initState();
    getReferralOfferStatusData();

    // init();

    // scrollController.addListener(() {
    //   if (scrollController.position.pixels ==
    //       scrollController.position.maxScrollExtent) {
    //     if (currentPage < totalPage) {
    //       appStore.setLoading(true);
    //       currentPage++;
    //       setState(() {});

    //       init();
    //     }
    //   }
    // });
    // afterBuildCreated(() => appStore.setLoading(true));
  }

  void init() async {
    // await getRiderRequestList(
    //         page: currentPage,
    //         status: widget.status,
    //         driverId: sharedPref.getInt(USER_ID))
    //     .then((value) {
    //   appStore.setLoading(false);

    //   currentPage = value.pagination!.currentPage!;
    //   totalPage = value.pagination!.totalPages!;
    //   if (currentPage == 1) {
    //     riderData.clear();
    //   }
    //   riderData.addAll(value.data!);
    //   setState(() {});
    // }).catchError((error) {
    //   appStore.setLoading(false);
    //   log("Server error");
    // });
  }
  //   void getReferralOffersData() async {
  //   await getReferralOffersApi().then((value) {
  //     appStore.setLoading(false);

  //     _refferalDataList = value.data ?? [];

  //     setState(() {});
  //   }).catchError((error) {
  //     appStore.setLoading(false);
  //   });
  // }

  void getReferralOfferStatusData() async {
          appStore.setLoading(true);

    await getReferralOffersStatusApi().then((value) {
      appStore.setLoading(false);

      _refferalDataList = value;

      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);

      log("Server error" + "+++");
    });
  }
String formatDate(DateTime dateTime) {
  // List of month names
  List<String> monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Get the month name, date, and year
  String monthName = monthNames[dateTime.month - 1];
  String date = dateTime.day.toString();
  String year = dateTime.year.toString();

  return '$monthName $date, $year';
}

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
        final primaryColor = AppColors.primaryColor(context);

    return Observer(builder: (context) {
      return Stack(
        children: [
          Padding(
            padding: screenPadding,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Text(
                  //   language.completedTxt,
                  //   style: TextStyle(
                  //     fontSize: 20,
                  //     fontWeight: FontWeight.bold,
                  //   ),
                  // ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(language.referralCompleted),
                            Text(
                              _refferalDataList?.offer_completed.toString() ?? "",
                              style: TextStyle(fontSize: 22),
                            ),
                          ],
                        ),
                      ),
                      
                      SizedBox(
                        height: 40,
                        child: VerticalDivider(
                          color: primaryColor,
                          thickness: 3,
                        ),
                      ),
                        
                    
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(language.referralEarning),
                            Text(
                              _refferalDataList?.you_made.toString() ?? "",
                              style: TextStyle(fontSize: 22),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                height10, 
                  const Divider(
                  ),          Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(language.offerCompleted),
                            Text(
                              _refferalDataList?.offer_completed_new.toString() ?? "",
                              style: TextStyle(fontSize: 22),
                            ),
                          ],
                        ),
                      ),
                      
                      SizedBox(
                        height: 40,
                        child: VerticalDivider(
                          color: primaryColor,
                          thickness: 3,
                        ),
                      ),
                        
                    
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(language.offerEarning),
                            Text(
                              _refferalDataList?.you_made_offer.toString() ?? "",
                              style: TextStyle(fontSize: 22),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                  height10,Divider(
                    thickness: 3,
                    color: Theme.of(context).dividerColor,
                  ),
              
                  ListView.separated(
                      shrinkWrap: true,
                      itemCount: _refferalDataList?.referrals?.length ?? 0,
                      separatorBuilder: (context, index) => const Divider(

                          ),
                      itemBuilder: (context, index) {
                        return ListTile(
                          contentPadding: EdgeInsets.all(0),
                          title: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("Congratulations! "+
                                
                                (_refferalDataList?.referrals?[index].userName??"")+"'s registration has done successfully",
                                    style: TextStyle( fontSize: 15, color: Colors.white),
                              ),
                               Text(
                                "you were paid "+(_refferalDataList?.referrals?[index].amount??"")+" on "+formatDate(_refferalDataList!.referrals![index].createdAt),
                                    style: TextStyle( fontSize: 15,color: Colors.white),
                              ),
                            ],
                          ),
                          // subtitle: Text(
                          //   _refferalDataList?.data?[index].sub_title
                          //           .toString() ??
                          //       "",
                          // ),
                        );
                      }),
              
                  // Text('User A' +hit the goal),
                ],
              ),
            ),
          ),

          Visibility(
            visible: appStore.isLoading,
            child: loaderWidget(),
          ),
          // if (riderData.isEmpty)
          //   appStore.isLoading ? SizedBox() : emptyWidget(),
        ],
      );
    });
  }
}
