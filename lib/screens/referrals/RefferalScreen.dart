// import 'package:rooo_driver/components/CreateReferralTabsScreen.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/screens/referrals/referral_offer_referral_screen.dart';
// import 'package:rooo_driver/screens/referrals/referral_status_screen.dart';
// import 'package:rooo_driver/screens/refferal_offer_screen.dart';
// import 'package:rooo_driver/utils/Colors.dart';
// import 'package:rooo_driver/utils/Common.dart';
// import 'package:rooo_driver/utils/Constants.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';
// import 'package:flutter/material.dart';



// class ReferralScreen extends StatefulWidget {
//   @override
//   ReferralScreenState createState() => ReferralScreenState();
// }

// class ReferralScreenState extends State<ReferralScreen> {
//   int currentPage = 1;
//   int totalPage = 1;
//   List<String> riderStatus = [REFERRAL_OFFER, REFERRAL_STATUS,REFERRALS];

//   @override
//   void initState() {
//     super.initState();
//     init();
//   }

//   void init() async {
//     //
//   }

//   @override
//   void setState(fn) {
//     if (mounted) super.setState(fn);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return DefaultTabController(
//       length: riderStatus.length,
//       child: Scaffold(
//         
//         appBar: AppBar(
//           title: Text(language.promotionsTxt,
//               style: boldTextStyle(color: Colors.white)),
//         ),
//         body: Column(
//           children: [
//             tabContainer(tabs: riderStatus),
//             // Container(
//             //   height: 40,
//             //   margin: EdgeInsets.only(right: 16, left: 16, top: 16),
//             //   decoration: BoxDecoration(
//             //      
//             //     borderRadius: radius(),
//             //   ),
//             //   child: TabBar(
//             //     indicator: BoxDecoration(
//             //       borderRadius: radius(),
//             //        
//             //     ),
//             //     labelColor: IS_DARKMODE ? Colors.black : Colors.white,
//             //     unselectedLabelColor:  ,
//             //     labelStyle: boldTextStyle(color: Colors.white, size: 14),
//             //     tabs: riderStatus.map((e) {
//             //       return Tab(
//             //         child: Text(changeStatusText(e)),
//             //       );
//             //     }).toList(),
//             //   ),
//             // ),
//             Expanded(
//               child: TabBarView(
//                 children: [ReferralOfferScreen(),ReferralStatusScreen(),ReferralOfferReferralScreen()],
//                 // children: riderStatus.map((e) {
//                 //   return CreateReferralTabScreen(status: e);
//                 // }).toList(),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
