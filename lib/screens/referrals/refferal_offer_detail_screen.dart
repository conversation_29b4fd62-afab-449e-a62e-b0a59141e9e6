
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:webview_flutter/webview_flutter.dart';

class ReferralOfferDetailScreen extends StatefulWidget {
  final String title;
    final String description;

  const ReferralOfferDetailScreen({super.key, required this.description, required this.title});

  @override
  State<ReferralOfferDetailScreen> createState() => _ReferralOfferDetailScreenState();
}

class _ReferralOfferDetailScreenState extends State<ReferralOfferDetailScreen> {
  // late WebViewController webViewController;

  // OpportunityModel details = OpportunityModel(id: 5000, title: "", imageURL: "");


  bool webpageLoaded = false;

  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    appStore.setLoading(true);
    controller.setBackgroundColor(Colors.white);
controller.loadHtmlString(widget.description);

print(widget.description);
    controller.setNavigationDelegate(NavigationDelegate(
      onPageFinished: (url) {
                  if (Theme.of(context).brightness == Brightness.dark) {
          controller.runJavaScript('''
            document.body.style.backgroundColor = '#121212';
            document.body.style.color = '#FFFFFF';
            document.querySelectorAll('*').forEach(element => {
              if (element.tagName === 'A') {
                element.style.color = '#90CAF9';
              }
              if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3' || 
                  element.tagName === 'H4' || element.tagName === 'H5' || element.tagName === 'H6') {
                element.style.color = '#FFFFFF';
              }
              if (element.tagName === 'P' || element.tagName === 'SPAN' || element.tagName === 'DIV') {
                element.style.color = '#E0E0E0';
              }
            });
          ''');
        }
     
      },
      
    ));
       Future.delayed(Duration(seconds: 3), () {
          setState(() {
            appStore.setLoading(false);
          });
        });

    super.initState();
  }





  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: RoooAppbar(title: "Referral Terms"),
      body: Stack(
        children: [
          Column(
            children: [
              
              Expanded(
                child: WebViewWidget(
                  controller: controller,
                ),
              ),
            ],
          ),
          Visibility(
            visible: appStore.isLoading,
            child: Observer(
              builder: (context) => AppLoader(),
            ),
          )
        ],
      ),
    );
  }
}
