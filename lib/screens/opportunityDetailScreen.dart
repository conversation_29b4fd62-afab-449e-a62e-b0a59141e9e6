import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:webview_flutter/webview_flutter.dart';

class OpportunityDetailsScreen extends StatefulWidget {
  final int id;
  final String title;
  const OpportunityDetailsScreen(
      {super.key, required this.id, required this.title});

  @override
  State<OpportunityDetailsScreen> createState() =>
      _OpportunityDetailsScreenState();
}

class _OpportunityDetailsScreenState extends State<OpportunityDetailsScreen> {
  // late WebViewController webViewController;

  // OpportunityModel details = OpportunityModel(id: 5000, title: "", imageURL: "");

  String htmldata = '';

  bool webpageLoaded = false;

  bool webPageLoaded = false;
  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    appStore.setLoading(true);

    controller.setNavigationDelegate(NavigationDelegate(
      onPageStarted: (t) {
        getOpportunityDetailData(id: widget.id, controller: controller);
      },
      onPageFinished: (url) {
        Future.delayed(Duration(seconds: 3), () {
          setState(() {
            appStore.setLoading(false);
          });
        });
      },
    ));

    super.initState();
  }

  @override
  void dispose() {
    appStore.setLoading(false);
    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  Future<void> loadHtml(WebViewController controller) async {
    // webViewController = controller;
    await controller.loadHtmlString(htmldata);
  }

  Future getOpportunityDetailData(
      {required int id, required WebViewController controller}) async {
    getOpportunityDetailsApi(id).then((value) async {
      if (value != null) {
        await controller.loadHtmlString(value);
          if (Theme.of(context).brightness == Brightness.dark) {
          controller.runJavaScript('''
            document.body.style.backgroundColor = '#121212';
            document.body.style.color = '#FFFFFF';
            document.querySelectorAll('*').forEach(element => {
              if (element.tagName === 'A') {
                element.style.color = '#90CAF9';
              }
              if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3' || 
                  element.tagName === 'H4' || element.tagName === 'H5' || element.tagName === 'H6') {
                element.style.color = '#FFFFFF';
              }
              if (element.tagName === 'P' || element.tagName === 'SPAN' || element.tagName === 'DIV') {
                element.style.color = '#E0E0E0';
              }
            });
          ''');
        }
      }
    }).onError((error, stackTrace) {
      GlobalMethods.infoToast(context,  "Server error");
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.detailScreen),
      body: Stack(
        children: [
          Column(
            children: [
              height10,
              CustomText(
                data: widget.title,
                size: 15,
                fontweight: FontWeight.bold,
              ),
              height10,
              Expanded(
                child: WebViewWidget(
                  controller: controller,
                ),
              ),
            ],
          ),
          Visibility(
            visible: appStore.isLoading,
            child: Observer(
              builder: (context) => loaderWidget(),
            ),
          )
        ],
      ),
    );
  }
}
