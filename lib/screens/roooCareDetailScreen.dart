import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';

class RoooCareDetailScreen extends StatefulWidget {
  final String title;
  final String details;
  const RoooCareDetailScreen(
      {super.key, required this.details, required this.title});

  @override
  State<RoooCareDetailScreen> createState() => _RoooCareDetailScreenState();
}

class _RoooCareDetailScreenState extends State<RoooCareDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: widget.title.toString()),
      body: Padding(
        padding: screenPadding,
        child: Column(children: [
          CustomText(
            data: widget.title.toString(),
            size: 20,
            fontweight: FontWeight.bold,
          ),
          height10,
          CustomText(
            data: widget.details.toString(),
            size: 15,
          )
        ]),
      ),
    );
  }
}
