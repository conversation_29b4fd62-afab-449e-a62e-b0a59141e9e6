import 'package:rooo_driver/features/login/screens/roo_login_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';

import '../model/WalkThroughModel.dart';

class WalkThroughScreen extends StatefulWidget {
  @override
  WalkThroughScreenState createState() => WalkThroughScreenState();
}

class WalkThroughScreenState extends State<WalkThroughScreen> {
  PageController pageController = PageController();
  int currentPage = 0;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  List<WalkThroughModel> walkThroughClass = [
    // WalkThroughModel(
    //   name: 'Get Ride Request',
    //   text: "Get A Ride Request By\nNearest Rider",
    //   img: 'images/car.png',
    // ),
    WalkThroughModel(
      name: 'Pickup Rider',
      text: "Accept a ride request and pickup\na rider for destination",
      img: 'images/driver1.jpg',
    ),
    WalkThroughModel(
      name: 'Drop Rider',
      text: "Drop A Rider To Destination",
      img: 'images/driver2.jpg',
    ),
    // WalkThroughModel(
    //   name: 'Drop Rider',
    //   text: "Drop A Rider To Destination",
    //   img: 'images/driver3.jpg',
    // )
  ];

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageView.builder(
            itemCount: walkThroughClass.length,
            controller: pageController,
            itemBuilder: (context, i) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(
                    walkThroughClass[i].img.toString(),
                    fit: BoxFit.cover,
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                  ),
                  Positioned(
                    bottom: 120,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(walkThroughClass[i].name!,
                            style: TextStyle(color: Colors.white,fontSize: 30,fontWeight: FontWeight.bold),
                            textAlign: TextAlign.center),
                        SizedBox(height: 16),
                        Text(walkThroughClass[i].text.toString(),
                            style: TextStyle(color: Colors.white,fontSize: 15,),
                            textAlign: TextAlign.center),
                      ],
                    ),
                  ),
                ],
              );
            },
            onPageChanged: (int i) {
              currentPage = i;
              setState(() {});
            },
          ),
          Positioned(
            bottom: 20,
            right: 16,
            left: 16,
            child: Column(
              children: [
                dotIndicator(walkThroughClass, currentPage),
                SizedBox(height: 16),
                GestureDetector(
                  onTap: () {
                    GlobalMethods.pushAndRemoveAll(context: context, screen: LoginScreen(), screenIdentifier: ScreenIdentifier.LoginScreen);
                    
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    padding: EdgeInsets.all(12),
                    child: Icon(Icons.arrow_forward, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          // Positioned(
          //   top: 40,
          //   right: 0,
          //   child: TextButton(
          //     onPressed: () {
          //       launchScreen(context, NewLoginScreen(), isNewTask: true);
          //       sharedPref.setBool(IS_FIRST_TIME, false);
          //     },
          //     child: Text('Skip', style: boldTextStyle(color: Colors.white)),
          //   ),
          // ),
        ],
      ),
    );
  }
}
