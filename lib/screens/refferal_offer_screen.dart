import 'package:rooo_driver/components/ReferralOfferCard.dart';
import 'package:rooo_driver/global/widgets/empty_widget.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

class ReferralOfferScreen extends StatefulWidget {
  const ReferralOfferScreen({super.key});

  State<ReferralOfferScreen> createState() => _ReferralOfferScreenState();
}

class _ReferralOfferScreenState extends State<ReferralOfferScreen> {
  String? api_message;
  List<RefferalOfferModel> _refferalDataList = [];
  // final FlutterContactPicker _contactPicker = new FlutterContactPicker();

  void getReferralOffersData() async {
    appStore.setLoading(true);

    await getReferralOffersApi(offer_type: "offer").then((value) {
      appStore.setLoading(false);

      _refferalDataList = value.data ?? [];
      api_message = value.message;

      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  @override
  void initState() {
    getReferralOffersData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(builder: (context) {
      return Stack(
        children: [
          _refferalDataList.isEmpty
              ? RooEmptyWidegt(title: api_message.toString())
              : ListView.separated(
                  itemBuilder: (context, index) {
                    return ReferralOfferCard(
                        refferalOfferData: _refferalDataList[index]);
                  },
                  separatorBuilder: (context, index) {
                    return const Divider(
                      thickness: 3,
                    );
                  },
                  itemCount: _refferalDataList.length),
          Observer(
            builder: (context) {
              return Visibility(
                visible: appStore.isLoading,
                child: loaderWidget(),
              );
            },
          ),
          Observer(
            builder: (context) {
              return Visibility(
                visible: _refferalDataList.isEmpty && !appStore.isLoading,
                child: RooEmptyWidegt(title: api_message.toString()),
              );
            },
          )
        ],
      );
    });
  }
}
