import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/bank_info/screens/bank_info_screen.dart';
import 'package:rooo_driver/screens/MyWalletScreen.dart';
import 'package:flutter/material.dart';

import '../main.dart';
import '../utils/Common.dart';
import '../utils/Constants.dart';

class BankAndWalletScreen extends StatefulWidget {
  final bool isWalletScreen;

  const BankAndWalletScreen({super.key, required this.isWalletScreen});
  @override
  BankAndWalletScreenState createState() => BankAndWalletScreenState();
}

class BankAndWalletScreenState extends State<BankAndWalletScreen>
    with AutomaticKeepAliveClientMixin {
  List<String> riderStatus = [BANK_INFO, WALLET];

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return DefaultTabController(
      initialIndex: widget.isWalletScreen ? 1 : 0,
      length: riderStatus.length,
      child: Scaffold(
        appBar: RoooAppbar(
          title: language.bankInfoTxt,
        ),
        body: Column(
          children: [
            tabContainer(tabs: riderStatus),
            Expanded(
              child: TabBarView(children: [BankScreen(), MyWalletScreen()]),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
