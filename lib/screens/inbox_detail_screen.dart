import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:webview_flutter/webview_flutter.dart';

class InboxDetailsScreen extends StatefulWidget {
  final int id;
  final String title;
  const InboxDetailsScreen({super.key, required this.id, required this.title});

  @override
  State<InboxDetailsScreen> createState() => _InboxDetailsScreenState();
}

class _InboxDetailsScreenState extends State<InboxDetailsScreen> {
  // late WebViewController webViewController;

  // OpportunityModel details = OpportunityModel(id: 5000, title: "", imageURL: "");

  String htmldata = '';

  bool webpageLoaded = false;
  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    appStore.setLoading(true);
    controller.setBackgroundColor(Colors.white);
    getInboxDetailData(id: widget.id, controller: controller);
    super.initState();

    controller.setNavigationDelegate(NavigationDelegate(
      onPageFinished: (url) {
        Future.delayed(Duration(seconds: 3), () {
          setState(() {
            appStore.setLoading(false);
          });
                  if (Theme.of(context).brightness == Brightness.dark) {
          controller.runJavaScript('''
            document.body.style.backgroundColor = '#121212';
            document.body.style.color = '#FFFFFF';
            document.querySelectorAll('*').forEach(element => {
              if (element.tagName === 'A') {
                element.style.color = '#90CAF9';
              }
              if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3' || 
                  element.tagName === 'H4' || element.tagName === 'H5' || element.tagName === 'H6') {
                element.style.color = '#FFFFFF';
              }
              if (element.tagName === 'P' || element.tagName === 'SPAN' || element.tagName === 'DIV') {
                element.style.color = '#E0E0E0';
              }
            });
          ''');
        }
        });
      },
      onPageStarted: (t) {},
    ));
  }

  @override
  void dispose() {
    appStore.setLoading(false);
    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  void loadHtml(WebViewController controller) {
    // webViewController = controller;
    controller.loadHtmlString(htmldata);
  }

  Future getInboxDetailData(
      {required int id, required WebViewController controller}) async {
    getInboxDetailsApi(id).then((value) {
      if (value != null) {
        controller.loadHtmlString(value);
      }
    }).onError((error, stackTrace) {
      GlobalMethods.infoToast(context, "Server error");
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: RoooAppbar(title: widget.title),
      body: Stack(
        children: [
          Column(
            children: [
              
              Expanded(
                child: WebViewWidget(controller: controller),
              ),
            ],
          ),
          Visibility(
            visible: appStore.isLoading,
            child: Observer(
              builder: (context) => loaderWidget(color: Colors.black),
            ),
          )
        ],
      ),
    );
  }
}
