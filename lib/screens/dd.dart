var map = {
  "id": 6,
  "waiting_for_rider_time": 300,
  "display_name": "Driver90 Driver90",
  "email": "<EMAIL>",
  "username": "driver90",
  "user_type": "driver",
  "profile_image":
      "https://www.rooo.com//uploads/1496/990be88b-301c-4683-83ed-1daa2abddc9d6586314387654773973.jpg",
  "status": "active",
  "latitude": "31.13058",
  "longitude": "75.77819",
  "on_ride_request": {
    "reached_otp": null,
    "id": 2,
    "rider_id": 5,
    "service_id": 3,
    "datetime": "2023-09-23 11:50:18",
    "is_schedule": 0,
    "ride_attempt": 0,
    "otp": "4041",
    "total_amount": 0,
    "subtotal": 0,
    "extra_charges_amount": 0,
    "driver_id": 6,
    "driver_name": "Driver90 Driver90",
    "rider_name": "ROOO Rider 1",
    "driver_profile_image":
        "https://www.rooo.com//uploads/1496/990be88b-301c-4683-83ed-1daa2abddc9d6586314387654773973.jpg",
    "rider_profile_image":
        "https://www.rooo.com//uploads/1424/2c3f276f-013e-42e4-bbad-115e8db3bb436437731719940566014.jpg",
    "start_latitude": "31.0189883",
    "start_longitude": "75.78794",
    "start_address":
        "Unnamed Road, Puraana Bazar, Phillaur, Punjab 144410, India",
    "end_latitude": "19.1602177",
    "end_longitude": "77.31027519999999",
    "end_address": "Hazur Sahib",
    "distance_unit": "km",
    "start_time": null,
    "end_time": null,
    "distance": null,
    "duration": null,
    "seat_count": 1,
    "reason": null,
    "status": "arriving",
    "base_fare": null,
    "minimum_fare": null,
    "per_distance": null,
    "per_minute_drive": null,
    "per_minute_waiting": null,
    "waiting_time": null,
    "waiting_time_limit": null,
    "waiting_time_charges": null,
    "cancelation_charges": null,
    "cancel_by": null,
    "payment_id": null,
    "payment_type": "pre-auth",
    "payment_status": "pending",
    "extra_charges": [],
    "coupon_discount": null,
    "coupon_code": null,
    "coupon_data": null,
    "is_rider_rated": 0,
    "is_driver_rated": 0,
    "max_time_for_find_driver_for_ride_request": 0,
    "created_at": "2023-09-23T06:20:18.000000Z",
    "updated_at": "2023-09-23T06:20:29.000000Z",
    "rider_contact_number": "+91 9008007006",
    "driver_contact_number": "+91 9090909090",
    "driver_email": "<EMAIL>",
    "rider_email": "<EMAIL>",
    "regionId": 1,
    "is_ride_for_other": 0
  },
  "rider": {
    "address":
        "Addreeses fgd g dfg df g fd g fd g fd g  fd g fd g fd g fd g fd g",
    "contact_number": "+91 9008007006",
    "created_at": "2023-09-22T05:49:16.000000Z",
    "display_name": "ROOO Rider 1",
    "email": "<EMAIL>",
    "fcm_token": null,
    "first_name": "ROOO Rider",
    "gender": "male",
    "id": 5,
    "is_online": 1,
    "is_verified_driver": null,
    "last_name": "1",
    "last_notification_seen": "2023-09-22 09:19:34",
    "latitude": "31.0189883",
    "login_type": null,
    "longitude": "75.78794",
    "player_id": null,
    "profile_image":
        "https://www.rooo.com//uploads/1424/2c3f276f-013e-42e4-bbad-115e8db3bb436437731719940566014.jpg",
    "service_ids": null,
    "status": "active",
    "timezone": "UTC",
    "uid": "8cu1LXAz7Hd4gSSjoZ6OHjdc0Mv1",
    "updated_at": "2023-09-23T06:19:47.000000Z",
    "user_type": "rider",
    "username": "<EMAIL>",
    "api_token": null,
    "is_document_required": null,
    "rating": 0,
    "is_available": 1,
    "referralCode": "az1ZI5"
  }
};
