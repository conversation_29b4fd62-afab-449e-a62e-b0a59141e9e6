import 'package:rooo_driver/features/login/screens/roo_login_screen.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';

import 'WalkThroughtScreen.dart';

class SplashScreen extends StatefulWidget {
  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    await Future.delayed(Duration(seconds: 3));
    String? token = sharedPref.getString(TOKEN);
    int? userId = sharedPref.getInt(USER_ID);
    GlobalState.isProfileComplete = sharedPref.getBool(IS_PROFILE_COMPLETE) ?? true;

    if (sharedPref.getBool(IS_FIRST_TIME) ?? true) {
      GlobalMethods.pushAndRemoveAll(
          context: context,
          screen: WalkThroughScreen(),
          screenIdentifier: ScreenIdentifier.WalkThroughScreen);
    } else {
      if (token == null || userId == null) {
        GlobalMethods.pushAndRemoveAll(
            context: context,
            screen: LoginScreen(),
            screenIdentifier: ScreenIdentifier.LoginScreen);
      } else {
        GlobalMethods.pushAndRemoveAll(
            context: context,
            screen: RideScreen(),
            screenIdentifier: ScreenIdentifier.InitialScreen);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
                Theme.of(context).brightness == Brightness.dark
                    ? 'images/logo-white.png'
                    : 'images/logo-black.png',
                fit: BoxFit.contain,
                height: 150,
                width: 150),
            SizedBox(height: 16),
            Text(language.appName,
                style: boldTextStyle(color: Colors.white, size: 22)),
          ],
        ),
      ),
    );
  }
}
