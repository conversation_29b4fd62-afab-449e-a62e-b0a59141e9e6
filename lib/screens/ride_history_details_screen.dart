import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/care/screens/ComplaintScreen.dart';
import 'package:rooo_driver/features/care/screens/RideHelpScreen.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/constants/spacer.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/DriverRatting.dart';
import 'package:rooo_driver/model/RideHistoryDetailsModel.dart';
import 'package:rooo_driver/model/RiderModel.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';
// import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';

class RideHistoryDetailScreen extends StatefulWidget {
  final int id;
  const RideHistoryDetailScreen({super.key, required this.id});

  @override
  State<RideHistoryDetailScreen> createState() =>
      _RideHistoryDetailScreenState();
}

class _RideHistoryDetailScreenState extends State<RideHistoryDetailScreen> {
  RideHistoryDetailsModel? data;

  void getRideHistoryDetailsList() async {
    setState(() {
      appStore.setLoading(true);
    });
    await getRideHistoryDetailApi(id: widget.id).then((value) {
      if (value.data != null) {
        data = value;
      }
      setState(() {
        appStore.setLoading(false);
      });
    }).catchError((error) {
      setState(() {
        appStore.setLoading(false);
      });
      log("Server error" +
          "qweeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee");
    });
  }

  @override
  void initState() {
    //  : implement initState
    super.initState();

    getRideHistoryDetailsList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: language.detailScreen,
        actionIconList: [
          /* Raise complaint button*/
          AppButton(
              text: "Raise Complaint",
              backgroundColor: Colors.red,
              onPressed: () async {
                if ((data?.complaint?.status ?? "resolved").toLowerCase().trim() ==
                    "resolved") {
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => ComplaintScreen(
                      driverRatting: data?.driverRating ?? DriverRatting(),
                      complaintModel: data?.complaint,
                      riderModel: RiderModel(
                        id: data!.data!.id!,
                        riderName: data!.data?.riderName ?? "",
                        riderProfileImage: data!.data?.riderProfileImage ?? "",
                        riderId: data!.data!.riderId!,
                        driverId: data!.data!.driverId!,
                      ),
                      isFromHistory: true,
                    ),
                  ));
                } else {
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) =>
                        RideHelpScreen(indicatorUpdater: () {}),
                  ));
                }
              })
        ],
      ),
      body: appStore.isLoading
          ? Center(
              child: CircularProgressIndicator(),
            )
          : SingleChildScrollView(
              child: Stack(
                children: [
                  Padding(
                    padding: screenPadding,
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Card(
                            child: Container(
                              padding: screenPadding,
                              child: Column(
                                children: [
                                  inkWellWidget(
                                    onTap: () {},
                                    child: HeaderkeyValue(
                                        key: language.PickupLocationTxt,
                                        value: data!.data!.startAddress!),
                                  ),

                                  Divider(),
                                  HeaderkeyValue(
                                      key: language.destinationLocation,
                                      value: data!.data!.endAddress!),
                                  Divider(),

                                  keyValue(
                                      key: language.TimeTxt,
                                      value: data!.data!.datetime!),
                                  keyValue(
                                      key: language.riderName,
                                      value: data!.data?.riderName ?? ""),
                                  keyValue(
                                      key: language.statusTxt,
                                      value: data!.data!.status
                                              .toString()[0]
                                              .toUpperCase() +
                                          data!.data!.status
                                              .toString()
                                              .substring(1)),
                                  keyValue(
                                      key: language.distance,
                                      value: data!.data!.distance.toString() +
                                          data!.data!.distanceUnit.toString()),

                                  Divider(),

                                  Container(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 8),
                                    decoration: BoxDecoration(
                                        borderRadius: appRadius,
                                        color: Colors.green.withOpacity(.3)),
                                    child: Column(
                                      children: [
                                        amountkeyValue(
                                            key: language.earning,
                                            value: data?.data
                                                    ?.driver_subtract_earning ??
                                                0),
                                        amountkeyValue(
                                            key: language.tip,
                                            value: data?.data?.tips ?? 0),
                                        amountkeyValue(
                                            key: language.tax,
                                            value: data?.data?.driver_tax ?? 0),
                                        amountkeyValue(
                                            key: language.waitingTime,
                                            value: data?.data
                                                    ?.perMinuteWaitingCharge ??
                                                0),

                                        amountkeyValue(
                                            key: language.totalEarning,
                                            value: data?.data?.driver_earning ??
                                                0),

                                        // amountkeyValue(
                                        //     key: language.total +
                                        //         " " +
                                        //         language.earning +
                                        //         "s",
                                        //     value: (appStore.currencyCode) +
                                        //         ((data!.data?.driver_earning ??
                                        //                     0.0) +
                                        //                 (data!.data
                                        //                         ?.perDistanceCharge ??
                                        //                     0.0)

                                        //                     )
                                        //             .toStringAsFixed(2)),

                                        // ,amountkeyValue(key: "Total", value: value)
                                      ],
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 4,
                                  ),

                                  // Container(
                                  //   decoration: BoxDecoration(

                                  //     color: Colors.green.withOpacity(.3)
                                  //   ),
                                  //   child: Column(

                                  //     children: [
                                  //       amountkeyValue(key: key, value: data.data.wa)
                                  //     ],
                                  //   )

                                  // )

                                  // Container(
                                  //   padding:
                                  //       EdgeInsets.symmetric(horizontal: 8),
                                  //   decoration: BoxDecoration(
                                  //       color: Colors.red.withOpacity(.3),
                                  //       borderRadius: radius()),
                                  //   child: Column(
                                  //     children: [
                                  //       amountkeyValue(
                                  //           key: "Cancellation charges",
                                  //           value: data!
                                  //               .data!.cancelationCharges
                                  //               .toString()),
                                  //       amountkeyValue(
                                  //           key: "Tax",
                                  //           value: data!.data?.tax.toString()      ??
                                  //               "0"),

                                  //     ],
                                  //   ),
                                  // ),

                                  // // Row(
                                  // //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  // //   children: [
                                  // //     Expanded(child: CustomText(data: "Ride id")),
                                  // //     Expanded(
                                  // //       child: CustomText(
                                  // //           data:
                                  // //               ride_history_list[index].id.toString()),
                                  // //     ),
                                  // //   ],
                                  // // ),
                                  // // Divider(
                                  // //   thickness: 1,
                                  // // ),
                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data:
                                  //                 language.PickupLocationTxt)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.data!.startAddress
                                  //               .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // Divider(
                                  //   thickness: 1,
                                  // ),
                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data: language
                                  //                 .destinationLocation)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.data!.endAddress
                                  //               .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // Divider(
                                  //   thickness: 1,
                                  // ),
                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data: language.TimeTxt)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.data!.datetime
                                  //               .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // height10,

                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child:
                                  //             CustomText(data: language.ride)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.data!.riderName
                                  //               .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // height10,

                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data: language.payment)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.payment!.paymentStatus
                                  //               .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // height10,

                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data: language.amount)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.payment!.totalAmount
                                  //               .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // height10,

                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data: language.paymentMethod)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.payment!.paymentType
                                  //               .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // height10,
                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data: language.DistanceTxt)),
                                  //     Expanded(
                                  //       child: CustomText(
                                  //           data: data!.data!.distance!
                                  //                   .toString() +
                                  //               " " +
                                  //               data!.data!.distanceUnit
                                  //                   .toString()),
                                  //     ),
                                  //   ],
                                  // ),
                                  // height10,

                                  // height20,
                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Expanded(
                                  //         child: CustomText(
                                  //             data: language.statusTxt)),
                                  //     Expanded(
                                  //       child: Container(
                                  //         alignment: Alignment.center,
                                  //         padding: EdgeInsets.all(5),
                                  //         color:
                                  //             data!.data!.status == "cancelled"
                                  //                 ? Colors.red
                                  //                 : Colors.green,
                                  //         child: CustomText(
                                  //             color: Colors.white,
                                  //             data: data!.data!.status
                                  //                 .toString()),
                                  //       ),
                                  //     ),
                                  //   ],
                                  // ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Observer(
                    builder: (context) {
                      return Visibility(
                        visible: appStore.isLoading,
                        child: Loader(),
                      );
                    },
                  )
                ],
              ),
            ),
    );
  }

  HeaderkeyValue({required String key, required String value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
            child: CustomText(
          data: key,
          size: 20,
        )),
        const SizedBox(
          width: 20,
        ),
        const SizedBox(
          width: 20,
        ),
        Expanded(
          child: CustomText(
            size: 15,
            data: value,
            fontweight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  keyValue({required String key, required String value}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              child: CustomText(
            data: key,
            size: 12,
          )),
          const SizedBox(
            width: 20,
          ),
          const SizedBox(
            width: 20,
          ),
          Expanded(
            child: CustomText(
              size: 12,
              data: value,
              fontweight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  amountkeyValue({required String key, required num value}) {
    if (value <= 0) {
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              child: CustomText(
            data: key,
            size: 12,
          )),
          CustomText(
            size: 12,
            data: "AUD " + value.toString(),
            fontweight: FontWeight.bold,
          ),
        ],
      ),
    );
  }
}
