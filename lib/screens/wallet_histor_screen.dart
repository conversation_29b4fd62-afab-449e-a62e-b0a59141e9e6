import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/WalletListModel.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

class WalletHistoryScreen extends StatefulWidget {
  const WalletHistoryScreen({super.key});

  @override
  State<WalletHistoryScreen> createState() => _WalletHistoryScreenState();
}

class _WalletHistoryScreenState extends State<WalletHistoryScreen> {
  int currentPage = 1;
  int totalPage = 1;

  int currentIndex = -1;

  List<WalletModel> walletData = [];

  int totalAmount = 0;

  @override
  void initState() {
    //  : implement initState
    super.initState();
    getWalletList();
  }

  void getWalletList() async {
    setState(() {
      appStore.setLoading(true);
    });
    await getWalletListApi(pageData: currentPage).then((value) {
      setState(() {
        appStore.setLoading(false);
      });
      currentPage = value.pagination!.currentPage!;
      totalPage = value.pagination!.totalPages!;
      totalAmount = value.walletBalance!.totalAmount!.toInt();
      if (currentPage == 1) {
        walletData.clear();
      }
      walletData = value.data ?? [];
      setState(() {});
    }).catchError((error) {
      setState(() {
        appStore.setLoading(false);
      });
      log("Server error");
    });
  }

  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.WalletHistoryTxt),
      body: Observer(builder: (context) {
        return Stack(
          children: [
            SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                padding: EdgeInsets.fromLTRB(16, 20, 16, 16),
                child: walletData.isEmpty
                    ? Center(
                        child: CustomText(data: language.notAvailable),
                      )
                    : ListView.separated(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Card(
                              child: Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: 10, horizontal: 10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomText(data: language.amount),
                                    CustomText(
                                        data: appStore.currencyCode.toString() +
                                            walletData[index]
                                                .amount
                                                .toString()),
                                  ],
                                ),
                                height5,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomText(data: language.TimeTxt),
                                    CustomText(
                                        data: walletData[index].datetime ?? ""),
                                  ],
                                ),
                                height5,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomText(data: language.rideId),
                                    CustomText(
                                        data: walletData[index]
                                            .rideRequestId
                                            .toString()),
                                  ],
                                ),
                                height5,
                                // Row(
                                //   mainAxisAlignment:
                                //       MainAxisAlignment.spaceBetween,
                                //   children: [
                                //     CustomText(data: language.DescriptionTxt),
                                //     CustomText(
                                //         data: walletData[index].description ??
                                //             ""),
                                //   ],
                                // ),
                              ],
                            ),
                          ));
                        },
                        separatorBuilder: (context, index) {
                          return height10;
                        },
                        itemCount: walletData.length)),
            Visibility(
              visible: appStore.isLoading,
              child: loaderWidget(),
            ),
            // !appStore.isLoading && walletData.isEmpty
            //     ? emptyWidget()
            //     : SizedBox(),
          ],
        );
      }),
    );
  }
}
