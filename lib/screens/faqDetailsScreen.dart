
// import 'package:rooo_driver/global/export/app_export.dart';

// class DataDetailsScreen extends StatelessWidget {
//   final String description;
//   final String appBarTitle;

//   final String title;
//   const DataDetailsScreen(
//       {super.key,
//       required this.title,
//       required this.description,
//       required this.appBarTitle});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: RoooAppbar(title: appBarTitle),
//       body: Padding(
//         padding: screenPadding,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             CustomText(
//               data: title,
//               size: 18,
//               fontweight: FontWeight.w700,
//             ),
//             height20,
//             CustomText(data: description)
//           ],
//         ),
//       ),
//     );
//   }
// }
