// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'AppStore.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AppStore on _AppStore, Store {
  late final _$isLoggedInAtom =
      Atom(name: '_AppStore.isLoggedIn', context: context);

  @override
  bool get isLoggedIn {
    _$isLoggedInAtom.reportRead();
    return super.isLoggedIn;
  }

  @override
  set isLoggedIn(bool value) {
    _$isLoggedInAtom.reportWrite(value, super.isLoggedIn, () {
      super.isLoggedIn = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: '_AppStore.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$userIdAtom = Atom(name: '_AppStore.userId', context: context);

  @override
  int get userId {
    _$userIdAtom.reportRead();
    return super.userId;
  }

  @override
  set userId(int value) {
    _$userIdAtom.reportWrite(value, super.userId, () {
      super.userId = value;
    });
  }

  late final _$userEmailAtom =
      Atom(name: '_AppStore.userEmail', context: context);

  @override
  String get userEmail {
    _$userEmailAtom.reportRead();
    return super.userEmail;
  }

  @override
  set userEmail(String value) {
    _$userEmailAtom.reportWrite(value, super.userEmail, () {
      super.userEmail = value;
    });
  }

  late final _$uIdAtom = Atom(name: '_AppStore.uId', context: context);

  @override
  String get uId {
    _$uIdAtom.reportRead();
    return super.uId;
  }

  @override
  set uId(String value) {
    _$uIdAtom.reportWrite(value, super.uId, () {
      super.uId = value;
    });
  }

  late final _$userNameAtom =
      Atom(name: '_AppStore.userName', context: context);

  @override
  String get userName {
    _$userNameAtom.reportRead();
    return super.userName;
  }

  @override
  set userName(String value) {
    _$userNameAtom.reportWrite(value, super.userName, () {
      super.userName = value;
    });
  }

  late final _$userProfileAtom =
      Atom(name: '_AppStore.userProfile', context: context);

  @override
  String get userProfile {
    _$userProfileAtom.reportRead();
    return super.userProfile;
  }

  @override
  set userProfile(String value) {
    _$userProfileAtom.reportWrite(value, super.userProfile, () {
      super.userProfile = value;
    });
  }

  late final _$firstNameAtom =
      Atom(name: '_AppStore.firstName', context: context);

  @override
  String get firstName {
    _$firstNameAtom.reportRead();
    return super.firstName;
  }

  @override
  set firstName(String value) {
    _$firstNameAtom.reportWrite(value, super.firstName, () {
      super.firstName = value;
    });
  }

  late final _$isDarkModeAtom =
      Atom(name: '_AppStore.isDarkMode', context: context);

  @override
  bool get isDarkMode {
    _$isDarkModeAtom.reportRead();
    return super.isDarkMode;
  }

  @override
  set isDarkMode(bool value) {
    _$isDarkModeAtom.reportWrite(value, super.isDarkMode, () {
      super.isDarkMode = value;
    });
  }

  late final _$currencyAtom =
      Atom(name: '_AppStore.currency', context: context);

  @override
  String get currency {
    _$currencyAtom.reportRead();
    return super.currency;
  }

  @override
  set currency(String value) {
    _$currencyAtom.reportWrite(value, super.currency, () {
      super.currency = value;
    });
  }

  late final _$selectedLanguageAtom =
      Atom(name: '_AppStore.selectedLanguage', context: context);

  @override
  String get selectedLanguage {
    _$selectedLanguageAtom.reportRead();
    return super.selectedLanguage;
  }

  @override
  set selectedLanguage(String value) {
    _$selectedLanguageAtom.reportWrite(value, super.selectedLanguage, () {
      super.selectedLanguage = value;
    });
  }

  late final _$walletPresetTopUpAmountAtom =
      Atom(name: '_AppStore.walletPresetTopUpAmount', context: context);

  @override
  String get walletPresetTopUpAmount {
    _$walletPresetTopUpAmountAtom.reportRead();
    return super.walletPresetTopUpAmount;
  }

  @override
  set walletPresetTopUpAmount(String value) {
    _$walletPresetTopUpAmountAtom
        .reportWrite(value, super.walletPresetTopUpAmount, () {
      super.walletPresetTopUpAmount = value;
    });
  }

  late final _$walletPresetTipAmountAtom =
      Atom(name: '_AppStore.walletPresetTipAmount', context: context);

  @override
  String get walletPresetTipAmount {
    _$walletPresetTipAmountAtom.reportRead();
    return super.walletPresetTipAmount;
  }

  @override
  set walletPresetTipAmount(String value) {
    _$walletPresetTipAmountAtom.reportWrite(value, super.walletPresetTipAmount,
        () {
      super.walletPresetTipAmount = value;
    });
  }

  late final _$currencyCodeAtom =
      Atom(name: '_AppStore.currencyCode', context: context);

  @override
  String get currencyCode {
    _$currencyCodeAtom.reportRead();
    return super.currencyCode;
  }

  @override
  set currencyCode(String value) {
    _$currencyCodeAtom.reportWrite(value, super.currencyCode, () {
      super.currencyCode = value;
    });
  }

  late final _$currencyPositionAtom =
      Atom(name: '_AppStore.currencyPosition', context: context);

  @override
  String get currencyPosition {
    _$currencyPositionAtom.reportRead();
    return super.currencyPosition;
  }

  @override
  set currencyPosition(String value) {
    _$currencyPositionAtom.reportWrite(value, super.currencyPosition, () {
      super.currencyPosition = value;
    });
  }

  late final _$currencyNameAtom =
      Atom(name: '_AppStore.currencyName', context: context);

  @override
  String get currencyName {
    _$currencyNameAtom.reportRead();
    return super.currencyName;
  }

  @override
  set currencyName(String value) {
    _$currencyNameAtom.reportWrite(value, super.currencyName, () {
      super.currencyName = value;
    });
  }

  late final _$rideSecondAtom =
      Atom(name: '_AppStore.rideSecond', context: context);

  @override
  String? get rideSecond {
    _$rideSecondAtom.reportRead();
    return super.rideSecond;
  }

  @override
  set rideSecond(String? value) {
    _$rideSecondAtom.reportWrite(value, super.rideSecond, () {
      super.rideSecond = value;
    });
  }

  late final _$minAmountToAddAtom =
      Atom(name: '_AppStore.minAmountToAdd', context: context);

  @override
  int? get minAmountToAdd {
    _$minAmountToAddAtom.reportRead();
    return super.minAmountToAdd;
  }

  @override
  set minAmountToAdd(int? value) {
    _$minAmountToAddAtom.reportWrite(value, super.minAmountToAdd, () {
      super.minAmountToAdd = value;
    });
  }

  late final _$maxAmountToAddAtom =
      Atom(name: '_AppStore.maxAmountToAdd', context: context);

  @override
  int? get maxAmountToAdd {
    _$maxAmountToAddAtom.reportRead();
    return super.maxAmountToAdd;
  }

  @override
  set maxAmountToAdd(int? value) {
    _$maxAmountToAddAtom.reportWrite(value, super.maxAmountToAdd, () {
      super.maxAmountToAdd = value;
    });
  }

  late final _$extraChargeValueAtom =
      Atom(name: '_AppStore.extraChargeValue', context: context);

  @override
  String? get extraChargeValue {
    _$extraChargeValueAtom.reportRead();
    return super.extraChargeValue;
  }

  @override
  set extraChargeValue(String? value) {
    _$extraChargeValueAtom.reportWrite(value, super.extraChargeValue, () {
      super.extraChargeValue = value;
    });
  }

  late final _$setFirstNameAsyncAction =
      AsyncAction('_AppStore.setFirstName', context: context);

  @override
  Future<void> setFirstName(String? val) {
    return _$setFirstNameAsyncAction.run(() => super.setFirstName(val));
  }

  late final _$setExtraChargesAsyncAction =
      AsyncAction('_AppStore.setExtraCharges', context: context);

  @override
  Future<void> setExtraCharges(String? val) {
    return _$setExtraChargesAsyncAction.run(() => super.setExtraCharges(val));
  }

  late final _$setMaxAmountToAddAsyncAction =
      AsyncAction('_AppStore.setMaxAmountToAdd', context: context);

  @override
  Future<void> setMaxAmountToAdd(int? val) {
    return _$setMaxAmountToAddAsyncAction
        .run(() => super.setMaxAmountToAdd(val));
  }

  late final _$setMinAmountToAddAsyncAction =
      AsyncAction('_AppStore.setMinAmountToAdd', context: context);

  @override
  Future<void> setMinAmountToAdd(int? val) {
    return _$setMinAmountToAddAsyncAction
        .run(() => super.setMinAmountToAdd(val));
  }

  late final _$setRiderSecondAsyncAction =
      AsyncAction('_AppStore.setRiderSecond', context: context);

  @override
  Future<void> setRiderSecond(String? val) {
    return _$setRiderSecondAsyncAction.run(() => super.setRiderSecond(val));
  }

  late final _$setCurrencyNameAsyncAction =
      AsyncAction('_AppStore.setCurrencyName', context: context);

  @override
  Future<void> setCurrencyName(String val) {
    return _$setCurrencyNameAsyncAction.run(() => super.setCurrencyName(val));
  }

  late final _$setCurrencyCodeAsyncAction =
      AsyncAction('_AppStore.setCurrencyCode', context: context);

  @override
  Future<void> setCurrencyCode(String val) {
    return _$setCurrencyCodeAsyncAction.run(() => super.setCurrencyCode(val));
  }

  late final _$setCurrencyPositionAsyncAction =
      AsyncAction('_AppStore.setCurrencyPosition', context: context);

  @override
  Future<void> setCurrencyPosition(String val) {
    return _$setCurrencyPositionAsyncAction
        .run(() => super.setCurrencyPosition(val));
  }

  late final _$setWalletTipAmountAsyncAction =
      AsyncAction('_AppStore.setWalletTipAmount', context: context);

  @override
  Future<void> setWalletTipAmount(String val) {
    return _$setWalletTipAmountAsyncAction
        .run(() => super.setWalletTipAmount(val));
  }

  late final _$setWalletPresetTopUpAmountAsyncAction =
      AsyncAction('_AppStore.setWalletPresetTopUpAmount', context: context);

  @override
  Future<void> setWalletPresetTopUpAmount(String val) {
    return _$setWalletPresetTopUpAmountAsyncAction
        .run(() => super.setWalletPresetTopUpAmount(val));
  }

  late final _$setUIdAsyncAction =
      AsyncAction('_AppStore.setUId', context: context);

  @override
  Future<void> setUId(String val, {bool isInitialization = false}) {
    return _$setUIdAsyncAction
        .run(() => super.setUId(val, isInitialization: isInitialization));
  }

  late final _$setCurrencyAsyncAction =
      AsyncAction('_AppStore.setCurrency', context: context);

  @override
  Future<void> setCurrency(String val) {
    return _$setCurrencyAsyncAction.run(() => super.setCurrency(val));
  }

  late final _$setUserProfileAsyncAction =
      AsyncAction('_AppStore.setUserProfile', context: context);

  @override
  Future<void> setUserProfile(String val) {
    return _$setUserProfileAsyncAction.run(() => super.setUserProfile(val));
  }

  late final _$setUserNameAsyncAction =
      AsyncAction('_AppStore.setUserName', context: context);

  @override
  Future<void> setUserName(String val, {bool isInitialization = false}) {
    return _$setUserNameAsyncAction
        .run(() => super.setUserName(val, isInitialization: isInitialization));
  }

  late final _$setUserEmailAsyncAction =
      AsyncAction('_AppStore.setUserEmail', context: context);

  @override
  Future<void> setUserEmail(String val, {bool isInitialization = false}) {
    return _$setUserEmailAsyncAction
        .run(() => super.setUserEmail(val, isInitialization: isInitialization));
  }

  late final _$setUserIdAsyncAction =
      AsyncAction('_AppStore.setUserId', context: context);

  @override
  Future<void> setUserId(int val, {bool isInitializing = false}) {
    return _$setUserIdAsyncAction
        .run(() => super.setUserId(val, isInitializing: isInitializing));
  }

  late final _$setLoadingAsyncAction =
      AsyncAction('_AppStore.setLoading', context: context);

  @override
  Future<void> setLoading(bool val) {
    return _$setLoadingAsyncAction.run(() => super.setLoading(val));
  }

  late final _$setLoggedInAsyncAction =
      AsyncAction('_AppStore.setLoggedIn', context: context);

  @override
  Future<void> setLoggedIn(bool val, {bool isInitializing = false}) {
    return _$setLoggedInAsyncAction
        .run(() => super.setLoggedIn(val, isInitializing: isInitializing));
  }

  late final _$setDarkModeAsyncAction =
      AsyncAction('_AppStore.setDarkMode', context: context);

  @override
  Future<void> setDarkMode(bool aIsDarkMode) {
    return _$setDarkModeAsyncAction.run(() => super.setDarkMode(aIsDarkMode));
  }

  late final _$setLanguageAsyncAction =
      AsyncAction('_AppStore.setLanguage', context: context);

  @override
  Future<void> setLanguage(String aCode, {BuildContext? context}) {
    return _$setLanguageAsyncAction
        .run(() => super.setLanguage(aCode, context: context));
  }

  @override
  String toString() {
    return '''
isLoggedIn: ${isLoggedIn},
isLoading: ${isLoading},
userId: ${userId},
userEmail: ${userEmail},
uId: ${uId},
userName: ${userName},
userProfile: ${userProfile},
firstName: ${firstName},
isDarkMode: ${isDarkMode},
currency: ${currency},
selectedLanguage: ${selectedLanguage},
walletPresetTopUpAmount: ${walletPresetTopUpAmount},
walletPresetTipAmount: ${walletPresetTipAmount},
currencyCode: ${currencyCode},
currencyPosition: ${currencyPosition},
currencyName: ${currencyName},
rideSecond: ${rideSecond},
minAmountToAdd: ${minAmountToAdd},
maxAmountToAdd: ${maxAmountToAdd},
extraChargeValue: ${extraChargeValue}
    ''';
  }
}
