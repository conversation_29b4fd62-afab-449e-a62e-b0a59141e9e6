// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDY0eYHMkXAq4JdpL1XWf1B3hU7jZqPNFE',
    appId: '1:972284281345:android:ef6c31c0e61ddfe7da7600',
    messagingSenderId: '972284281345',
    projectId: 'rooo-2c8b1',
    storageBucket: 'rooo-2c8b1.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBE1ks4PYI4zjuLEph1qCo5201O2XKrqM8',
    appId: '1:972284281345:ios:3ebf426276ae7987da7600',
    messagingSenderId: '972284281345',
    projectId: 'rooo-2c8b1',
    storageBucket: 'rooo-2c8b1.firebasestorage.app',
    androidClientId: '972284281345-4r8ddcbnn95tvu7e4fq8lk80b3najb9a.apps.googleusercontent.com',
    iosClientId: '972284281345-csbm4ti46p9t2vmr2id07giekub8v368.apps.googleusercontent.com',
    iosBundleId: 'app.rooo.driver',
  );

}