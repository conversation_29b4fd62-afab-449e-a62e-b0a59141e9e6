// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:rooo_driver/utils/Colors.dart';
// import 'package:rooo_driver/utils/Extensions/app_common.dart';

// class AppTheme {
//   //
//   AppTheme._();

//   static final ThemeData lightTheme = ThemeData(
//     primarySwatch: createMaterialColor( ),
//      
//     scaffoldBackgroundColor: Colors.white,
//     fontFamily: GoogleFonts.poppins().fontFamily,
//     bottomNavigationBarTheme:
//         BottomNavigationBarThemeData(backgroundColor: Colors.white),
//     iconTheme: IconThemeData(color: scaffoldSecondaryDark),
//     textTheme: TextTheme(titleLarge: TextStyle()),
//     dialogBackgroundColor: Colors.white,
//     unselectedWidgetColor: Colors.black,
//     dividerColor: viewLineColor,
//     cardColor: Colors.white,
//     dialogTheme: DialogTheme(shape: dialogShape()),
//     appBarTheme: AppBarTheme(
//       color: Colors.black,
//       systemOverlayStyle: SystemUiOverlayStyle(
//         statusBarIconBrightness: Brightness.light,
//         statusBarBrightness: Brightness.light,
//       ),
//     ),
//   ).copyWith(
//     pageTransitionsTheme: PageTransitionsTheme(
//       builders: <TargetPlatform, PageTransitionsBuilder>{
//         TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
//         TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
//         TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
//       },
//     ),
//   );

//   static final ThemeData darkTheme = ThemeData(
//     primarySwatch: createMaterialColor( ),
//      
//     // scaffoldBackgroundColor: scaffoldColorDark,
//     fontFamily: GoogleFonts.poppins().fontFamily,
//     bottomNavigationBarTheme:
//         BottomNavigationBarThemeData(backgroundColor: scaffoldSecondaryDark),
//     iconTheme: IconThemeData(color: Colors.white),
//     textTheme: TextTheme(
//         bodyMedium: TextStyle(
//           color: Colors.white,
//         ),
//         bodySmall: TextStyle(
//           color: Colors.red,
//         ),
//         titleLarge: TextStyle(
//           color: Colors.white,
//         )),
//     dialogBackgroundColor: scaffoldSecondaryDark,
//     unselectedWidgetColor: Colors.white60,
//     dividerColor: Colors.white12,
//     cardColor: scaffoldSecondaryDark,
//     dialogTheme: DialogTheme(shape: dialogShape()),
//     appBarTheme: AppBarTheme(
//       backgroundColor: Colors.black,
//       systemOverlayStyle: SystemUiOverlayStyle(
//         statusBarIconBrightness: Brightness.light,
//         statusBarBrightness: Brightness.dark,
//       ),
//     ),
//   ).copyWith(
//     pageTransitionsTheme: PageTransitionsTheme(
//       builders: <TargetPlatform, PageTransitionsBuilder>{
//         TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
//         TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
//         TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
//       },
//     ),
//   );
// }
