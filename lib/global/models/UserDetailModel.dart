import 'package:rooo_driver/features/bank_info/models/bank_info_model.dart';
import 'package:rooo_driver/features/ride_flow/model/online_offline_model.dart';
import 'package:rooo_driver/global/models/vehicle_details_model.dart';
import 'package:rooo_driver/model/ServiceModel.dart';

class UserDetailModel {
  UserData? data;
  String? message;

  UserDetailModel({this.data, this.message});

  factory UserDetailModel.fromJson(Map<String, dynamic> json) {
    return UserDetailModel(
      data: json['data'] != null ? UserData.fromJson(json['data']) : null,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class UserData {
  var id;
  var serviceIds;
  String? login_screen;
  String? contactNumber;
  String? createdAt;
  String? apiToken;
  String? displayName;
  ServiceList? driverService;
  String? email;
  String? firstName;
  bool? isProfileComplete;
  String? gender;
  String? otherGenderText;
  String? referralCode;
  bool? isNewSignUp;
  int? isOnline;
  int? isVerifiedDriver;
  String? lastName;
  String? lastNotificationSeen;
  String? latitude;
  String? loginType;
  String? longitude;
  String? playerId;
  String? profileImage;
  String? status;
  String? uid;
  String? updatedAt;
  BankInfoModel? user_bank_account;
  UserDetail? userDetail;
  String? userType;
  num? rating;
  int? age;
  String? selected_map;
  String? email_verified_at;
  VehicleDetailsModel? vehicleDetailsModel;
  OnlineOfflineModel? onlineOfflineModel;
  int? region_id;
  int? province_id;
  List<KeyMessage>? waitingForApproval;

  UserData({
    this.selected_map,
    this.onlineOfflineModel,
    this.vehicleDetailsModel,
    this.age,
    this.contactNumber,
    this.createdAt,
    this.displayName,
    this.driverService,
    this.email,
    this.firstName,
    this.isProfileComplete,
    this.gender,
    this.id,
    this.isNewSignUp,
    this.isOnline,
    this.isVerifiedDriver,
    this.lastName,
    this.lastNotificationSeen,
    this.latitude,
    this.loginType,
    this.longitude,
    this.playerId,
    this.profileImage,
    this.serviceIds,
    this.status,
    this.login_screen,
    this.uid,
    this.updatedAt,
    this.user_bank_account,
    this.userDetail,
    this.userType,
    this.apiToken,
    this.rating,
    this.region_id,
    this.province_id,
    this.referralCode,
    this.email_verified_at,
    this.waitingForApproval,
    this.otherGenderText,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      selected_map: json['selected_map'],
      age: json['age'],
      login_screen: json['login_screen'],
      contactNumber: json['contact_number'],
      createdAt: json['created_at'],
      displayName: json['display_name'],
      email: json['email'],
      firstName: json['first_name'],
      gender: json['gender'],
      id: json['id'],
      isNewSignUp: json['wizard_show'],
      isOnline: json['is_online'],
      isVerifiedDriver: json['is_verified_driver'],
      lastName: json['last_name'],
      lastNotificationSeen: json['last_notification_seen'],
      latitude: json['latitude'],
      loginType: json['login_type'],
      longitude: json['longitude'],
      playerId: json['player_id'],
      profileImage: json['profile_image'],
      serviceIds: json['service_ids'],
      status: json['status'],
      uid: json['uid'],
      updatedAt: json['updated_at'],
      userDetail: json['user_detail'] != null
          ? UserDetail.fromJson(json['user_detail'])
          : null,
      onlineOfflineModel: json['online_offline'] != null
          ? OnlineOfflineModel.fromJson(json['user_detail'])
          : null,
      vehicleDetailsModel: json['vehicle_details'] != null
          ? VehicleDetailsModel.fromJson(json['vehicle_details'])
          : null,
      user_bank_account: json['user_bank_account'] != null
          ? BankInfoModel.fromJson(json['user_bank_account'])
          : null,
      driverService: json['driver_service'] != null
          ? ServiceList.fromJson(json['driver_service'])
          : null,
      userType: json['user_type'],
      apiToken: json['api_token'],
      rating: json['rating'],
      referralCode: json['referral_code'],
      region_id: json['region_id'],
      province_id: json["province_id"],
      email_verified_at: json['email_verified_at'],
      waitingForApproval: json['waitingForApproval'] != null
          ? List<KeyMessage>.from(
              json['waitingForApproval'].map((x) => KeyMessage.fromMap(x)))
          : null,
      otherGenderText: json['other_gender_text'],
      isProfileComplete: json['is_profile_complete'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();

    data['selected_map'] = this.selected_map;

    data['age'] = this.age;
    data['login_screen'] = this.login_screen;

    data['contact_number'] = this.contactNumber;
    data['created_at'] = this.createdAt;
    data['display_name'] = this.displayName;
    data['email'] = this.email;
    data['first_name'] = this.firstName;
    data['gender'] = this.gender;
    data['id'] = this.id;
    data['is_online'] = this.isOnline;
    data['is_verified_driver'] = this.isVerifiedDriver;
    data['last_name'] = this.lastName;
    data['last_notification_seen'] = this.lastNotificationSeen;
    data['latitude'] = this.latitude;
    data['login_type'] = this.loginType;
    data['longitude'] = this.longitude;
    data['player_id'] = this.playerId;
    data['profile_image'] = this.profileImage;
    data['service_ids'] = this.serviceIds;
    data['status'] = this.status;
    data['uid'] = this.uid;
    data['updated_at'] = this.updatedAt;
    data['user_type'] = this.userType;
    data['api_token'] = this.apiToken;
    data['rating'] = this.rating;

    data['region_id'] = this.region_id;

    data['province_id'] = this.province_id;

    data['referralCode'] = this.referralCode;
    data['email_verified_at'] = this.email_verified_at;

    if (this.userDetail != null) {
      data['user_detail'] = this.userDetail!.toJson();
    }
    if (this.user_bank_account != null) {
      data['user_bank_account'] = this.user_bank_account!.toJson();
    }
    if (this.onlineOfflineModel != null) {
      data['online_offline_model'] = this.onlineOfflineModel!.toJson();
    }
    if (this.driverService != null) {
      // data['driver_service'] = this.driverService!.toJson();
    }
    return data;
  }
}

class UserDetail {
  String? carColor;
  String? carModel;
  String? carPlateNumber;
  String? carProductionYear;
  String? createdAt;
  // String? homeAddress;
  // String? homeLatitude;
  // String? homeLongitude;
  int? id;
  String? updatedAt;
  int? userId;
  // String? workAddress;
  // String? workLatitude;
  // String? workLongitude;
  // bool? nightDrivingPreference;
  // String? socialSecurityNumber;
  int? age;

  UserDetail({
    this.carColor,
    this.carModel,
    this.carPlateNumber,
    this.carProductionYear,
    this.createdAt,
    // this.homeAddress,
    // this.homeLatitude,
    // this.homeLongitude,
    this.id,
    this.updatedAt,
    this.userId,
    // this.workAddress,
    // this.workLatitude,
    // this.workLongitude,
    this.age,
    // this.socialSecurityNumber,
    // this.nightDrivingPreference,
  });

  factory UserDetail.fromJson(Map<String, dynamic> json) {
    return UserDetail(
        carColor: json['car_color'],
        carModel: json['car_model'],
        carPlateNumber: json['car_plate_number'],
        carProductionYear: json['car_production_year'],
        createdAt: json['created_at'],
        // homeAddress: json['home_address'],
        // homeLatitude: json['home_latitude'],
        // homeLongitude: json['home_longitude'],
        id: json['id'],
        updatedAt: json['updated_at'],
        userId: json['user_id'],
        // workAddress: json['work_address'],
        // workLatitude: json['work_latitude'],
        // workLongitude: json['work_longitude'],
        age: json['age']);
    //   nightDrivingPreference: json['nightDrivingPreference'],
    //   socialSecurityNumber: json['securityNumber'],
    // );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['car_color'] = this.carColor;
    data['car_model'] = this.carModel;
    data['car_plate_number'] = this.carPlateNumber;
    data['car_production_year'] = this.carProductionYear;
    data['created_at'] = this.createdAt;
    // data['home_address'] = this.homeAddress;
    // data['home_latitude'] = this.homeLatitude;
    // data['home_longitude'] = this.homeLongitude;
    data['id'] = this.id;
    data['updated_at'] = this.updatedAt;
    data['user_id'] = this.userId;
    // data['work_address'] = this.workAddress;
    // data['work_latitude'] = this.workLatitude;
    // data['work_longitude'] = this.workLongitude;
    data['age'] = this.age;
    // data['nightDrivingPreference'] = this.nightDrivingPreference;
    // data['securityNumber'] = this.socialSecurityNumber;
    return data;
  }
}

class UserBankAccount {
  String? account_holder_name;
  String? account_number;
  String? bank_code;
  String? bank_name;
  String? created_at;
  int? id;
  String? updated_at;
  int? user_id;

  String? bank_address;
  String? bank_city;
  String? postal_code;

  String? dob;
  int? bank_id;
  String? institution_number;
  String? transit_number;

  UserBankAccount(
      {this.account_holder_name,
      this.account_number,
      this.bank_code,
      this.bank_name,
      this.created_at,
      this.id,
      this.updated_at,
      this.user_id,
      this.bank_address,
      this.bank_city,
      this.postal_code,
      this.dob,
      this.bank_id,
      this.institution_number,
      this.transit_number});

  factory UserBankAccount.fromJson(Map<String, dynamic> json) {
    return UserBankAccount(
        account_holder_name: json['account_holder_name'],
        account_number: json['account_number'],
        bank_code: json['bank_code'],
        bank_name: json['bank_name'],
        created_at: json['created_at'],
        id: json['id'],
        updated_at: json['updated_at'],
        user_id: json['user_id'],
        bank_address: json["bank_address"],
        bank_city: json["bank_city"],
        postal_code: json["postal_code"],
        dob: json["dob"],
        bank_id: json["bank_id"],
        institution_number: json["institution_number"],
        transit_number: json["transit_number"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['account_holder_name'] = this.account_holder_name;
    data['account_number'] = this.account_number;
    data['bank_code'] = this.bank_code;
    data['bank_name'] = this.bank_name;
    data['created_at'] = this.created_at;
    data['id'] = this.id;
    data['updated_at'] = this.updated_at;
    data['user_id'] = this.user_id;
    data['bank_address'] = bank_address;
    data["bank_city"] = bank_city;
    data["postal_code"] = postal_code;
    data["dob"] = dob;
    data["bank_id"] = bank_id;
    data["institution_number"] = institution_number;
    data["transit_number"] = transit_number;
    return data;
  }
}

class KeyMessage {
  String key;
  String message;
  KeyMessage({required this.key, required this.message});

  factory KeyMessage.fromMap(Map<String, dynamic> json) {
    return KeyMessage(
      key: json['key'],
      message: json['message'],
    );
  }
}
