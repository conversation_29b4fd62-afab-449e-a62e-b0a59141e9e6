class VehicleDetailsModel {
  final String carModel;
  final String carProductionYear;
  final String carPlateNumber;
  final String carColor;
  final int carCategoryId;

  VehicleDetailsModel({
    required this.carCategoryId,
    required this.carModel,
    required this.carProductionYear,
    required this.carPlateNumber,
    required this.carColor,
  });

  factory VehicleDetailsModel.fromJson(Map<String, dynamic> json) {
    return VehicleDetailsModel(
      carCategoryId: json["carCategoryId"],
      carModel: json['carModel'],
      carProductionYear: json['carProductionYear'],
      carPlateNumber: json['carPlateNumber'],
      carColor: json['carColor'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'carModel': carModel,
      'carProductionYear': carProductionYear,
      'carPlateNumber': carPlateNumber,
      'carColor': carColor,
      'carCategoryId': carCategoryId,
    };
  }
}
