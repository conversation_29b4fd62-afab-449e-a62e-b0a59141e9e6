import 'package:rooo_driver/global/models/response_model.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';

class AppSettingResponseModel extends ResponseModel<AppSettingModel> {
  AppSettingResponseModel({
    required bool status,
    required String message,
    required AppSettingModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory AppSettingResponseModel.fromJson(Map<String, dynamic> json) {
    return AppSettingResponseModel(
      status: json['status'],
      message: json['message'],
      data:
          json['data'] != null ? AppSettingModel.fromJson(json['data']) : null,
    );
  }
}
