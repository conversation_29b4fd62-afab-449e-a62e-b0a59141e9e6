import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class CurrentRideResponseModel extends ResponseModel<RideModel> {
  CurrentRideResponseModel({
    required bool status,
    required String message,
    required RideModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory CurrentRideResponseModel.fromJson(Map<String, dynamic> json) {
    return CurrentRideResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? RideModel.fromJson(json['data'])
          : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
