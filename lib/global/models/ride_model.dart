import 'package:rooo_driver/features/ride_flow/model/cancellation_reason.dart';
import 'package:rooo_driver/features/ride_flow/model/multiple_stop_model.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';

import '../../model/CouponData.dart';
import '../../model/ExtraChargeRequestModel.dart';

class RideModel {
  int? id;
  String? displayName;
  String? email;
  String? username;
  String? userType;
  String? profileImage;
  String? status;
  OnRideRequest? rideRequest;
  OnRideRequest? onRideRequest;
  List<OnRideRequest>? pool_rides;

  Driver? driver;
  UserData? rider;

  Payment? payment;
  WaitingCharges? charges;
  num? waiting_charges;
  bool? is_tip_confirmed;
  bool? is_second_preauth;
  bool? isPaymentCardUsed;

  RideModel({
    this.id,
    this.displayName,
    this.email,
    this.username,
    this.userType,
    this.profileImage,
    this.status,
    this.onRideRequest,
    this.driver,
    this.payment,
    this.charges,
    this.is_tip_confirmed,
    this.is_second_preauth,
    this.waiting_charges,
    this.isPaymentCardUsed,
    this.rideRequest,
    this.rider,
    this.pool_rides,
  });

  RideModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    displayName = json['display_name'];
    email = json['email'];
    username = json['username'];
    userType = json['user_type'];
    profileImage = json['profile_image'];
    is_second_preauth = json['is_second_preauth'];
    isPaymentCardUsed = json['isUsedSavedCard'];
    status = json['status'];
    rideRequest = json['ride_request'];
    rider = json['rider'] != null ? new UserData.fromJson(json['rider']) : null;

    onRideRequest = json['on_ride_request'] != null
        ? new OnRideRequest.fromJson(json['on_ride_request'])
        : null;

    driver =
        json['driver'] != null ? new Driver.fromJson(json['driver']) : null;
    payment =
        json['payment'] != null ? new Payment.fromJson(json['payment']) : null;

    waiting_charges = json['waiting_charges'];
    charges = json['charges'] != null
        ? new WaitingCharges.fromJson(json['charges'])
        : null;
    is_tip_confirmed = json['is_tip_confirmed'];
    if (json['pool_rides'] != null) {
      pool_rides = <OnRideRequest>[];
      json['pool_rides'].forEach((v) {
        pool_rides!.add(OnRideRequest.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['display_name'] = this.displayName;
    data['email'] = this.email;
    data['waiting_charges'] = this.waiting_charges;
    data['username'] = this.username;
    data['user_type'] = this.userType;
    data['profile_image'] = this.profileImage;
    data['status'] = this.status;
    if (this.rider != null) {
      data['rider'] = this.rider!.toJson();
    }
    if (this.onRideRequest != null) {
      data['on_ride_request'] = this.onRideRequest!.toJson();
    }
    if (this.rideRequest != null) {
      data['ride_request'] = this.rideRequest!.toJson();
    }
    if (this.driver != null) {
      data['driver'] = this.driver!.toJson();
    }
    if (this.payment != null) {
      data['payment'] = this.payment!.toJson();
    }

    if (this.pool_rides != null) {
      data['pool_rides'] = this.pool_rides!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OnRideRequest {
  int? id;
  int? riderId;
  int? serviceId;
  num? driver_subtract_earning;
  String? datetime;
  num? driver_tax;
  num? tips;

  String? driverPlayerId;
  String? riderPlayerId;
  String? driverUid;
  String? riderUid;
  String? riderFirestoreId;

  int? isSchedule;
  int? rideAttempt;
  String? otp;
  String? reached_otp;
  num? totalAmount;
  num? subtotal;
  num? extraChargesAmount;
  int? driverId;
  String? driverName;
  String? riderName;
  String? driverProfileImage;
  String? riderProfileImage;
  String? startLatitude;
  String? startLongitude;
  String? startAddress;
  String? endLatitude;
  String? endLongitude;
  String? endAddress;
  num? driver_earning;

  StopsModel? stop_pending;

  String? distanceUnit;
  String? startTime;
  String? endTime;
  num? distance;
  num? duration;
  num? seatCount;
  String? reason;
  String? status;
  num? baseFare;
  num? minimumFare;
  num? perDistance;
  num? perMinuteDrive;
  num? perMinuteWaiting;
  num? waitingTime;
  num? waitingTimeLimit;
  num? waitingTimeCharges;
  num? cancelationCharges;
  String? estimatedArrivalTime;
  String? cancelBy;
  num? paymentId;
  String? paymentType;
  String? paymentStatus;
  List<ExtraChargeRequestModel>? extraCharges;

  List<CancellationReason>? ride_cancel;

  num? couponDiscount;
  num? couponCode;
  List<CouponData>? couponData;
  bool? isOtpEnable;
  bool? isPool;
  bool? isAlreadyCancelled;

  num? isRiderRated;
  num? isDriverRated;
  num? maxTimeForFindDriverForRideRequest;
  String? createdAt;
  String? updatedAt;
  num? perMinuteWaitingCharge;
  num? perMinuteDriveCharge;
  num? perDistanceCharge;
  String? driverContactNumber;
  String? riderContactNumber;
  String? driverEmail;
  String? riderEmail;
  String? arrived_time;
  String? inprogress_time;
  int? arrived_waiting_time;
  int? regionId;
  List<StopsModel>? destinationPlace;
  List<StopsModel>? stops;
  List<CancellationReason>? cancel_reasons;
  bool? canDriverCancelTheRide;

  OnRideRequest({
    this.id,
    this.stop_pending,
    this.riderId,
    this.isAlreadyCancelled,
    this.serviceId,
    this.ride_cancel,
    this.datetime,
    this.isSchedule,
    this.rideAttempt,
    this.otp,
    this.totalAmount,
    this.subtotal,
    this.driver_subtract_earning,
    this.extraChargesAmount,
    this.destinationPlace,
    this.driverId,
    this.driverName,
    this.isOtpEnable,
    this.driver_tax,
    this.riderName,
    this.driverProfileImage,
    this.riderProfileImage,
    this.startLatitude,
    this.startLongitude,
    this.startAddress,
    this.endLatitude,
    this.isPool,
    this.endLongitude,
    this.endAddress,
    this.distanceUnit,
    this.startTime,
    this.riderFirestoreId,
    this.tips,
    this.endTime,
    this.distance,
    this.duration,
    this.seatCount,
    this.reason,
    this.driver_earning,
    this.status,
    this.baseFare,
    this.minimumFare,
    this.perDistance,
    this.perMinuteDrive,
    this.perMinuteWaiting,
    this.waitingTime,
    this.waitingTimeLimit,
    this.waitingTimeCharges,
    this.cancelationCharges,
    this.cancelBy,
    this.paymentId,
    this.paymentType,
    this.paymentStatus,
    this.extraCharges,
    this.couponDiscount,
    this.couponCode,
    this.couponData,
    this.isRiderRated,
    this.isDriverRated,
    this.maxTimeForFindDriverForRideRequest,
    this.createdAt,
    this.updatedAt,
    this.perDistanceCharge,
    this.perMinuteDriveCharge,
    this.perMinuteWaitingCharge,
    this.driverContactNumber,
    this.riderContactNumber,
    this.driverEmail,
    this.riderEmail,
    this.regionId,
    this.estimatedArrivalTime,
    this.reached_otp,
    this.stops,
    this.cancel_reasons,
    this.driverUid,
    this.riderUid,
    this.driverPlayerId,
    this.arrived_time,
    this.inprogress_time,
    this.arrived_waiting_time,
    this.canDriverCancelTheRide,
    this.riderPlayerId,
  });

  OnRideRequest.fromJson(Map<String, dynamic> json) {
    if (json['id'] != null) {
      id = json['id'];
      riderFirestoreId = json['rider_firestore_id'];
      riderId = json['rider_id'];
      isAlreadyCancelled = json['already_canceled'];
      arrived_time = json['arrived_time'];
      inprogress_time = json['inprogress_time'];
      arrived_waiting_time = json['arrived_waiting_time'];
      serviceId = json['service_id'];
      datetime = json['datetime'];
      isSchedule = json['is_schedule'];
      rideAttempt = json['ride_attempt'];
      otp = json['otp'];
      totalAmount = json['total_amount'];
      subtotal = json['subtotal'];
      extraChargesAmount = json['extra_charges_amount'];
      driverId = json['driver_id'];
      driverName = json['driver_name'];
      riderName = json['rider_name'];
      riderUid = json['rider_uid'];
      driverUid = json['driver_uid'];
      driverPlayerId = json['driver_playerid'];
      riderPlayerId = json['rider_playerid'];
      driverProfileImage = json['driver_profile_image'];
      riderProfileImage = json['rider_profile_image'];
      startLatitude = json['start_latitude'];
      startLongitude = json['start_longitude'];
      startAddress = json['start_address'];
      endLatitude = json['end_latitude'];
      endLongitude = json['end_longitude'];
      endAddress = json['end_address'];
      distanceUnit = json['distance_unit'];
      startTime = json['start_time'];
      endTime = json['end_time'];
      distance = json['distance'];
      duration = json['duration'];
      isPool = json['is_pool'];
      driver_earning = json['driver_earning'];
      driver_tax = json['driver_tax'];
      seatCount = json['seat_count'];
      tips = json['tips'];
      reason = json['reason'];
      status = json['status'];
      baseFare = json['base_fare'];
      minimumFare = json['minimum_fare'];
      perDistance = json['per_distance'];
      perMinuteDrive = json['per_minute_drive'];
      perMinuteWaiting = json['per_minute_waiting'];
      waitingTime = json['waiting_time'];
      waitingTimeLimit = json['waiting_time_limit'];
      waitingTimeCharges = json['waiting_time_charges'];
      cancelationCharges = json['cancelation_charges'];
      cancelBy = json['cancel_by'];
      isOtpEnable = json['is_otp_enable'];
      driver_subtract_earning = json['driver_subtract_earning'];
      paymentId = json['payment_id'];
      paymentType = json['payment_type'];
      paymentStatus = json['payment_status'];
      estimatedArrivalTime = json['estimatedArrivalTime'];
      reached_otp = json['reached_otp'];
      if (json['extra_charges'] != null) {
        extraCharges = <ExtraChargeRequestModel>[];
        json['extra_charges'].forEach((v) {
          extraCharges!.add(new ExtraChargeRequestModel.fromJson(v));
        });
      }
      if (json['ride_cancel'] != null) {
        ride_cancel = <CancellationReason>[];
        json['ride_cancel'].forEach((v) {
          ride_cancel!.add(CancellationReason.fromJson(v));
        });
      }
      if (json['destinationPlaces'] != null) {
        destinationPlace = <StopsModel>[];
        json['destinationPlaces'].forEach((v) {
          destinationPlace!.add(StopsModel.fromJson(v));
        });
      }
      if (json['stops'] != null) {
        stops = <StopsModel>[];
        json['stops'].forEach((v) {
          stops!.add(StopsModel.fromJson(v));
        });
      }
      stop_pending = json['stop_pending'] != null
          ? new StopsModel.fromJson(json['stop_pending'])
          : null;
      if (json['cancel_reasons'] != null) {
        cancel_reasons = <CancellationReason>[];
        json['cancel_reasons'].forEach((v) {
          cancel_reasons!.add(CancellationReason.fromJson(v));
        });
      }
      couponDiscount = json['coupon_discount'];
      couponCode = json['coupon_code'];
      couponData = json['coupon_data'] != null
          ? []
          // CouponData.fromJson(json['coupon_data'])
          : null;
      isRiderRated = json['is_rider_rated'];
      isDriverRated = json['is_driver_rated'];
      maxTimeForFindDriverForRideRequest =
          json['max_time_for_find_driver_for_ride_request'];
      createdAt = json['created_at'];
      updatedAt = json['updated_at'];
      perDistanceCharge = json['per_distance_charge'];
      perMinuteDriveCharge = json['per_minute_drive_charge'];
      perMinuteWaitingCharge = json['per_minute_waiting_charge'];
      driverContactNumber = json['driver_contact_number'];
      riderContactNumber = json['rider_contact_number'];
      riderEmail = json['rider_email'];
      driverEmail = json['driver_email'];
      regionId = json['region_id'];
      canDriverCancelTheRide = json['can_driver_cancel_ride'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;

    data['arrived_time'] = this.arrived_time;
    data['already_canceled'] = this.isAlreadyCancelled;

    data['arrived_waiting_time'] = this.arrived_waiting_time;

    data['arrived_waiting_time'] = this.inprogress_time;

    data['rider_firestore_id'] = this.riderFirestoreId;

    data['rider_id'] = this.riderId;
    data['is_otp_enable'] = this.isOtpEnable;
    data['driver_subtract_earning'] = this.driver_subtract_earning;

    data['service_id'] = this.serviceId;
    data['datetime'] = this.datetime;
    data['is_schedule'] = this.isSchedule;
    data['ride_attempt'] = this.rideAttempt;
    data['otp'] = this.otp;
    data['reached_otp'] = this.reached_otp;
    data['total_amount'] = this.totalAmount;
    data['driver_earning'] = this.driver_earning;
    data['stop_pending'] = this.stop_pending;

    data['subtotal'] = this.subtotal;
    data['extra_charges_amount'] = this.extraChargesAmount;
    data['driver_id'] = this.driverId;
    data['is_pool'] = this.isPool;

    data['driver_playerid'] = this.driverPlayerId;

    data['rider_playerid'] = this.riderPlayerId;

    data['driver_uid'] = this.driverUid;

    data['rider_uid'] = this.riderUid;

    data['driver_name'] = this.driverName;
    data['rider_name'] = this.riderName;
    data['driver_profile_image'] = this.driverProfileImage;
    data['rider_profile_image'] = this.riderProfileImage;
    data['start_latitude'] = this.startLatitude;
    data['start_longitude'] = this.startLongitude;
    data['tips'] = this.tips;

    data['start_address'] = this.startAddress;
    data['end_latitude'] = this.endLatitude;
    data['end_longitude'] = this.endLongitude;
    data['end_address'] = this.endAddress;
    data['distance_unit'] = this.distanceUnit;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    data['distance'] = this.distance;
    data['duration'] = this.duration;
    data['seat_count'] = this.seatCount;
    data['reason'] = this.reason;
    data['status'] = this.status;
    data['base_fare'] = this.baseFare;
    data['minimum_fare'] = this.minimumFare;
    data['per_distance'] = this.perDistance;
    data['per_minute_drive'] = this.perMinuteDrive;
    data['per_minute_waiting'] = this.perMinuteWaiting;
    data['waiting_time'] = this.waitingTime;
    data['waiting_time_limit'] = this.waitingTimeLimit;
    data['waiting_time_charges'] = this.waitingTimeCharges;
    data['cancelation_charges'] = this.cancelationCharges;
    data['cancel_by'] = this.cancelBy;
    data['payment_id'] = this.paymentId;
    data['payment_type'] = this.paymentType;
    data['payment_status'] = this.paymentStatus;
    if (this.extraCharges != null) {
      data['extra_charges'] =
          this.extraCharges!.map((v) => v.toJson()).toList();
    }
    if (this.ride_cancel != null) {
      data['ride_cancel'] = this.ride_cancel!.map((v) => v.toJson()).toList();
    }
    if (this.destinationPlace != null) {
      data['destinationPlaces'] =
          this.destinationPlace!.map((v) => v.toJson()).toList();
    }
    if (this.stops != null) {
      data['stops'] = this.stops!.map((v) => v.toJson()).toList();
    }

    if (this.cancel_reasons != null) {
      data['cancel_reasons'] =
          this.cancel_reasons!.map((v) => v.toJson()).toList();
    }
    if (this.stop_pending != null) {
      data['stop_pending'] = this.stop_pending!.toJson();
    }
    data['coupon_discount'] = this.couponDiscount;
    data['coupon_code'] = this.couponCode;
    data['coupon_data'] = this.couponData;
    data['is_rider_rated'] = this.isRiderRated;

    data['is_driver_rated'] = this.isDriverRated;
    data['max_time_for_find_driver_for_ride_request'] =
        this.maxTimeForFindDriverForRideRequest;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['per_distance_charge'] = this.perDistanceCharge;
    data['per_minute_drive_charge'] = this.perMinuteDriveCharge;
    data['per_minute_waiting_charge'] = this.perMinuteWaitingCharge;
    data['rider_contact_number'] = this.riderContactNumber;
    data['driver_contact_number'] = this.driverContactNumber;
    data['driver_email'] = this.driverEmail;
    data['rider_email'] = this.riderEmail;
    data['region_id'] = this.regionId;
    data['driver_tax'] = this.driver_tax;

    data['estimatedArrivalTime'] = this.estimatedArrivalTime;

    return data;
  }
}

class Driver {
  int? id;
  String? firstName;
  num? rating;
  String? lastName;
  String? displayName;
  String? email;
  String? username;
  String? status;
  String? userType;
  String? address;
  String? contactNumber;
  String? gender;
  String? profileImage;
  String? loginType;
  String? latitude;
  String? longitude;
  String? uid;
  String? playerId;
  num? isOnline;
  num? isAvailable;
  String? timezone;
  String? fcmToken;
  UserDetail? userDetail;
  var userBankAccount;
  List<int>? serviceId;
  DriverService? driverService;
  int? isVerifiedDriver;
  var lastNotificationSeen;
  String? createdAt;
  String? updatedAt;

  Driver({
    this.id,
    this.firstName,
    this.lastName,
    this.displayName,
    this.email,
    this.username,
    this.status,
    this.userType,
    this.address,
    this.contactNumber,
    this.gender,
    this.profileImage,
    this.loginType,
    this.latitude,
    this.longitude,
    this.uid,
    this.playerId,
    this.isOnline,
    this.isAvailable,
    this.timezone,
    this.fcmToken,
    this.userDetail,
    this.userBankAccount,
    this.serviceId,
    this.driverService,
    this.isVerifiedDriver,
    this.lastNotificationSeen,
    this.createdAt,
    this.updatedAt,
  });

  Driver.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    displayName = json['display_name'];
    email = json['email'];
    username = json['username'];
    status = json['status'];
    userType = json['user_type'];
    address = json['address'];
    contactNumber = json['contact_number'];
    gender = json['gender'];
    profileImage = json['profile_image'];
    loginType = json['login_type'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    uid = json['uid'];
    playerId = json['player_id'];
    isOnline = json['is_online'];
    isAvailable = json['is_available'];
    timezone = json['timezone'];
    fcmToken = json['fcm_token'];
    userDetail = json['user_detail'] != null
        ? new UserDetail.fromJson(json['user_detail'])
        : null;
    userBankAccount = json['user_bank_account'];
    serviceId = List<int>.from((json['service_id'] ?? []));
    driverService = json['driver_service'] != null
        ? new DriverService.fromJson(json['driver_service'])
        : null;
    isVerifiedDriver = json['is_verified_driver'];
    lastNotificationSeen = json['last_notification_seen'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['display_name'] = this.displayName;
    data['email'] = this.email;
    data['username'] = this.username;
    data['status'] = this.status;
    data['user_type'] = this.userType;
    data['address'] = this.address;
    data['contact_number'] = this.contactNumber;
    data['gender'] = this.gender;
    data['profile_image'] = this.profileImage;
    data['login_type'] = this.loginType;
    data['latitude'] = this.latitude;
    data['longitude'] = this.longitude;
    data['uid'] = this.uid;
    data['player_id'] = this.playerId;
    data['is_online'] = this.isOnline;
    data['is_available'] = this.isAvailable;
    data['timezone'] = this.timezone;
    data['fcm_token'] = this.fcmToken;
    if (this.userDetail != null) {
      data['user_detail'] = this.userDetail!.toJson();
    }
    data['user_bank_account'] = this.userBankAccount;
    data['service_id'] = this.serviceId;
    if (this.driverService != null) {
      data['driver_service'] = this.driverService!.toJson();
    }
    data['is_verified_driver'] = this.isVerifiedDriver;
    data['last_notification_seen'] = this.lastNotificationSeen;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;

    return data;
  }
}

class UserDetail {
  int? id;
  int? userId;
  String? carModel;
  String? carColor;
  String? carPlateNumber;
  String? carProductionYear;
  String? workAddress;
  String? homeAddress;
  String? workLatitude;
  String? workLongitude;
  String? homeLatitude;
  String? homeLongitude;
  String? createdAt;
  String? updatedAt;

  UserDetail({
    this.id,
    this.userId,
    this.carModel,
    this.carColor,
    this.carPlateNumber,
    this.carProductionYear,
    this.workAddress,
    this.homeAddress,
    this.workLatitude,
    this.workLongitude,
    this.homeLatitude,
    this.homeLongitude,
    this.createdAt,
    this.updatedAt,
  });

  UserDetail.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    carModel = json['car_model'];
    carColor = json['car_color'];
    carPlateNumber = json['car_plate_number'];
    carProductionYear = json['car_production_year'];
    workAddress = json['work_address'];
    homeAddress = json['home_address'];
    workLatitude = json['work_latitude'];
    workLongitude = json['work_longitude'];
    homeLatitude = json['home_latitude'];
    homeLongitude = json['home_longitude'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['car_model'] = this.carModel;
    data['car_color'] = this.carColor;
    data['car_plate_number'] = this.carPlateNumber;
    data['car_production_year'] = this.carProductionYear;
    data['work_address'] = this.workAddress;
    data['home_address'] = this.homeAddress;
    data['work_latitude'] = this.workLatitude;
    data['work_longitude'] = this.workLongitude;
    data['home_latitude'] = this.homeLatitude;
    data['home_longitude'] = this.homeLongitude;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class DriverService {
  int? id;
  String? name;
  int? regionId;
  int? capacity;
  num? baseFare;
  num? minimumFare;
  num? minimumDistance;
  num? perDistance;
  num? perMinuteDrive;
  num? perMinuteWait;
  num? waitingTimeLimit;
  num? cancellationFee;
  num? perMinutePriorCancel;
  num? perDistancePriorCancel;
  String? paymentMethod;
  String? commissionType;
  num? adminCommission;
  num? fleetCommission;
  num? status;
  String? createdAt;
  String? updatedAt;

  DriverService({
    this.id,
    this.name,
    this.regionId,
    this.capacity,
    this.baseFare,
    this.minimumFare,
    this.minimumDistance,
    this.perDistance,
    this.perMinuteDrive,
    this.perMinuteWait,
    this.waitingTimeLimit,
    this.cancellationFee,
    this.perMinutePriorCancel,
    this.perDistancePriorCancel,
    this.paymentMethod,
    this.commissionType,
    this.adminCommission,
    this.fleetCommission,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  DriverService.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    regionId = json['region_id'];
    capacity = json['capacity'];
    baseFare = json['base_fare'];
    minimumFare = json['minimum_fare'];
    minimumDistance = json['minimum_distance'];
    perDistance = json['per_distance'];
    perMinuteDrive = json['per_minute_drive'];
    perMinuteWait = json['per_minute_wait'];
    waitingTimeLimit = json['waiting_time_limit'];
    cancellationFee = json['cancellation_fee'];
    perMinutePriorCancel = json['per_minute_prior_cancel'];
    perDistancePriorCancel = json['per_distance_prior_cancel'];
    paymentMethod = json['payment_method'];
    commissionType = json['commission_type'];
    adminCommission = json['admin_commission'];
    fleetCommission = json['fleet_commission'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['region_id'] = this.regionId;
    data['capacity'] = this.capacity;
    data['base_fare'] = this.baseFare;
    data['minimum_fare'] = this.minimumFare;
    data['minimum_distance'] = this.minimumDistance;
    data['per_distance'] = this.perDistance;
    data['per_minute_drive'] = this.perMinuteDrive;
    data['per_minute_wait'] = this.perMinuteWait;
    data['waiting_time_limit'] = this.waitingTimeLimit;
    data['cancellation_fee'] = this.cancellationFee;
    data['per_minute_prior_cancel'] = this.perMinutePriorCancel;
    data['per_distance_prior_cancel'] = this.perDistancePriorCancel;
    data['payment_method'] = this.paymentMethod;
    data['commission_type'] = this.commissionType;
    data['admin_commission'] = this.adminCommission;
    data['fleet_commission'] = this.fleetCommission;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class Payment {
  int? id;
  int? rideRequestId;
  int? riderId;
  String? riderName;
  String? datetime;
  num? totalAmount;
  var receivedBy;
  num? adminCommission;
  num? fleetCommission;
  num? driverFee;
  int? driverTips;
  var driverCommission;
  var txnId;
  String? paymentType;
  String? paymentStatus;
  var transactionDetail;
  String? createdAt;
  String? updatedAt;

  Payment({
    this.id,
    this.rideRequestId,
    this.riderId,
    this.riderName,
    this.datetime,
    this.totalAmount,
    this.receivedBy,
    this.adminCommission,
    this.fleetCommission,
    this.driverFee,
    this.driverTips,
    this.driverCommission,
    this.txnId,
    this.paymentType,
    this.paymentStatus,
    this.transactionDetail,
    this.createdAt,
    this.updatedAt,
  });

  Payment.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    rideRequestId = json['ride_request_id'];
    riderId = json['rider_id'];
    riderName = json['rider_name'];
    datetime = json['datetime'];
    totalAmount = json['total_amount'];
    receivedBy = json['received_by'];
    adminCommission = json['admin_commission'];
    fleetCommission = json['fleet_commission'];
    driverFee = json['driver_fee'];
    driverTips = json['driver_tips'];
    driverCommission = json['driver_commission'];
    txnId = json['txn_id'];
    paymentType = json['payment_type'];
    paymentStatus = json['payment_status'];
    transactionDetail = json['transaction_detail'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['ride_request_id'] = this.rideRequestId;
    data['rider_id'] = this.riderId;
    data['rider_name'] = this.riderName;
    data['datetime'] = this.datetime;
    data['total_amount'] = this.totalAmount;
    data['received_by'] = this.receivedBy;
    data['admin_commission'] = this.adminCommission;
    data['fleet_commission'] = this.fleetCommission;
    data['driver_fee'] = this.driverFee;
    data['driver_tips'] = this.driverTips;
    data['driver_commission'] = this.driverCommission;
    data['txn_id'] = this.txnId;
    data['payment_type'] = this.paymentType;
    data['payment_status'] = this.paymentStatus;
    data['transaction_detail'] = this.transactionDetail;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class WaitingCharges {
  List<ChargesItem>? items;
  num? total_amount;
  num? waiting_charges;
  num? due_amount;
  num? refundable_amount;
  num? advanced_paid;

  WaitingCharges({
    this.items,
    this.total_amount,
    this.advanced_paid,
    this.due_amount,
    this.refundable_amount,
    this.waiting_charges,
  });

  factory WaitingCharges.fromJson(dynamic json) {
    return WaitingCharges(
      items: json['items'] == null
          ? null
          : (json['items'] as List<dynamic>)
              .map((e) => ChargesItem.fromJson(e))
              .toList(),
      total_amount: json['total_amount'],
      advanced_paid: json['advanced_paid'],
      due_amount: json['due_amount'],
      refundable_amount: json['refundable_amount'],
      waiting_charges: json['waiting_charges'],
    );
  }
}

class ChargesItem {
  String? title;
  num? amount;

  ChargesItem({this.amount, this.title});

  factory ChargesItem.fromJson(dynamic json) {
    return ChargesItem(
      amount: json['amount'],
      title: json['title'],
    );
  }
}
