import 'package:flutter/material.dart';


// Color   {
//   return IS_DARK_MODE_ON ? darkGreyColor: Colors.black;
// }

// Color getPrimaryTextColor() {
//   return IS_DARK_MODE_ON ? Color(0xff111111) : Colors.black;
// }

// Color getPrimaryInputBorderColor() {
//   return IS_DARK_MODE_ON ? Color(0xff111111) : Colors.black;
// }

// Color getPrimaryInputTextColor() {
//   return IS_DARK_MODE_ON ? Color(0xff111111) : Colors.black;
// }

// Color getPrimaryButtonTextColor() {
//   return IS_DARK_MODE_ON ? Colors.grey[300]! : Colors.white;
// }

// Color getPrimaryScaffoldColor() {
//   return IS_DARK_MODE_ON ? Color.fromARGB(255, 236, 236, 236) : Colors.white;
// }

// Color getBlackColor() {
//   return IS_DARK_MODE_ON ? Colors.black : Colors.white;
// }
// Color getDangeColor() {
//   return IS_DARK_MODE_ON ? Colors.black : dangerColor;
// }

// Color   {
//   return IS_DARK_MODE_ON ? Colors.white : Colors.black;
// }



// Color getRedColor() {
//   return IS_DARK_MODE_ON ? Colors.red : Colors.black;
// }

// Color getPinkColor() {
//   return !IS_DARK_MODE_ON ? Color.fromARGB(255, 249, 195, 199) : Colors.black;
// }
// Color getGreenColor() {
//   return !IS_DARK_MODE_ON ? Colors.green : Colors.black;
// }

// Color getTextFieldColor() {
//   return IS_DARK_MODE_ON
//       ? Colors.white
//       : Color.fromARGB(255, 49, 48, 48).withOpacity(.1);
// }

// Color getNewScheduleCardColor() {
//   return IS_DARK_MODE_ON ? Colors.black : Colors.white;
// }

// Color getTabColor() {
//   return IS_DARK_MODE_ON ? darkGreyColor : Colors.grey;
// }

// Color getTabIndicatorColor() {
//   return IS_DARK_MODE_ON ? Colors.grey : Colors.black;
// }

// Color getTabLableColor() {
//   return IS_DARK_MODE_ON ? Colors.black : Colors.white;
// }

// Color getTabUnselectedColor() {
//   return IS_DARK_MODE_ON ? Colors.white : Colors.black;
// }

// Color getDropDownFillColor() {
//   return IS_DARK_MODE_ON ? darkGreyColor : extraLightGreyColor;
// }

// Color getRatingStarColor() {
//   return IS_DARK_MODE_ON ? Colors.white : Colors.grey;
// }

// Color getDocumentVerifiedColor() {
//   return IS_DARK_MODE_ON ? Colors.grey : Colors.green[100]!.withOpacity(.5);
// }

// Color getDocumentNotVerifiedColor() {
//   return IS_DARK_MODE_ON ? darkGreyColor : Colors.green[100]!.withOpacity(.5);
// }

// Color getOtpTimerTextColor() {
//   return IS_DARK_MODE_ON ? Colors.white : dividerColor;
// }
// Color getOtpTimerTextColorWhentimerOff() {
//   return IS_DARK_MODE_ON ? Colors.white : Colors.black;
// }


// Color getOtpTimerBoxColor() {
//   return IS_DARK_MODE_ON ? darkGreyColor : extraLightGreyColor;
// }


// Color getAppButtonWidgetColor() {
//   return IS_DARK_MODE_ON ? Colors.grey : Colors.black;
// }

// Color getReportFillColor() {
//   return IS_DARK_MODE_ON ? Colors.grey : Colors.black;
// }
// Color getDarkgreycolorAndCementColor() {
//   return IS_DARK_MODE_ON ? Colors.grey : darkGreyColor;
// }
// Color getCementColorAndDarkgreycolor() {
//   return IS_DARK_MODE_ON ? darkGreyColor : Colors.grey;
// }


// Color colorSelector({required  Color darkModeColor, required Color lightmodeColor}){

//   return IS_DARK_MODE_ON?darkModeColor:lightmodeColor;
// }

// Color getDarkgreycolorAndExtraLightGreyColor({required  Color darkModeColor, required Color lightmodeColor}){

//   return IS_DARK_MODE_ON?darkModeColor:lightmodeColor;
// }




// Color borderColor = Color(0xFFEAEAEA);
// const scaffoldSecondaryDark = Color(0xFF1E1E1E);
// const scaffoldColorDark = Color(0xFF090909);
// const scaffoldColorLight = Colors.white;
// const appButtonColorDark = Color(0xFF282828);
// const dividerColor = Color.fromARGB(255, 178, 178, 178);
// const Colors.grey = Color(0xffc2c2c2);

// const textPrimaryColor = Color(0xFF2E3033);
// const textSecondaryColor = Color(0xFF757575);
// const viewLineColor = Color(0xFFEAEAEA);
// const extraLightGreyColor = Color.fromARGB(255, 241, 241, 241);
// const peachColor = Color.fromARGB(255, 255, 184, 184);
// const dangerColor=Color(0xfff8d7da);
// const darkGreyColor=Color(0xff1e1e1e);


// Color appBarBackgroundColorGlobal = Colors.white;
// Color appButtonBackgroundColorGlobal = Colors.white;
// Color defaultAppButtonTextColorGlobal = textPrimaryColorGlobal;
class AppColors{
  static Color primaryBlackColor = Colors.black;
    static Color primaryWhiteColor = Colors.white;

    static Color textfieldBorderBlackColor = Colors.black;

    static Color greenColor = Colors.green;
static Color primaryMustardColr=Color(0xffff9800);
static Color primaryTealColor= Color(0xff009688);


static Color blackColor(BuildContext context){
  return Theme.of(context).colorScheme.secondary;
}
static Color whiteColor(BuildContext context){
  return Theme.of(context).colorScheme.tertiary;
}
static Color primaryColor(BuildContext context){
  return Theme.of(context).colorScheme.primary;
}

}