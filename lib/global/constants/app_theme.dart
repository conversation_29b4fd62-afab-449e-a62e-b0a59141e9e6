import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/global/constants/constants.dart';

class AppTheme {
  static ThemeData lightTheme = FlexThemeData.light(
    fontFamily: GoogleFonts.lexend().fontFamily,
    scaffoldBackground: Colors.white,
    primaryContainer: Colors.white,
    useMaterial3: true,
    tertiary: Colors.white,
    secondary: Colors.black,
    dialogBackground: Colors.white,
    appBarBackground: AppColors.primaryMustardColr,
    primary: AppColors.primaryMustardColr,
    subThemesData: FlexSubThemesData(
        fabSchemeColor: SchemeColor.primary,
        inputDecoratorRadius: borderRadiusValue,
        inputDecoratorIsFilled: false,
        inputDecoratorFillColor: Colors.white,
        dialogRadius: 8,
        tabBarUnselectedItemSchemeColor: SchemeColor.secondary,
        tabBarItemSchemeColor: SchemeColor.primary,
        tabBarIndicatorSchemeColor: SchemeColor.primary,
        tabBarIndicatorWeight: 5,
        materialButtonSchemeColor: SchemeColor.primaryContainer),
  );

  static ThemeData darkTheme = FlexThemeData.dark(
    fontFamily: GoogleFonts.lexend().fontFamily,
    primaryContainer: Colors.black,
    scaffoldBackground: Colors.black,
    useMaterial3: true,
    error: Colors.red,
    tertiary: Colors.black,
    secondary: Colors.white,
    appBarBackground: AppColors.primaryTealColor,
    primary: AppColors.primaryTealColor,
    subThemesData: FlexSubThemesData(
        fabSchemeColor: SchemeColor.primary,
        inputDecoratorRadius: borderRadiusValue,
        alignedDropdown: true,
        inputDecoratorBorderSchemeColor: SchemeColor.primary,
        inputDecoratorBorderWidth: .2,
        inputDecoratorFocusedBorderWidth: 1,
        dialogRadius: 8,
        // inputDecoratorFillColor: Colors.white,
        tabBarUnselectedItemSchemeColor: SchemeColor.secondary,
        tabBarItemSchemeColor: SchemeColor.primary,
        tabBarIndicatorSchemeColor: SchemeColor.primary,
        tabBarIndicatorWeight: 5,
        materialButtonSchemeColor: SchemeColor.primaryContainer),
  );
}
