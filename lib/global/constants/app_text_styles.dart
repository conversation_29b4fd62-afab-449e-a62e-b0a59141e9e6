import 'package:flutter/material.dart';

class AppTextStyles {
//  static TextStyle header =TextStyle(fontWeight: FontWeight.bold,fontSize: 20);

//     static TextStyle subHeader =TextStyle(fontWeight: FontWeight.w500,fontSize: 20);

//   static TextStyle title =TextStyle(fontWeight: FontWeight.bold,fontSize: 15);
//     static TextStyle subTitle =TextStyle(fontWeight: FontWeight.w500,fontSize: 15);

//   static TextStyle text =TextStyle(fontWeight: FontWeight.bold,fontSize: 10);
  // static TextStyle subText =TextStyle(fontWeight: FontWeight.w500,fontSize: 10);

  static TextStyle header({Color? color}) {
    return TextStyle(fontWeight: FontWeight.bold, fontSize: 20, color: color);
  }

  static TextStyle title({Color? color}) {
    return TextStyle(fontWeight: FontWeight.bold, fontSize: 15, color: color);
  }

  static TextStyle subtitle({Color? color}) {
    return TextStyle(fontWeight: FontWeight.w500, fontSize: 15, color: color);
  }

  static TextStyle LowTitle({Color? color}) {
    return TextStyle(fontWeight: FontWeight.bold, fontSize: 12, color: color);
  }

  static TextStyle LowSubtitle({Color? color}) {
    return TextStyle(fontWeight: FontWeight.w500, fontSize: 12, color: color);
  }

  static TextStyle text({Color? color}) {
    return TextStyle(fontWeight: FontWeight.bold, fontSize: 10, color: color);
  }

  static TextStyle subHeader({Color? color}) {
    return TextStyle(fontWeight: FontWeight.w500, fontSize: 20, color: color);
  }

  static TextStyle subText({Color? color}) {
    return TextStyle(fontWeight: FontWeight.w500, fontSize: 10, color: color);
  }
}
