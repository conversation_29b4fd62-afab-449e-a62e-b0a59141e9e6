import 'package:flutter/material.dart';

SizedBox heightSpace1 = const SizedBox(
  height: 1,
);
SizedBox height2 = const SizedBox(
  height: 2,
);

SizedBox height4 = const SizedBox(
  height: 4,
);
SizedBox height6 = const SizedBox(
  height: 6,
);

SizedBox height8 = const SizedBox(
  height: 8,
);





SizedBox height10 = const SizedBox(
  height: 10,
);
SizedBox height12 = const SizedBox(
  height: 12,
);
SizedBox height14 = const SizedBox(
  height: 14,
);
SizedBox height16 = const SizedBox(
  height: 16,
);
SizedBox height18 = const SizedBox(
  height: 18,
);



SizedBox height20 = const SizedBox(height: 20);

//sizedboxes===width
SizedBox width1 = const SizedBox(
  width: 1,
);
SizedBox width2 = const SizedBox(
  width: 2,
);

SizedBox width4 = const SizedBox(
  width: 4,
);
SizedBox width6 = const SizedBox(
  width: 6,
);
SizedBox width8 = const SizedBox(
  width: 8,
);


SizedBox width10 = const SizedBox(
  width: 10,
);
SizedBox width12 = const SizedBox(
  width: 12,
);
SizedBox width14 = const SizedBox(
  width: 14,
);
SizedBox width16 = const SizedBox(
  width: 16,
);

SizedBox width18 = const SizedBox(
  width: 18,
);

SizedBox width20 = const SizedBox(
  width: 20,
);

