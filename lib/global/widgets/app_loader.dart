
import 'package:flutter/material.dart';
import 'package:rooo_driver/global/constants/Colors.dart';

class AppLoader extends StatelessWidget {
  final double? size;
  const AppLoader({super.key, this.size});
  
  get appRadius => null;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
      
        child: CircularProgressIndicator(
        strokeWidth: 4,
        color: AppColors.primaryColor(context),
        ),
      ),
    );
    
    
    
    // SpinKitWave(
    //   size: size ?? 60,
    //   itemBuilder: (BuildContext context, int index) {
    //     return DecoratedBox(
    //       child: LinearProgressIndicator(
    //         color: AppColors.primaryColor(context),
    //         backgroundColor: Colors.black,
    //       ),
    //       decoration: BoxDecoration(
    //         borderRadius: appRadius,
    //       ),
    //     );
    //   },
    // );
  }
}
