// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:google_map_marker_animation/widgets/animarker.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';

// class GoogleMapWidget extends StatelessWidget {
//   final bool state;
//   final bool is_polyline_created;
//   final Completer<GoogleMapController> completer;
//   final Set<Polyline> polyLines;
//   final LatLng initialCameraPosition;

//   final Map<MarkerId, Marker> markers;
//   const GoogleMapWidget(
//       {super.key,
//       required this.state,
//       required this.markers,
//       required this.polyLines,
//       required this.completer,
//       required this.is_polyline_created,
//       required this.initialCameraPosition});

//   @override
//   Widget build(BuildContext context) {
//     onMapCreated(GoogleMapController controller) {
//       completer.complete(controller);
//     }

//     return Stack(
//       alignment: Alignment.topCenter,
//       children: [
//         Animarker(
//           shouldAnimateCamera: false,
//           useRotation: false,
//           duration: Duration(milliseconds: 1000),
//           mapId: completer.future.then((value) => value.mapId),
//           markers: markers.values.toSet(),
//           child: GoogleMap(
            
//             zoomControlsEnabled: false,
//             compassEnabled: false,
//             myLocationEnabled: false,
//             onMapCreated: onMapCreated,
//             initialCameraPosition:
//                 CameraPosition(target: initialCameraPosition, zoom: 15),
//             mapType: MapType.normal,
//             polylines: polyLines,
//           ),
//         ),
//         state ? SizedBox() : LinearProgressIndicator()
//       ],
//     );
//   }
// }
