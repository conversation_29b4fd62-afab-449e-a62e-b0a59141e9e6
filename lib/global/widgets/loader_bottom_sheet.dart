
import 'package:flutter/material.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/global/constants/constants.dart';

class LoaderBottomSheet extends StatelessWidget {
  const LoaderBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
    height: MediaQuery.of(context).size.height,
    color: Colors.grey.withOpacity(.3),
    child: BottomSheet(
        onClosing: () {},
        builder: (context) {
          return Padding(
            padding: screenPadding,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                  data: "Loading....",
                ),
                SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: .5,
                    ))
              ],
            ),
          );
        }),
  );
  }
}



