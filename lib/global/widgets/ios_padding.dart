import 'dart:io';


import 'package:flutter/material.dart';
import 'package:rooo_driver/global/constants/constants.dart';

class IosPadding extends StatefulWidget {
  final Widget child;
  const IosPadding({required this.child});

  @override
  State<IosPadding> createState() => _IosPaddingState();
}

class _IosPaddingState extends State<IosPadding> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        screenPaddingValue,
        screenPaddingValue,
        screenPaddingValue,
        Platform.isIOS ? screenPaddingValue * 2 : screenPaddingValue,
      ),
      child: widget.child,
    );
  }
}
