import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class RooEmptyWidegt extends StatelessWidget {
  final String title;
  const RooEmptyWidegt({super.key,  required this.title});

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        height20,
        height20,
        height20,
        height20,
        Container(
          padding: screenPadding * 2,
          alignment: Alignment.bottomCenter,
          child: Column(
            children: [
              Lottie.asset("assets/lottie/empty.json"),
              height20,
              height20,
              height20,
              Text(title,style: AppTextStyles.header(color: AppColors.blackColor(context)),),
            ],
          )
        ),
      ],
    );
  }
}
