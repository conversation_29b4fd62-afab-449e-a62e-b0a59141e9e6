// import 'package:flutter/material.dart';
// import 'package:rooo_driver/global/models/on_ride_request_model.dart';
// import 'package:rooo_driver/global/models/ride_model.dart';
// import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';

// class ZegoCallButton extends StatelessWidget {
//   final OnRideRequest onRideRequest;

//   const ZegoCallButton({super.key, required this.onRideRequest});

//   @override
//   Widget build(BuildContext context) {
//     return ZegoSendCallInvitationButton(
//                                             // clickableBackgroundColor: Colors.red,
//                                             buttonSize: Size.square(70),
//                                             iconSize: Size.square(30),
//                                             borderRadius: 2,
//                                             verticalLayout: true,
//                                             text: 'Call',
//                                             iconTextSpacing: 5,
//                                             textStyle: TextStyle(fontSize: 16),
//                                             icon: ButtonIcon(
//                                               icon: Icon(
//                                                 Icons.call,
//                                               ),
//                                             ),
//                                             isVideoCall: false,

//                                             resourceID:
//                                                 "zego_voip_call3", // For offline call notification
//                                             invitees: [
//                                               ZegoUIKitUser(
//                                                 id: onRideRequest!.riderId.toString()   ,
//                                                 name: onRideRequest!.riderName!,
//                                               ),
//                                             ],
//                                           );
//   }
// }