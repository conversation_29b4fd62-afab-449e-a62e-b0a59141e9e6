import 'package:flutter/material.dart';
import 'package:rooo_driver/components/AlertScreen.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';
import 'package:rooo_driver/main.dart';

class RideSosButton extends StatelessWidget {
  final int ride_id;
  final int region_id;
  const RideSosButton(
      {super.key, required this.ride_id, required this.region_id});

  @override
  Widget build(BuildContext context) {
    return AppButton(
        text: language.sos,
        onPressed: ()  async {
          showDialog(
            context: context,
            builder: (context) {
              return Center(
                child: SizedBox(
                  height: MediaQuery.sizeOf(context).height - 200,
                  width: MediaQuery.sizeOf(context).width - 60,
                  child: AlertScreen(
                    rideId: ride_id,
                    regionId: region_id,
                  ),
                ),
              );
            },
          );
        });
  }
}
