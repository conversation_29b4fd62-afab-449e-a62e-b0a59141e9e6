
import 'package:rooo_driver/global/export/app_export.dart';

class IconContainer extends StatelessWidget {
  final Widget icon;
 final void Function()? onTap;
  const IconContainer({
    Key? key,
    required this.icon,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Card(
        shadowColor: AppColors.blackColor(context),
        color: AppColors.primaryColor(context),
        elevation: 2,
        child: Padding(
          padding: screenPadding/2.5,
          child: icon,
        ),
      ),
    );
  }
}
