
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/empty_widget.dart';

class ScreenBody extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final bool isEmpty;
  final String emptyMessage;
  final Future<void> Function()? onPullToRefresh;
  const ScreenBody({
    super.key,
    required this.isLoading,
    required this.child,
    required this.isEmpty,
    required this.emptyMessage,
    this.onPullToRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (onPullToRefresh == null) {
      return Stack(
        // alignment: Alignment.center,
        children: [
          ListView(),
          isEmpty
              ? Visibility(
                  visible: !isLoading,
                  child: RooEmptyWidegt(
                    title: emptyMessage,
                  ))
              : child,
          isLoading
              ? Container(
                  color: AppColors.whiteColor(context).withOpacity(.5),
                  width: double.infinity,
                  child: AppLoader(),
                )
              : SizedBox()
        ],
      );
    } else {
      return RefreshIndicator(
        onRefresh: onPullToRefresh!,
        child: Stack(
          // alignment: Alignment.center,
          children: [
            ListView(),
            isEmpty
                ? Visibility(
                    visible: !isLoading,
                    child: RooEmptyWidegt(
                      title:emptyMessage,
                    ))
                : child,
            isLoading
                ? Container(
                    color: AppColors.whiteColor(context).withOpacity(.5),
                    width: double.infinity,
                    child: AppLoader(),
                  )
                : SizedBox()
          ],
        ),
      );
    }
  }
}
