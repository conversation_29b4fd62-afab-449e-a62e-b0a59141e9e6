import 'package:flutter/material.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';

class RideCancelButton extends StatelessWidget {
  final void Function() onTap;

  const RideCancelButton({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Column(
        children: [
          SizedBox(
            height: 8,
          ),
          Icon(
            Icons.close,
            size: 28,
          ),
          SizedBox(height: 10),
          Text(language.cancelTxt, style: secondaryTextStyle()),
        ],
      ),
    );
  }
}
