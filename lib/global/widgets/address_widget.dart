import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/global/constants/spacer.dart';
import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/utils/Extensions/StringExtensions.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';

class AddressWidget extends StatelessWidget {
  final OnRideRequest onRideRequest;
  const AddressWidget({super.key, required this.onRideRequest});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
            if (onRideRequest.isAlreadyCancelled == true)
              Column(
                children: [
                  Row(
                    children: [Icon(Icons.taxi_alert_rounded,color: AppColors.primaryColor(context),),
                    width10,
                    
                      SizedBox(
                          width: MediaQuery.of(context).size.width * .7,
                          child: Text(
                              "This is cancelled ride, if you want to cancel this ride, please provide valid information")),
                    ],
                  ),
              
              Divider(),
                ],
              ),
       
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                 Icon(Icons.near_me, color: Colors.green, size: 18),
                SizedBox(height: 2),
                SizedBox(
                  height: 40,
                  child: DottedLine(
                    dashGapColor: AppColors.blackColor(context),
                    dashColor: AppColors.greenColor,
                    direction: Axis.vertical,
                    lineLength: double.infinity,
                    lineThickness: 1,
                    dashLength: 2,
                  ),
                ),
                SizedBox(height: 2),
                Icon(Icons.location_on, color: Colors.red, size: 18),
              ],
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 50,
                    child: Text(
                      onRideRequest.startAddress ?? ''.validate(),
                      style: primaryTextStyle(
                        size: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(height: 10),
                  Text(
                    onRideRequest.endAddress ?? '',
                    style: primaryTextStyle(
                      size: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
