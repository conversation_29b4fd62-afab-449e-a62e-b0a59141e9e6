import 'package:rooo_driver/features/ride_flow/model/multiple_stop_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class TestState{

  RideModel testRide=RideModel(onRideRequest:       
  OnRideRequest(
              otp: "1234",
              riderName: "Rider",
              status: IN_PROGRESS,
              reached_otp: "4567",
              driverName: "Driver",
              id: 88,
              regionId: 55,
              // destinationPlace: [],
              serviceId: 99,
              riderId: 77,
              riderProfileImage: "riderProfileImage",
              endLatitude: "31.3260",
              endLongitude: "75.5762",
              startLatitude: "30.9010",
              startLongitude: "75.8573",
              endAddress: "jalandhar",
              startAddress: "Ludhi<PERSON>",
              stops: [
            StopsModel(
              
              type: "stop",
                id:1,
                title: "goraya",
                stopLat: 31.1241,
                stopLng: 75.7713),
            StopsModel(
                id: 2,
                title: "phagwara",
                stopLat: 31.2232,
                stopLng: 75.7670),
            // Stops(
            //     id: 3, title: "lpu", stopLat: 31.2560, stopLng: 75.7051),
          ]));
}