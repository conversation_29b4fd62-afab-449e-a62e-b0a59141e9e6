import 'BaseLanguage.dart';

class LanguageHi extends BaseLanguage {
  @override
  String get appName => 'Driver';

  @override
  String get welcomeBack => 'वापसी पर स्वागत है !';

  @override
  String get signInYourAccount => 'अपने खाते में साइन इन करें';

  @override
  String get thisFieldRequired => 'यह फ़ील्ड आवश्यक है';

  @override
  String get email => 'ईमेल';

  @override
  String get password => 'पासवर्ड';

  @override
  String get forgotPassword => 'पासवर्ड भूल गए?';

  @override
  String get logIn => 'लॉग इन करें';

  @override
  String get orLogInWith => 'या इसके साथ लॉग इन करें';

  @override
  String get donHaveAnAccount => 'खाता नहीं है?';

  @override
  String get signUp => 'साइन अप करें';

  @override
  String get createAccount => 'खाता बनाएं';

  @override
  String get createYourAccountToContinue => 'जारी रखने के लिए अपना खाता बनाएं';

  @override
  String get firstName => 'पहला नाम';

  @override
  String get lastName => 'अंतिम नाम';

  @override
  String get userName => 'उपयोगकर्ता नाम';

  @override
  String get phoneNumber => 'फ़ोन नंबर';

  @override
  String get alreadyHaveAnAccount => 'पहले से ही एक खाता है?';

  @override
  String get contactUs => 'हमसे संपर्क करें';

  @override
  String get purchase => 'खरीदें';

  @override
  String get changePassword => 'पासवर्ड बदलें';

  @override
  String get oldPassword => 'पुराना पासवर्ड';

  @override
  String get newPassword => 'नया पासवर्ड';

  @override
  String get confirmPassword => 'पासवर्ड की पुष्टि करें';

  @override
  String get passwordDoesNotMatch => 'पासवर्ड मेल नहीं खाता';

  @override
  String get passwordInvalid => 'न्यूनतम पासवर्ड लंबाई 8 होनी चाहिए';

  @override
  String get yes => 'हाँ';

  @override
  String get no => 'नहीं';

  @override
  String get writeMessage => 'संदेश लिखें....';

  @override
  String get enterTheEmailAssociatedWithYourAccount =>
      'अपने खाते से संबंधित ईमेल दर्ज करें';

  @override
  String get submit => 'सबमिट करें';

  @override
  String get language => 'भाषा';

  @override
  String get notification => 'सूचना';

  @override
  String get otpVeriFiCation => 'ओटीपी सत्यापन';

  @override
  String get weHaveSentDigitCode => 'हमने एक 4 अंकों का कोड भेजा है';

  @override
  String get contactLength =>
      'संपर्क नंबर की लंबाई 10 या 12 अंकों की होनी चाहिए।';

  @override
  String get about => 'के बारे में';

  @override
  String get useInCaseOfEmergency => 'आपात स्थिति में उपयोग करें';

  @override
  String get notifyAdmin => 'प्रशासक को सूचित करें';

  @override
  String get notifiedSuccessfully => 'सफलतापूर्वक सूचित किया गया';

  @override
  String get complain => 'शिकायत';

  @override
  String get pleaseEnterSubject => 'कृपया विषय दर्ज करें';

  @override
  String get writeDescription => 'विवरण लिखें....';

  @override
  String get saveComplain => 'शिकायत सहेजें';

  @override
  String get editProfile => 'प्रोफ़ाइल संपादित करें';

  @override
  String get gender => 'लिंग';

  @override
  String get addressTxt => 'पता';

  @override
  String get updateProfile => 'प्रोफ़ाइल अपडेट करें';

  @override
  String get notChangeUsername =>
      'आप संपादित करें बटन पर क्लिक करके उपयोगकर्ता नाम बदल सकते हैं';

  @override
  String get notChangeEmail =>
      'आप संपादित करें बटन पर क्लिक करके ईमेल आईडी बदल सकते हैं';

  @override
  String get profileUpdateMsg => 'प्रोफ़ाइल सफलतापूर्वक अपडेट की गई';

  @override
  String get emergencyContact => 'आपातकालीन संपर्क';

  @override
  String get areYouSureYouWantDeleteThisNumber =>
      'क्या आप वाकई इस नंबर को हटाना चाहते हैं?';

  @override
  String get addContact => 'संपर्क जोड़ें';

  @override
  String get googleMap => 'गूगल मैप';

  @override
  String get save => 'सहेजें';

  @override
  String get myRides => 'सवारी';

  @override
  String get myWallet => 'मेरा वॉलेट';

  @override
  String get availableBalance => 'उपलब्ध शेष राशि';

  @override
  String get recentTransactions => 'हाल के लेन-देन';

  @override
  String get moneyDeposited => 'जमा की गई राशि';

  @override
  String get addMoney => 'पैसे जोड़ें';

  @override
  String get cancelTxt => 'रद्द करें';

  @override
  String get pleaseSelectAmount => 'कृपया राशि चुनें';

  @override
  String get amount => 'राशि';

  @override
  String get capacity => 'क्षमता';

  @override
  String get paymentMethod => 'भुगतान का तरीका';

  @override
  String get chooseYouPaymentLate => 'अभी या बाद में भुगतान करें चुनें';

  @override
  String get enterPromoCode => 'प्रोमो कोड दर्ज करें';

  @override
  String get confirm => 'पुष्टि करें';

  @override
  String get forInstantPayment => 'तत्काल भुगतान के लिए';

  @override
  String get bookNow => 'अभी बुक करें';

  @override
  String get wallet => 'वॉलेट';

  @override
  String get paymentDetail => 'भुगतान विवरण';

  @override
  String get rideId => 'सवारी आईडी';

  @override
  String get createdAt => 'पर बनाया गया';

  @override
  String get viewHistory => 'इतिहास देखें';

  @override
  String get paymentDetails => 'भुगतान विवरण';

  @override
  String get paymentType => 'भुगतान का प्रकार';

  @override
  String get paymentStatus => 'भुगतान की स्थिति';

  @override
  String get priceDetail => 'मूल्य विवरण';

  @override
  String get basePrice => 'मूल्य';

  @override
  String get distancePrice => 'दूरी की कीमत';

  @override
  String get timePrice => 'समय की कीमत';

  @override
  String get waitTime => 'प्रतीक्षा समय';

  @override
  String get extraCharges => 'अतिरिक्त शुल्क';

  @override
  String get couponDiscount => 'कूपन छूट';

  @override
  String get total => 'कुल';

  @override
  String get payment => 'भुगतान';

  @override
  String get cash => 'नकद';

  @override
  String get updatePaymentStatus => 'भुगतान की स्थिति अपडेट करें';

  @override
  String get waitingForDriverConformation =>
      'सवारी की पुष्टि की प्रतीक्षा कर रहा है';

  @override
  String get continueNewRide => 'नई सवारी जारी रखें';

  @override
  String get payToPayment => 'भुगतान करें';

  @override
  String get tip => 'टिप';

  @override
  String get pay => 'भुगतान करें';

  @override
  String get howWasYourRide => 'आपकी सवारी कैसी थी?';

  @override
  String get wouldYouLikeToAddTip => 'क्या आप टिप जोड़ना चाहेंगे?';

  @override
  String get addMoreTip => 'अधिक टिप जोड़ें';

  @override
  String get addMore => 'अधिक जोड़ें';

  @override
  String get addReviews => 'समीक्षाएँ जोड़ें';

  @override
  String get writeYourComments => 'अपनी समीक्षा लिखें....';

  @override
  String get continueD => 'जारी रखें';

  @override
  String get detailScreen => 'सवारी का विवरण';
  
  @override
  String get helpdetail => 'सहायता विवरण';

  @override
  String get aboutDriver => 'ड्राइवर के बारे में';

  @override
  String get rideHistory => 'सवारी का इतिहास';

  @override
  String get myProfile => 'मेरा प्रोफ़ाइल';

  @override
  String get myTrips => 'मेरी यात्राएँ';

  @override
  String get emergencyContacts => 'आपातकालीन संपर्क';

  @override
  String get logOut => 'लॉग आउट';

  @override
  String get areYouSureYouWantToLogoutThisApp =>
      'क्या आप वाकई इस ऐप से लॉग आउट करना चाहते हैं?';

  @override
  String get whatWouldYouLikeToGo => 'आप कहाँ जाना चाहेंगे?';

  @override
  String get enterYourDestination => 'अपना गंतव्य दर्ज करें';

  @override
  String get currentLocation => 'वर्तमान स्थान';

  @override
  String get destinationLocation => 'गंतव्य स्थान';

  @override
  String get chooseOnMap => 'मानचित्र पर चुनें';

  @override
  String get profile => 'प्रोफ़ाइल';

  @override
  String get theme => 'थीम';

  @override
  String get privacyPolicy => 'गोपनीयता नीति';

  @override
  String get helpSupport => 'सहायता और समर्थन';

  @override
  String get termsConditions => 'नियम और शर्तें';

  @override
  String get aboutUs => 'हमारे बारे में';

  @override
  String get lookingForNearbyDrivers => 'निकटतम ड्राइवरों की खोज कर रहे हैं';

  @override
  String get weAreLookingForNearDriversAcceptsYourRide =>
      'हम आपके सवारी को स्वीकार करने के लिए\nनिकटतम ड्राइवरों की खोज कर रहे हैं';

  @override
  String get areYouSureYouWantToCancelThisRide =>
      'क्या आप वाकई इस सवारी को रद्द करना चाहते हैं?';

  @override
  String get serviceDetail => 'सेवा विवरण';

  @override
  String get get => 'प्राप्त करें';

  @override
  String get rides => 'सवारी';

  @override
  String get people => 'लोग';

  @override
  String get fare => 'किराया';

  @override
  String get done => 'हो गया';

  @override
  String get availableOffers => 'उपलब्ध ऑफ़र';

  @override
  String get off => 'छूट';

  @override
  String get sendOTP => 'सत्यापन कोड भेजें';

  @override
  String get otpVerification => 'ओटीपी सत्यापन';

  @override
  String get enterTheCodeSendTo => 'भेजा गया कोड दर्ज करें';

  @override
  String get didNotReceiveTheCode => 'कोड प्राप्त नहीं हुआ';

  @override
  String get resend => 'फिर से भेजें';

  @override
  String get drivingExperience => 'ड्राइविंग अनुभव (वर्ष)';

  @override
  String get sos => ' एसओएस (S.O.S)';

  @override
  String get driverReview => 'ड्राइवर समीक्षा';

  @override
  String get signInUsingYourMobileNumber => 'अपने\nमोबाइल नंबर का उपयोग करके साइन इन करें';

  @override
  String get otp => 'सत्यापन कोड';

  @override
  String get newRideRequested => 'नई सवारी का अनुरोध किया गया';

  @override
  String get accepted => 'स्वीकृत';

  @override
  String get arriving => 'आ रहा है';

  @override
  String get arrived => 'पहुंच गया';

  @override
  String get inProgress => 'प्रगति में';

  @override
  String get cancelled => 'रद्द किया गया';

  @override
  String get completed => 'पूर्ण';

  @override
  String get pleaseEnableLocationPermission =>
      'कृपया स्थान की अनुमति सक्षम करें';

  @override
  String get pending => "लंबित";

  @override
  String get failed => "विफल";

  @override
  String get paid => "भुगतान किया";

  @override
  String get male => "पुरुष";

  @override
  String get female => "महिला";

  @override
  String get other => "अन्य";

  @override
  String get addExtraCharges => "अतिरिक्त शुल्क जोड़ें";

  @override
  String get enterAmount => "राशि दर्ज करें";

  @override
  String get pleaseAddedAmount => "कृपया राशि जोड़ें";

  @override
  String get title => "शीर्षक";

  @override
  String get charges => "शुल्क";
  @override
String get saveCharges => "चार्जेस बचाएं";

@override
String get bankDetail => "बैंक विवरण";

@override
String get bankName => "बैंक का नाम";

@override
String get bankCode => "बैंक कोड";

@override
String get accountHolderName => "खाता धारक का नाम";

@override
String get accountNumber => "खाता संख्या";

@override
String get updateBankDetail => "बैंक विवरण अपडेट करें";

@override
String get addBankDetail => "बैंक विवरण जोड़ें";

@override
String get bankInfoUpdateSuccessfully => "बैंक जानकारी सफलतापूर्वक अपडेट हो गई";

@override
String get vehicleDetail => "वाहन विवरण";

@override
String get document => "दस्तावेज़";

@override
String get setting => "सेटिंग्स";

@override
String get youAreOnlineNow => "आप अब ऑनलाइन हैं";

@override
String get youAreOfflineNow => "आप अब ऑफ़लाइन हैं";

@override
String get requests => "अनुरोध";

@override
String get areYouSureYouWantToCancelThisRequest => "क्या आप निश्चित हैं कि आप इस अनुरोध को रद्द करना चाहते हैं?";

@override
String get decline => "अस्वीकृत";

@override
String get accept => "स्वीकृत";

@override
String get areYouSureYouWantToAcceptThisRequest => "क्या आप सुनिश्चित हैं कि आप इस अनुरोध को स्वीकार करना चाहते हैं?";

@override
String get call => "कॉल";

@override
String get chat => "चैट";

@override
String get applyExtraFree => "अतिरिक्त शुल्क लागू करें?";

@override
String get areYouSureYouWantToArriving => "क्या आप सुनिश्चित हैं कि आप आ रहे हैं?";

@override
String get areYouSureYouWantToArrived => "क्या आप सुनिश्चित हैं कि आप पहुँच गए हैं?";

@override
String get enterOtp => "सत्यापन कोड दर्ज करें";

@override
String get enterTheOtpDisplayInCustomersMobileToStartTheRide => "यात्री के मोबाइल पर प्रदर्शित सत्यापन कोड दर्ज करें ताकि राइड शुरू हो सके";

@override
String get enterTheOtpDisplayInCustomersMobileToEndTheRide => "यात्री के मोबाइल पर प्रदर्शित सत्यापन कोड दर्ज करें ताकि राइड समाप्त हो सके";

@override
String get pleaseEnterValidOtp => "कृपया मान्य सत्यापन कोड दर्ज करें";

@override
String get areYouSureYouWantToCompletedThisRide => "क्या आप सुनिश्चित हैं कि आप इस राइड को पूरा करना चाहते हैं?";

@override
String get updateBankInfo => "बैंक जानकारी अपडेट करें";

@override
String get regisTRation => "पंजीकरण";

@override
String get pleaseSelectVehiclePreferences => "कृपया वाहन प्राथमिकताएँ चुनें";

@override
String get userDetail => "चौफर विवरण";

@override
String get selectVehiclePreferences => "वाहन प्राथमिकताएँ चुनें";

@override
String get selectGender => "लिंग चुनें";

@override
String get age => "उम्र";

@override
String get socialSecurityNumber => "सामाजिक सुरक्षा संख्या";

@override
String get nightDrivingPreference => "रात की ड्राइविंग प्राथमिकता";

@override
String get withDraw => "निकासी";

@override
String get withdrawHistory => "निकासी इतिहास";

@override
String get approved => "मंजूर";

@override
String get requested => "अनुरोध किया";

@override
String get updateVehicle => "वाहन अपडेट करें";

@override
String get userNotApproveMsg => "आपका प्रोफाइल समीक्षा के तहत है। कृपया कुछ समय प्रतीक्षा करें या अपने व्यवस्थापक से संपर्क करें।";

@override
String get uploadFileConfirmationMsg => "क्या आप सुनिश्चित हैं कि आप इस फ़ाइल को अपलोड करना चाहते हैं?";

@override
String get selectDocument => "दस्तावेज़ चुनें";

@override
String get addDocument => "दस्तावेज़ जोड़ें";

@override
String get areYouSureYouWantToDeleteThisDocument => "क्या आप सुनिश्चित हैं कि आप इस दस्तावेज़ को हटाना चाहते हैं?";

@override
String get expireDate => "समाप्ति तिथि";

@override
String get goDashBoard => "डैशबोर्ड पर जाएं";

@override
String get deleteAccount => "खाता हटाएँ";

@override
String get account => "खाता";

@override
String get areYouSureYouWantPleaseReadAffect => "क्या आप सुनिश्चित हैं कि आप अपना खाता हटाना चाहते हैं? कृपया पढ़ें कि खाता हटाने से क्या असर होगा।";

@override
String get deletingAccountEmail => "अपना खाता हटाने से व्यक्तिगत जानकारी हमारे डेटाबेस से हटा दी जाएगी। आपका ईमेल स्थायी रूप से आरक्षित हो जाएगा और उसी ईमेल का उपयोग करके नया खाता पंजीकृत नहीं किया जा सकेगा।";

@override
String get areYouSureYouWantDeleteAccount => "क्या आप सुनिश्चित हैं कि आप खाता हटाना चाहते हैं?";

@override
String get yourInternetIsNotWorking => "आपका इंटरनेट काम नहीं कर रहा";

@override
String get allow => "अनुमति दें";

@override
String get mostReliableMightyDriverApp => "सबसे विश्वसनीय चौफर ड्राइवर ऐप";

@override
String get toEnjoyYourRideExperiencePleaseAllowPermissions => "अपना राइड अनुभव का आनंद लेने के लिए कृपया हमें निम्न अनुमतियाँ दें";

@override
String get cashCollected => "नकद एकत्रित";

@override
String get areYouSureCollectThisPayment => "क्या आप सुनिश्चित हैं कि आप यह भुगतान एकत्रित करना चाहते हैं?";

@override
String get txtURLEmpty => "URL खाली है";

@override
String get lblFollowUs => "हमें फॉलो करें";

@override
String get bankInfo => "बैंक जानकारी";

@override
String get duration => "अवधि";

@override
String get paymentVia => "भुगतान माध्यम";

@override
String get moneyDebit => "धन डेबिट";

@override
String get roooCare => "चौफर देखभाल";

@override
String get demoMsg => "टेस्टर भूमिका को यह क्रिया करने की अनुमति नहीं है";

@override
String get youCannotChangePhoneNumber => "आप फ़ोन नंबर बदल सकते हैं, संपादित बटन पर क्लिक करके";

@override
String get offLine => "ऑफ़लाइन";

@override
String get online => "ऑनलाइन";

@override
String get walletLessAmountMsg => "आप यात्रा नहीं कर सकते क्योंकि आपके वॉलेट में पर्याप्त पैसा नहीं है। इसलिए आपको अपने वॉलेट में पैसे जोड़ने होंगे ताकि आप बाद में यात्रा कर सकें।";

@override
String get aboutRider => "राइडर के बारे में";

@override
String get pleaseEnterMessage => "कृपया संदेश दर्ज करें";

@override
String get complainList => "शिकायत सूची";

@override
String get viewAll => "सभी देखें";

@override
String get pleaseSelectRating => "कृपया रेटिंग चुनें";

@override
String get serviceInfo => "सेवा जानकारी";

@override
String get youCannotChangeService => "आप सेवा नहीं बदल सकते";

@override
String get vehicleInfoUpdateSucessfully => "वाहन जानकारी सफलतापूर्वक अपडेट हो गई";

@override
String get subscription => "सदस्यता";

@override
String get yourCurrentBalanceIs => "आपका वर्तमान बैलेंस है:";

@override
String get yourSubscriptionPlanIsOver => "आपकी सदस्यता योजना समाप्त हो गई है। कृपया इस एप्लिकेशन का उपयोग करने के लिए सदस्यता लें";

@override
String get perDay => "प्रति दिन";

@override
String get renew => "पुनः नवीकरण";

@override
String get yourWalletDoNotHaveEnoughBalance => "आपके वॉलेट में पर्याप्त बैलेंस नहीं है। कृपया वॉलेट में बैलेंस जोड़ें और फिर अपनी सदस्यता योजना का नवीकरण करें।";

@override
String get addWallet => "वॉलेट जोड़ें";

@override
String get yourDailyAppUseLimitHasBeenExpired => "आपकी दैनिक ऐप उपयोग सीमा समाप्त हो गई है। रिचार्ज करने और ऐप का उपयोग जारी रखने के लिए टैप करें।";

@override
String get recharge => "रिचार्ज";

@override
String get isMandatoryDocument => "* अनिवार्य दस्तावेज़ है।";

@override
String get someRequiredDocumentAreNotUploaded => "कुछ आवश्यक दस्तावेज़ अपलोड नहीं किए गए हैं। कृपया सभी आवश्यक दस्तावेज़ अपलोड करें।";

@override
String get areYouCertainOffline => "क्या आप निश्चित हैं कि आप ऑफ़लाइन जाना चाहते हैं?";

@override
String get areYouCertainOnline => "क्या आप निश्चित हैं कि आप ऑनलाइन जाना चाहते हैं?";

@override
String get pleaseAcceptTermsOfServicePrivacyPolicy => "कृपया सेवा की शर्तों और गोपनीयता नीति को स्वीकार करें";

@override
String get rememberMe => "मुझे याद रखें";

@override
String get agreeToThe => "मैं सहमत हूँ";

@override
String get invoice => "चालान";

@override
String get riderInformation => "राइडर की जानकारी";

@override
String get customerName => "ग्राहक का नाम";

@override
String get sourceLocation => "स्रोत स्थान";

@override
String get invoiceNo => "चालान संख्या";

@override
String get invoiceDate => "चालान की तारीख";

@override
String get orderedDate => "आदेश की तारीख";

@override
String get totalCash => "कुल नकद";

@override
String get totalRide => "कुल राइड राशि";

@override
String get totalWallet => "कुल वॉलेट";

@override
String get totalEarning => "कुल कमाई";

@override
String get pleaseSelectFromDateAndToDate => "कृपया प्रारंभ तिथि और समाप्ति तिथि चुनें";

@override
String get from => "से";

@override
String get fromDate => "प्रारंभ तिथि";

@override
String get to => "तक";

@override
String get toDate => "समाप्ति तिथि";

@override
String get ride => "सवारी";

@override
String get todayRide => "आज की सवारी";

@override
String get weeklyOrderCount => "साप्ताहिक आदेश संख्या";

@override
String get distance => "दूरी";

@override
String get rideInformation => "सवारी की जानकारी";

@override
String get iAgreeToThe => "मैं सहमत हूँ";

@override
String get today => "आज";

@override
String get weekly => "साप्ताहिक";

@override
String get report => "रिपोर्ट";

@override
String get earning => "कमाई";

@override
String get todayEarning => "आज की कमाई";

@override
String get available => "उपलब्ध";

@override
String get notAvailable => "उपलब्ध नहीं";

@override
String get youWillReceiveNewRidersAndNotifications => "आपको नए राइडर और सूचनाएं प्राप्त होंगी";

@override
String get youWillNotReceiveNewRidersAndNotifications => "आपको नए राइडर और सूचनाएं प्राप्त नहीं होंगी";

@override
String get yourAccountIs => "आपका खाता है";

@override
String get pleaseContactSystemAdministrator => "कृपया सिस्टम व्यवस्थापक से संपर्क करें";

@override
String get youCanNotThisActionsPerformBecauseYourCurrentRideIsNotCompleted => "आप यह क्रियाएं नहीं कर सकते, क्योंकि आपकी वर्तमान सवारी पूरी नहीं हुई है";

@override
String get selectDisplayPicture => "प्रोफाइल चित्र चुनें";

@override
String get invalidAge => "उम्र 18 वर्ष और उससे अधिक होनी चाहिए";

@override
String get go => "जाएं";

@override
String get inbox => "इनबॉक्स";

@override
String get referralCompleted => "लक्ष्य पूरे हुए";

@override
String get referralEarning => "आपका बना हुआ";

@override
String get continueText => "जारी रखें";

@override
String get enterYourMobileNumber => "अपना मोबाइल नंबर दर्ज करें";

@override
String get orText => "या";

@override
String get signInWithFacebook => "फेसबुक से साइन इन करें";

@override
String get signInWithGoogle => "गूगल से साइन इन करें";

@override
String get inBoxTxt => "इनबॉक्स";

@override
String get referralsTxt => "संदर्भ";

@override
String get opportunityTxt => "अवसर";

@override
String get accountTxt => "खाता";

@override
String get earningTxt => "कमाई";

@override
String get helpTxt => "सहायता";

@override
String get learningCenterTxt => "लर्निंग सेंटर";

@override
String get completedTxt => "पूरा हुआ";

@override
String get faqsTxt => "अक्सर पूछे जाने वाले प्रश्न";

@override
String get settingTxt => "सेटिंग्स";

@override
String get nightModeTxt => "रात मोड";

@override
String get helpWithTripTxt => "यात्रा में सहायता";

@override
String get verifyOTP => "सत्यापित करें";

@override
String get verifyOTPHeading => "आपको भेजा गया 6 अंकों का कोड दर्ज करें";

@override
String get enterOTP => "सत्यापन कोड दर्ज करें";

@override
String get invalidOTP => "अमान्य सत्यापन कोड";

@override
String get referralCode => "संदर्भ कोड";
@override
String get allMessageTxt => 'सभी संदेश';

@override
String get bankInfoTxt => 'बैंक जानकारी';
@override
String get offerTxt => 'ऑफर';
@override
String get statusTxt => 'स्थिति';

@override
String get balanceTxt => 'बैलेंस';
@override
String get paymentScheduledTxt => 'भुगतान अनुसूचित';
@override
String get paymentMethodsTxt => 'भुगतान विधियाँ';

@override
String get cashOutTxt => 'कैश आउट';

@override
String get walletTxt => 'वॉलेट';

@override
String get verifyPhoneMsg => 'कृपया अपने फोन की पुष्टि करें';

@override
String get errorMsg => 'कुछ गलत हो गया';

@override
String get SorryYouDontHaveAnyOpportunitiesTxt => 'माफ़ करें, आपके पास कोई अवसर नहीं हैं';
@override
String get NewOpportunitiesAreComingTxt => 'नए अवसर आ रहे हैं';

@override
String get stayTunedTxt => 'जुड़े रहें';
@override
String get roooCareTxt => 'चौफर देखभाल';
@override
String get NotificationsTxt => 'सूचनाएँ';
@override
String get WeeklyOrderCountTxt => 'साप्ताहिक आदेश गणना';
@override
String get cashoutTxt => 'कैशआउट';
@override
String get TotalCashTxt => 'कुल नकद';
@override
String get TotalWalletTxt => 'कुल वॉलेट';
@override
String get TotalRideTxt => 'कुल राइड';
@override
String get adminNotifiedTxt => 'एडमिन को सूचित किया गया';

@override
String get YouAreOnlineNowTxt => 'आप अब ऑनलाइन हैं';

@override
String get AreYouSureYouWantToNotifyAdminTxt => 'क्या आप सुनिश्चित हैं कि आप एडमिन को सूचित करना चाहते हैं';

@override
String get PleaseWaitForTimerTxt => 'कृपया टाइमर का इंतजार करें';

@override
String get NotifiedAdminTxt => 'एडमिन को सूचित किया गया';

@override
String get BookYourroooIn3EasyStepsTxt => '3 आसान चरणों में अपना चौफर बुक करें';

@override
String get NowBookingYourroooIsEasyTxt => 'अब अपना चौफर बुक करना आसान है!';

@override
String get PleaseUploadAllTheDocumentsAndWaitForVerificationTxt => 'कृपया सभी दस्तावेज़ अपलोड करें और पुष्टि का इंतजार करें';

@override
String get OkTxt => 'ठीक है';

@override
String get goOfflineTxt => 'ऑफ़लाइन जाएँ';

@override
String get clickCarPhotographsTxt => 'कार की फ़ोटो क्लिक करें';

@override
String get frontTxt => 'फ्रंट';
@override
String get backTxt => 'बैक';
@override
String get leftTxt => 'लेफ्ट';
@override
String get rightTxt => 'राइट';

@override
String get WelcomeTxt => 'स्वागत है';

@override
String get UserTxt => 'उपयोगकर्ता';

@override
String get pleaseUploadImageProperlyTxt => 'कृपया सभी चित्र अपलोड करें';

@override
String get IhaventRecievedACodeTxt => 'मुझे कोड नहीं मिला';
@override
String get CONTINUETxt => 'जारी रखें';
@override
String get NotUploadedTxt => 'अपलोड नहीं किया गया';
@override
String get CompletedTxt => 'पूर्ण';
@override
String get rejectedTxt => 'अस्वीकृत';
@override
String get pendingTxt => 'लंबित';
@override
String get resendOTPTxt => 'पुनः सत्यापन कोड भेजें';
@override
String get uploadTxt => 'अपलोड';

@override
String get accountHolderNameTxt => 'खाता धारक का नाम (जैसा कि आपके बैंक स्टेटमेंट पर दिखता है)';
@override
String get pleaseEnterTheBenificieryNameTxt => 'कृपया बैंक खाते पर लाभार्थी का नाम दर्ज करें, यदि खाता किसी व्यवसाय के नाम पर पंजीकृत है, तो कृपया खाते पर व्यवसाय का नाम दर्ज करें';
@override
String get PleaseEnterTheBeneficieryAddressOnABankAccountTxt => 'कृपया बैंक खाते पर लाभार्थी का पता दर्ज करें';
@override
String get cityTxt => 'शहर';
@override
String get PleaseEnterYourCityTxt => 'कृपया अपना शहर दर्ज करें';
@override
String get postalTxt => 'डाक कोड';
@override
String get PleaseEnterTheBeneficieryPostalCodeTxt => 'कृपया बैंक खाते पर लाभार्थी का डाक कोड दर्ज करें, यदि खाता किसी व्यवसाय के नाम पर पंजीकृत है, तो कृपया खाते पर व्यवसाय का डाक कोड दर्ज करें';
@override
String get DateOfBirthTxt => 'जन्म तिथि';
@override
String get BankNameTxt => 'बैंक का नाम';
@override
String get BankameOthersTxt => 'बैंक का नाम (अन्य)';
@override
String get BankOfNovoScotioTxt => 'उदाहरण: बैंक ऑफ नोवा स्कोटिया (BNS)';
@override
String get InstituitionNumberTxt => 'संस्थान संख्या';
@override
String get InstitutionNumberMustContainAtLeastDdigitsTxt => 'संस्थान संख्या में कम से कम 3 अंक होने चाहिए';
@override
String get TransitNumberTxt => 'ट्रांजिट संख्या';
@override
String get TransitNumberMustContainAtLeastDigitsTxt => 'ट्रांजिट संख्या में कम से कम 5 अंक होने चाहिए';
@override
String get BankAccountNumberTxt => 'बैंक खाता संख्या';
@override
String get AccountNumberMustContainAtLeastDigitsTxt => 'खाता संख्या में कम से कम 7 अंक होने चाहिए';
@override
String get FillCorrectAccountNumberTxt => 'सही खाता संख्या भरें';
@override
String get ReEnterBankAccountNumberTxt => 'बैंक खाता संख्या फिर से दर्ज करें';
@override
String get PleaseReEnterYourAccountNumberTxt => 'कृपया अपनी खाता संख्या फिर से दर्ज करें ताकि हम आपकी भरी हुई खाता संख्या की पुष्टि कर सकें';
@override
String get TermsTxt => 'नियम';
@override
String get ShareYourLinkTxt => 'अपना लिंक साझा करें';
@override
String get ReEnteredAccountIsWrongTxt => 'फिर से दर्ज की गई खाता संख्या गलत है';

@override
String get waitingTime => 'प्रतीक्षा समय';

@override
String get waitingTimeOverTxt => 'प्रतीक्षा समय समाप्त';

@override
String get cancelTimerTxt => 'समय समाप्त करें';

@override
String get wouldYouLikeToStartIdleTimeTxt => 'क्या आप निष्क्रिय समय शुरू करना चाहेंगे';

@override
String get aboutUsTxt => 'चौफर: आपका भरोसेमंद यात्रा साथी। हम आपको उच्च गुणवत्ता के ड्राइवरों से जोड़ते हैं ताकि आपकी यात्रा निर्बाध और सुरक्षित हो सके। आपका आराम, हमारी प्राथमिकता';

@override
String get referralTxt => 'संदर्भ';

@override
String get promotionsTxt => 'प्रचार';

@override
String get inviteTxt => 'निमंत्रण';

@override
String get inviteFromContactsTxt => 'संपर्कों से निमंत्रण';
@override
String get termsApplyTxt => 'नियम लागू होते हैं';

@override
String get totalReferralsTxt => 'कुल संदर्भ';

@override
String get closedTxt => 'बंद';

@override
String get AdminalreadynotifiedTxt => 'एडमिन को पहले ही सूचित किया गया है';

@override
String get PleasewaitTxt => 'कृपया प्रतीक्षा करें';
@override
String get NotifyToAdminAboutLateRideTxt => 'लेट राइड के बारे में एडमिन को सूचित करें';
@override
String get YouHaveReachedTheDestinationTxt => 'क्या आप गंतव्य पर पहुँच गए हैं';
@override
String get upload4ImagesWarningTxt => 'कृपया चित्रों को चित्रित के अनुसार अपलोड करें, अन्यथा आपका खाता अस्थायी रूप से अक्षम हो सकता है या स्थायी रूप से प्रतिबंधित किया जा सकता है';
@override
String get CodecopiedTxt => 'कोड कॉपी किया गया';
@override
String get RideAcceptedTxt => 'राइड स्वीकार कर ली गई';

@override
String get PostalCodeShouldHave6DigitTxt => 'डाक कोड में 6 अंक होने चाहिए';
@override
String get LowBalanceTxt => 'कम बैलेंस';
@override
String get automaticText => 'स्वचालित';
@override
String get timeOfDayTxt => 'दिन का समय';
@override
String get alwaysOnTxt => 'हमेशा चालू';
@override
String get AlwaysOffTxt => 'हमेशा बंद';
@override
String get UsePhoneSettingsTxt => 'फोन सेटिंग्स का उपयोग करें';
@override
String get PleaseEnterValidDetailsTxt => 'कृपया मान्य विवरण दर्ज करें';
@override
String get AddTxt => 'जोड़ें';
@override
String get messageTxt => 'संदेश';

@override
String get PleaseEnterMobileNumberTxt => 'कृपया मोबाइल नंबर दर्ज करें';
@override
String get InvalidMobileNumberTxt => 'अमान्य मोबाइल नंबर';
@override
String get ServerErrorTxt => 'सर्वर त्रुटि';

@override
String get Message => 'संदेश';

@override
String get OpportunityDetailTxt => 'अवसर विवरण';
@override
String get DistanceTxt => 'दूरी';
@override
String get TimeTxt => 'समय';
@override
String get PickupLocationTxt => 'पिकअप स्थान';

@override
String get passCode => 'पासकोड';
@override
String get TakePhoto => 'फोटो लें';

@override
String get CopyrightText => 'कॉपीराइट';

@override
String get roooTxt => 'चौफर';

@override
String get roooDriverTxt => 'चौफर चालक';

@override
String get ChangeEmailTxt => 'ईमेल बदलें';

@override
String get NewEmailTxt => 'नया ईमेल';

@override
String get EditTxt => 'संपादित करें';

@override
String get PleaseEnterValidNameTxt => 'कृपया मान्य नाम दर्ज करें';

@override
String get RequireAdminPermission => 'एडमिन अनुमति की आवश्यकता है';

@override
String get UnverifiedEmailTxt => 'अविवेचित ईमेल';

@override
String get VerifiedEmailTxt => 'पुष्ट ईमेल';

@override
String get RequestChangeTxt => 'बदलाव का अनुरोध करें';

@override
String get ChangeMobileTxt => 'मोबाइल बदलें';

@override
String get MobileNumberTxt => 'मोबाइल नंबर';

@override
String get optionalTxt => 'वैकल्पिक';

@override
String get NoResultsFoundTxt => 'कोई परिणाम नहीं मिला';

@override
String get PleaseTryDifferentKeywordTxt => 'कृपया अलग कीवर्ड का प्रयास करें';

@override
String get ManageProfileTxt => 'प्रोफ़ाइल';

@override
String get NumberAlreadyExistPleaseEnterOrUpdateNewNumbertxt => 'नंबर पहले से मौजूद है, कृपया नया नंबर दर्ज करें या अपडेट करें।';

@override
String get WalletHistoryTxt => 'वॉलेट इतिहास';
@override
String get DescriptionTxt => 'विवरण';
@override
String get ClickAProperProfilePictureWithYourFaceClearAndVisibleProfilePictureCantBeChangedOnceUploadedTxt => 'कृपया एक उचित प्रोफ़ाइल चित्र क्लिक करें जिसमें आपका चेहरा स्पष्ट और दृश्यमान हो, प्रोफ़ाइल चित्र एक बार अपलोड करने के बाद बदला नहीं जा सकता।';

@override
String get PleaseUploadImagesAsIllustratedOtherwiseYourAccountWillBeTemporarilyDisabledOrThereMightBeAPermanentBanText => 'कृपया चित्रों को चित्रित के अनुसार अपलोड करें, अन्यथा आपका खाता अस्थायी रूप से अक्षम हो सकता है या स्थायी रूप से प्रतिबंधित किया जा सकता है।';

@override
String get enterthePasscodewhichIsDisplayingInTheCustomersMobile => 'कृपया पासकोड दर्ज करें जो ग्राहक के मोबाइल पर प्रदर्शित हो रहा है';

@override
String get requiredTxt => 'आवश्यक';

@override
String get startNavigationText => 'नेविगेशन शुरू करें';

@override
String get signInWithApple => 'एप्पल के साथ साइन इन करें';

@override
String get locationDeniedText1 => 'स्थान अनुमति "समय के लिए अनुमति दें" ऐप की कार्यक्षमता के लिए आवश्यक है। इस स्थान अनुमति के बिना, नई राइड बुकिंग और वर्तमान राइड कार्यक्षमता काम नहीं करेगी।';

@override
String get locationDeniedText2 => 'आप अनुमति बदल सकते हैं "समय के लिए अनुमति दें" सेटिंग्स से';

@override
String get deleteText => 'हटाएँ';

@override
String get updateText => 'अपडेट करें';

@override
String get retryText => 'फिर से प्रयास करें';

@override
String get AreYouSureWantToPerformThisAction => 'क्या आप सुनिश्चित हैं कि आप यह क्रिया करना चाहते हैं?';

@override
String get DoYouWantToDelete => 'क्या आप हटाना चाहते हैं?';

@override
String get DoYouWantToUpdate => 'क्या आप अपडेट करना चाहते हैं?';

@override
String get DoYouWantToAdd => 'क्या आप जोड़ना चाहते हैं?';

@override
String get DoYouWantToAccept => 'क्या आप स्वीकार करना चाहते हैं?';

@override
String get ClickToRetry => 'फिर से प्रयास करने के लिए क्लिक करें';
@override
String get NewTripText => 'नई यात्रा';
@override
String get AcceptedTripText => 'स्वीकृत यात्रा';
@override
String get adminDidNotApproved => 'एडमिन ने आपकी अनुरोध को अनुमोदित नहीं किया कृपया यात्रा जारी रखें';

@override
String get ImageUploadedSuccessfullyText => 'चित्र सफलतापूर्वक अपलोड किया गया';
@override
String get OKText => 'ठीक है';
@override
String get NotifyAdminAboutLateRiderText => 'लेट राइडर के बारे में एडमिन को सूचित करें';
@override
String get AdminNotifiedText => 'एडमिन को सूचित किया गया';
@override
String get CallText => 'कॉल';
@override
String get StartRideText => 'यात्रा शुरू करें';
@override
String get GetVerificationCodeText => 'सत्यापन कोड दर्ज करें';
@override
String get YouRequestedForRideCancellationText => 'आपने यात्रा रद्द करने का अनुरोध किया';
@override
String get RideCanceledText => 'यात्रा रद्द कर दी गई';

@override
String get AdminApproveYourRequestPleaseStayOnlineText => 'एडमिन ने आपकी अनुरोध को अनुमोदित कर दिया कृपया ऑनलाइन रहें';
@override
String get YouHaveAlreadyOneRideDriverSafelyText => 'आपके पास पहले से ही एक यात्रा है, \nसुरक्षित ड्राइवर';
@override
String get YouForgotToReviewPleaseGiveYourReviewAndFeedbackAboutRideText => 'आपने समीक्षा करना भूल गए। \nकृपया अपनी यात्रा के बारे में समीक्षा और प्रतिक्रिया दें';

@override
String get KeepYourselfOnlineToGetMoreRidesText =>
    "ज़्यादा राइड्स पाने के लिए ऑनलाइन रहें";

@override
String get youHaveCanceledTheRidePleaseWaitForAdminApproval =>
    "आपने राइड रद्द कर दी है, कृपया एडमिन की मंजूरी का इंतजार करें";

@override
String get reached => "पहुंच गए";

@override
String get pleaseCancelWaitingTimer => "कृपया प्रतीक्षा टाइमर रद्द करें";

@override
String get waitingTimeStarted => "प्रतीक्षा समय शुरू हो गया";

@override
String get waitingTimeEnded => "प्रतीक्षा समय समाप्त";

@override
String get newRide => "नई राइड";

@override
String get pleaseAcceptTheRide => "कृपया राइड स्वीकार करें";

@override
String get endRide => "यात्रा समाप्त करें";

@override
String get careInfo =>
    'यदि आपके पास कोई अन्य प्रश्न है या आपको किसी समर्थन की आवश्यकता है, तो आप हमें ईमेल कर सकते हैं';

@override
String get deleted => "हटाया गया";

@override
String get details => "विवरण";

@override
String get pleaseCompleteThisRideFirst => "कृपया पहले इस राइड को पूरा करें";

@override
String get AreYouSureYouWantToEndWaitingTime =>
    "क्या आप सुनिश्चित हैं कि आप प्रतीक्षा समय समाप्त करना चाहते हैं";

@override
String get handleMiddimgregionId =>
    "आपका क्षेत्र डेटा गायब है। कृपया अपना क्षेत्र अपडेट करें।";

@override
String get TripEarning => "यात्रा आय";

@override
String get handleInCompleteProfile =>
    "कृपया आगे बढ़ने से पहले अपनी प्रोफाइल पूरी करें";

@override
String get call_911 => "911 पर कॉल करें";

@override
String get enterYourNewEmail => "अपना नया ईमेल दर्ज करें";

@override
String get invalidReferalCode => "अमान्य संदर्भ कोड";

@override
String get noSuggestedMessage => "कोई सुझाया गया संदेश नहीं";

@override
String get pleaseAddDescription => "कृपया विवरण जोड़ें";

@override
String get pleaseSelectProvinceAndRegionId =>
    "कृपया क्षेत्र आईडी और प्रांत आईडी चुनें";

@override
String get sendEmailLinkOn => "ईमेल सत्यापन लिंक भेजें";

@override
String get pleaseSelectProvinceId => "कृपया प्रांत आईडी चुनें";

@override
String get pleaseSelectRegionId => "कृपया क्षेत्र आईडी चुनें";

@override
String get provinceId => "प्रांत आईडी";

@override
String get regionId => "क्षेत्र आईडी";

@override
String get referralCodeAppliedSuccesfully =>
    "संदर्भ कोड सफलतापूर्वक लागू किया गया";

@override
String get verified => "पुष्ट";

@override
String get doubleTapToExit => "बाहर निकलने के लिए डबल टैप करें";

@override
String get openSetting => "सेटिंग्स खोलें";

@override
String get send => "भेजें";

@override
String get pleaseEnterValidTitle => "कृपया मान्य शीर्षक दर्ज करें";

@override
String get pleaseEnterValidMessage => "कृपया मान्य संदेश दर्ज करें";

@override
String get cameraAccesMessage =>
    "बेहतर अनुभव के लिए कृपया कैमरा एक्सेस की अनुमति दें";

@override
String get microphoneAccessMessage =>
    "VOIP कॉल फीचर का उपयोग करने के लिए, माइक्रोफोन एक्सेस की आवश्यकता है। कृपया ऐप सेटिंग्स में अनुमति प्रदान करें।";

@override
String get riderName => "राइडर का नाम";

@override
String get tax => "कर";

@override
String get suggestedReviews => "सुझाए गए समीक्षाएं";

@override
String get verify => "सत्यापित करें";

@override
String get startLocation => "आरंभ स्थान";

@override
String get youMissedARide => "आपने एक राइड चूकी";

@override
String get youCancelledARide => "आपने एक राइड रद्द कर दी";

@override
String get youHaveNewRide => "आपके पास एक नई राइड है";

@override
String get waitingTimeCancelledMessage =>
    "आपने चलना शुरू कर दिया है, प्रतीक्षा समय रद्द कर दिया गया";
@override
String get offerCompleted => "ऑफ़र पूरा हुआ";

@override
String get offerEarning => "ऑफ़र कमाई";

  @override
  // TODO: implement goTxt
  String get goTxt => "चलो";
  
  @override
  // TODO: implement RidedeclinedText
  String get RidedeclinedText => 'सवारी अस्वीकृत की गई है।';
  
  @override
  // TODO: implement systemAlertMessage
  String get systemAlertMessage => "throw UnimplementedError()";


}



