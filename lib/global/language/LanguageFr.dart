
import 'package:rooo_driver/global/language/BaseLanguage.dart';

class LanguageFr extends BaseLanguage {
  @override
String get appName => 'Driver';

@override
String get welcomeBack => 'Bienvenue !';

@override
String get signInYourAccount => 'Connectez-vous à votre compte';

@override
String get thisFieldRequired => 'Ce champ est obligatoire';

@override
String get email => 'Email';

@override
String get password => 'Mot de passe';

@override
String get forgotPassword => 'Mot de passe oublié ?';

@override
String get logIn => 'Se connecter';

@override
String get orLogInWith => 'Ou connectez-vous avec';

@override
String get donHaveAnAccount => 'Vous n\'avez pas de compte ?';

@override
String get signUp => 'S\'inscrire';

@override
String get createAccount => 'Créer un compte';

@override
String get createYourAccountToContinue => 'Créez votre compte pour continuer';

@override
String get firstName => 'Prénom';

@override
String get lastName => 'Nom';

@override
String get userName => 'Nom d\'utilisateur';

@override
String get phoneNumber => 'Numéro de téléphone';

@override
String get alreadyHaveAnAccount => 'Vous avez déjà un compte ?';

@override
String get contactUs => 'Nous contacter';

@override
String get purchase => 'Acheter';

@override
String get changePassword => 'Changer le mot de passe';

@override
String get oldPassword => 'Ancien mot de passe';

@override
String get newPassword => 'Nouveau mot de passe';

@override
String get confirmPassword => 'Confirmer le mot de passe';

@override
String get passwordDoesNotMatch => 'Le mot de passe ne correspond pas';

@override
String get passwordInvalid => 'La longueur minimale du mot de passe doit être de 8';

@override
String get yes => 'Oui';

@override
String get no => 'Non';

@override
String get writeMessage => 'Écrire un message....';

@override
String get enterTheEmailAssociatedWithYourAccount =>
    'Entrez l\'email associé à votre compte';

@override
String get submit => 'Soumettre';

@override
String get language => 'Langue';

@override
String get notification => 'Notification';

@override
String get otpVeriFiCation => 'Vérification OTP';

@override
String get weHaveSentDigitCode => 'Nous avons envoyé un code à 4 chiffres';

@override
String get contactLength =>
    'La longueur du numéro de contact doit être de 10 ou 12 chiffres.';

@override
String get about => 'À propos';

@override
String get useInCaseOfEmergency => 'UTILISER EN CAS D\'URGENCE';

@override
String get notifyAdmin => 'Notifier l\'administrateur';

@override
String get notifiedSuccessfully => 'Notifié avec succès';

@override
String get complain => 'Réclamation';

@override
String get pleaseEnterSubject => 'Veuillez entrer l\'objet';

@override
String get writeDescription => 'Écrire une description....';

@override
String get saveComplain => 'Enregistrer la réclamation';

@override
String get editProfile => 'Modifier le profil';

@override
String get gender => 'Genre';

@override
String get addressTxt => 'Adresse';

@override
String get updateProfile => 'Mettre à jour le profil';

@override
String get notChangeUsername =>
    'Vous pouvez changer le nom d\'utilisateur en cliquant sur le bouton de modification';

@override
String get notChangeEmail =>
    'Vous pouvez changer l\'email en cliquant sur le bouton de modification';

@override
String get profileUpdateMsg => 'Profil mis à jour avec succès';

@override
String get emergencyContact => 'Contact d\'urgence';

@override
String get areYouSureYouWantDeleteThisNumber =>
    'Êtes-vous sûr de vouloir supprimer ce numéro ?';

@override
String get addContact => 'Ajouter un contact';

@override
String get googleMap => 'Google Maps';

@override
String get save => 'Enregistrer';

@override
String get myRides => 'Trajets';

@override
String get myWallet => 'Mon portefeuille';

@override
String get availableBalance => 'Solde disponible';

@override
String get recentTransactions => 'Transactions récentes';

@override
String get moneyDeposited => 'Argent déposé';

@override
String get addMoney => 'Ajouter de l\'argent';

@override
String get cancelTxt => 'Annuler';

@override
String get pleaseSelectAmount => 'Veuillez sélectionner le montant';

@override
String get amount => 'Montant';

@override
String get capacity => 'Capacité';

@override
String get paymentMethod => 'Méthode de paiement';

@override
String get chooseYouPaymentLate => 'Choisissez votre paiement maintenant ou plus tard';

@override
String get enterPromoCode => 'Entrez le code promo';

@override
String get confirm => 'Confirmer';

@override
String get forInstantPayment => 'Pour un paiement instantané';

@override
String get bookNow => 'Réserver maintenant';

@override
String get wallet => 'Portefeuille';

@override
String get paymentDetail => 'Détail du paiement';

@override
String get rideId => 'ID du trajet';

@override
String get createdAt => 'Créé le';

@override
String get viewHistory => 'Voir l\'historique';

@override
String get paymentDetails => 'Détails du paiement';

@override
String get paymentType => 'Type de paiement';

@override
String get paymentStatus => 'Statut du paiement';

@override
String get priceDetail => 'Détail du prix';

@override
String get basePrice => 'Prix de base';

@override
String get distancePrice => 'Prix de la distance';

@override
String get timePrice => 'Prix du temps';

@override
String get waitTime => 'Temps d\'attente';

@override
String get extraCharges => 'Frais supplémentaires';

@override
String get couponDiscount => 'Remise coupon';

@override
String get total => 'Total';

@override
String get payment => 'Paiement';

@override
String get cash => 'Espèces';

@override
String get updatePaymentStatus => 'Mettre à jour le statut du paiement';

@override
String get waitingForDriverConformation =>
    'EN ATTENTE DE LA CONFIRMATION DU CONDUCTEUR';

@override
String get continueNewRide => 'Continuer le nouveau trajet';

@override
String get payToPayment => 'Payer pour le paiement';

@override
String get tip => 'Pourboire';

@override
String get pay => 'Payer';

@override
String get howWasYourRide => 'Comment était votre trajet ?';

@override
String get wouldYouLikeToAddTip => 'Souhaitez-vous ajouter un pourboire ?';

@override
String get addMoreTip => 'Ajouter plus de pourboire';

@override
String get addMore => 'Ajouter plus';

@override
String get addReviews => 'Ajouter des avis';

@override
String get writeYourComments => 'Écrivez vos avis....';

@override
String get continueD => 'Continuer';

@override
String get detailScreen => 'Détails du trajet';

@override
String get helpdetail => 'Détails de l\'aide';

@override
String get aboutDriver => 'À propos du conducteur';

@override
String get rideHistory => 'Historique des trajets';

@override
String get myProfile => 'Mon profil';

@override
String get myTrips => 'Mes trajets';

@override
String get emergencyContacts => 'Contacts d\'urgence';

@override
String get logOut => 'Déconnexion';

@override
String get areYouSureYouWantToLogoutThisApp =>
    'Êtes-vous sûr de vouloir vous déconnecter de cette application ?';

@override
String get whatWouldYouLikeToGo => 'Où souhaitez-vous aller ?';

@override
String get enterYourDestination => 'Entrez votre destination';

@override
String get currentLocation => 'Lieu de départ';

@override
String get destinationLocation => 'Lieu de destination';

@override
String get chooseOnMap => 'Choisir sur la carte';

@override
String get profile => 'Profil';

@override
String get theme => 'Thème';

@override
String get privacyPolicy => 'Politique de confidentialité';

@override
String get helpSupport => 'Aide & Support';

@override
String get termsConditions => 'Conditions générales';

@override
String get aboutUs => 'À propos de nous';

@override
String get lookingForNearbyDrivers => 'Recherche de conducteurs à proximité';

@override
String get weAreLookingForNearDriversAcceptsYourRide =>
    'Nous recherchons des conducteurs à proximité pour\naccepter votre trajet';

@override
String get areYouSureYouWantToCancelThisRide =>
    'Êtes-vous sûr de vouloir annuler ce trajet ?';

@override
String get serviceDetail => 'Détail du service';

@override
String get get => 'Obtenir';

@override
String get rides => 'Trajets';

@override
String get people => 'Personnes';

@override
String get fare => 'Tarif';

@override
String get done => 'Fait';

@override
String get availableOffers => 'Offres disponibles';

@override
String get off => 'Réduction';

@override
String get sendOTP => 'Envoyer le code de vérification';

@override
String get otpVerification => 'Vérification OTP';

@override
String get enterTheCodeSendTo => 'Entrez le code envoyé à';

@override
String get didNotReceiveTheCode => 'Code non reçu';

@override
String get resend => 'Renvoyer';

@override
String get drivingExperience => 'Expérience de conduite (années)';

@override
String get sos => 'SOS';

@override
String get driverReview => 'Avis sur le conducteur';

@override
String get signInUsingYourMobileNumber => 'Connectez-vous avec votre\nnuméro de téléphone mobile';

@override
String get otp => 'Code de vérification';

@override
String get newRideRequested => 'Nouveau trajet demandé';

@override
String get accepted => 'Accepté';

@override
String get arriving => 'Arrivée';

@override
String get arrived => 'Arrivé';

@override
String get inProgress => 'En cours';

@override
String get cancelled => 'Annulé';

@override
String get completed => 'Complété';

@override
String get pleaseEnableLocationPermission =>
    'Veuillez activer la permission de localisation';

@override
String get pending => 'En attente';

@override
String get failed => 'Échoué';

@override
String get paid => 'Payé';

@override
String get male => 'Homme';

@override
String get female => 'Femme';

@override
String get other => 'Autre';

@override
String get addExtraCharges => 'Ajouter des frais supplémentaires';

@override
String get enterAmount => 'Entrez le montant';

@override
String get pleaseAddedAmount => 'Veuillez ajouter le montant';

@override
String get title => 'Titre';
@override
String get saveCharges => "Sauvegarder les frais";

@override
String get bankDetail => 'Détails bancaires';

@override
String get bankName => 'Nom de la banque';

@override
String get bankCode => 'Code de la banque';

@override
String get accountHolderName => 'Nom du titulaire du compte';

@override
String get accountNumber => 'Numéro de compte';

@override
String get updateBankDetail => 'Mettre à jour les détails bancaires';

@override
String get addBankDetail => 'Ajouter des détails bancaires';

@override
String get bankInfoUpdateSuccessfully => 'Informations bancaires mises à jour avec succès';

@override
String get vehicleDetail => 'Détails du véhicule';

@override
String get document => 'Documents';

@override
String get setting => 'Paramètres';

@override
String get youAreOnlineNow => "Vous êtes en ligne maintenant";

@override
String get youAreOfflineNow => "Vous êtes hors ligne maintenant";

@override
String get requests => 'Demandes';

@override
String get areYouSureYouWantToCancelThisRequest =>
    'Êtes-vous sûr de vouloir annuler cette demande ?';

@override
String get decline => 'Refuser';

@override
String get accept => 'Accepter';

@override
String get areYouSureYouWantToAcceptThisRequest =>
    'Êtes-vous sûr de vouloir accepter cette demande ?';

@override
String get call => 'Appeler';

@override
String get chat => 'Discuter';

@override
String get applyExtraFree => 'Appliquer des frais supplémentaires ?';

@override
String get areYouSureYouWantToArriving =>
    'Êtes-vous sûr de vouloir marquer comme arrivé ?';

@override
String get areYouSureYouWantToArrived => 'Êtes-vous sûr d\'être arrivé ?';

@override
String get enterOtp => 'Entrez le code de vérification';

@override
String get enterTheOtpDisplayInCustomersMobileToStartTheRide =>
    'Entrez le code de vérification affiché sur le mobile du client pour commencer le trajet';

@override
String get enterTheOtpDisplayInCustomersMobileToEndTheRide =>
    'Entrez le code de vérification affiché sur le mobile du client pour terminer le trajet';

@override
String get pleaseEnterValidOtp => 'Veuillez entrer un code de vérification valide';

@override
String get areYouSureYouWantToCompletedThisRide =>
    'Êtes-vous sûr de vouloir compléter ce trajet ?';

@override
String get updateBankInfo => 'Mettre à jour les informations bancaires';

@override
String get regisTRation => 'Inscription';

@override
String get pleaseSelectVehiclePreferences =>
    'Veuillez sélectionner les préférences de véhicule';

@override
String get userDetail => 'Détails du conducteur';

@override
String get selectVehiclePreferences => 'Sélectionner les préférences de véhicule';

@override
String get selectGender => 'Sélectionner le sexe';

@override
String get age => 'Âge';

@override
String get socialSecurityNumber => 'Numéro de sécurité sociale';

@override
String get nightDrivingPreference => 'Préférence pour la conduite de nuit';

@override
String get withDraw => 'Retirer';

@override
String get withdrawHistory => 'Historique des retraits';

@override
String get approved => 'Approuvé';

@override
String get requested => 'Demandé';

@override
String get updateVehicle => 'Mettre à jour le véhicule';

@override
String get userNotApproveMsg =>
    'Votre profil est en cours de révision. Attendez un moment ou contactez votre administrateur.';

@override
String get uploadFileConfirmationMsg =>
    'Êtes-vous sûr de vouloir télécharger ce fichier ?';

@override
String get selectDocument => 'Sélectionner un document';

@override
String get addDocument => 'Ajouter un document';

@override
String get areYouSureYouWantToDeleteThisDocument =>
    'Êtes-vous sûr de vouloir supprimer ce document ?';

@override
String get expireDate => 'Date d\'expiration';

@override
String get goDashBoard => 'Aller au tableau de bord';

@override
String get deleteAccount => 'Supprimer le compte';

@override
String get account => 'Compte ';

@override
String get areYouSureYouWantPleaseReadAffect =>
    'Êtes-vous sûr de vouloir supprimer votre compte ? Veuillez lire comment la suppression du compte affectera.';

@override
String get deletingAccountEmail =>
    'La suppression de votre compte supprime les informations personnelles de notre base de données. Votre email devient définitivement réservé et le même email ne peut pas être réutilisé pour enregistrer un nouveau compte';

@override
String get areYouSureYouWantDeleteAccount =>
    'Êtes-vous sûr de vouloir supprimer le compte ?';

@override
String get yourInternetIsNotWorking => 'Votre Internet ne fonctionne pas';

@override
String get allow => 'Autoriser';

@override
String get mostReliableMightyDriverApp => 'Application de conducteur la plus fiable';

@override
String get toEnjoyYourRideExperiencePleaseAllowPermissions =>
    'Pour profiter de votre expérience de trajet\nVeuillez nous accorder les autorisations suivantes';

@override
String get cashCollected => 'Espèces collectées';

@override
String get areYouSureCollectThisPayment =>
    'Êtes-vous sûr de vouloir collecter ce paiement ?';

@override
String get txtURLEmpty => "L'URL est vide";

@override
String get lblFollowUs => "Suivez-nous";

@override
String get bankInfo => "Informations bancaires";

@override
String get duration => 'Durée';

@override
String get paymentVia => 'Paiement via';

@override
String get moneyDebit => 'Débit d\'argent';

@override
String get roooCare => 'ROOO care';

@override
String get demoMsg => 'Le rôle de testeur n\'est pas autorisé à effectuer cette action';

@override
String get youCannotChangePhoneNumber =>
    'Vous pouvez changer le numéro de téléphone en cliquant sur le bouton de modification';

@override
String get offLine => 'Hors ligne';

@override
String get online => 'En ligne';

@override
String get walletLessAmountMsg =>
    'Vous ne pouvez pas effectuer de trajet car vous avez peu d\'argent dans votre portefeuille. Vous devez ajouter de l\'argent à votre portefeuille pour pouvoir effectuer un trajet plus tard.';

@override
String get aboutRider => 'À propos du passager';

@override
String get pleaseEnterMessage => 'Veuillez entrer un message';

@override
String get complainList => 'Liste des plaintes';

@override
String get viewAll => 'Voir tout';

@override
String get pleaseSelectRating => 'Veuillez sélectionner une note';

@override
String get serviceInfo => 'Informations sur le service';

@override
String get youCannotChangeService => 'Vous ne pouvez pas changer le service';

@override
String get vehicleInfoUpdateSucessfully => 'Informations sur le véhicule mises à jour avec succès';

@override
String get subscription => 'Abonnement';

@override
String get yourCurrentBalanceIs => 'Votre solde actuel est :';

@override
String get yourSubscriptionPlanIsOver =>
    'Votre plan d\'abonnement est expiré. Veuillez vous abonner pour utiliser cette application';

@override
String get perDay => 'PAR JOUR';

@override
String get renew => 'Renouveler';

@override
String get yourWalletDoNotHaveEnoughBalance =>
    'Votre portefeuille ne dispose pas d\'un solde suffisant. Veuillez ajouter du solde dans le portefeuille puis renouveler votre plan d\'abonnement.';

@override
String get addWallet => 'Ajouter au portefeuille';

@override
String get yourDailyAppUseLimitHasBeenExpired =>
    'Votre limite d\'utilisation quotidienne de l\'application est expirée. Appuyez pour recharger et utiliser l\'application.';

@override
String get recharge => 'Recharger';

@override
String get isMandatoryDocument => '* est un document obligatoire.';

@override
String get someRequiredDocumentAreNotUploaded =>
    'Certains documents requis ne sont pas téléchargés. Veuillez télécharger tous les documents requis.';

@override
String get areYouCertainOffline => 'Êtes-vous certain de vouloir passer hors ligne ?';

@override
String get areYouCertainOnline => 'Êtes-vous certain de vouloir passer en ligne ?';

@override
String get pleaseAcceptTermsOfServicePrivacyPolicy =>
    'Veuillez accepter les Conditions de service et la Politique de confidentialité';

@override
String get rememberMe => 'Se souvenir de moi';

@override
String get agreeToThe => 'J\'accepte les';

@override
String get invoice => 'Facture';

@override
String get riderInformation => 'INFORMATIONS SUR LE PASSAGER';

@override
String get customerName => 'Nom du client';

@override
String get sourceLocation => 'Lieu de départ';

@override
String get invoiceNo => 'Numéro de facture';

@override
String get invoiceDate => 'Date de la facture';

@override
String get orderedDate => 'Date de commande';

@override
String get totalCash => 'Total des espèces';

@override
String get totalRide => 'Montant total du trajet';

@override
String get totalWallet => 'Total du portefeuille';

@override
String get totalEarning => 'Gains totaux';

@override
String get pleaseSelectFromDateAndToDate =>
    'Veuillez sélectionner la date de début et la date de fin';

@override
String get from => 'De';

@override
String get fromDate => 'Date de début';

@override
String get to => 'À';

@override
String get toDate => 'Date de fin';

@override
String get ride => 'Trajet';

@override
String get todayRide => 'Trajet du jour';

@override
String get weeklyOrderCount => 'Nombre de commandes hebdomadaires';

@override
String get distance => 'Distance';

@override
String get rideInformation => 'Informations sur le trajet';

@override
String get iAgreeToThe => 'J\'accepte les';

@override
String get today => 'Aujourd\'hui';

@override
String get weekly => 'Hebdomadaire';

@override
String get report => 'Rapport';

@override
String get earning => 'Gains';

@override
String get todayEarning => 'Gains d\'aujourd\'hui';

@override
String get available => 'Disponible';

@override
String get notAvailable => 'Non disponible';

@override
String get youWillReceiveNewRidersAndNotifications =>
    'Vous recevrez de nouveaux passagers et des notifications';

@override
String get youWillNotReceiveNewRidersAndNotifications =>
    'Vous ne recevrez pas de nouveaux passagers et de notifications';

@override
String get yourAccountIs => 'Votre compte est';

@override
String get pleaseContactSystemAdministrator =>
    'Veuillez contacter l\'administrateur système';

@override
String get youCanNotThisActionsPerformBecauseYourCurrentRideIsNotCompleted =>
    'Vous ne pouvez pas effectuer ces actions car votre trajet actuel n\'est pas terminé';

@override
String get selectDisplayPicture => 'Sélectionner la photo de profil';

@override
String get invalidAge => 'L\'âge doit être de 18 ans ou plus';

@override
String get go => "Aller";

@override
String get inbox => "Boîte de réception";

@override
String get referralCompleted => 'OBJECTIFS ATTEINTS';

@override
String get referralEarning => 'Votre réalisation';

@override
String get continueText => 'Continuer';

@override
String get enterYourMobileNumber => 'Entrez votre numéro de téléphone mobile';

@override
String get orText => 'OU';

@override
String get signInWithFacebook => 'Se connecter avec Facebook';

@override
String get signInWithGoogle => 'Se connecter avec Google';

@override
String get inBoxTxt => 'Boîte de réception';

@override
String get referralsTxt => 'Références';

@override
String get opportunityTxt => 'Opportunités';

@override
String get accountTxt => 'Compte';

@override
String get earningTxt => 'Gains';

@override
String get helpTxt => 'Aide';

@override
String get learningCenterTxt => 'Centre de formation';

@override
String get completedTxt => 'Complété';

@override
String get faqsTxt => 'FAQs';

@override
String get settingTxt => 'Paramètres';

@override
String get nightModeTxt => 'Mode nuit';

@override
String get helpWithTripTxt => 'Aide avec un trajet';

@override
String get verifyOTP => "Vérifier";

@override
String get verifyOTPHeading => 'Entrez le code à 6 chiffres envoyé';

@override
String get enterOTP => 'Veuillez entrer le code de vérification';

@override
String get invalidOTP => 'Code de vérification invalide';

@override
String get referralCode => 'Code de parrainage';


@override
String get charges => 'Frais';

  @override
String get KeepYourselfOnlineToGetMoreRidesText =>
    "Restez en ligne pour obtenir plus de trajets";

@override
String get youHaveCanceledTheRidePleaseWaitForAdminApproval =>
    "Vous avez annulé le trajet, veuillez attendre l'approbation de l'administrateur";

@override
String get reached => "Arrivé";

@override
String get pleaseCancelWaitingTimer => "Veuillez annuler le chronomètre d'attente";

@override
String get waitingTimeStarted => "Le temps d'attente a commencé";

@override
String get waitingTimeEnded => "Le temps d'attente est terminé";

@override
String get newRide => "Nouveau trajet";

@override
String get pleaseAcceptTheRide => "Veuillez accepter le trajet";

@override
String get endRide => "Terminer le trajet";

@override
String get careInfo =>
    "Si vous avez d'autres questions ou avez besoin de support, vous pouvez nous envoyer un e-mail à";

@override
String get deleted => "Supprimé";

@override
String get details => "Détails";

@override
String get pleaseCompleteThisRideFirst => "Veuillez d'abord compléter ce trajet";

@override
String get AreYouSureYouWantToEndWaitingTime =>
    "Êtes-vous sûr de vouloir mettre fin au temps d'attente ?";

@override
String get handleMiddimgregionId =>
    "Les données de votre région sont manquantes.\nVeuillez mettre à jour votre région.";

@override
String get TripEarning => "Gains du trajet";

@override
String get handleInCompleteProfile =>
    "Veuillez compléter votre profil avant de continuer";

@override
String get call_911 => "Appeler le 911";

@override
String get enterYourNewEmail => "Entrez votre nouvel e-mail";

@override
String get invalidReferalCode => "Code de parrainage invalide";

@override
String get noSuggestedMessage => "Aucun message suggéré";

@override
String get pleaseAddDescription => "Veuillez ajouter une description";

@override
String get pleaseSelectProvinceAndRegionId =>
    "Veuillez sélectionner l'ID de la province et l'ID de la région";

@override
String get sendEmailLinkOn => "Envoyer un lien de vérification par e-mail à";

@override
String get pleaseSelectProvinceId => "Veuillez sélectionner l'ID de la province";

@override
String get pleaseSelectRegionId => "Veuillez sélectionner l'ID de la région";

@override
String get provinceId => "ID de la province";

@override
String get regionId => "ID de la région";

@override
String get referralCodeAppliedSuccesfully =>
    "Code de parrainage appliqué avec succès";

@override
String get verified => "Vérifié";

@override
String get doubleTapToExit => "Double-tapoter pour quitter";

@override
String get openSetting => "Ouvrir les paramètres";

@override
String get send => "Envoyer";

@override
String get pleaseEnterValidTitle => "Veuillez entrer un titre valide";

@override
String get pleaseEnterValidMessage => "Veuillez entrer un message valide";

@override
String get cameraAccesMessage =>
    "Veuillez autoriser l'accès à la caméra pour une meilleure expérience";

@override
String get microphoneAccessMessage =>
    "Pour utiliser la fonction d'appel VOIP, l'accès au microphone est requis. Veuillez accorder la permission dans les paramètres de l'application.";

@override
String get riderName => "Nom du passager";

@override
String get tax => "Taxe";

@override
String get suggestedReviews => "Critiques suggérées";

@override
String get verify => "Vérifier";

@override
String get startLocation => "Lieu de départ";

@override
String get youMissedARide => "Vous avez manqué un trajet";

@override
String get youCancelledARide => "Vous avez annulé un trajet";

@override
String get youHaveNewRide => "Vous avez un nouveau trajet";

@override
String get waitingTimeCancelledMessage =>
    "Vous avez commencé à vous déplacer, le temps d'attente a été annulé";
@override
  String get allMessageTxt => 'Tous les messages';

  @override
  String get bankInfoTxt => "Infos bancaires";
  @override
  String get offerTxt => "Offre";
  @override
  String get statusTxt => "Statut";

  @override
  String get balanceTxt => "Solde";
  @override
  String get paymentScheduledTxt => "Payout programmé";
  @override
  String get paymentMethodsTxt => "Méthodes de paiement";

  @override
  String get cashOutTxt => "Retrait";

  @override
  String get walletTxt => "Portefeuille";

  @override
  String get verifyPhoneMsg => 'Veuillez vérifier votre téléphone';

  @override
  String get errorMsg => 'Quelque chose a mal tourné';

  @override
  String get SorryYouDontHaveAnyOpportunitiesTxt =>
      "Désolé, vous n'avez pas d'opportunités";
  @override
  String get NewOpportunitiesAreComingTxt => "De nouvelles opportunités arrivent";

  @override
  String get stayTunedTxt => "Restez à l'écoute";
  @override
  String get roooCareTxt => "ROOO Care";
  @override
  String get NotificationsTxt => "Notifications";
  @override
  String get WeeklyOrderCountTxt => "Nombre de commandes hebdomadaire";
  @override
  String get cashoutTxt => "Retrait";
  @override
  String get TotalCashTxt => "Total des liquidités";
  @override
  String get TotalWalletTxt => "Total portefeuille";
  @override
  String get TotalRideTxt => "Total des trajets";
  @override
  String get adminNotifiedTxt => "Administrateur notifié";

  @override
  String get YouAreOnlineNowTxt => "Vous êtes maintenant en ligne";

  @override
  String get AreYouSureYouWantToNotifyAdminTxt =>
      "Êtes-vous sûr de vouloir notifier l'administrateur";

  @override
  String get PleaseWaitForTimerTxt => "Veuillez attendre le minuteur";

  @override
  String get NotifiedAdminTxt => "Administrateur notifié";

  @override
  String get BookYourroooIn3EasyStepsTxt =>
      "Réservez votre ROOO en 3 étapes faciles";

  @override
  String get NowBookingYourroooIsEasyTxt =>
      "Réserver votre rooo est maintenant facile !";

  @override
  String get PleaseUploadAllTheDocumentsAndWaitForVerificationTxt =>
      "Veuillez télécharger tous les documents et attendre la vérification";

  @override
  String get OkTxt => "Ok";

  @override
  String get goOfflineTxt => "DEVENIR HORS LIGNE";

  @override
  String get clickCarPhotographsTxt => "Cliquez sur les photos de voiture";

  @override
  String get frontTxt => "Avant";
  @override
  String get backTxt => "Arrière";
  @override
  String get leftTxt => "Gauche";
  @override
  String get rightTxt => "Droite";

  @override
  String get WelcomeTxt => "Bienvenue";

  @override
  String get UserTxt => "Utilisateur";

  @override
  String get pleaseUploadImageProperlyTxt => "Veuillez télécharger toutes les images";

  @override
  String get IhaventRecievedACodeTxt => "Je n'ai pas reçu de code";
  @override
  String get CONTINUETxt => "CONTINUER";
  @override
  String get NotUploadedTxt => "Non téléchargé";
  @override
  String get CompletedTxt => "Terminé";
  @override
  String get rejectedTxt => "Rejeté";
  @override
  String get pendingTxt => "En attente";
  @override
  String get resendOTPTxt => "Renvoyer le code de vérification";
  @override
  String get uploadTxt => "Télécharger";

  @override
  String get accountHolderNameTxt =>
      "Nom du titulaire du compte (exactement tel qu'il apparaît sur vos relevés bancaires)";
  @override
  String get pleaseEnterTheBenificieryNameTxt =>
      "Veuillez entrer le nom du bénéficiaire sur un compte bancaire, Si un compte est enregistré au nom d'une entreprise, veuillez mettre le nom de l'entreprise sur le compte";
  @override
  String get PleaseEnterTheBeneficieryAddressOnABankAccountTxt =>
      "Veuillez entrer l'adresse du bénéficiaire sur un compte bancaire";
  @override
  String get cityTxt => "Ville";
  @override
  String get PleaseEnterYourCityTxt => "Veuillez entrer votre ville";
  @override
  String get postalTxt => "Code postal";
  @override
  String get PleaseEnterTheBeneficieryPostalCodeTxt =>
      "Veuillez entrer le code postal du bénéficiaire sur le compte bancaire, Si un compte est enregistré au nom d'une entreprise, veuillez mettre le code postal de l'entreprise sur le compte.";
  @override
  String get DateOfBirthTxt => "Date de naissance";
  @override
  String get BankNameTxt => "Nom de la banque";
  @override
  String get BankameOthersTxt => "Nom de la banque (Autres)";
  @override
  String get BankOfNovoScotioTxt => "Par exemple Banque de Nouvelle-Écosse (BNS)";
  @override
  String get InstituitionNumberTxt => "Numéro d'institution";
  @override
  String get InstitutionNumberMustContainAtLeastDdigitsTxt =>
      "Le numéro d'institution doit contenir au moins 3 chiffres";
  @override
  String get TransitNumberTxt => "Numéro de transit";
  @override
  String get TransitNumberMustContainAtLeastDigitsTxt =>
      "Le numéro de transit doit contenir au moins 5 chiffres";
  @override
  String get BankAccountNumberTxt => "Numéro de compte bancaire";
  @override
  String get AccountNumberMustContainAtLeastDigitsTxt =>
      "Le numéro de compte doit contenir au moins 7 chiffres";
  @override
  String get FillCorrectAccountNumberTxt => "Remplir le numéro de compte correct";
  @override
  String get ReEnterBankAccountNumberTxt => "Re-saisir le numéro de compte bancaire";
  @override
  String get PleaseReEnterYourAccountNumberTxt =>
      "Veuillez re-saisir votre numéro de compte afin que nous puissions valider le numéro de compte que vous avez saisi";
  @override
  String get TermsTxt => "Conditions";
  @override
  String get ShareYourLinkTxt => 'Partager votre lien';
  @override
  String get ReEnteredAccountIsWrongTxt => "Le mot de passe ressaisi est incorrect";

  @override
  String get waitingTime => "Temps d'attente";

  @override
  String get waitingTimeOverTxt => "Temps d'attente terminé";

  @override
  String get cancelTimerTxt => "Arrêter le minuteur";

  @override
  String get wouldYouLikeToStartIdleTimeTxt =>
      "Souhaitez-vous commencer le temps d'inactivité";

  String get aboutUsTxt =>
      "ROOO : Votre compagnon de voyage de confiance. Nous vous connectons avec des conducteurs de premier choix pour un voyage sans accroc et en toute sécurité. Votre confort, notre priorité";

  @override
  String get referralTxt => "Références";

  @override
  String get promotionsTxt => "Promotions";

  @override
  String get inviteTxt => "Inviter";

  @override
  String get inviteFromContactsTxt => "Inviter depuis les contacts";
  @override
  String get termsApplyTxt => "Conditions applicables";

  @override
  String get totalReferralsTxt => "Références totales";

  @override
  String get closedTxt => "fermé";
  @override
  String get AdminalreadynotifiedTxt => "Administrateur déjà notifié";

  @override
  String get PleasewaitTxt => "Veuillez patienter";
  @override
  String get NotifyToAdminAboutLateRideTxt => "Notifier l'administrateur du retard de trajet";
  @override
  String get YouHaveReachedTheDestinationTxt =>
      "Êtes-vous arrivé à destination";
  @override
  String get upload4ImagesWarningTxt =>
      "Veuillez télécharger les images comme illustré, sinon votre compte sera temporairement désactivé ou il pourrait y avoir un bannissement permanent";
  @override
  String get CodecopiedTxt => "Code copié";
  @override
  String get RideAcceptedTxt => "Trajet accepté";

  @override
  String get PostalCodeShouldHave6DigitTxt => "Le code postal doit comporter 6 chiffres";
  @override
  String get LowBalanceTxt => "Solde faible";
  @override
  String get automaticText => "Automatique";
  @override
  String get timeOfDayTxt => "Heure de la journée";
  @override
  String get alwaysOnTxt => "Toujours activé";
  @override
  String get AlwaysOffTxt => "Toujours désactivé";
  @override
  String get UsePhoneSettingsTxt => "Utiliser les paramètres du téléphone";
  @override
  String get PleaseEnterValidDetailsTxt => "Veuillez entrer des détails valides";
  @override
  String get AddTxt => "Ajouter";
  @override
  String get messageTxt => "Message";

  String get PleaseEnterMobileNumberTxt => "Veuillez entrer le numéro de mobile";
  @override
  String get InvalidMobileNumberTxt => "Numéro de mobile invalide";
  @override
  String get ServerErrorTxt => "Erreur du serveur";

  @override
  String get Message => "message";

  @override
  String get OpportunityDetailTxt => "Détail de l'opportunité";
  @override
  String get DistanceTxt => "Distance";
  @override
  String get TimeTxt => "Temps";
  @override
  String get PickupLocationTxt => "Lieu de prise en charge";

  @override
  String get passCode => 'Code de vérification';
  @override
  String get TakePhoto => 'Prendre une photo';

  @override
  String get CopyrightText => 'Droits d\'auteur';

  @override
  String get roooTxt => 'ROOO';

  @override
  String get roooDriverTxt => 'Conducteur ROOO';

  @override
  String get ChangeEmailTxt => 'Changer l\'email';

  @override
  String get NewEmailTxt => 'Nouvel email';

  @override
  String get EditTxt => 'Modifier';

  @override
  String get PleaseEnterValidNameTxt => '"Veuillez entrer un nom valide"';

  @override
  String get RequireAdminPermission => 'Nécessite une autorisation d\'administrateur';

  @override
  String get UnverifiedEmailTxt => 'Email non vérifié';

  @override
  String get VerifiedEmailTxt => 'Email vérifié';

  @override
  String get RequestChangeTxt => 'Demander un changement';

  @override
  String get ChangeMobileTxt => "Changer le mobile";

  @override
  String get MobileNumberTxt => 'Numéro de mobile';

  @override
  String get optionalTxt => 'optionnel';

  @override
  String get NoResultsFoundTxt => 'Aucun résultat trouvé';

  @override
  String get PleaseTryDifferentKeywordTxt => "Veuillez essayer un mot-clé différent";

  @override
  String get ManageProfileTxt => "Profil";

  @override
  String get NumberAlreadyExistPleaseEnterOrUpdateNewNumbertxt =>
      'Le numéro existe déjà, veuillez entrer ou mettre à jour un nouveau numéro.';

  @override
  String get WalletHistoryTxt => 'Historique du portefeuille';
  @override
  String get DescriptionTxt => "Description";
  @override
  String get ClickAProperProfilePictureWithYourFaceClearAndVisibleProfilePictureCantBeChangedOnceUploadedTxt =>
      "Cliquez sur une photo de profil appropriée avec votre visage clair et visible, la photo de profil ne peut pas être changée une fois téléchargée.";

  @override
  String get PleaseUploadImagesAsIllustratedOtherwiseYourAccountWillBeTemporarilyDisabledOrThereMightBeAPermanentBanText =>
      'Veuillez télécharger les images comme illustré, sinon votre compte sera temporairement désactivé ou il pourrait y avoir un bannissement permanent.';

  @override
  String get enterthePasscodewhichIsDisplayingInTheCustomersMobile =>
      "Entrez le code de vérification affiché sur le mobile du client";

  @override
  String get requiredTxt => "obligatoire";

  @override
  String get startNavigationText => "Démarrer la navigation";

  @override
  String get signInWithApple => "Se connecter avec Apple";

  @override
  String get locationDeniedText1 =>
      "L'autorisation de localisation pour " +
      '\n"Autoriser tout le temps"\n' +
      "est requise pour le bon fonctionnement de l'application. Sans cette autorisation de localisation, la réservation de nouveaux trajets et la fonctionnalité des trajets en cours ne fonctionneront pas.";

  @override
  String get locationDeniedText2 =>
      'Vous pouvez modifier l\'autorisation \n"Autoriser tout le temps" \nà partir des paramètres';

  @override
  String get deleteText => "Supprimer";

  @override
  String get updateText => "Mettre à jour";

  @override
  String get retryText => "Réessayer";

  @override
  String get AreYouSureWantToPerformThisAction =>
      "Êtes-vous sûr de vouloir effectuer cette action ?";

  @override
  String get DoYouWantToDelete => "Voulez-vous supprimer ?";

  @override
  String get DoYouWantToUpdate => "Voulez-vous mettre à jour ?";

  @override
  String get DoYouWantToAdd => "Voulez-vous ajouter ?";

  @override
  String get DoYouWantToAccept => "Voulez-vous accepter ?";

  @override
  String get ClickToRetry => "Cliquez pour réessayer";
  @override
  String get NewTripText => "Nouveau trajet";
  @override
  String get AcceptedTripText => "Trajet accepté";
  @override
  String get adminDidNotApproved =>
      "L'administrateur n'a pas approuvé votre demande, veuillez continuer le trajet";

  @override
  String get ImageUploadedSuccessfullyText => "Image téléchargée avec succès";
  @override
  String get OKText => "OK";
  @override
  String get NotifyAdminAboutLateRiderText => "Notifier l'administrateur du retard du conducteur";
  @override
  String get AdminNotifiedText => "Administrateur notifié";
  @override
  String get CallText => "Appeler";
  @override
  String get StartRideText => "Démarrer le trajet";
  @override
  String get GetVerificationCodeText => "Entrez le code de vérification";
  @override
  String get YouRequestedForRideCancellationText =>
      "Vous avez demandé l'annulation du trajet";
  @override
  String get RideCanceledText => "Trajet annulé";

  @override
  String get AdminApproveYourRequestPleaseStayOnlineText =>
      "L'administrateur a approuvé votre demande, veuillez rester en ligne";
  @override
  String get YouHaveAlreadyOneRideDriverSafelyText =>
      "Vous avez déjà un trajet, \nConducteur en sécurité";
  @override
  String get YouForgotToReviewPleaseGiveYourReviewAndFeedbackAboutRideText =>
      "Vous avez oublié de laisser un avis. \nVeuillez donner votre avis et vos commentaires sur le trajet";
      
       @override
String get offerCompleted => "OFFRE TERMINÉE";

@override
String get offerEarning => "OFFRE GAIN";

  @override
  // TODO: implement goTxt
  String get goTxt => "Allez";
  
  @override
  // TODO: implement RidedeclinedText
  String get RidedeclinedText =>'La course a été refusée.';
  
  @override
  // TODO: implement systemAlertMessage
  String get systemAlertMessage => "throw UnimplementedError()";




}
