import 'BaseLanguage.dart';

class LanguagePa extends BaseLanguage {
  @override
  String get appName => 'Driver';

  @override
  String get welcomeBack => 'ਤੁਹਾਨੂੰ ਮੁੜ ਸਵਾਗਤ ਹੈ!';

  @override
  String get signInYourAccount => 'ਆਪਣੇ ਖਾਤੇ ਵਿੱਚ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get thisFieldRequired => 'ਇਹ ਫੀਲਡ ਜਰੂਰੀ ਹੈ';

  @override
  String get email => 'ਈਮੇਲ';

  @override
  String get password => 'ਪਾਸਵਰਡ';

  @override
  String get forgotPassword => 'ਪਾਸਵਰਡ ਭੁੱਲ ਗਏ?';

  @override
  String get logIn => 'ਲੌਗ ਇਨ';

  @override
  String get orLogInWith => 'ਜਾਂ ਲੌਗਿਨ ਕਰੋ';

  @override
  String get donHaveAnAccount => 'ਖਾਤਾ ਨਹੀਂ ਹੈ?';

  @override
  String get signUp => 'ਸਾਈਨ ਅਪ ਕਰੋ';

  @override
  String get createAccount => 'ਖਾਤਾ ਬਣਾਓ';

  @override
  String get createYourAccountToContinue => 'ਜਾਰੀ ਰੱਖਣ ਲਈ ਆਪਣਾ ਖਾਤਾ ਬਣਾਓ';

  @override
  String get firstName => 'ਪਹਿਲਾ ਨਾਮ';

  @override
  String get lastName => 'ਆਖਰੀ ਨਾਮ';

  @override
  String get userName => 'ਉਪਭੋਗਤਾ ਨਾਮ';

  @override
  String get phoneNumber => 'ਫੋਨ ਨੰਬਰ';

  @override
  String get alreadyHaveAnAccount => 'ਤੁਹਾਡੇ ਕੋਲ ਪਹਿਲਾਂ ਤੋਂ ਹੀ ਖਾਤਾ ਹੈ?';

  @override
  String get contactUs => 'ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰੋ';

  @override
  String get purchase => 'ਖਰੀਦੋ';

  @override
  String get changePassword => 'ਪਾਸਵਰਡ ਬਦਲੋ';

  @override
  String get oldPassword => 'ਪੁਰਾਣਾ ਪਾਸਵਰਡ';

  @override
  String get newPassword => 'ਨਵਾਂ ਪਾਸਵਰਡ';

  @override
  String get confirmPassword => 'ਪਾਸਵਰਡ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get passwordDoesNotMatch => 'ਪਾਸਵਰਡ ਮੈਲ ਨਹੀਂ ਖਾਂਦਾ';

  @override
  String get passwordInvalid => 'ਘੱਟੋ-ਘੱਟ ਪਾਸਵਰਡ ਦੀ ਲੰਬਾਈ 8 ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ';

  @override
  String get yes => 'ਹਾਂ';

  @override
  String get no => 'ਨਹੀਂ';

  @override
  String get writeMessage => 'ਸਨੇਹਾ ਲਿਖੋ....';

  @override
  String get enterTheEmailAssociatedWithYourAccount =>
      'ਆਪਣੇ ਖਾਤੇ ਨਾਲ ਜੁੜਿਆ ਈਮੇਲ ਦਰਜ ਕਰੋ';

  @override
  String get submit => 'ਸਬਮਿਟ ਕਰੋ';

  @override
  String get language => 'ਭਾਸ਼ਾ';

  @override
  String get notification => 'ਅਲਰਟ';

  @override
  String get otpVeriFiCation => 'ਓਟੀਪੀ ਤਸਦੀਕ';

  @override
  String get weHaveSentDigitCode => 'ਅਸੀਂ 4 ਅੰਕਾਂ ਦਾ ਕੋਡ ਭੇਜਿਆ ਹੈ';

  @override
  String get contactLength =>
      'ਸੰਪਰਕ ਨੰਬਰ ਦੀ ਲੰਬਾਈ 10 ਜਾਂ 12 ਅੰਕਾਂ ਦੀ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ।';

  @override
  String get about => 'ਸਾਡੇ ਬਾਰੇ';

  @override
  String get useInCaseOfEmergency => 'ਆਪਾਤਕਾਲ ਦੀ ਸਥਿਤੀ ਵਿੱਚ ਵਰਤੋਂ ਕਰੋ';

  @override
  String get notifyAdmin => 'ਐਡਮਿਨ ਨੂੰ ਸੂਚਿਤ ਕਰੋ';

  @override
  String get notifiedSuccessfully => 'ਸਫਲਤਾਪੂਰਵਕ ਸੂਚਿਤ ਕੀਤਾ ਗਿਆ';

  @override
  String get complain => 'ਸ਼ਿਕਾਇਤ';

  @override
  String get pleaseEnterSubject => 'ਕਿਰਪਾ ਕਰਕੇ ਵਿਸ਼ਾ ਦਰਜ ਕਰੋ';

  @override
  String get writeDescription => 'ਵੇਰਵਾ ਲਿਖੋ....';

  @override
  String get saveComplain => 'ਸ਼ਿਕਾਇਤ ਸੇਵ ਕਰੋ';

  @override
  String get editProfile => 'ਪ੍ਰੋਫਾਈਲ ਸੰਪਾਦਿਤ ਕਰੋ';

  @override
  String get gender => 'ਲਿੰਗ';

  @override
  String get addressTxt => 'ਪਤਾ';

  @override
  String get updateProfile => 'ਪ੍ਰੋਫਾਈਲ ਅੱਪਡੇਟ ਕਰੋ';

  @override
  String get notChangeUsername =>
      'ਤੁਸੀਂ ਸੰਪਾਦਨ ਬਟਨ ਤੇ ਕਲਿੱਕ ਕਰਕੇ ਉਪਯੋਗਕਰਤਾ ਨਾਮ ਬਦਲ ਸਕਦੇ ਹੋ';

  @override
  String get notChangeEmail =>
      'ਤੁਸੀਂ ਸੰਪਾਦਨ ਬਟਨ ਤੇ ਕਲਿੱਕ ਕਰਕੇ ਈਮੇਲ ਪਤਾ ਬਦਲ ਸਕਦੇ ਹੋ';

  @override
  String get profileUpdateMsg => 'ਪ੍ਰੋਫਾਈਲ ਸਫਲਤਾਪੂਰਵਕ ਅੱਪਡੇਟ ਕੀਤੀ ਗਈ';

  @override
  String get emergencyContact => 'ਐਮਰਜੈਂਸੀ ਸੰਪਰਕ';

  @override
  String get areYouSureYouWantDeleteThisNumber =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਸ ਨੰਬਰ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get addContact => 'ਸੰਪਰਕ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get googleMap => 'ਗੂਗਲ ਮੈਪ';

  @override
  String get save => 'ਸੰਭਾਲੋ';

  @override
  String get myRides => 'ਸਫ਼ਰ';

  @override
  String get myWallet => 'ਮੇਰਾ ਬਰਤਨ';

  @override
  String get availableBalance => 'ਉਪਲਬਧ ਬੈਲੈਂਸ';

  @override
  String get recentTransactions => 'ਤਾਜ਼ਾ ਲੈਣ-ਦੇਣ';

  @override
  String get moneyDeposited => 'ਪੈਸਾ ਜਮ੍ਹਾਂ ਕੀਤਾ';

  @override
  String get addMoney => 'ਪੈਸਾ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get cancelTxt => 'ਰੱਦ ਕਰੋ';

  @override
  String get pleaseSelectAmount => 'ਕਿਰਪਾ ਕਰਕੇ ਰਕਮ ਚੁਣੋ';

  @override
  String get amount => 'ਰਕਮ';

  @override
  String get capacity => 'ਸਮਰੱਥਾ';

  @override
  String get paymentMethod => 'ਭੁਗਤਾਨ ਦੀ ਤਰੀਕਾ';

  @override
  String get chooseYouPaymentLate => 'ਹੁਣ ਜਾਂ ਬਾਅਦ ਵਿੱਚ ਭੁਗਤਾਨ ਚੁਣੋ';

  @override
  String get enterPromoCode => 'ਪ੍ਰੋਮੋ ਕੋਡ ਦਰਜ ਕਰੋ';

  @override
  String get confirm => 'ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get forInstantPayment => 'ਤੁਰੰਤ ਭੁਗਤਾਨ ਲਈ';

  @override
  String get bookNow => 'ਹੁਣ ਬੁੱਕ ਕਰੋ';

  @override
  String get wallet => 'ਵਾਲੇਟ';

  @override
  String get paymentDetail => 'ਭੁਗਤਾਨ ਦੀ ਵਿਸਥਾਰ';

  @override
  String get rideId => 'ਸਫ਼ਰ ਆਈਡੀ';

  @override
  String get createdAt => 'ਬਣਾਇਆ ਗਿਆ';

  @override
  String get viewHistory => 'ਤਾਰੀਖ ਦੇਖੋ';

  @override
  String get paymentDetails => 'ਭੁਗਤਾਨ ਵੇਰਵੇ';

  @override
  String get paymentType => 'ਭੁਗਤਾਨ ਦੀ ਕਿਸਮ';

  @override
  String get paymentStatus => 'ਭੁਗਤਾਨ ਦੀ ਸਥਿਤੀ';

  @override
  String get priceDetail => 'ਕੀਮਤ ਵੇਰਵੇ';

  @override
  String get basePrice => 'ਬੇਸ ਕੀਮਤ';

  @override
  String get distancePrice => 'ਦੂਰੀ ਦੀ ਕੀਮਤ';

  @override
  String get timePrice => 'ਸਮਾਂ ਦੀ ਕੀਮਤ';

  @override
  String get waitTime => 'ਰੁਕਣ ਦਾ ਸਮਾਂ';

  @override
  String get extraCharges => 'ਜਿਆਦਾ ਚਾਰਜ';

  @override
  String get couponDiscount => 'ਕੂਪਨ ਛੂਟ';

  @override
  String get total => 'ਕੁੱਲ';

  @override
  String get payment => 'ਭੁਗਤਾਨ';

  @override
  String get cash => 'ਨਗਦ';

  @override
  String get updatePaymentStatus => 'ਭੁਗਤਾਨ ਦੀ ਸਥਿਤੀ ਅੱਪਡੇਟ ਕਰੋ';

  @override
  String get waitingForDriverConformation =>
      'ਡ੍ਰਾਈਵਰ ਦੀ ਪੁਸ਼ਟੀ ਦੀ ਉਡੀਕ ਕਰ ਰਹੇ ਹਨ';

  @override
  String get continueNewRide => 'ਨਵਾਂ ਸਫ਼ਰ ਜਾਰੀ ਰੱਖੋ';

  @override
  String get payToPayment => 'ਭੁਗਤਾਨ ਕਰਨ ਲਈ ਭੁਗਤਾਨ ਕਰੋ';

  @override
  String get tip => 'ਟਿੱਪ';

  @override
  String get pay => 'ਭੁਗਤਾਨ ਕਰੋ';

  @override
  String get howWasYourRide => 'ਤੁਹਾਡਾ ਸਫ਼ਰ ਕਿਵੇਂ ਸੀ?';

  @override
  String get wouldYouLikeToAddTip => 'ਕੀ ਤੁਸੀਂ ਟਿੱਪ ਸ਼ਾਮਿਲ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get addMoreTip => 'ਹੋਰ ਟਿੱਪ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get addMore => 'ਹੋਰ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get addReviews => 'ਟਿੱਪਣੀਆਂ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get writeYourComments => 'ਆਪਣੀ ਟਿੱਪਣੀਆਂ ਲਿਖੋ....';

  @override
  String get continueD => 'ਜਾਰੀ ਰੱਖੋ';

  @override
  String get detailScreen => 'ਸਫ਼ਰ ਦਾ ਵੇਰਵਾ';
  @override
  String get helpdetail => 'ਸਹਾਇਤਾ ਵੇਰਵਾ';

  @override
  String get aboutDriver => 'ਡਰਾਈਵਰ ਬਾਰੇ';

  @override
  String get rideHistory => 'ਸਫ਼ਰ ਦੀ ਤਾਰੀਖ';

  @override
  String get myProfile => 'ਮੇਰਾ ਪ੍ਰੋਫਾਈਲ';

  @override
  String get myTrips => 'ਮੇਰੇ ਯਾਤਰਾ';

  @override
  String get emergencyContacts => 'ਐਮਰਜੈਂਸੀ ਸੰਪਰਕ';

  @override
  String get logOut => 'ਲੌਗਆਊਟ';

  @override
  String get areYouSureYouWantToLogoutThisApp =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਸ ਐਪ ਨੂੰ ਲੌਗਆਊਟ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get whatWouldYouLikeToGo => 'ਤੁਸੀਂ ਕਿੱਥੇ ਜਾਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get enterYourDestination => 'ਆਪਣੀ ਮੰਜ਼ਿਲ ਦਰਜ ਕਰੋ';

  @override
  String get currentLocation => 'ਮੌਜੂਦਾ ਸਥਿਤੀ';

  @override
  String get destinationLocation => 'ਮੰਜ਼ਿਲ ਦੀ ਸਥਿਤੀ';

  @override
  String get chooseOnMap => 'ਨਕਸ਼ੇ ਉੱਤੇ ਚੁਣੋ';

  @override
  String get profile => 'ਪ੍ਰੋਫਾਈਲ';

  @override
  String get theme => 'ਥੀਮ';

  @override
  String get privacyPolicy => 'ਗੋਪਨੀਯਤਾ ਨੀਤੀ';

  @override
  String get helpSupport => 'ਸਹਾਇਤਾ ਅਤੇ ਸਮਰਥਨ';

  @override
  String get termsConditions => 'ਨਿਯਮ ਅਤੇ ਸ਼ਰਤਾਂ';

  @override
  String get aboutUs => 'ਸਾਡੇ ਬਾਰੇ';

  @override
  String get lookingForNearbyDrivers => 'ਨਜ਼ਦੀਕੀ ਡ੍ਰਾਈਵਰਾਂ ਦੀ ਤਲਾਸ਼';

  @override
  String get weAreLookingForNearDriversAcceptsYourRide =>
      'ਅਸੀਂ ਤੁਹਾਡੇ ਸਫ਼ਰ ਨੂੰ ਸਵੀਕਾਰ ਕਰਨ ਲਈ ਨਜ਼ਦੀਕੀ ਡ੍ਰਾਈਵਰਾਂ ਦੀ ਤਲਾਸ਼ ਕਰ ਰਹੇ ਹਾਂ';

  @override
  String get areYouSureYouWantToCancelThisRide =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਸ ਸਫ਼ਰ ਨੂੰ ਰੱਦ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get serviceDetail => 'ਸੇਵਾ ਵੇਰਵਾ';

  @override
  String get get => 'ਪ੍ਰਾਪਤ ਕਰੋ';

  @override
  String get rides => 'ਸਫ਼ਰ';

  @override
  String get people => 'ਲੋਕ';

  @override
  String get fare => 'ਭਾਅ';

  @override
  String get done => 'ਹੋ ਗਿਆ';

  @override
  String get availableOffers => 'ਉਪਲਬਧ ਓਫਰ';

  @override
  String get off => 'ਛੂਟ';

  @override
  String get sendOTP => 'ਵੈਰੀਫਿਕੇਸ਼ਨ ਕੋਡ ਭੇਜੋ';

  @override
  String get otpVerification => 'ਓਟੀਪੀ ਤਸਦੀਕ';

  @override
  String get enterTheCodeSendTo => 'ਜੋ ਕੋਡ ਭੇਜਿਆ ਗਿਆ ਹੈ, ਉਸਨੂੰ ਦਰਜ ਕਰੋ';

  @override
  String get didNotReceiveTheCode => 'ਕੋਡ ਪ੍ਰਾਪਤ ਨਹੀਂ ਹੋਇਆ';

  @override
  String get resend => 'ਦੁਬਾਰਾ ਭੇਜੋ';

  @override
  String get drivingExperience => 'ਡਰਾਈਵਿੰਗ ਅਨੁਭਵ (ਸਾਲ)';

  @override
  String get sos => 'ਐਸਓਐਸ (S.O.S)';

  @override
  String get driverReview => 'ਡਰਾਈਵਰ ਸਮੀਖਿਆ';

  @override
  String get signInUsingYourMobileNumber => 'ਆਪਣੇ ਮੋਬਾਈਲ ਨੰਬਰ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get otp => 'ਵੇਰੀਫਿਕੇਸ਼ਨ ਕੋਡ';

  @override
  String get newRideRequested => 'ਨਵਾਂ ਸਫ਼ਰ ਬੇਨਤੀ ਕੀਤੀ';

  @override
  String get accepted => 'ਸਵੀਕਾਰ ਕੀਤਾ';

  @override
  String get arriving => 'ਪਹੁੰਚਣ ਵਾਲਾ';

  @override
  String get arrived => 'ਪਹੁੰਚ ਗਿਆ';

  @override
  String get inProgress => 'ਜਾਰੀ ਹੈ';

  @override
  String get cancelled => 'ਰੱਦ ਕੀਤਾ';

  @override
  String get completed => 'ਪੂਰਾ ਹੋ ਗਿਆ';

  @override
  String get pleaseEnableLocationPermission =>
      'ਕਿਰਪਾ ਕਰਕੇ ਸਥਿਤੀ ਦੀ ਆਗਿਆ ਨੂੰ ਸਫ਼ਲ ਬਣਾਓ';

  @override
  String get pending => "ਬਕਾਇਆ";

  @override
  String get failed => "ਅਸਫਲ";

  @override
  String get paid => "ਭੁਗਤਾਨ ਕੀਤਾ";

  @override
  String get male => "ਪੁਰਸ਼";

  @override
  String get female => "ਔਰਤ";

  @override
  String get other => "ਹੋਰ";

  @override
  String get addExtraCharges => "ਵਾਧੂ ਚਾਰਜ ਸ਼ਾਮਿਲ ਕਰੋ";

  @override
  String get enterAmount => "ਰਕਮ ਦਰਜ ਕਰੋ";

  @override
  String get pleaseAddedAmount => "ਕਿਰਪਾ ਕਰਕੇ ਰਕਮ ਸ਼ਾਮਿਲ ਕਰੋ";

  @override
  String get title => "ਸ਼ੀਰਸ਼ਕ";

  @override
  String get charges => "ਚਾਰਜ";
    @override
  String get saveCharges => "ਵਿਅਧਾਨ ਸੰਭਾਲੋ";

  @override
  String get bankDetail => 'ਬੈਂਕ ਦੇ ਵੇਰਵੇ';

  @override
  String get bankName => 'ਬੈਂਕ ਦਾ ਨਾਮ';

  @override
  String get bankCode => 'ਬੈਂਕ ਕੋਡ';

  @override
  String get accountHolderName => 'ਖਾਤੇਧਾਰਕ ਦਾ ਨਾਮ';

  @override
  String get accountNumber => 'ਖਾਤਾ ਨੰਬਰ';

  @override
  String get updateBankDetail => 'ਬੈਂਕ ਦੇ ਵੇਰਵੇ ਅਪਡੇਟ ਕਰੋ';

  @override
  String get addBankDetail => 'ਬੈਂਕ ਦੇ ਵੇਰਵੇ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get bankInfoUpdateSuccessfully => 'ਬੈਂਕ ਜਾਣਕਾਰੀ ਸਫਲਤਾ ਨਾਲ ਅਪਡੇਟ ਕੀਤੀ ਗਈ';

  @override
  String get vehicleDetail => 'ਵਾਹਨ ਦੇ ਵੇਰਵੇ';

  @override
  String get document => 'ਡੌਕੂਮੈਂਟ';

  @override
  String get setting => 'ਸੈਟਿੰਗ';

  @override
  String get youAreOnlineNow => "ਤੁਸੀਂ ਹੁਣ ਔਨਲਾਈਨ ਹੋ";

  @override
  String get youAreOfflineNow => "ਤੁਸੀਂ ਹੁਣ ਔਫਲਾਈਨ ਹੋ";

  @override
  String get requests => 'ਬੇਨਤੀਆਂ';

  @override
  String get areYouSureYouWantToCancelThisRequest =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਸ ਬੇਨਤੀ ਨੂੰ ਰੱਦ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get decline => 'ਅਸਵੀਕਾਰ ਕਰੋ';

  @override
  String get accept => 'ਸਵੀਕਾਰ ਕਰੋ';

  @override
  String get areYouSureYouWantToAcceptThisRequest =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਸ ਬੇਨਤੀ ਨੂੰ ਸਵੀਕਾਰ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get call => 'ਕਾਲ ਕਰੋ';

  @override
  String get chat => 'ਚੈਟ';

  @override
  String get applyExtraFree => 'ਵਾਧੂ ਮੁਫ਼ਤ ਲਾਗੂ ਕਰੋ?';

  @override
  String get areYouSureYouWantToArriving =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਆਉਣ ਵਾਲੇ ਨੂੰ ਚਿੰਨ੍ਹਤ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get areYouSureYouWantToArrived => 'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਪਹੁੰਚ ਗਏ ਹੋ?';

  @override
  String get enterOtp => 'ਵੇਰੀਫਿਕੇਸ਼ਨ ਕੋਡ ਦਰਜ ਕਰੋ';

  @override
  String get enterTheOtpDisplayInCustomersMobileToStartTheRide =>
      'ਸਫ਼ਰ ਸ਼ੁਰੂ ਕਰਨ ਲਈ ਗਾਹਕ ਦੇ ਮੋਬਾਈਲ ਵਿੱਚ ਦਿਖਾਏ ਗਏ ਕੋਡ ਨੂੰ ਦਰਜ ਕਰੋ';

  @override
  String get enterTheOtpDisplayInCustomersMobileToEndTheRide =>
      'ਸਫ਼ਰ ਖਤਮ ਕਰਨ ਲਈ ਗਾਹਕ ਦੇ ਮੋਬਾਈਲ ਵਿੱਚ ਦਿਖਾਏ ਗਏ ਕੋਡ ਨੂੰ ਦਰਜ ਕਰੋ';

  @override
  String get pleaseEnterValidOtp => 'ਕਿਰਪਾ ਕਰਕੇ ਸਹੀ ਵੇਰੀਫਿਕੇਸ਼ਨ ਕੋਡ ਦਰਜ ਕਰੋ';

  @override
  String get areYouSureYouWantToCompletedThisRide =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਸ ਸਫ਼ਰ ਨੂੰ ਪੂਰਾ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get updateBankInfo => 'ਬੈਂਕ ਦੀ ਜਾਣਕਾਰੀ ਅਪਡੇਟ ਕਰੋ';

  @override
  String get regisTRation => 'ਰਜਿਸਟ੍ਰੇਸ਼ਨ';

  @override
  String get pleaseSelectVehiclePreferences =>
      'ਕਿਰਪਾ ਕਰਕੇ ਵਾਹਨ ਦੀ ਪਸੰਦ ਚੁਣੋ';

  @override
  String get userDetail => 'ਚੌਫ਼ਰ ਦੇ ਵੇਰਵੇ';

  @override
  String get selectVehiclePreferences => 'ਵਾਹਨ ਦੀ ਪਸੰਦ ਚੁਣੋ';

  @override
  String get selectGender => 'ਜੈਂਡਰ ਚੁਣੋ';

  @override
  String get age => 'ਉਮਰ';

  @override
  String get socialSecurityNumber => 'ਸਮਾਜਿਕ ਸੁਰੱਖਿਆ ਨੰਬਰ';

  @override
  String get nightDrivingPreference => 'ਰਾਤ ਦੀ ਡ੍ਰਾਈਵਿੰਗ ਦੀ ਪਸੰਦ';

  @override
  String get withDraw => 'ਪੈਸਾ ਨਿਕਾਸ';

  @override
  String get withdrawHistory => 'ਪੈਸਾ ਨਿਕਾਸ ਦਾ ਇਤਿਹਾਸ';

  @override
  String get approved => 'ਮੰਜ਼ੂਰ ਕੀਤਾ';

  @override
  String get requested => 'ਬੇਨਤੀ ਕੀਤੀ';

  @override
  String get updateVehicle => 'ਵਾਹਨ ਅਪਡੇਟ ਕਰੋ';

  @override
  String get userNotApproveMsg =>
      'ਤੁਹਾਡਾ ਪ੍ਰੋਫਾਈਲ ਸਮੀਖਿਆ ਹੇਠਾਂ ਹੈ। ਕੁਝ ਸਮਾਂ ਰੁਕੋ ਜਾਂ ਆਪਣੇ ਪ੍ਰਬੰਧਕ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।';

  @override
  String get uploadFileConfirmationMsg =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਹ ਫਾਈਲ ਅਪਲੋਡ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get selectDocument => 'ਡੌਕੂਮੈਂਟ ਚੁਣੋ';

  @override
  String get addDocument => 'ਡੌਕੂਮੈਂਟ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get areYouSureYouWantToDeleteThisDocument =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਸ ਡੌਕੂਮੈਂਟ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get expireDate => 'ਮਿਆਦ ਖਤਮ ਹੋਣ ਦੀ ਤਾਰੀਖ';

  @override
  String get goDashBoard => 'ਡੈਸ਼ਬੋਰਡ ਤੇ ਜਾਓ';

  @override
  String get deleteAccount => 'ਖਾਤਾ ਮਿਟਾਓ';

  @override
  String get account => 'ਖਾਤਾ';

  @override
  String get areYouSureYouWantPleaseReadAffect =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਆਪਣਾ ਖਾਤਾ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ? ਕਿਰਪਾ ਕਰਕੇ ਪੜ੍ਹੋ ਕਿ ਖਾਤਾ ਮਿਟਾਉਣ ਦਾ ਕੀ ਪ੍ਰਭਾਵ ਪਵੇਗਾ।';

  @override
  String get deletingAccountEmail =>
      'ਆਪਣਾ ਖਾਤਾ ਮਿਟਾਉਣ ਨਾਲ ਸਾਡੇ ਡੇਟਾਬੇਸ ਤੋਂ ਨਿੱਜੀ ਜਾਣਕਾਰੀ ਹਟਾਈ ਜਾਂਦੀ ਹੈ। ਤੁਹਾਡਾ ਈਮੇਲ ਸਦਾ ਲਈ ਰੀਜ਼ਰਵਡ ਹੋ ਜਾਂਦਾ ਹੈ ਅਤੇ ਇੱਕ ਨਵਾਂ ਖਾਤਾ ਰਜਿਸਟਰ ਕਰਨ ਲਈ ਉਹੇ ਈਮੇਲ ਮੁੜ ਵਰਤਿਆ ਨਹੀਂ ਜਾ ਸਕਦਾ।';

  @override
  String get areYouSureYouWantDeleteAccount =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਖਾਤਾ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get yourInternetIsNotWorking => 'ਤੁਹਾਡੀ ਇੰਟਰਨੈਟ ਕੰਮ ਨਹੀਂ ਕਰ ਰਹੀ';

  @override
  String get allow => 'ਆਗਿਆ ਦਿਓ';

  @override
  String get mostReliableMightyDriverApp => 'ਸਭ ਤੋਂ ਭਰੋਸੇਯੋਗ ਚੌਫ਼ਰ ਐਪ';

  @override
  String get toEnjoyYourRideExperiencePleaseAllowPermissions =>
      'ਆਪਣੇ ਸਫ਼ਰ ਦੇ ਅਨੁਭਵ ਦਾ ਆਨੰਦ ਮਾਣਨ ਲਈ\nਕਿਰਪਾ ਕਰਕੇ ਸਾਨੂੰ ਹੇਠ ਲਿਖੀਆਂ ਆਗਿਆਵਾਂ ਦਿਓ';

  @override
  String get cashCollected => 'ਨਕਦ ਇਕੱਠਾ ਕੀਤਾ';

  @override
  String get areYouSureCollectThisPayment =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਹ ਭੁਗਤਾਨ ਇਕੱਠਾ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get txtURLEmpty => "ਯੂਆਰਐਲ ਖਾਲੀ ਹੈ";

  @override
  String get lblFollowUs => "ਸਾਨੂੰ ਫੋਲੋ ਕਰੋ";

  @override
  String get bankInfo => "ਬੈਂਕ ਦੀ ਜਾਣਕਾਰੀ";

  @override
  String get duration => 'ਅਵਧੀ';

  @override
  String get paymentVia => 'ਭੁਗਤਾਨ ਰਾਹੀਂ';

  @override
  String get moneyDebit => 'ਪੈਸਾ ਡੈਬਿਟ';

  @override
  String get roooCare => 'ਚੌਫ਼ਰ ਦੇਖਭਾਲ';

  @override
  String get demoMsg => 'ਟੈਸਟਰ ਰੋਲ ਨੂੰ ਇਹ ਕਾਰਵਾਈ ਕਰਨ ਦੀ ਆਗਿਆ ਨਹੀਂ ਹੈ';

  @override
  String get youCannotChangePhoneNumber =>
      'ਤੁਸੀਂ ਫ਼ੋਨ ਨੰਬਰ ਨੂੰ ਸੰਪਾਦਿਤ ਬਟਨ ਤੇ ਕਲਿੱਕ ਕਰਕੇ ਬਦਲ ਸਕਦੇ ਹੋ';

  @override
  String get offLine => 'ਆਫਲਾਈਨ';

  @override
  String get online => 'ਆਨਲਾਈਨ';

  @override
  String get walletLessAmountMsg =>
      'ਤੁਸੀਂ ਸਫ਼ਰ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਤੁਹਾਡੇ ਪਾਸ ਆਪਣੇ ਵੈਲਟ ਵਿੱਚ ਘੱਟ ਪੈਸੇ ਹਨ। ਇਸ ਲਈ ਤੁਹਾਨੂੰ ਆਪਣੇ ਵੈਲਟ ਵਿੱਚ ਪੈਸੇ ਸ਼ਾਮਿਲ ਕਰਨੇ ਪੈਣਗੇ ਤਾਂ ਜੋ ਤੁਸੀਂ ਬਾਅਦ ਵਿੱਚ ਸਫ਼ਰ ਕਰ ਸਕੋ।';

  @override
  String get aboutRider => 'ਰਾਈਡਰ ਬਾਰੇ';

  @override
  String get pleaseEnterMessage => 'ਕਿਰਪਾ ਕਰਕੇ ਸੁਨੇਹਾ ਦਰਜ ਕਰੋ';

  @override
  String get complainList => 'ਸ਼ਿਕਾਇਤ ਸੂਚੀ';

  @override
  String get viewAll => 'ਸਭ ਦੇਖੋ';

  @override
  String get pleaseSelectRating => 'ਕਿਰਪਾ ਕਰਕੇ ਰੇਟਿੰਗ ਚੁਣੋ';

  @override
  String get serviceInfo => 'ਸੇਵਾ ਜਾਣਕਾਰੀ';

  @override
  String get youCannotChangeService => 'ਤੁਸੀਂ ਸੇਵਾ ਬਦਲ ਨਹੀਂ ਸਕਦੇ';

  @override
  String get vehicleInfoUpdateSucessfully => 'ਵਾਹਨ ਦੀ ਜਾਣਕਾਰੀ ਸਫ਼ਲਤਾ ਨਾਲ ਅਪਡੇਟ ਕੀਤੀ ਗਈ';

  @override
  String get subscription => 'ਸਬਸਕ੍ਰਿਪਸ਼ਨ';

  @override
  String get yourCurrentBalanceIs => 'ਤੁਹਾਡਾ ਮੌਜੂਦਾ ਬਕਾਇਆ ਹੈ:';

  @override
  String get yourSubscriptionPlanIsOver =>
      'ਤੁਹਾਡੀ ਸਬਸਕ੍ਰਿਪਸ਼ਨ ਯੋਜਨਾ ਖਤਮ ਹੋ ਗਈ ਹੈ। ਇਸ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ ਵਰਤਣ ਲਈ ਕਿਰਪਾ ਕਰਕੇ ਸਬਸਕ੍ਰਾਈਬ ਕਰੋ';

  @override
  String get perDay => 'ਪ੍ਰਤੀ ਦਿਨ';

  @override
  String get renew => 'ਨਵਾਂ ਕਰੋ';

  @override
  String get yourWalletDoNotHaveEnoughBalance =>
      'ਤੁਹਾਡੇ ਵੈਲਟ ਵਿੱਚ ਪ੍ਰਾਪਤ ਬਕਾਇਆ ਨਹੀਂ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਵੈਲਟ ਵਿੱਚ ਬਕਾਇਆ ਸ਼ਾਮਿਲ ਕਰੋ ਫਿਰ ਆਪਣੀ ਸਬਸਕ੍ਰਿਪਸ਼ਨ ਯੋਜਨਾ ਨਵਾਂ ਕਰੋ।';

  @override
  String get addWallet => 'ਵੈਲਟ ਸ਼ਾਮਿਲ ਕਰੋ';

  @override
  String get yourDailyAppUseLimitHasBeenExpired =>
      'ਤੁਹਾਡਾ ਦੈਨੀਕ ਐਪ ਵਰਤਣ ਦੀ ਸੀਮਾ ਖਤਮ ਹੋ ਗਈ ਹੈ। ਚਾਰਜ ਕਰਨ ਲਈ ਟੈਪ ਕਰੋ ਅਤੇ ਐਪ ਵਰਤੋ।';

  @override
  String get recharge => 'ਰੀਚਾਰਜ';

  @override
  String get isMandatoryDocument => '* ਇੱਕ ਲਾਜ਼ਮੀ ਦਸਤਾਵੇਜ਼ ਹੈ।';

  @override
  String get someRequiredDocumentAreNotUploaded =>
      'ਕੁਝ ਲਾਜ਼ਮੀ ਦਸਤਾਵੇਜ਼ ਅਪਲੋਡ ਨਹੀਂ ਕੀਤੇ ਗਏ ਹਨ। ਕਿਰਪਾ ਕਰਕੇ ਸਾਰੇ ਲਾਜ਼ਮੀ ਦਸਤਾਵੇਜ਼ ਅਪਲੋਡ ਕਰੋ।';

  @override
  String get areYouCertainOffline => 'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਆਫਲਾਈਨ ਜਾਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get areYouCertainOnline => 'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਆਨਲਾਈਨ ਜਾਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get pleaseAcceptTermsOfServicePrivacyPolicy =>
      'ਕਿਰਪਾ ਕਰਕੇ ਸੇਵਾ ਦੀਆਂ ਸ਼ਰਤਾਂ ਅਤੇ ਪ੍ਰਾਈਵੇਸੀ ਪਾਲਿਸੀ ਸਵੀਕਾਰ ਕਰੋ';

  @override
  String get rememberMe => 'ਮੈਨੂੰ ਯਾਦ ਰੱਖੋ';

  @override
  String get agreeToThe => 'ਮੈਂ ਸਹਿਮਤ ਹਾਂ';

  @override
  String get invoice => 'ਚਿੱਠੀ';

  @override
  String get riderInformation => 'ਰਾਈਡਰ ਜਾਣਕਾਰੀ';

  @override
  String get customerName => 'ਗਾਹਕ ਦਾ ਨਾਮ';

  @override
  String get sourceLocation => 'ਸਰੋਤ ਸਥਾਨ';

  @override
  String get invoiceNo => 'ਚਿੱਠੀ ਨੰਬਰ';

  @override
  String get invoiceDate => 'ਚਿੱਠੀ ਦੀ ਤਾਰੀਖ';

  @override
  String get orderedDate => 'ਆਰਡਰ ਕੀਤੀ ਤਾਰੀਖ';

  @override
  String get totalCash => 'ਕੁੱਲ ਨਕਦ';

  @override
  String get totalRide => 'ਕੁੱਲ ਸਫ਼ਰ ਦੀ ਰਕਮ';

  @override
  String get totalWallet => 'ਕੁੱਲ ਵੈਲਟ';

  @override
  String get totalEarning => 'ਕੁੱਲ ਕਮਾਈ';

  @override
  String get pleaseSelectFromDateAndToDate =>
      'ਕਿਰਪਾ ਕਰਕੇ ਤਾਰੀਖ ਤੋਂ ਅਤੇ ਤਾਰੀਖ ਤੱਕ ਚੁਣੋ';

  @override
  String get from => 'ਤੋਂ';

  @override
  String get fromDate => 'ਤਾਰੀਖ ਤੋਂ';

  @override
  String get to => 'ਤੱਕ';

  @override
  String get toDate => 'ਤਾਰੀਖ ਤੱਕ';

  @override
  String get ride => 'ਸਫ਼ਰ';

  @override
  String get todayRide => 'ਅੱਜ ਦਾ ਸਫ਼ਰ';

  @override
  String get weeklyOrderCount => 'ਹਫ਼ਤਾਵਾਰੀ ਆਰਡਰ ਗਿਣਤੀ';

  @override
  String get distance => 'ਅੰਤਰ';

  @override
  String get rideInformation => 'ਸਫ਼ਰ ਜਾਣਕਾਰੀ';

  @override
  String get iAgreeToThe => 'ਮੈਂ ਸਹਿਮਤ ਹਾਂ';

  @override
  String get today => 'ਅੱਜ';

  @override
  String get weekly => 'ਹਫ਼ਤਾਵਾਰੀ';

  @override
  String get report => 'ਰਿਪੋਰਟ';

  @override
  String get earning => 'ਕਮਾਈ';

  @override
  String get todayEarning => 'ਅੱਜ ਦੀ ਕਮਾਈ';

  @override
  String get available => 'ਉਪਲਬਧ';

  @override
  String get notAvailable => 'ਉਪਲਬਧ ਨਹੀਂ';

  @override
  String get youWillReceiveNewRidersAndNotifications =>
      'ਤੁਸੀਂ ਨਵੇਂ ਰਾਈਡਰ ਅਤੇ ਸੁਚਨਾਵਾਂ ਪ੍ਰਾਪਤ ਕਰੋਗੇ';

  @override
  String get youWillNotReceiveNewRidersAndNotifications =>
      'ਤੁਸੀਂ ਨਵੇਂ ਰਾਈਡਰ ਅਤੇ ਸੁਚਨਾਵਾਂ ਪ੍ਰਾਪਤ ਨਹੀਂ ਕਰੋਗੇ';

  @override
  String get yourAccountIs => 'ਤੁਹਾਡਾ ਖਾਤਾ ਹੈ';

  @override
  String get pleaseContactSystemAdministrator =>
      'ਕਿਰਪਾ ਕਰਕੇ ਸਿਸਟਮ ਪ੍ਰਬੰਧਕ ਨਾਲ ਸੰਪਰਕ ਕਰੋ';

  @override
  String get youCanNotThisActionsPerformBecauseYourCurrentRideIsNotCompleted =>
      'ਤੁਸੀਂ ਇਹ ਕਾਰਵਾਈ ਨਹੀਂ ਕਰ ਸਕਦੇ ਕਿਉਂਕਿ ਤੁਹਾਡਾ ਮੌਜੂਦਾ ਸਫ਼ਰ ਪੂਰਾ ਨਹੀਂ ਹੋਇਆ';

  @override
  String get selectDisplayPicture => 'ਪ੍ਰੋਫਾਈਲ ਤਸਵੀਰ ਚੁਣੋ';

  @override
  String get invalidAge => 'ਉਮਰ 18 ਸਾਲ ਜਾਂ ਉਸ ਤੋਂ ਵੱਧ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ';

  @override
  String get go => "ਜਾਓ";

  @override
  String get inbox => "ਇੰਬਾਕਸ";

  @override
  String get referralCompleted => 'ਲਕਸ਼ ਪੂਰੇ ਹੋਏ';

  @override
  String get referralEarning => 'ਤੁਹਾਡਾ ਬਣਾਇਆ';

  @override
  String get continueText => 'ਜਾਰੀ ਰੱਖੋ';

  @override
  String get enterYourMobileNumber => 'ਤੁਹਾਡਾ ਮੋਬਾਈਲ ਨੰਬਰ ਦਰਜ ਕਰੋ';

  @override
  String get orText => 'ਜਾਂ';

  @override
  String get signInWithFacebook => 'ਫੇਸਬੁੱਕ ਨਾਲ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get signInWithGoogle => 'ਗੂਗਲ ਨਾਲ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get inBoxTxt => 'ਇੰਬਾਕਸ';

  @override
  String get referralsTxt => 'ਰੈਫਰਲ';

  @override
  String get opportunityTxt => 'ਮੌਕੇ';

  @override
  String get accountTxt => 'ਖਾਤਾ';

  @override
  String get earningTxt => 'ਕਮਾਈ';

  @override
  String get helpTxt => 'ਸਹਾਇਤਾ';

  @override
  String get learningCenterTxt => 'ਲਰਨਿੰਗ ਸੈਂਟਰ';

  @override
  String get completedTxt => 'ਪੂਰਾ ਕੀਤਾ';

@override
String get faqsTxt => 'ਆਮ ਸਵਾਲ (FAQs)';

  @override
  String get settingTxt => 'ਸੈਟਿੰਗਸ';

  @override
  String get nightModeTxt => 'ਰਾਤ ਮੋਡ';

  @override
  String get helpWithTripTxt => 'ਸਫ਼ਰ ਵਿੱਚ ਸਹਾਇਤਾ';




@override
String get verifyOTP => "ਤਸਦੀਕ ਕਰੋ";

@override
String get verifyOTPHeading => 'ਕਿਰਪਾ ਕਰਕੇ 6 ਅੰਕਾਂ ਦਾ ਕੋਡ ਦਾਖਲ ਕਰੋ ਜੋ ਤੁਹਾਨੂੰ ਭੇਜਿਆ ਗਿਆ ਹੈ';

@override
String get enterOTP => 'ਕਿਰਪਾ ਕਰਕੇ ਤਸਦੀਕੀ ਕੋਡ ਦਾਖਲ ਕਰੋ';

@override
String get invalidOTP => 'ਅਵੈਧ ਤਸਦੀਕੀ ਕੋਡ';

@override
String get referralCode => 'ਰੈਫਰਲ ਕੋਡ';

@override
String get allMessageTxt => 'ਸਾਰੇ ਸੁਨੇਹੇ';

@override
String get bankInfoTxt => "ਬੈਂਕ ਜਾਣਕਾਰੀ";
@override
String get offerTxt => "ਪੇਸ਼ਕਸ਼";
@override
String get statusTxt => "ਸਥਿਤੀ";

@override
String get balanceTxt => "ਬਕਾਇਆ";
@override
String get paymentScheduledTxt => "ਭੁਗਤਾਨ ਤਹਿਤ ਹੈ";
@override
String get paymentMethodsTxt => "ਭੁਗਤਾਨ ਦੇ ਤਰੀਕੇ";

@override
String get cashOutTxt => "ਨਕਦ ਕਢੋ";

@override
String get walletTxt => "ਵਾਲਟ";

@override
String get verifyPhoneMsg => 'ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਫੋਨ ਤਸਦੀਕ ਕਰੋ';

@override
String get errorMsg => 'ਕੁਝ ਗਲਤ ਹੋ ਗਿਆ';

@override
String get SorryYouDontHaveAnyOpportunitiesTxt =>
    "ਮਾਫ ਕਰਨਾ, ਤੁਹਾਡੇ ਕੋਲ ਕੋਈ ਮੌਕੇ ਨਹੀਂ ਹਨ";
@override
String get NewOpportunitiesAreComingTxt => "ਨਵੇਂ ਮੌਕੇ ਆ ਰਹੇ ਹਨ";

@override
String get stayTunedTxt => "ਬਣੇ ਰਹੋ";
@override
String get roooCareTxt => "ਚੌਫਰ ਕేర్";
@override
String get NotificationsTxt => "ਸੂਚਨਾਵਾਂ";
@override
String get WeeklyOrderCountTxt => "ਹਫ਼ਤਾਵਾਰੀ ਆਰਡਰ ਗਿਣਤੀ";
@override
String get cashoutTxt => "ਨਕਦ ਕਢੋ";
@override
String get TotalCashTxt => "ਕੁੱਲ ਨਕਦ";
@override
String get TotalWalletTxt => "ਕੁੱਲ ਵਾਲਟ";
@override
String get TotalRideTxt => "ਕੁੱਲ ਸਫ਼ਰ";
@override
String get adminNotifiedTxt => "ਪ੍ਰਸ਼ਾਸਕ ਨੂੰ ਸੂਚਿਤ ਕੀਤਾ ਗਿਆ";

@override
String get YouAreOnlineNowTxt => "ਤੁਸੀਂ ਹੁਣ ਆਨਲਾਈਨ ਹੋ";

@override
String get AreYouSureYouWantToNotifyAdminTxt =>
    "ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਪ੍ਰਸ਼ਾਸਕ ਨੂੰ ਸੂਚਿਤ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ";

@override
String get PleaseWaitForTimerTxt => "ਕਿਰਪਾ ਕਰਕੇ ਘੜੀ ਲਈ ਉਡੀਕ ਕਰੋ";

@override
String get NotifiedAdminTxt => "ਪ੍ਰਸ਼ਾਸਕ ਨੂੰ ਸੂਚਿਤ ਕੀਤਾ";

@override
String get BookYourroooIn3EasyStepsTxt =>
    "ਆਪਣੀ ਚੌਫਰ ਬੁੱਕ ਕਰੋ ਸਿਰਫ 3 ਅਸਾਨ ਕਦਮਾਂ ਵਿੱਚ";

@override
String get NowBookingYourroooIsEasyTxt =>
    "ਹੁਣ ਆਪਣੀ ਚੌਫਰ ਦੀ ਬੁੱਕਿੰਗ ਅਸਾਨ ਹੈ!";

@override
String get PleaseUploadAllTheDocumentsAndWaitForVerificationTxt =>
    "ਕਿਰਪਾ ਕਰਕੇ ਸਾਰੇ ਦਸਤਾਵੇਜ਼ ਅਪਲੋਡ ਕਰੋ ਅਤੇ ਤਸਦੀਕ ਲਈ ਉਡੀਕ ਕਰੋ";

@override
String get OkTxt => "ਠੀਕ ਹੈ";

@override
String get goOfflineTxt => "ਆਫਲਾਈਨ ਜਾਓ";

@override
String get clickCarPhotographsTxt => "ਕਾਰ ਦੀਆਂ ਫੋਟੋਗ੍ਰਾਫ਼ ਖਿੱਚੋ";

@override
String get frontTxt => "ਸਾਹਮਣੇ";
@override
String get backTxt => "ਪਿੱਛੇ";
@override
String get leftTxt => "ਖੱਬੇ";
@override
String get rightTxt => "ਸੱਜੇ";

@override
String get WelcomeTxt => "ਸਵਾਗਤ ਹੈ";

@override
String get UserTxt => "ਉਪਭੋਗਤਾ";

@override
String get pleaseUploadImageProperlyTxt => "ਕਿਰਪਾ ਕਰਕੇ ਸਾਰੀਆਂ ਚਿੱਤਰਾਂ ਨੂੰ ਸਹੀ ਤਰ੍ਹਾਂ ਅਪਲੋਡ ਕਰੋ";

@override
String get IhaventRecievedACodeTxt => "ਮੈਨੂੰ ਕੋਈ ਕੋਡ ਨਹੀਂ ਮਿਲਿਆ";
@override
String get CONTINUETxt => "ਜਾਰੀ ਰੱਖੋ";
@override
String get NotUploadedTxt => "ਅਪਲੋਡ ਨਹੀਂ ਕੀਤਾ";
@override
String get CompletedTxt => "ਮੁਕੰਮਲ ਕੀਤਾ";
@override
String get rejectedTxt => "ਰੱਦ ਕੀਤਾ";
@override
String get pendingTxt => "ਬਕਾਇਆ";
@override
String get resendOTPTxt => "ਸਰਟੀਫਿਕੇਸ਼ਨ ਕੋਡ ਦੁਬਾਰਾ ਭੇਜੋ";
@override
String get uploadTxt => "ਅਪਲੋਡ ਕਰੋ";

@override
String get accountHolderNameTxt =>
    "ਖਾਤਾ ਧਾਰਕ ਦਾ ਨਾਮ (ਜਿਵੇਂ ਤੁਹਾਡੇ ਬੈਂਕ ਬਿਆਨਾਂ ਵਿੱਚ ਦਿਖਦਾ ਹੈ)";
@override
String get pleaseEnterTheBenificieryNameTxt =>
    "ਕਿਰਪਾ ਕਰਕੇ ਖਾਤੇ ਦੇ ਲਾਭਪਾਤਰੀ ਦਾ ਨਾਮ ਦਾਖਲ ਕਰੋ, ਜੇ ਖਾਤਾ ਕਾਰੋਬਾਰ ਦੇ ਨਾਂ ਤੇ ਦਰਜ ਹੈ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਖਾਤੇ ਤੇ ਕਾਰੋਬਾਰ ਦਾ ਨਾਮ ਦਿਓ";
@override
String get PleaseEnterTheBeneficieryAddressOnABankAccountTxt =>
    "ਕਿਰਪਾ ਕਰਕੇ ਬੈਂਕ ਖਾਤੇ ਤੇ ਲਾਭਪਾਤਰੀ ਦਾ ਪਤਾ ਦਿਓ";
@override
String get cityTxt => "ਸ਼ਹਿਰ";
@override
String get PleaseEnterYourCityTxt => "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਸ਼ਹਿਰ ਦਾਖਲ ਕਰੋ";
@override
String get postalTxt => "ਡਾਕ ਕੋਡ";
@override
String get PleaseEnterTheBeneficieryPostalCodeTxt =>
    "ਕਿਰਪਾ ਕਰਕੇ ਬੈਂਕ ਖਾਤੇ ਤੇ ਲਾਭਪਾਤਰੀ ਦਾ ਡਾਕ ਕੋਡ ਦਾਖਲ ਕਰੋ, ਜੇ ਖਾਤਾ ਕਾਰੋਬਾਰ ਦੇ ਨਾਂ ਤੇ ਦਰਜ ਹੈ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਖਾਤੇ ਤੇ ਕਾਰੋਬਾਰ ਦਾ ਡਾਕ ਕੋਡ ਦਿਓ";
@override
String get DateOfBirthTxt => "ਜਨਮ ਮਿਤੀ";
@override
String get BankNameTxt => "ਬੈਂਕ ਦਾ ਨਾਮ";
@override
String get BankameOthersTxt => "ਬੈਂਕ ਦਾ ਨਾਮ (ਹੋਰ)";
@override
String get BankOfNovoScotioTxt => "ਉਦਾਹਰਣ ਲਈ: ਨੋਵਾ ਸਕੋਟੀਆ ਬੈਂਕ (BNS)";
@override
String get InstituitionNumberTxt => "ਸੰਸਥਾ ਨੰਬਰ";
@override
String get InstitutionNumberMustContainAtLeastDdigitsTxt =>
    "ਸੰਸਥਾ ਨੰਬਰ ਵਿੱਚ ਘੱਟੋ ਘੱਟ 3 ਅੰਕ ਹੋਣੇ ਚਾਹੀਦੇ ਹਨ";
@override
String get TransitNumberTxt => "ਟ੍ਰਾਂਜ਼ਿਟ ਨੰਬਰ";
@override
String get TransitNumberMustContainAtLeastDigitsTxt =>
    "ਟ੍ਰਾਂਜ਼ਿਟ ਨੰਬਰ ਵਿੱਚ ਘੱਟੋ ਘੱਟ 5 ਅੰਕ ਹੋਣੇ ਚਾਹੀਦੇ ਹਨ";
@override
String get BankAccountNumberTxt => "ਬੈਂਕ ਖਾਤਾ ਨੰਬਰ";
@override
String get AccountNumberMustContainAtLeastDigitsTxt =>
    "ਖਾਤਾ ਨੰਬਰ ਵਿੱਚ ਘੱਟੋ ਘੱਟ 7 ਅੰਕ ਹੋਣੇ ਚਾਹੀਦੇ ਹਨ";
@override
String get FillCorrectAccountNumberTxt => "ਸਹੀ ਖਾਤਾ ਨੰਬਰ ਭਰੋ";
@override
String get ReEnterBankAccountNumberTxt => "ਬੈਂਕ ਖਾਤਾ ਨੰਬਰ ਦੁਬਾਰਾ ਦਾਖਲ ਕਰੋ";
@override
String get PleaseReEnterYourAccountNumberTxt =>
    "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਖਾਤਾ ਨੰਬਰ ਦੁਬਾਰਾ ਦਾਖਲ ਕਰੋ ਤਾਂ ਜੋ ਅਸੀਂ ਤੁਹਾਡੇ ਦਾਖਲ ਕੀਤੇ ਖਾਤਾ ਨੰਬਰ ਦੀ ਪ੍ਰਮਾਣਤਾ ਕਰ ਸਕੀਏ";
@override
String get TermsTxt => "ਨਿਯਮ";
@override
String get ShareYourLinkTxt => 'ਆਪਣਾ ਲਿੰਕ ਸਾਂਝਾ ਕਰੋ';
@override
String get ReEnteredAccountIsWrongTxt => "ਦੁਬਾਰਾ ਦਾਖਲ ਕੀਤਾ ਨੰਬਰ ਗਲਤ ਹੈ";

@override
String get waitingTime => "ਉਡੀਕ ਦਾ ਸਮਾਂ";

@override
String get waitingTimeOverTxt => "ਉਡੀਕ ਦਾ ਸਮਾਂ ਖਤਮ ਹੋ ਗਿਆ";

@override
String get cancelTimerTxt => "ਘੜੀ ਖਤਮ ਕਰੋ";

@override
String get wouldYouLikeToStartIdleTimeTxt =>
    "ਕੀ ਤੁਸੀਂ ਖਾਲੀ ਸਮਾਂ ਸ਼ੁਰੂ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ";

String get aboutUsTxt =>
    "ਚੌਫਰ: ਤੁਹਾਡਾ ਵਿਸ਼ਵਾਸਯੋਗ ਸਫ਼ਰ ਸਾਥੀ। ਅਸੀਂ ਤੁਹਾਨੂੰ ਸੇਰ ਸਫ਼ਰ ਲਈ ਸਰਵੋਤਮ ਡਰਾਈਵਰਾਂ ਨਾਲ ਜੋੜਦੇ ਹਾਂ। ਤੁਹਾਡੀ ਸਹੂਲਤ, ਸਾਡੀ ਪ੍ਰਾਇਕਤਾ";

@override
String get referralTxt => "ਰੈਫਰਲ";

@override
String get promotionsTxt => "ਪ੍ਰਮੋਸ਼ਨ";

@override
String get inviteTxt => "ਸੱਦਾ";

@override
String get inviteFromContactsTxt => "ਸੰਪਰਕਾਂ ਤੋਂ ਸੱਦਾ";

@override
String get termsApplyTxt => "ਨਿਯਮ ਲਾਗੂ ਹੁੰਦੇ ਹਨ";

@override
String get totalReferralsTxt => "ਕੁੱਲ ਰੈਫਰਲ";

@override
String get closedTxt => "ਬੰਦ";

@override
String get AdminalreadynotifiedTxt => "ਪ੍ਰਸ਼ਾਸਕ ਨੂੰ ਪਹਿਲਾਂ ਹੀ ਸੂਚਿਤ ਕੀਤਾ ਗਿਆ ਹੈ";

@override
String get PleasewaitTxt => "ਕਿਰਪਾ ਕਰਕੇ ਉਡੀਕ ਕਰੋ";

@override
String get NotifyToAdminAboutLateRideTxt => "ਦੇਰ ਨਾਲ ਆਈ ਸਫ਼ਰ ਬਾਰੇ ਪ੍ਰਸ਼ਾਸਕ ਨੂੰ ਸੂਚਿਤ ਕਰੋ";

@override
String get YouHaveReachedTheDestinationTxt =>
    "ਕੀ ਤੁਸੀਂ ਮੰਜ਼ਿਲ ਤੇ ਪਹੁੰਚ ਗਏ ਹੋ";

@override
String get upload4ImagesWarningTxt =>
    "ਕਿਰਪਾ ਕਰਕੇ ਦਿੱਤੇ ਉਦਾਹਰਣ ਦੇ ਅਨੁਸਾਰ ਚਿੱਤਰ ਅਪਲੋਡ ਕਰੋ, ਨਹੀਂ ਤਾਂ ਤੁਹਾਡਾ ਖਾਤਾ ਅਸਥਾਈ ਤੌਰ ਤੇ ਬੰਦ ਹੋ ਸਕਦਾ ਹੈ ਜਾਂ ਇਸ ਤੇ ਸਥਾਈ ਪਾਬੰਦੀ ਲੱਗ ਸਕਦੀ ਹੈ";

@override
String get CodecopiedTxt => "ਕੋਡ ਨਕਲ ਕੀਤਾ ਗਿਆ";

@override
String get RideAcceptedTxt => "ਸਫ਼ਰ ਸਵੀਕਾਰਿਆ ਗਿਆ";

@override
String get PostalCodeShouldHave6DigitTxt => "ਡਾਕ ਕੋਡ ਵਿੱਚ 6 ਅੰਕ ਹੋਣੇ ਚਾਹੀਦੇ ਹਨ";

@override
String get LowBalanceTxt => "ਘੱਟ ਬਕਾਇਆ";

@override
String get automaticText => "ਆਟੋਮੈਟਿਕ";

@override
String get timeOfDayTxt => "ਦਿਨ ਦਾ ਸਮਾਂ";

@override
String get alwaysOnTxt => "ਹਮੇਸ਼ਾ ਚਾਲੂ";

@override
String get AlwaysOffTxt => "ਹਮੇਸ਼ਾ ਬੰਦ";

@override
String get UsePhoneSettingsTxt => "ਫੋਨ ਸੈਟਿੰਗਾਂ ਵਰਤੋ";

@override
String get PleaseEnterValidDetailsTxt => "ਕਿਰਪਾ ਕਰਕੇ ਸਹੀ ਵੇਰਵੇ ਦਿਓ";

@override
String get AddTxt => "ਜੋੜੋ";

@override
String get messageTxt => "ਸੁਨੇਹਾ";


  /////////////////////////////
  ///
  ///
  ///
  ///
  ///
  ///
  ///////////////////////////////////////
String get PleaseEnterMobileNumberTxt => "ਕਿਰਪਾ ਕਰਕੇ ਮੋਬਾਇਲ ਨੰਬਰ ਦਾਖਲ ਕਰੋ";

@override
String get InvalidMobileNumberTxt => "ਗਲਤ ਮੋਬਾਇਲ ਨੰਬਰ";

@override
String get ServerErrorTxt => "ਸਰਵਰ ਦੀ ਗਲਤੀ";

@override
String get Message => "ਸੁਨੇਹਾ";

@override
String get OpportunityDetailTxt => "ਮੌਕੇ ਦੀ ਵਿਸ਼ੇਸ਼ਤਾ";

@override
String get DistanceTxt => "ਦੂਰੀ";

@override
String get TimeTxt => "ਸਮਾਂ";

@override
String get PickupLocationTxt => "ਪਿਕਅਪ ਸਥਾਨ";

@override
String get passCode => 'ਪਾਸਕੋਡ';

@override
String get TakePhoto => 'ਫੋਟੋ ਲਓ';

@override
String get CopyrightText => 'ਕਾਪੀਰਾਈਟ';

@override
String get roooTxt => 'ਚੌਫਰ';

@override
String get roooDriverTxt => 'ਚੌਫਰ ਡਰਾਈਵਰ';

@override
String get ChangeEmailTxt => 'ਈਮੇਲ ਬਦਲੋ';

@override
String get NewEmailTxt => 'ਨਵਾਂ ਈਮੇਲ';

@override
String get EditTxt => 'ਸੰਪਾਦਿਤ ਕਰੋ';

@override
String get PleaseEnterValidNameTxt => 'ਕਿਰਪਾ ਕਰਕੇ ਸਹੀ ਨਾਮ ਦਾਖਲ ਕਰੋ';

@override
String get RequireAdminPermission => 'ਪ੍ਰਸ਼ਾਸਕ ਦੀ ਆਗਿਆ ਦੀ ਲੋੜ ਹੈ';

@override
String get UnverifiedEmailTxt => 'ਅਸੁਚਿਤ ਈਮੇਲ';

@override
String get VerifiedEmailTxt => 'ਪ੍ਰਮਾਣਿਤ ਈਮੇਲ';

@override
String get RequestChangeTxt => 'ਬਦਲਾਅ ਦੀ ਬੇਨਤੀ';

@override
String get ChangeMobileTxt => "ਮੋਬਾਇਲ ਬਦਲੋ";

@override
String get MobileNumberTxt => 'ਮੋਬਾਇਲ ਨੰਬਰ';

@override
String get optionalTxt => 'ਵਿਕਲਪਿਕ';

@override
String get NoResultsFoundTxt => 'ਕੋਈ ਨਤੀਜੇ ਨਹੀਂ ਮਿਲੇ';

@override
String get PleaseTryDifferentKeywordTxt => "ਕਿਰਪਾ ਕਰਕੇ ਵੱਖਰਾ ਕੁੰਜੀ ਸ਼ਬਦ ਅਜ਼ਮਾਓ";

@override
String get ManageProfileTxt => "ਪ੍ਰੋਫਾਈਲ";

@override
String get NumberAlreadyExistPleaseEnterOrUpdateNewNumbertxt =>
    'ਨੰਬਰ ਪਹਿਲਾਂ ਹੀ ਮੌਜੂਦ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਨਵਾਂ ਨੰਬਰ ਦਾਖਲ ਕਰੋ ਜਾਂ ਅਪਡੇਟ ਕਰੋ।';

@override
String get WalletHistoryTxt => 'ਵਾਲਿਟ ਇਤਿਹਾਸ';

@override
String get DescriptionTxt => "ਵੇਰਵਾ";

@override
String get ClickAProperProfilePictureWithYourFaceClearAndVisibleProfilePictureCantBeChangedOnceUploadedTxt =>
    "ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਸਾਫ਼ ਅਤੇ ਵਿਸ਼ੇਸ਼ ਦ੍ਰਿਸ਼ ਦੀਆਂ ਤਸਵੀਰਾਂ ਨਾਲ ਇੱਕ ਸਹੀ ਪ੍ਰੋਫਾਈਲ ਪਿਕਚਰ ਲਓ, ਪ੍ਰੋਫਾਈਲ ਪਿਕਚਰ ਇੱਕ ਵਾਰੀ ਅਪਲੋਡ ਹੋਣ ਤੋਂ ਬਾਅਦ ਬਦਲੀ ਨਹੀਂ ਜਾ ਸਕਦੀ।";

@override
String get PleaseUploadImagesAsIllustratedOtherwiseYourAccountWillBeTemporarilyDisabledOrThereMightBeAPermanentBanText =>
    'ਕਿਰਪਾ ਕਰਕੇ ਦਿੱਤੇ ਉਦਾਹਰਣ ਦੇ ਅਨੁਸਾਰ ਚਿੱਤਰ ਅਪਲੋਡ ਕਰੋ, ਨਹੀਂ ਤਾਂ ਤੁਹਾਡਾ ਖਾਤਾ ਅਸਥਾਈ ਤੌਰ ਤੇ ਬੰਦ ਹੋ ਸਕਦਾ ਹੈ ਜਾਂ ਇਸ ਤੇ ਸਥਾਈ ਪਾਬੰਦੀ ਲੱਗ ਸਕਦੀ ਹੈ।';

@override
String get enterthePasscodewhichIsDisplayingInTheCustomersMobile =>
    "ਗਾਹਕ ਦੇ ਮੋਬਾਇਲ 'ਤੇ ਦਿਖਾਈ ਦੇ ਰਹੇ ਪਾਸਕੋਡ ਨੂੰ ਦਾਖਲ ਕਰੋ";

@override
String get requiredTxt => "ਲੋੜੀਂਦਾ";

@override
String get startNavigationText => "ਨੇਵੀਗੇਸ਼ਨ ਸ਼ੁਰੂ ਕਰੋ";

@override
String get signInWithApple => "ਐਪਲ ਨਾਲ ਸਾਈਨ ਇਨ ਕਰੋ";

@override
String get locationDeniedText1 =>
    "ਐਪ ਦੀ ਕਾਰਜਪ੍ਰਦਾਤਾ ਲਈ " +
    '\n"ਹਮੇਸ਼ਾ ਲਈ ਅਨੁਮਤ ਕਰੋ"\n' +
    "ਲੋਕੇਸ਼ਨ ਆਗਿਆ ਦੀ ਲੋੜ ਹੈ। ਇਸ ਲੋਕੇਸ਼ਨ ਆਗਿਆ ਦੇ ਬਗੈਰ, ਨਵੀਂ ਸਫ਼ਰ ਬੁਕਿੰਗ ਅਤੇ ਮੌਜੂਦਾ ਸਫ਼ਰ ਦੀ ਕਾਰਜਪ੍ਰਣਾਲੀ ਕੰਮ ਨਹੀਂ ਕਰੇਗੀ।";

@override
String get locationDeniedText2 =>
    'ਤੁਸੀਂ ਸੈਟਿੰਗਜ਼ ਵਿੱਚੋਂ "ਹਮੇਸ਼ਾ ਲਈ ਅਨੁਮਤ ਕਰੋ" ਆਗਿਆ ਬਦਲ ਸਕਦੇ ਹੋ';

@override
String get deleteText => "ਮਿਟਾਓ";

@override
String get updateText => "ਅਪਡੇਟ";

@override
String get retryText => "ਪੁਨਰਕੋਸ਼ਿਸ਼ ਕਰੋ";

@override
String get AreYouSureWantToPerformThisAction =>
    "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਹੋ ਕਿ ਤੁਸੀਂ ਇਹ ਕਾਰਵਾਈ ਕਰਨੀ ਚਾਹੁੰਦੇ ਹੋ?";

@override
String get DoYouWantToDelete => "ਕੀ ਤੁਸੀਂ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?";

@override
String get DoYouWantToUpdate => "ਕੀ ਤੁਸੀਂ ਅਪਡੇਟ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?";

@override
String get DoYouWantToAdd => "ਕੀ ਤੁਸੀਂ ਜੋੜਨਾ ਚਾਹੁੰਦੇ ਹੋ?";

@override
String get DoYouWantToAccept => "ਕੀ ਤੁਸੀਂ ਸਵੀਕਾਰਣਾ ਚਾਹੁੰਦੇ ਹੋ?";

@override
String get ClickToRetry => "ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰਨ ਲਈ ਕਲਿੱਕ ਕਰੋ";

@override
String get NewTripText => "ਨਵਾਂ ਯਾਤਰਾ";

@override
String get AcceptedTripText => "ਸਵੀਕਾਰ ਕੀਤਾ ਯਾਤਰਾ";

@override
String get adminDidNotApproved =>
    "ਪ੍ਰਸ਼ਾਸਕ ਨੇ ਤੁਹਾਡੇ ਬੇਨਤੀ ਨੂੰ ਮਨਜ਼ੂਰੀ ਨਹੀਂ ਦਿੱਤੀ, ਕਿਰਪਾ ਕਰਕੇ ਸਫ਼ਰ ਜਾਰੀ ਰੱਖੋ";

@override
String get ImageUploadedSuccessfullyText => "ਤਸਵੀਰ ਸਫਲਤਾਪੂਰਵਕ ਅਪਲੋਡ ਕੀਤੀ ਗਈ";

@override
String get OKText => "ਠੀਕ ਹੈ";

@override
String get NotifyAdminAboutLateRiderText => "ਦੇਰ ਨਾਲ ਆਈ ਸਵਾਰੀ ਬਾਰੇ ਪ੍ਰਸ਼ਾਸਕ ਨੂੰ ਸੂਚਿਤ ਕਰੋ";

@override
String get AdminNotifiedText => "ਪ੍ਰਸ਼ਾਸਕ ਨੂੰ ਸੂਚਿਤ ਕੀਤਾ ਗਿਆ";

@override
String get CallText => "ਕਾਲ";

@override
String get StartRideText => "ਸਫ਼ਰ ਸ਼ੁਰੂ ਕਰੋ";

@override
String get GetVerificationCodeText => "ਸਰਟੀਫਿਕੇਸ਼ਨ ਕੋਡ ਦਾਖਲ ਕਰੋ";

@override
String get YouRequestedForRideCancellationText =>
    "ਤੁਸੀਂ ਸਫ਼ਰ ਰੱਦ ਕਰਨ ਦੀ ਬੇਨਤੀ ਕੀਤੀ";

@override
String get RideCanceledText => "ਸਫ਼ਰ ਰੱਦ ਕੀਤਾ";

@override
String get AdminApproveYourRequestPleaseStayOnlineText =>
    "ਪ੍ਰਸ਼ਾਸਕ ਨੇ ਤੁਹਾਡੇ ਬੇਨਤੀ ਨੂੰ ਮਨਜ਼ੂਰ ਕਰ ਲਿਆ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਆਨਲਾਈਨ ਰਹੋ";

@override
String get YouHaveAlreadyOneRideDriverSafelyText =>
    "ਤੁਹਾਡੇ ਕੋਲ ਪਹਿਲਾਂ ਹੀ ਇੱਕ ਸਫ਼ਰ ਹੈ, \nਡਰਾਈਵਰ ਨੂੰ ਸੁਰੱਖਿਅਤ ਰੱਖੋ";

@override
String get YouForgotToReviewPleaseGiveYourReviewAndFeedbackAboutRideText =>
    "ਤੁਸੀਂ ਸਮੀਖਿਆ ਕਰਨਾ ਭੁੱਲ ਗਏ ਹੋ। \nਕਿਰਪਾ ਕਰਕੇ ਸਫ਼ਰ ਬਾਰੇ ਆਪਣੀ ਸਮੀਖਿਆ ਅਤੇ ਫੀਡਬੈਕ ਦਿਓ";

@override
String get KeepYourselfOnlineToGetMoreRidesText =>
    "ਹੋਰ ਸਫ਼ਰ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਆਪਣੇ ਆਪ ਨੂੰ ਆਨਲਾਈਨ ਰੱਖੋ";

  @override
  String get youHaveCanceledTheRidePleaseWaitForAdminApproval =>
      "ਤੁਸੀਂ ਸਫਰ ਰੱਦ ਕਰ ਦਿੱਤਾ ਹੈ, ਕਿਰਪਾ ਕਰਕੇ ਪ੍ਰਬੰਧਕ ਦੀ ਮਨਜ਼ੂਰੀ ਲਈ ਉਡੀਕ ਕਰੋ।";

  @override
  String get reached => "ਪਹੁੰਚਿਆ";

  @override
  String get pleaseCancelWaitingTimer => "ਕਿਰਪਾ ਕਰਕੇ ਵੈਟਿੰਗ ਟਾਈਮਰ ਰੱਦ ਕਰੋ";

  @override
  String get waitingTimeStarted => "ਵੈਟਿੰਗ ਟਾਈਮ ਸ਼ੁਰੂ ਹੋ ਗਿਆ ਹੈ";

  @override
  String get waitingTimeEnded => "ਵੈਟਿੰਗ ਟਾਈਮ ਖਤਮ ਹੋ ਗਿਆ ਹੈ";

  @override
  String get newRide => "ਨਵਾਂ ਸਫਰ";

  @override
  String get pleaseAcceptTheRide => "ਕਿਰਪਾ ਕਰਕੇ ਸਫਰ ਨੂੰ ਸਵੀਕਾਰ ਕਰੋ";

  @override
  String get endRide => "ਸਫਰ ਖਤਮ ਕਰੋ";

  @override
  String get careInfo =>
      'ਜੇਕਰ ਤੁਹਾਡੇ ਕੋਲ ਕੋਈ ਹੋਰ ਪੁੱਛਣ ਵਾਲਾ ਪ੍ਰਸ਼ਨ ਹੈ ਜਾਂ ਸਹਾਇਤਾ ਦੀ ਜ਼ਰੂਰਤ ਹੈ ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਸਾਨੂੰ ਈਮੇਲ ਕਰੋ';

  @override
  String get deleted => "ਹਟਾਇਆ ਗਿਆ";

  @override
  String get details => "ਵੇਰਵੇ";

  @override
  String get pleaseCompleteThisRideFirst => "ਕਿਰਪਾ ਕਰਕੇ ਪਹਿਲਾਂ ਇਸ ਸਫਰ ਨੂੰ ਪੂਰਾ ਕਰੋ";

  @override
  String get AreYouSureYouWantToEndWaitingTime =>
      "ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਬਣਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ ਕਿ ਤੁਸੀਂ ਵੈਟਿੰਗ ਟਾਈਮ ਖਤਮ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ";

  @override
  String get handleMiddimgregionId =>
      "ਤੁਹਾਡੇ ਖੇਤਰ ਦਾ ਡਾਟਾ ਗੁੰਮ ਹੈ।\nਕਿਰਪਾ ਕਰਕੇ ਆਪਣੇ ਖੇਤਰ ਨੂੰ ਅਪਡੇਟ ਕਰੋ।";

  @override
  String get TripEarning => "ਸਫਰ ਦੀ ਕਮਾਈ";

  @override
  String get handleInCompleteProfile =>
      "ਕਿਰਪਾ ਕਰਕੇ ਅੱਗੇ ਵੱਧਣ ਤੋਂ ਪਹਿਲਾਂ ਆਪਣਾ ਪ੍ਰੋਫਾਈਲ ਪੂਰਾ ਕਰੋ";

  @override
  String get call_911 => "911 ਨੂੰ ਕਾਲ ਕਰੋ";

  @override
  String get enterYourNewEmail => "ਆਪਣਾ ਨਵਾਂ ਈਮੇਲ ਦਾਖਲ ਕਰੋ";

  @override
  String get invalidReferalCode => "ਗਲਤ ਰਿਫਰਲ ਕੋਡ";

  @override
  String get noSuggestedMessage => "ਕੋਈ ਸੁਝਾਇਆ ਗਿਆ ਸੁਨੇਹਾ ਨਹੀਂ";

  @override
  String get pleaseAddDescription => "ਕਿਰਪਾ ਕਰਕੇ ਵੇਰਵਾ ਸ਼ਾਮਲ ਕਰੋ";

  @override
  String get pleaseSelectProvinceAndRegionId =>
      "ਕਿਰਪਾ ਕਰਕੇ ਖੇਤਰ ID ਅਤੇ ਪ੍ਰਾਂਤ ID ਚੁਣੋ";

  @override
  String get sendEmailLinkOn => "ਇੱਕ ਈਮੇਲ ਪ੍ਰਮਾਣੀਕਰਨ ਲਿੰਕ ਭੇਜੋ";

  @override
  String get pleaseSelectProvinceId => "ਕਿਰਪਾ ਕਰਕੇ ਪ੍ਰਾਂਤ ID ਚੁਣੋ";

  @override
  String get pleaseSelectRegionId => "ਕਿਰਪਾ ਕਰਕੇ ਖੇਤਰ ID ਚੁਣੋ";

  @override
  String get provinceId => "ਪ੍ਰਾਂਤ ID";

  @override
  String get regionId => "ਖੇਤਰ ID";

  @override
  String get referralCodeAppliedSuccesfully =>
      "ਰਿਫਰਲ ਕੋਡ ਸਫਲਤਾਪੂਰਵਕ ਲਾਗੂ ਕੀਤਾ ਗਿਆ ਹੈ";

  @override
  String get verified => "ਪ੍ਰਮਾਣਿਤ";

  @override
  String get doubleTapToExit => "ਬਾਹਰ ਨਿਕਲਣ ਲਈ ਡਬਲ ਟੈਪ ਕਰੋ";

  @override
  String get openSetting => "ਸੈਟਿੰਗ ਖੋਲ੍ਹੋ";

  @override
  String get send => "ਭੇਜੋ";

  @override
  String get pleaseEnterValidTitle => "ਕਿਰਪਾ ਕਰਕੇ ਸਹੀ ਸ਼ੀਰਸ਼ਕ ਦਾਖਲ ਕਰੋ";

  @override
  String get pleaseEnterValidMessage => "ਕਿਰਪਾ ਕਰਕੇ ਸਹੀ ਸੁਨੇਹਾ ਦਾਖਲ ਕਰੋ";

  @override
  String get cameraAccesMessage =>
      "ਉੱਤਮ ਅਨੁਭਵ ਲਈ ਕੈਮਰਾ ਪਹੁੰਚ ਦੀ ਆਗਿਆ ਦਿਓ";

  @override
  String get microphoneAccessMessage =>
      "VOIP ਕਾਲ ਫੀਚਰ ਦੀ ਵਰਤੋਂ ਕਰਨ ਲਈ, ਮਾਈਕ੍ਰੋਫੋਨ ਪਹੁੰਚ ਦੀ ਜ਼ਰੂਰਤ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਐਪ ਸੈਟਿੰਗ ਵਿੱਚ ਅਨੁਮਤ ਦਿਓ।";

  @override
  String get riderName => "ਰਾਈਡਰ ਦਾ ਨਾਮ";

  @override
  String get tax => "ਟੈਕਸ";

  @override
  String get suggestedReviews => "ਸੁਝਾਏ ਗਏ ਸਮੀਖਿਆਵਾਂ";

  @override
  String get verify => "ਪ੍ਰਮਾਣਿਤ ਕਰੋ";

  @override
  String get startLocation => "ਸ਼ੁਰੂਆਤ ਸਥਾਨ";

  @override
  String get youMissedARide => "ਤੁਸੀਂ ਇੱਕ ਸਫਰ ਛੱਡ ਦਿੱਤਾ";

  @override
  String get youCancelledARide => "ਤੁਸੀਂ ਇੱਕ ਸਫਰ ਰੱਦ ਕਰ ਦਿੱਤਾ ਹੈ";

  @override
  String get youHaveNewRide => "ਤੁਹਾਡੇ ਕੋਲ ਨਵਾਂ ਸਫਰ ਹੈ";

  @override
  String get waitingTimeCancelledMessage =>
      "ਤੁਸੀਂ ਚਲਣਾ ਸ਼ੁਰੂ ਕਰ ਦਿੱਤਾ ਹੈ, ਵੈਟਿੰਗ ਟਾਈਮ ਰੱਦ ਕਰ ਦਿੱਤਾ";
@override
String get offerCompleted => "ਪੇਸ਼ਕਸ਼ ਪੂਰੀ ਹੋਈ";

@override
String get offerEarning => "ਪੇਸ਼ਕਸ਼ ਕਮਾਈ";

  @override
  // TODO: implement goTxt
  String get goTxt => "ਚੱਲੋ";
  
  @override
  // TODO: implement RidedeclinedText
  String get RidedeclinedText =>'ਰਾਈਡ ਨੂੰ ਅਸਵੀਕਾਰ ਕਰ ਦਿੱਤਾ ਗਿਆ ਹੈ।';
  
  @override
  // TODO: implement systemAlertMessage
  String get systemAlertMessage => "throw UnimplementedError()";

}



  // \\\\\\\\\\\\\\\\\\\\\\\


  

