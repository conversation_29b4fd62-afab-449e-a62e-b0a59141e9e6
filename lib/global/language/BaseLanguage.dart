import 'package:flutter/material.dart';

abstract class BaseLanguage {
  static BaseLanguage? of(BuildContext context) =>
      Localizations.of<BaseLanguage>(context, BaseLanguage);

  String get inbox;
  String get details;

  String get appName;

  String get handleMiddimgregionId;

  String get goTxt;

  String get welcomeBack;

  String get signInYourAccount;

  String get thisFieldRequired;

  String get email;
  String get passCode;

  String get password;

  String get forgotPassword;

  String get logIn;

  String get orLogInWith;

  String get donHaveAnAccount;

  String get signUp;

  String get createAccount;

  String get createYourAccountToContinue;

  String get firstName;

  String get lastName;

  String get userName;

  String get phoneNumber;

  String get alreadyHaveAnAccount;
  String get requiredTxt;

  String get contactUs;

  String get purchase;

  String get changePassword;

  String get oldPassword;

  String get newPassword;

  String get confirmPassword;

  String get passwordDoesNotMatch;

  String get passwordInvalid;

  String get yes;

  String get no;

  String get writeMessage;

  String get enterTheEmailAssociatedWithYourAccount;

  String get submit;

  String get language;

  String get notification;

  String get otpVeriFiCation;

  String get weHaveSentDigitCode;

  String get contactLength;

  String get about;

  String get useInCaseOfEmergency;

  String get notifyAdmin;

  String get notifiedSuccessfully;

  String get complain;

  String get pleaseEnterSubject;

  String get writeDescription;

  String get saveComplain;

  String get editProfile;

  String get gender;

  String get addressTxt;

  String get updateProfile;

  String get notChangeUsername;

  String get notChangeEmail;

  String get profileUpdateMsg;

  String get emergencyContact;

  String get areYouSureYouWantDeleteThisNumber;

  String get addContact;

  String get googleMap;

  String get save;

  String get myRides;

  String get myWallet;

  String get availableBalance;

  String get recentTransactions;

  String get moneyDeposited;

  String get addMoney;

  String get NotUploadedTxt;

  String get pleaseSelectAmount;

  String get amount;

  String get capacity;

  String get paymentMethod;

  String get chooseYouPaymentLate;

  String get enterPromoCode;

  String get confirm;

  String get forInstantPayment;

  String get bookNow;

  String get wallet;

  String get paymentDetail;
  String get startNavigationText;

  String get rideId;

  String get createdAt;

  String get viewHistory;

  String get paymentDetails;

  String get paymentType;

  String get paymentStatus;

  String get priceDetail;

  String get basePrice;

  String get distancePrice;

  String get timePrice;

  String get waitTime;

  String get extraCharges;

  String get couponDiscount;

  String get total;

  String get payment;

  String get cash;

  String get updatePaymentStatus;

  String get waitingForDriverConformation;

  String get continueNewRide;

  String get payToPayment;

  String get tip;

  String get pay;

  String get howWasYourRide;

  String get wouldYouLikeToAddTip;

  String get addMoreTip;

  String get addMore;

  String get addReviews;

  String get writeYourComments;

  String get continueD;

  String get detailScreen;
  String get helpdetail;

  String get aboutDriver;

  String get rideHistory;

  String get myProfile;

  String get myTrips;

  String get emergencyContacts;

  String get logOut;

  String get areYouSureYouWantToLogoutThisApp;

  String get whatWouldYouLikeToGo;

  String get enterYourDestination;

  String get currentLocation;

  String get destinationLocation;

  String get chooseOnMap;

  String get profile;

  String get theme;

  String get privacyPolicy;

  String get helpSupport;

  String get termsConditions;

  String get aboutUs;

  String get lookingForNearbyDrivers;

  String get weAreLookingForNearDriversAcceptsYourRide;

  String get areYouSureYouWantToCancelThisRide;

  String get serviceDetail;

  String get get;

  String get rides;

  String get people;
  String get TripEarning;

  String get fare;

  String get done;

  String get availableOffers;

  String get off;

  String get sendOTP;

  String get otpVerification;

  String get enterTheCodeSendTo;

  String get didNotReceiveTheCode;

  String get resend;

  String get drivingExperience;

  String get sos;

  String get driverReview;

  String get signInUsingYourMobileNumber;

  String get otp;

  String get newRideRequested;

  String get accepted;

  String get arriving;

  String get arrived;

  String get inProgress;

  String get cancelled;

  String get completed;

  String get pleaseEnableLocationPermission;

  String get pending;

  String get failed;

  String get paid;

  String get male;

  String get female;

  String get other;

  String get addExtraCharges;

  String get enterAmount;

  String get pleaseAddedAmount;

  String get title;

  String get charges;

  String get saveCharges;


  String get bankDetail;

  String get bankName;

  String get bankCode;

  String get accountHolderName;

  String get accountNumber;

  String get updateBankDetail;

  String get addBankDetail;

  String get bankInfoUpdateSuccessfully;

  String get vehicleDetail;

  String get document;

  String get setting;

  String get youAreOnlineNow;

  String get youAreOfflineNow;

  String get requests;

  String get areYouSureYouWantToCancelThisRequest;

  String get decline;
    String get RidedeclinedText;


  String get accept;

  String get areYouSureYouWantToAcceptThisRequest;

  String get call;

  String get chat;

  String get applyExtraFree;

  String get areYouSureYouWantToArriving;

  String get areYouSureYouWantToArrived;

  String get enterOtp;

  String get enterTheOtpDisplayInCustomersMobileToStartTheRide;
  String get enterTheOtpDisplayInCustomersMobileToEndTheRide;

  String get pleaseEnterValidOtp;

  String get areYouSureYouWantToCompletedThisRide;

  String get updateBankInfo;

  String get regisTRation;

  String get pleaseSelectVehiclePreferences;

  String get userDetail;

  String get selectVehiclePreferences;

  String get selectGender;

  String get age;

  String get socialSecurityNumber;

  String get nightDrivingPreference;

  String get withDraw;

  String get withdrawHistory;

  String get approved;

  String get requested;

  String get updateVehicle;

  String get userNotApproveMsg;

  String get uploadFileConfirmationMsg;

  String get selectDocument;

  String get addDocument;

  String get areYouSureYouWantToDeleteThisDocument;

  String get expireDate;

  String get goDashBoard;

  String get deleteAccount;
  String get deleted;

  String get account;

  String get areYouSureYouWantPleaseReadAffect;

  String get deletingAccountEmail;

  String get areYouSureYouWantDeleteAccount;
  String get AreYouSureYouWantToEndWaitingTime;

  String get yourInternetIsNotWorking;

  String get allow;

  String get mostReliableMightyDriverApp;

  String get toEnjoyYourRideExperiencePleaseAllowPermissions;

  String get cashCollected;

  String get areYouSureCollectThisPayment;

  String get txtURLEmpty;

  String get lblFollowUs;

  String get bankInfo;

  String get duration;

  String get paymentVia;

  String get moneyDebit;

  String get roooCare;

  String get demoMsg;

  String get youCannotChangePhoneNumber;

  String get offLine;

  String get online;

  String get walletLessAmountMsg;

  String get aboutRider;

  String get pleaseEnterMessage;

  String get complainList;

  String get viewAll;

  String get pleaseSelectRating;

  String get serviceInfo;

  String get youCannotChangeService;

  String get vehicleInfoUpdateSucessfully;

  String get subscription;

  String get yourCurrentBalanceIs;

  String get yourSubscriptionPlanIsOver;

  String get perDay;

  String get renew;

  String get yourWalletDoNotHaveEnoughBalance;

  String get addWallet;

  String get yourDailyAppUseLimitHasBeenExpired;

  String get recharge;

  String get isMandatoryDocument;

  String get someRequiredDocumentAreNotUploaded;

  String get areYouCertainOffline;

  String get areYouCertainOnline;

  String get pleaseAcceptTermsOfServicePrivacyPolicy;

  String get rememberMe;

  String get agreeToThe;

  String get invoice;

  String get riderInformation;

  String get customerName;

  String get sourceLocation;

  String get invoiceNo;

  String get invoiceDate;

  String get orderedDate;

  String get totalCash;

  String get totalRide;
  String get totalReferralsTxt;

  String get totalWallet;

  String get totalEarning;

  String get pleaseSelectFromDateAndToDate;

  String get from;

  String get fromDate;

  String get to;

  String get toDate;

  String get ride;

  String get todayRide;

  String get weeklyOrderCount;

  String get distance;

  String get rideInformation;

  String get iAgreeToThe;

  String get today;

  String get weekly;

  String get report;

  String get earning;

  String get todayEarning;

  String get available;

  String get notAvailable;

  String get youWillReceiveNewRidersAndNotifications;

  String get youWillNotReceiveNewRidersAndNotifications;

  String get yourAccountIs;

  String get pleaseContactSystemAdministrator;

  String get youCanNotThisActionsPerformBecauseYourCurrentRideIsNotCompleted;

  String get selectDisplayPicture;

  String get invalidAge;

  String get referralCompleted;
    String get offerCompleted;

  String get referralEarning;
    String get offerEarning;

  String get enterYourMobileNumber;
  String get continueText;
  String get orText;
  String get signInWithGoogle;
  String get signInWithFacebook;
  String get signInWithApple;

  // //////////////////////////////

  String get inBoxTxt => 'Inbox';

  String get referralsTxt;

  String get promotionsTxt;

  String get opportunityTxt;
  String get accountTxt;

  String get earningTxt;
  String get helpTxt;
  String get learningCenterTxt;

  String get completedTxt;
  String get faqsTxt;

  String get settingTxt;
  String get nightModeTxt;
  String get helpWithTripTxt;

  String get verifyOTP;
  String get verifyOTPHeading;
  String get enterOTP;
  String get invalidOTP;
  String get referralCode;
  String get allMessageTxt;
  String get bankInfoTxt;
  String get offerTxt;
  String get statusTxt;
  String get walletTxt;

  String get balanceTxt;

  String get paymentScheduledTxt;

  String get paymentMethodsTxt;

  String get cashOutTxt;

  String get verifyPhoneMsg;
  String get errorMsg;

  // String get earningTxt;
  // String get helpTxt;
  // String get learningCenterTxt ;

  //   String get earningTxt;
  // String get helpTxt;
  // String get learningCenterTxt ;
  String get SorryYouDontHaveAnyOpportunitiesTxt;

  String get NewOpportunitiesAreComingTxt;
  String get stayTunedTxt;
  String get roooCareTxt;
  String get NotificationsTxt;

  String get WeeklyOrderCountTxt;
  String get cashoutTxt;
  String get TotalCashTxt;
  String get TotalWalletTxt;
  String get TotalRideTxt;
  String get adminNotifiedTxt;
  String get YouAreOnlineNowTxt;
  String get AreYouSureYouWantToNotifyAdminTxt;

  String get PleaseWaitForTimerTxt;
  String get NotifiedAdminTxt;
  String get BookYourroooIn3EasyStepsTxt;
  String get NowBookingYourroooIsEasyTxt;
  String get PleaseUploadAllTheDocumentsAndWaitForVerificationTxt;
  String get OkTxt;
  String get goOfflineTxt;
  String get clickCarPhotographsTxt;

  String get frontTxt;
  String get backTxt;
  String get leftTxt;
  String get rightTxt;
  String get pleaseUploadImageProperlyTxt;
  String get WelcomeTxt;
  String get UserTxt;
  String get resendOTPTxt;

  String get IhaventRecievedACodeTxt;
  String get CONTINUETxt;
  String get CompletedTxt;
  String get rejectedTxt;
  String get pendingTxt;
  String get referralTxt;

  String get uploadTxt;

  String get cancelTxt;

  String get accountHolderNameTxt;

  String get pleaseEnterTheBenificieryNameTxt;

  String get PleaseEnterTheBeneficieryAddressOnABankAccountTxt;

  String get cityTxt;

  String get PleaseEnterYourCityTxt;

  String get postalTxt;

  String get PleaseEnterTheBeneficieryPostalCodeTxt;

  String get DateOfBirthTxt;

  String get BankNameTxt;

  String get BankameOthersTxt;

  String get BankOfNovoScotioTxt;
  String get InstituitionNumberTxt;
  String get InstitutionNumberMustContainAtLeastDdigitsTxt;
  String get TransitNumberTxt;

  String get TransitNumberMustContainAtLeastDigitsTxt;
  String get BankAccountNumberTxt;

  String get AccountNumberMustContainAtLeastDigitsTxt;
  String get FillCorrectAccountNumberTxt;

  String get ReEnterBankAccountNumberTxt;
  String get PleaseReEnterYourAccountNumberTxt;
  String get ReEnteredAccountIsWrongTxt;

  String get TermsTxt;
  String get termsApplyTxt;

  String get ShareYourLinkTxt;
  String get inviteTxt;
  String get inviteFromContactsTxt;

  String get wouldYouLikeToStartIdleTimeTxt;
  String get waitingTime;

  String get waitingTimeOverTxt;
  String get cancelTimerTxt;

  String get AdminalreadynotifiedTxt;
  String get closedTxt;

  String get PleasewaitTxt;

  String get NotifyToAdminAboutLateRideTxt;

  String get YouHaveReachedTheDestinationTxt;

  String get upload4ImagesWarningTxt;

  String get CodecopiedTxt;

  String get RideAcceptedTxt;

  String get PostalCodeShouldHave6DigitTxt;

  String get LowBalanceTxt;

  String get automaticText;

  String get timeOfDayTxt;
  String get alwaysOnTxt;
  String get AlwaysOffTxt;

  String get UsePhoneSettingsTxt;

  String get messageTxt;
  String get PleaseEnterValidDetailsTxt;
  String get AddTxt;

  String get Message;

  String get PleaseEnterMobileNumberTxt;
  String get InvalidMobileNumberTxt;
  String get ServerErrorTxt;

  String get OpportunityDetailTxt;
  String get DistanceTxt;
  String get TimeTxt;

  String get PickupLocationTxt;

  String get enterthePasscodewhichIsDisplayingInTheCustomersMobile;
  String
      get PleaseUploadImagesAsIllustratedOtherwiseYourAccountWillBeTemporarilyDisabledOrThereMightBeAPermanentBanText;

  String get TakePhoto;

  String get CopyrightText;

  String get roooTxt;

  String get roooDriverTxt;
  String get ChangeEmailTxt;

  String get NewEmailTxt;

  String
      get ClickAProperProfilePictureWithYourFaceClearAndVisibleProfilePictureCantBeChangedOnceUploadedTxt;

  String get DescriptionTxt;
  String get WalletHistoryTxt;

  String get EditTxt;

  String get RequestChangeTxt;
  String get UnverifiedEmailTxt;
  String get VerifiedEmailTxt;

  String get RequireAdminPermission;

  String get PleaseEnterValidNameTxt;

  String get NumberAlreadyExistPleaseEnterOrUpdateNewNumbertxt;
  String get ChangeMobileTxt;

  String get MobileNumberTxt;

  String get optionalTxt;
  String get NoResultsFoundTxt;
  String get PleaseTryDifferentKeywordTxt;

  String get ManageProfileTxt;
  String get locationDeniedText1;
  String get locationDeniedText2;

  String get deleteText;

  String get updateText;

  String get retryText;

  String get AreYouSureWantToPerformThisAction;

  String get DoYouWantToDelete;

  String get DoYouWantToUpdate;

  String get DoYouWantToAdd;

  String get DoYouWantToAccept;

  String get ClickToRetry;

  String get NewTripText;

  String get AcceptedTripText;

  String get adminDidNotApproved;
  String get ImageUploadedSuccessfullyText;
  String get OKText;
  String get NotifyAdminAboutLateRiderText;
  String get AdminNotifiedText;
  String get CallText;
  String get StartRideText;
  String get GetVerificationCodeText;
  String get YouRequestedForRideCancellationText;
  String get RideCanceledText;
  String get AdminApproveYourRequestPleaseStayOnlineText;

  String get YouHaveAlreadyOneRideDriverSafelyText;
  String get YouForgotToReviewPleaseGiveYourReviewAndFeedbackAboutRideText;

  String get KeepYourselfOnlineToGetMoreRidesText;
  String get youHaveCanceledTheRidePleaseWaitForAdminApproval;
  String get reached;

  String get pleaseCancelWaitingTimer;
  String get pleaseCompleteThisRideFirst;

  String get waitingTimeStarted;

  String get waitingTimeEnded;
  String get newRide;

  String get pleaseAcceptTheRide;

  String get endRide;

  String get careInfo;
  String get handleInCompleteProfile;
    String get call_911;
        String get enterYourNewEmail;



  String get invalidReferalCode;

  String get noSuggestedMessage;

  String get pleaseAddDescription;

  String get pleaseSelectProvinceAndRegionId;

  String get sendEmailLinkOn;

  String get pleaseSelectProvinceId;

  String get pleaseSelectRegionId;

  String get provinceId;

  String get regionId;

  String get referralCodeAppliedSuccesfully;

  String get verified;

  String get doubleTapToExit;

  String get openSetting;

  String get send;

  String get pleaseEnterValidTitle;

  String get pleaseEnterValidMessage;

  String get cameraAccesMessage;
    String get systemAlertMessage;


  String get microphoneAccessMessage;

  String get riderName;

  String get tax;

  String get suggestedReviews;

  String get verify;

  String get youMissedARide;

  String get youHaveNewRide;

  String get youCancelledARide;

  String get waitingTimeCancelledMessage;

  String get startLocation;
  //   String get closedTxt;
  //   String get closedTxt;

  // String get closedTxt;

  // String get closedTxt;

  //  String get closedTxt;
  //   String get closedTxt;

  // String get closedTxt;

  // String get closedTxt;
  //   String get closedTxt;
  //   String get closedTxt;

  // String get closedTxt;

  // String get closedTxt;
  //   String get closedTxt;
  //   String get closedTxt;

  // String get closedTxt;

  // String get closedTxt;

  //  String get closedTxt;
  //   String get closedTxt;

  // String get closedTxt;

  // String get closedTxt;
  //   String get closedTxt;
  //   String get closedTxt;

  // String get closedTxt;

  // String get closedTxt;
  //   String get closedTxt;
  //   String get closedTxt;

  // String get closedTxt;

  // String get closedTxt;
}
