import 'package:flutter/material.dart';
import 'package:rooo_driver/global/language/LanguageFr.dart';

import '../../model/LanguageDataModel.dart';
import 'BaseLanguage.dart';
import 'LanguageEn.dart';
import 'LanguageHi.dart';
import 'LanguagePa.dart';

class AppLocalizations extends LocalizationsDelegate<BaseLanguage> {
  const AppLocalizations();

  @override
  Future<BaseLanguage> load(Locale locale) async {
    switch (locale.languageCode) {
      case 'en':
        return LanguageEn();
      case 'hi':
        return LanguageHi();
      case 'fr':
        return LanguageFr();
      case 'pa':
        return LanguagePa();
      default:
        return LanguageEn();
    }
  }

  @override
  bool isSupported(Locale locale) => LanguageDataModel.languages().contains(locale.languageCode);

  @override
  bool shouldReload(LocalizationsDelegate<BaseLanguage> old) => false;
}
