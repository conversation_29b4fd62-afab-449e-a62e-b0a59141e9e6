import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class OneSignalService {
  static bool _isInitialised = false;
  static bool _isSentToServer = false;
  static init() async {
    try {
      if (!_isInitialised) {
        OneSignal.initialize(AppCred.mOneSignalAppIdDriver);
        OneSignal.Notifications.addForegroundWillDisplayListener((event) {
          if (GlobalState.lastNotificationId !=
              event.notification.notificationId) {
            GlobalState.lastNotificationId = event.notification.notificationId;

            GlobalMethods.handleNotification(event.notification, false);
          }
        });

        OneSignal.Notifications.addClickListener((event) {
          GlobalMethods.handleNotification(event.notification, true);
        });
        _isInitialised = true;
      }

      if (!_isSentToServer) {
        OneSignal.User.pushSubscription.addObserver((observer) async {
          if (_isSentToServer) {
            return;
          }
          final String currentTimeZone =
              await FlutterTimezone.getLocalTimezone();
          String? id = observer.current.id;
          if ((id ?? "").trim().isNotEmpty) {
            GlobalState.playerId = id!;
            await updatePlayerId(
                playerId: GlobalState.playerId, timezone: currentTimeZone);
            _isSentToServer = true;
          }
        });

        await OneSignal.User.pushSubscription.optIn();

        if (!_isSentToServer) {
          String? id = await OneSignal.User.pushSubscription.id;

          if ((id ?? "").trim().isNotEmpty) {
            final String currentTimeZone =
                await FlutterTimezone.getLocalTimezone();

            GlobalState.playerId = id!;
            await updatePlayerId(
                playerId: GlobalState.playerId, timezone: currentTimeZone);
            _isSentToServer = true;
          }
        }
      }
      return OneSignalServiceStatus.initialised;
    } catch (e) {
      return OneSignalServiceStatus.initError;

      //
    }
  }
}

enum OneSignalServiceStatus {
  permissionDenied,
  initError,
  initialised,
}

// class OneSignalService {
//   static init() async {
//     try {
//       await OneSignal.shared.setAppId(Constants.oneSignalRiderAppId);
//       await OneSignal.shared.consentGranted(true);
//       await OneSignal.shared.promptUserForPushNotificationPermission();
//       OneSignal.shared.setNotificationWillShowInForegroundHandler(
//           (OSNotificationReceivedEvent event) {
//         // event.complete(event.notification);
//         handleNotification(event.notification);
//       });

//       OneSignal.shared.setNotificationOpenedHandler((openedResult) {
//         handleNotification(openedResult.notification, fromTerminated: true);
//       });
//       Future.delayed(const Duration(seconds: 2), () async {
//         var result = await OneSignal.shared.getDeviceState();
//         Globals.playerId = result?.userId ?? "";
//         if (Globals.playerId.isNotEmpty) {
//           updatePlayerId(Globals.playerId);
//         }
//       });
//     } catch (e) {
//       //
//     }
//   }
// }
