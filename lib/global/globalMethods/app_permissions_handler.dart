import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/global/export/app_export.dart';

enum APP_PERMISSIONS {
  NOTIFICATION,
  LOCATION,
}

abstract class AppPermissionsHandler {
  static bool _shouldPromptWelcome = true;

  static Future<bool> handleRequiredPermissions() async {
    if (await _areAllAllowed()) {
      return true;
    }


    if (_shouldPromptWelcome) {
      bool userWantToContinue = await _showWelcomeMessage();
      if (!userWantToContinue) {
        return false;
      }
    }

    bool isAllowed = await _checkNotificationPermission();

    if (isAllowed) {
      isAllowed = await _checkLocationPermission();
    }

    return isAllowed;
  }

  static Future<bool> _areAllAllowed() async {
    PermissionStatus status = await Permission.notification.status;
    if (status != PermissionStatus.granted) {
      return false;
    }
    status = await Permission.locationAlways.status;
    if (status != PermissionStatus.granted) {
      return false;
    }

    return true;
  }

  static Future<bool> _showWelcomeMessage() async {
    _shouldPromptWelcome = false;
    bool? v = await GlobalMethods.showInfoDialogNew(
      barrierDismissible: true,
      context: navigatorKey.currentContext!,
      onClick: () {
        Navigator.of(navigatorKey.currentContext!).pop(true);
      },
      title:
          "Hi, we need the following permissions for the basic functionality of the app.\n\n1. Notifications - To provide you updates and alerts. \n2. Location in 'Always allow' mode - To track your location and provide you with the best services. It will allow us to send you new ride requests.\n\nPlease allow these permissions.",
    );
    return v ?? false;
  }

  static Future<bool> _checkNotificationPermission() async {
    PermissionStatus status = await Permission.notification.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else if (status == PermissionStatus.denied) {
      return await _askForPermission(APP_PERMISSIONS.NOTIFICATION);
    }

    _handleDeniedPermission(APP_PERMISSIONS.NOTIFICATION);
    return false;
  }

  static Future<bool> _checkLocationPermission() async {
    PermissionStatus status = await Permission.locationAlways.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else if (status == PermissionStatus.denied) {
      return await _askForPermission(APP_PERMISSIONS.LOCATION);
    }
    _handleDeniedPermission(APP_PERMISSIONS.LOCATION);

    return false;
  }

  static Future<bool> _askForPermission(APP_PERMISSIONS permission) async {
    if (permission == APP_PERMISSIONS.NOTIFICATION) {
      var status = await Permission.notification.request();
      if (!status.isGranted) {
        _handleDeniedPermission(permission);
        return false;
      }
      return true;
    } else if (permission == APP_PERMISSIONS.LOCATION) {
      var status = await Permission.locationWhenInUse.request();
      if (!status.isGranted) {
        _handleDeniedPermission(permission);
        return false;
      }

      status = await Permission.locationAlways.request();
      if (!status.isGranted) {
        _handleDeniedPermission(permission);
        return false;
      }

      return true;
    }
    return false;
  }

  static Future<void> _handleDeniedPermission(
      APP_PERMISSIONS permission) async {

    String text = "";
    if (permission == APP_PERMISSIONS.NOTIFICATION) {
      text =
          "We need notification permission for the app to function properly. Please grant the permission in settings.";
    } else if (permission == APP_PERMISSIONS.LOCATION) {
      text =
          "We need location permission for the app to function properly. Please grant the permission in settings. In the settings, please set the location permission to 'Always allow'.";
    }

    await GlobalMethods.showInfoDialogNew<bool>(
        context: navigatorKey.currentContext!,
        barrierDismissible: true,
        buttonText: "Open",
        onClick: () async {
          await openAppSettings();
          Navigator.of(navigatorKey.currentContext!).pop(true);
        },
        title: text);
  }
}
