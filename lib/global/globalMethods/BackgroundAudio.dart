import 'package:audioplayers/audioplayers.dart';

class BackgroundAudio {
  static final player = AudioPlayer();

  static void init() {
    player.setAudioContext(
      AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playback,
          options: {
            AVAudioSessionOptions.mixWithOthers,
          },
        ),
      ),
    );

    player.eventStream.listen((onData) {
      switch (onData.eventType) {
        case AudioEventType.complete:
          playSilence();
          break;

        default:
      }
    });

    playSilence();
  }

  static Future<void> playSilence() async {
    try {
      await player.play(AssetSource('audio/silence.mp3'));
    } catch (e) {}
  }

  static Future<void> playNewRideAudio() async {
    try {
      await player.play(AssetSource('audio/new_ride.mp3'));
    } catch (e) {}
  }

  static Future<void> playOfflineAudio() async {
    await player.play(AssetSource('audio/offline.mp3'));
  }

  static Future<void> playErrorAudio() async {
    await player.play(AssetSource('audio/error.mp3'));
  }

  static Future<void> playOnlineAudio() async {
    await player.play(AssetSource('audio/online.mp3'));
  }

  // static Future<void> playAcceptAudio() async {
  //   await player.play(AssetSource('audio/accept.mp3'));
  // }
}
