import 'dart:async';

class PausableTimer {
  Timer? _timer;
  Stopwatch _stopwatch = Stopwatch();
  int _initialDuration;
  int _remainingDuration;
  bool _isActive = false;

  PausableTimer(this._initialDuration) : _remainingDuration = _initialDuration;

  void start(void Function() callback) {
    if (_isActive) return; // Prevent starting a new timer if one is already active
    _isActive = true;
    _stopwatch.start();
    _timer = Timer.periodic(Duration(seconds: 1), (Timer timer) {
      if (_stopwatch.elapsed.inSeconds < _remainingDuration) {
        callback();
      } else {
        timer.cancel();
        _stopwatch.stop();
        _isActive = false;
      }
    });
  }

  void pause() {
    if (!_isActive) return; // Do nothing if not active
    _stopwatch.stop();
    _remainingDuration -= _stopwatch.elapsed.inSeconds;
    _stopwatch.reset();
    _timer?.cancel();
    _isActive = false;
  }

  void resume(void Function() callback) {
    if (_isActive) return; // Do nothing if already active
    _isActive = true;
    _stopwatch.start();
    _timer = Timer.periodic(Duration(seconds: 1), (Timer timer) {
      if (_stopwatch.elapsed.inSeconds < _remainingDuration) {
        callback();
      } else {
        timer.cancel();
        _stopwatch.stop();
        _isActive = false;
      }
    });
  }

  void reset() {
    _stopwatch.reset();
    _remainingDuration = _initialDuration;
    _timer?.cancel();
    _isActive = false;
  }

  void cancel() {
    _timer?.cancel();
    _stopwatch.stop();
    _stopwatch.reset();
    _isActive = false;
  }

  int get remainingTime => _remainingDuration - _stopwatch.elapsed.inSeconds;
}