class LocalWaitingTimeModel {
  DateTime startTime;
  DateTime? endTime;
  String type;
  LocalWaitingTimeModel({
    required this.startTime,
    required this.endTime,
    required this.type,
  });

  factory LocalWaitingTimeModel.fromMap(Map<String, dynamic> map) {
    return LocalWaitingTimeModel(
      startTime: DateTime.parse(map['startTime']),
      endTime:  map['endTime'] == null ? null : DateTime.parse(map['endTime']),
      type: map['type'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'startTime': startTime.toString(),
      'endTime': endTime == null ? null : endTime.toString(),
      'type': type,
    };
  }
}

abstract class LocalWaitingTimeTyes {
  static String arrived = "arrived";
  static String stop = "stop";
  static String inProgress = "in_progress";
}
