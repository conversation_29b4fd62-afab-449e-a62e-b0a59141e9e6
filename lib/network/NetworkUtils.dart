import 'package:chucker_flutter/chucker_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:rooo_driver/global/export/app_export.dart';

final chuckerHttpClient = ChuckerHttpClient(http.Client());

Map<String, String> buildHeaderTokens() {
  Map<String, String> header = {
    HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    HttpHeaders.cacheControlHeader: 'no-cache',
    HttpHeaders.acceptHeader: 'application/json; charset=utf-8',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Origin': '*',
    "user_type": "driver",
    "language": sharedPref.getString(SELECTED_LANGUAGE_CODE).toString(),
  };
  if (appStore.isLoggedIn) {
    header.putIfAbsent(HttpHeaders.authorizationHeader,
        () => 'Bearer ${sharedPref.getString(TOKEN)}');
    header.putIfAbsent('darkMode', () => IS_DARK_MODE_ON.toString());
  }
  if (global_region_id != -1) {
    header.putIfAbsent('RegionId', () => global_region_id.toString());
  }
  return header;
}

Uri buildBaseUrl(String endPoint) {
  Uri url = Uri.parse(endPoint);
  if (!endPoint.startsWith('http'))
    url = Uri.parse('${AppCred.baseUrl}$endPoint');
  return url;
}

Future<Response> buildHttpResponse(String endPoint,
    {HttpMethod method = HttpMethod.GET, Map? request}) async {
  Response response;

  Response response500 = Response(
      jsonEncode({"status": false, "message": "Server error 500"}), 500);
  Response clientError =
      Response(jsonEncode({"status": false, "message": "Network error"}), 500);

  for (int i = 0; i < 3; i++) {
    var headers = buildHeaderTokens();

    Uri url = buildBaseUrl(endPoint);

    try {
      if (method == HttpMethod.POST) {
        response = await chuckerHttpClient
            .post(url, body: jsonEncode(request), headers: headers)
            .timeout(Duration(seconds: 8), onTimeout: () => throw 'Timeout');
      } else if (method == HttpMethod.DELETE) {
        response = await chuckerHttpClient
            .delete(url, headers: headers)
            .timeout(Duration(seconds: 8), onTimeout: () => throw 'Timeout');
      } else if (method == HttpMethod.PUT) {
        response = await chuckerHttpClient
            .put(url, body: jsonEncode(request), headers: headers)
            .timeout(Duration(seconds: 8), onTimeout: () => throw 'Timeout');
      } else {
        response = await chuckerHttpClient
            .get(url, headers: headers)
            .timeout(Duration(seconds: 8), onTimeout: () => throw 'Timeout');
      }

      if (response.statusCode == 401) {
        logout(isUnAuthorised: true);
      }
      if (response.statusCode == 500) {
        return response500;
      }

      return response;
    } catch (e) {
      if (e.toString().startsWith('Api')) {
        return response500;
      } else if (((e as dynamic)?.message ?? "").toString().contains('host')) {
        GlobalMethods.showWWWConnectionError(
            context: navigatorKey.currentContext!);
        return clientError;
      } else {
        continue;
      }
    }
  }
  return clientError;
}

Future handleResponse(Response response, [bool? avoidTokenError]) async {
  if (response.statusCode == 401) {
    logout(isUnAuthorised: true);
  } else {
    return jsonDecode(response.body);
  }
}

Future<Map<String, dynamic>> sendMultiPartApi({
  required MultipartRequest multiPartRequest,
}) async {
  try {
    String? result;
    multiPartRequest.headers.addAll(buildHeaderTokens());

    // StreamedResponse response = await multiPartRequest.send();
    StreamedResponse response = await chuckerHttpClient.send(multiPartRequest);

    if (response.statusCode == 401) {
      logout(isUnAuthorised: true);
    } else {
      Uint8List responseData = await response.stream.toBytes();
      result = String.fromCharCodes(responseData);
    }

    if (result != null) {
      return jsonDecode(result);
    } else {
      throw "server error";
    }
  } catch (e) {
    throw e;
  }
}

enum HttpMethod { GET, POST, DELETE, PUT }

class TokenException implements Exception {
  final String message;
  const TokenException([this.message = ""]);
  String toString() => "FormatException: $message";
}
