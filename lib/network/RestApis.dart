import 'package:rooo_driver/features/Refferals/model/referrals_model_responce.dart';
import 'package:rooo_driver/features/care/screens/CreateRideHelpTabScreen.dart';
import 'package:rooo_driver/features/documents/models/document_model.dart';
import 'package:rooo_driver/features/earnings/screens/earning_report_response_model.dart';
import 'package:rooo_driver/features/help/models/help_response_model.dart';
import 'package:rooo_driver/features/homepage/model/advertisment_response_model.dart';
import 'package:rooo_driver/features/login/screens/roo_login_screen.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/features/ride_history/models/ride_history_response_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/BankListResponseModel.dart';
import 'package:rooo_driver/features/inbox/models/Inbox_model.dart';
import 'package:rooo_driver/model/OpposrtunityResponseModel.dart';
import 'package:rooo_driver/model/ReviewModel.dart';
import 'package:rooo_driver/model/RideHistoryDetailsModel.dart';
import 'package:rooo_driver/model/rooo_care/rooo_care_list_response_model.dart';
import 'package:rooo_driver/model/homepage_response_model.dart';
import 'package:rooo_driver/model/new_documentListModel.dart';
import 'package:rooo_driver/model/refferal_offers/referral_offer_status_model_response.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model_response.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';

import 'package:rooo_driver/model/RideDetailModel.dart';
import 'package:rooo_driver/model/RiderListModel.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/screens/DeleteAccountScreen.dart';

import '../model/AdditionalFeesList.dart';
import '../model/AppSettingModel.dart';
import '../model/ChangePasswordResponseModel.dart';
import '../model/ComplaintCommentModel.dart';
import '../model/ContactNumberListModel.dart';
import '../features/documents/models/driver_document_model.dart';
import '../model/EarningListModelWeek.dart';
import '../model/LoginResponse.dart';
import '../model/NotificationListModel.dart';
import '../model/PaymentListModel.dart';
import '../model/ProfileUpdateModel.dart';
import '../model/ServiceModel.dart';
import '../model/WalletDetailModel.dart';
import '../model/WalletListModel.dart';
import '../model/WithDrawListModel.dart';

// Future<LoginResponse?> signUpApi(Map request, String uid) async {
//   Response response = await buildHttpResponse('driver-register',
//       request: request, method: HttpMethod.POST);

//   if (response.statusCode != 200) {
//     var json = jsonDecode(response.body);
//     if (response.statusCode == 422) {
//       await userService.removeDocument(uid).then((value) async {
//         AuthServices authService = AuthServices();

//         await authService
//             .deleteUserFirebase()
//             .then((value) async {})
//             .catchError((error) {});
//       }).catchError((error) {});

//       throw json['message'];
//     } else {
//       throw json['message'];
//     }
//   }

//   return await handleResponse(response).then((json) async {
//     var loginResponse = LoginResponse.fromJson(json);

//     if (loginResponse.data != null) {
//       await sharedPref.setString(
//           TOKEN, loginResponse.data!.apiToken.validate());
//       await sharedPref.setString(
//           USER_TYPE, loginResponse.data!.userType.validate());
//       await sharedPref.setString(
//           FIRST_NAME, loginResponse.data!.firstName.validate());
//       await sharedPref.setString(
//           LAST_NAME, loginResponse.data!.lastName.validate());
//       await sharedPref.setString(
//           CONTACT_NUMBER, loginResponse.data!.contactNumber.validate());
//       await sharedPref.setString(
//           USER_EMAIL, loginResponse.data!.email.validate());
//       await sharedPref.setString(
//           USER_NAME, loginResponse.data!.username.validate());
//       await sharedPref.setString(
//           ADDRESS, loginResponse.data!.address.validate());
//       await sharedPref.setInt(USER_ID, loginResponse.data!.id ?? 0);
//       await sharedPref.setString(
//           USER_PROFILE_PHOTO, loginResponse.data!.profileImage.validate());
//       await sharedPref.setString(GENDER, loginResponse.data!.gender.validate());
//       await sharedPref.setInt(IS_ONLINE, loginResponse.data!.isOnline ?? 0);
//       await sharedPref.setString(UID, loginResponse.data!.uid.validate());
//       await sharedPref.setString(
//           LOGIN_TYPE, loginResponse.data!.loginType.validate());
//       await sharedPref.setInt(
//           IS_Verified_Driver, loginResponse.data!.isVerifiedDriver ?? 0);

//       await appStore.setLoggedIn(true);
//       await appStore.setUserEmail(loginResponse.data!.email.validate());
//       await appStore
//           .setUserProfile(loginResponse.data!.profileImage.validate());
//       return loginResponse;
//     }
//     return null;
//   }).catchError((e) {
//     log(e.toString());
//     return null;
//   });
// }

Future<LoginResponse> logInApi(Map request,
    {bool isSocialLogin = false}) async {
  Response response = await buildHttpResponse(
      isSocialLogin ? 'social-login' : 'login',
      request: request,
      method: HttpMethod.POST);

  if (!(response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);

      if (json.containsKey('code') &&
          json['code'].toString().contains('invalid_username')) {
        throw 'invalid_username';
      }
    }
  }

  return await handleResponse(response).then((json) async {
    var loginResponse = LoginResponse.fromJson(json);

    if (loginResponse.data != null) {
      await sharedPref.setString(
          TOKEN, loginResponse.data!.apiToken.validate());
      await sharedPref.setString(
          USER_TYPE, loginResponse.data!.userType.validate());
      await sharedPref.setString(
          FIRST_NAME, loginResponse.data!.firstName.validate());
      await sharedPref.setString(
          LAST_NAME, loginResponse.data!.lastName.validate());
      await sharedPref.setString(
          CONTACT_NUMBER, loginResponse.data!.contactNumber.validate());
      await sharedPref.setString(
          USER_EMAIL, loginResponse.data!.email.validate());
    
      await sharedPref.setInt(USER_ID, loginResponse.data!.id ?? 0);
      await sharedPref.setString(
          USER_PROFILE_PHOTO, loginResponse.data!.profileImage.validate());
      await sharedPref.setString(GENDER, loginResponse.data!.gender.validate());
      if (loginResponse.data!.isOnline != null)
        await sharedPref.setInt(IS_ONLINE, loginResponse.data!.isOnline ?? 0);
      await sharedPref.setInt(
          IS_Verified_Driver, loginResponse.data!.isVerifiedDriver ?? 0);
      if (loginResponse.data!.uid != null)
        await sharedPref.setString(UID, loginResponse.data!.uid.validate());
      await sharedPref.setString(
          LOGIN_TYPE, loginResponse.data!.loginType.validate());

      await appStore.setLoggedIn(true);
      await appStore.setUserEmail(loginResponse.data!.email.validate());
      await appStore
          .setUserProfile(loginResponse.data!.profileImage.validate());
    }
    return loginResponse;
  }).catchError((e) {
    throw e.toString();
  });
}

Future<MultipartRequest> getMultiPartRequest(String endPoint,
    {String? baseUrl}) async {
  String url = '${baseUrl ?? buildBaseUrl(endPoint).toString()}';
  log(url);
  return MultipartRequest('POST', Uri.parse(url));
}

Future sendMultiPartRequest(
  MultipartRequest multiPartRequest,
) async {
  String? result;
  multiPartRequest.headers.addAll(buildHeaderTokens());

  StreamedResponse response = await multiPartRequest.send();
  if (response.statusCode == 200) {
    Uint8List responseData = await response.stream.toBytes();
    result = String.fromCharCodes(responseData);
  }
  return result;
}

/// Profile Update
Future updateProfile(
    {String? firstName,
    String? lastName,
    String? userEmail,
    String? address,
    String? contactNumber,
    String? gender,
    bool? nightDrivingPreference,
    File? file}) async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('update-profile');
  multiPartRequest.fields['id'] = sharedPref.getInt(USER_ID).toString();
  multiPartRequest.fields['username'] =
      sharedPref.getString(USER_NAME).validate();
  multiPartRequest.fields['email'] = userEmail ?? appStore.userEmail;
  multiPartRequest.fields['first_name'] = firstName.validate();
  multiPartRequest.fields['last_name'] = lastName.validate();
  multiPartRequest.fields['contact_number'] = contactNumber.validate();
  multiPartRequest.fields['address'] = address.validate();
  multiPartRequest.fields['gender'] = gender.validate();
  multiPartRequest.fields['user_detail'] = jsonEncode({
    // 'age': age,
    // 'socialSecurityNumber': securityNumber!,
    'nightDrivingPreference': nightDrivingPreference!,
  });
  if (file != null)
    multiPartRequest.files
        .add(await MultipartFile.fromPath('profile_image', file.path));

  await sendMultiPartRequest(
    multiPartRequest,
  ).then((value) async {
    if (value != null) {
      ProfileUpdate res = ProfileUpdate.fromJson(value);
      await sharedPref.setString(FIRST_NAME, res.data!.firstName.validate());
      await sharedPref.setString(LAST_NAME, res.data!.lastName.validate());
      await sharedPref.setString(
          USER_PROFILE_PHOTO, res.data!.profileImage.validate());
      await sharedPref.setString(USER_NAME, res.data!.username.validate());
      await sharedPref.setString(USER_ADDRESS, res.data!.address.validate());
      await sharedPref.setString(
          CONTACT_NUMBER, res.data!.contactNumber.validate());
      await sharedPref.setString(GENDER, res.data!.gender.validate());
      // await sharedPref.setString(OTHER_GENDER_TEXT, res.data!.otherGenderText.validate());
      await appStore.setUserEmail(res.data!.email.validate());
      await appStore.setUserProfile(res.data!.profileImage.validate());
    }
  }).onError((e, _) {
    GlobalMethods.toast(errorMessage);
  });
}

Future<Map<String, dynamic>> upload4images({
  required int ride_request_id,
  required String type,
  required File front_image,
  required File right_image,
  required File back_image,
  required File left_image,
}) async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('save-riderequest-photo');

  multiPartRequest.fields['ride_request_id'] = ride_request_id.toString();
  multiPartRequest.fields['type'] = type;

  multiPartRequest.files
      .add(await MultipartFile.fromPath('left_image', left_image.path));

  multiPartRequest.files
      .add(await MultipartFile.fromPath('front_image', front_image.path));

  multiPartRequest.files
      .add(await MultipartFile.fromPath('right_image', right_image.path));

  multiPartRequest.files
      .add(await MultipartFile.fromPath('back_image', back_image.path));

  multiPartRequest.headers.addAll(buildHeaderTokens());

  log('nct-> uploading');

  var t = await multiPartRequest.send();

  var h = await http.Response.fromStream(t);
  return jsonDecode(h.body);
}

Future<void> logout({bool isUnAuthorised = false}) async {
  if (GlobalState.isLoggedIn && GlobalState.isLoggingOut == false) {
    GlobalState.isLoggingOut = true;

    if (!isUnAuthorised) {
      await logoutApi().then((value) async {
        logOutSuccess();
      }).catchError((e) {
        throw e.toString();
      });
    } else {
      logOutSuccess();
    }
  }
}

Future<InboxResponseModel> getInboxListApi({required int page}) async {
  return InboxResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('get-inbox', method: HttpMethod.GET)));
}

Future<StatusMessageModel> updateDriverProfileApi(
    {required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('update-driver-profile',
          method: HttpMethod.POST, request: request)));
}

Future<OpportunityResponseModel> getOpportunityListApi(
    {required int page, required String request}) async {
  return OpportunityResponseModel.fromJson(
      await handleResponse(await buildHttpResponse(
    'opportunity-list?type=${request}',
    method: HttpMethod.GET,
  )));
}

Future<BankListResponseModel> getBankNameListApi() async {
  return BankListResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('bank-list', method: HttpMethod.GET)));
}

Future<RoooCareResponseModel> getRoooCarePendingListApi(
    {required int page}) async {
  return RoooCareResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('care-pending-list', method: HttpMethod.GET)));
}

Future<RoooCareResponseModel> getRoooCareCompletedListApi(
    {required int page}) async {
  return RoooCareResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('care-completed-list', method: HttpMethod.GET)));
}

Future<WebViewDataResponseModel> getHelpListApi({required int page}) async {
  return WebViewDataResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('help-list', method: HttpMethod.GET)));
}

Future<HomePageModel> getHomePageDataApi() async {
  return HomePageModel.fromJson(await handleResponse(
      await buildHttpResponse('get-driver-dashboard', method: HttpMethod.GET)));
}

Future<StatusMessageModel> isReadFunction({required int id}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("read-inbox-message" + "/" + "$id",
          method: HttpMethod.POST)));
}

Future<RefferalModelResponse> getReferralApi(
    {required String offer_type}) async {
  Map request = {"referrals_type": offer_type};
  return RefferalModelResponse.fromJson(await handleResponse(
      await buildHttpResponse("referrals-list",
          request: request, method: HttpMethod.POST)));
}

Future<RefferalOfferModelResponse> getReferralOffersApi(
    {required String offer_type}) async {
  Map request = {"offer_type": offer_type};
  return RefferalOfferModelResponse.fromJson(await handleResponse(
      await buildHttpResponse("offer-list",
          request: request, method: HttpMethod.POST)));
}

Future verifyReferralCodeApi({required String referral_code}) async {
  Map request = {"referral_code": referral_code};
  return await handleResponse(await buildHttpResponse("verify-referral-code",
      request: request, method: HttpMethod.POST));
}

Future<RefferalOfferModelStatusResponse> getReferralOffersStatusApi() async {
  return RefferalOfferModelStatusResponse.fromJson(await handleResponse(
      await buildHttpResponse("get-offer-status", method: HttpMethod.GET)));
}

// Future<FaqResponseModel> getFaqListApi() async {
//   return FaqResponseModel.fromJson(await handleResponse(
//       await buildHttpResponse("faq-list", method: HttpMethod.GET)));
// }

Future updateBankInfoApi({required Map req}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("update-profile",
          method: HttpMethod.POST, request: req)));
}

Future<StatusMessageModel> roooCareAddApi(
    {required Map<dynamic, dynamic> req}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("care-save",
          request: req, method: HttpMethod.POST)));
}

Future<StatusMessageModel> roooCareUpdateApi(
    {required Map<dynamic, dynamic> req, required int id}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("care-update/" + id.toString(),
          request: req, method: HttpMethod.POST)));
}

Future<StatusMessageModel> deleteinboxApi({required int id}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("delete-inbox-message" + "/" + "$id",
          method: HttpMethod.POST)));
}

Future<ChangePasswordResponseModel> changePassword(Map req) async {
  return ChangePasswordResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('change-password',
          request: req, method: HttpMethod.POST)));
}

Future<ChangePasswordResponseModel> forgotPassword(Map req) async {
  return ChangePasswordResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('forget-password',
          request: req, method: HttpMethod.POST)));
}

Future<ServiceModel> getServices({required String countryCode}) async {
  return ServiceModel.fromJson(await handleResponse(await buildHttpResponse(
      'service-list?country_code=$countryCode',
      method: HttpMethod.GET)));
}

Future<UserDetailModel> getUserDetail({int? userId}) async {
  return UserDetailModel.fromJson(await handleResponse(await buildHttpResponse(
      'user-detail?id=$userId',
      method: HttpMethod.GET)));
}

Future<dynamic> getPrivacyPolicy() async {
  return await handleResponse(
    await buildHttpResponse('privacy-policy', method: HttpMethod.GET),
  );
}

Future<dynamic> getTAndC() async {
  return await handleResponse(
    await buildHttpResponse('term-conditions', method: HttpMethod.GET),
  );
}

Future<StatusMessageModel> changeStatus(Map request) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('update-user-status',
          method: HttpMethod.POST, request: request)));
}

Future<StatusMessageModel> saveBooking(Map request) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('update-user-status',
          method: HttpMethod.POST, request: request)));
}

Future<WalletListModel> getWalletListApi({required int pageData}) async {
  return WalletListModel.fromJson(await handleResponse(await buildHttpResponse(
      'wallet-list?page=$pageData',
      method: HttpMethod.GET)));
}

// Future<RideHistoryResponseModel> getRideHistoryApi(
//     {required String status}) async {
//   return RideHistoryResponseModel.fromJson(await handleResponse(
//       await buildHttpResponse('riderequest-list?status=${status}',
//            method: HttpMethod.GET)));
// }

Future<RideHistoryDetailsModel> getRideHistoryDetailApi(
    {required int id}) async {
  Map req = {"status": id};
  return RideHistoryDetailsModel.fromJson(await handleResponse(
      await buildHttpResponse('riderequest-detail?id=${id}',
          request: req, method: HttpMethod.GET)));
}

Future<PaymentListModel> getPaymentList() async {
  return PaymentListModel.fromJson(await handleResponse(await buildHttpResponse(
      'payment-gateway-list?status=1',
      method: HttpMethod.GET)));
}

Future<StatusMessageModel> saveWallet(Map request) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('save-wallet',
          method: HttpMethod.POST, request: request)));
}

Future<StatusMessageModel> saveSOS(Map request) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('save-sos',
          method: HttpMethod.POST, request: request)));
}

Future<ContactNumberListModel> getSosList({int? regionId}) async {
  return ContactNumberListModel.fromJson(await handleResponse(
      await buildHttpResponse(
          regionId != null ? 'sos-list?region_id=$regionId' : 'sos-list',
          method: HttpMethod.GET)));
}

Future<ContactNumberListModel> deleteSosList({int? id}) async {
  return ContactNumberListModel.fromJson(await handleResponse(
      await buildHttpResponse('sos-delete/$id', method: HttpMethod.POST)));
}

Future<WithDrawListModel> getWithDrawListApi({int? page}) async {
  return WithDrawListModel.fromJson(await handleResponse(
      await buildHttpResponse('withdrawrequest-list?page=$page',
          method: HttpMethod.GET)));
}

Future<StatusMessageModel> saveWithDrawRequest(Map request) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('save-withdrawrequest',
          method: HttpMethod.POST, request: request)));
}

Future<AppSettingModel> getAppSetting() async {
  return AppSettingModel.fromJson(await handleResponse(
      await buildHttpResponse('admin-dashboard', method: HttpMethod.GET)));
}

Future<RiderListModel> getRiderRequestList(
    {int? page, String? status, LatLng? sourceLatLog, int? driverId}) async {
  if (sourceLatLog != null) {
    return RiderListModel.fromJson(await handleResponse(await buildHttpResponse(
        'riderequest-list?page=$page&driver_id=$driverId',
        method: HttpMethod.GET)));
  } else {
    return RiderListModel.fromJson(await handleResponse(await buildHttpResponse(
        status != null
            ? 'riderequest-list?page=$page&status=$status&driver_id=$driverId'
            : 'riderequest-list?page=$page&driver_id=$driverId',
        method: HttpMethod.GET)));
  }
}

Future<DocumentListModel> getDocumentList() async {
  return DocumentListModel.fromJson(await handleResponse(
      await buildHttpResponse('document-list', method: HttpMethod.GET)));
}

Future<DocumentListModelResponse> getDocumentListApi() async {
  return DocumentListModelResponse.fromJson(await handleResponse(
      await buildHttpResponse('document-list', method: HttpMethod.GET)));
}

Future<DriverDocumentList> getDriverDocumentList() async {
  return DriverDocumentList.fromJson(await handleResponse(
      await buildHttpResponse('driver-document-list', method: HttpMethod.GET)));
}

/// Profile Update
Future uploadDocument(
    {int? driverId, int? documentId, File? file, int? isExpire}) async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('driver-document-save');
  multiPartRequest.fields['driver_id'] = driverId.toString();
  multiPartRequest.fields['document_id'] = documentId.toString();
  multiPartRequest.fields['is_verified'] = '0';
  if (isExpire != null) multiPartRequest.fields['is_verified'] = '0';
  if (file != null)
    multiPartRequest.files
        .add(await MultipartFile.fromPath('driver_document', file.path));

  await sendMultiPartRequest(
    multiPartRequest,
  ).then((value) {}).onError((e, _) {
    GlobalMethods.toast(errorMessage);
  });
}

Future<StatusMessageModel> deleteDeliveryDoc(int id) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('driver-document-delete/$id',
          method: HttpMethod.POST)));
}

// Future updateStatus(Map request) async {
//   return LoginResponse.fromJson(await handleResponse(await buildHttpResponse(
//       'update-user-status',
//       method: HttpMethod.POST,
//       request: request)));
// }

Future<LoginResponse> updateDriverPlayerIdApi(Map request) async {
  return LoginResponse.fromJson(await handleResponse(await buildHttpResponse(
      'update-user-playerid',
      method: HttpMethod.POST,
      request: request)));
}

Future<dynamic> heartBeat() async {
  return await handleResponse(
    await buildHttpResponse('privacy-policy', method: HttpMethod.GET),
  );
}

Future<LoginResponse> waitingTimeApi(Map request) async {
  return LoginResponse.fromJson(await handleResponse(await buildHttpResponse(
      'ride-waiting-time',
      method: HttpMethod.POST,
      request: request)));
}

Future<UserDetailModel> userDetail(int? userId) async {
  return UserDetailModel.fromJson(await handleResponse(await buildHttpResponse(
      'user-detail?id=$userId',
      method: HttpMethod.POST)));
}

/// Update Vehicle Info
Future updateVehicleDetail(
    {String? carModel,
    String? carColor,
    String? carPlateNumber,
    String? carProduction}) async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('update-profile');
  multiPartRequest.fields['id'] = sharedPref.getInt(USER_ID).toString();
  multiPartRequest.fields['email'] =
      sharedPref.getString(USER_EMAIL).validate();
  multiPartRequest.fields['contact_number'] =
      sharedPref.getString(CONTACT_NUMBER).validate();
  multiPartRequest.fields['username'] =
      sharedPref.getString(USER_NAME).validate();
  multiPartRequest.fields['user_detail[car_model]'] = carModel.validate();
  multiPartRequest.fields['user_detail[car_color]'] = carColor.validate();
  multiPartRequest.fields['user_detail[car_plate_number]'] =
      carPlateNumber.validate();
  multiPartRequest.fields['user_detail[car_production_year]'] =
      carProduction.validate();

  await sendMultiPartRequest(multiPartRequest).then((value) {}).onError((e, _) {
    GlobalMethods.toast(errorMessage);
  });
}

/// Update Bank Info
Future updateBankDetail(
    {String? bankName,
    String? accountHolderName,
    String? adress,
    String? city,
    String? postalCode,
    String? dateOfBirth,
    String? instituitionNumber,
    String? transitNumber,
    String? bank_id,
    String? accountNumber}) async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('update-profile');
  multiPartRequest.fields['email'] =
      sharedPref.getString(USER_EMAIL).validate();
  multiPartRequest.fields['contact_number'] =
      sharedPref.getString(CONTACT_NUMBER).validate();
  multiPartRequest.fields['username'] =
      sharedPref.getString(USER_NAME).validate();
  multiPartRequest.fields['bank_name'] = bankName.validate();
  multiPartRequest.fields['postal_code'] = postalCode.validate();
  multiPartRequest.fields['account_holder_name'] = accountHolderName.validate();
  multiPartRequest.fields['account_number'] = accountNumber.validate();

  multiPartRequest.fields['bank_address'] = adress.validate();

  multiPartRequest.fields['bank_city'] = city.validate();
  multiPartRequest.fields['dob'] = dateOfBirth.validate();
  multiPartRequest.fields['bank_id'] = accountNumber.validate();
  multiPartRequest.fields['institution_number'] = instituitionNumber.validate();
  multiPartRequest.fields['transit_number'] = transitNumber.validate();
  multiPartRequest.fields['bank_id'] = bank_id.validate();

  await sendMultiPartRequest(
    multiPartRequest,
  ).onError((e, _) {
    GlobalMethods.toast(errorMessage);
  }).then((value) {});
}

Future<RideModel> getCurrentRideRequest() async {
  return RideModel.fromJson(await handleResponse(
      await buildHttpResponse('current-riderequest', method: HttpMethod.GET)));
}

Future<StatusMessageModel> rideRequestUpdate(
    {required Map request, int? rideId}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('riderequest-update/$rideId',
          method: HttpMethod.POST, request: request)));
}

Future notifyAdminFromDriverApi({required int rideId}) async {
  Map request = {"rideId": rideId};
  return await handleResponse(await buildHttpResponse(
      'arrived-admin-notification',
      method: HttpMethod.POST,
      request: request));
}

// Future<List<FAQ>?> getFaqListApi() async {
//   Response response = await buildHttpResponse('faq-list');

//   if ((response.statusCode >= 200 && response.statusCode <= 206)) {
//     if (response.body.isJson()) {
//       var json = jsonDecode(response.body);
//       try {
//         List<FAQ> data = [];
//         for (var i = 0; i < json['data'].length; i++) {
//           data.add(
//             FAQ.fromJson(
//               json['data'][i],
//             ),
//           );
//         }
//         return data;
//       } catch (e) {
//         return null;
//       }
//     }
//     return null;
//   } else {
//     return null;
//   }
// }

Future<String> getFAQDetailsApi(int id) async {
  Response response = await buildHttpResponse('faq-details/' + id.toString());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return json["description"];
      } catch (e) {
        return "NotFound";
      }
    }
    return "NotFound";
  } else {
    return "NotFound";
  }
}

Future<String?> getHelpDetailsApi(int id) async {
  Response response = await buildHttpResponse('help-details/' + id.toString());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    // if (response.body.isJson()) {
    //   var json = jsonDecode(response.body);
    //   try {
    //     return json;
    //   } catch (e) {
    //     return null;
    //   }
    // }
    // return null;
    return response.body;
  } else {
    return null;
  }
}

Future<String?> getOpportunityDetailsApi(int id) async {
  Response response =
      await buildHttpResponse('driver-opportunity-detail/' + id.toString());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    // if (response.body.isJson()) {
    //   var json = jsonDecode(response.body);
    //   try {
    //     return json;
    //   } catch (e) {
    //     return null;
    //   }
    // }
    // return null;
    return response.body;
  } else {
    return null;
  }
}

Future<String?> getBlogsDetailsApi(int id) async {
  Response response =
      await buildHttpResponse('driver-blog-detail/' + id.toString());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    // if (response.body.isJson()) {
    //   var json = jsonDecode(response.body);
    //   try {
    //     return json;
    //   } catch (e) {
    //     return null;
    //   }
    // }
    // return null;
    return response.body;
  } else {
    return null;
  }
}

Future<String?> getInboxDetailsApi(int id) async {
  Response response = await buildHttpResponse('inbox-details/' + id.toString());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    // if (response.body.isJson()) {
    //   var json = jsonDecode(response.body);
    //   try {
    //     return json;
    //   } catch (e) {
    //     return null;
    //   }
    // }
    // return null;
    return response.body;
  } else {
    return null;
  }
}

Future<StatusMessageModel> ratingReview({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('save-ride-rating',
          method: HttpMethod.POST, request: request)));
}

Future<AdditionalFeesList> getAdditionalFees() async {
  return AdditionalFeesList.fromJson(await handleResponse(
      await buildHttpResponse('additional-fees-list', method: HttpMethod.GET)));
}

Future<StatusMessageModel> adminNotify({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('admin-sos-notify',
          method: HttpMethod.POST, request: request)));
}

Future<RideDetailModel> rideDetail({required int? orderId}) async {
  return RideDetailModel.fromJson(await handleResponse(await buildHttpResponse(
      'riderequest-detail?id=$orderId',
      method: HttpMethod.GET)));
}

Future<ReviewResponseModel> getAutomatedTextApi() async {
  return ReviewResponseModel.fromJson(await handleResponse(
      await buildHttpResponse("get-review-auto-suggestive-message",
          method: HttpMethod.GET)));
}

Future<RideHistoryResponseModel> getRideHistoryApi(
    {required String status, required int driver_id}) async {
  Map req = {
    "driver_id": driver_id,
    "status": status,
  };
  return RideHistoryResponseModel.fromJson(await handleResponse(
      await buildHttpResponse(
          'riderequest-list?status=$status&driver_id=$driver_id',
          request: req,
          method: HttpMethod.GET)));
}

Future<AdvertismentResponseModel> getAdvertismentApi(
    {required String type}) async {
  return AdvertismentResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('advertisements?type=${type}',
          method: HttpMethod.GET)));
}

Future<StatusMessageModel> saveComplain({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('save-complaint',
          method: HttpMethod.POST, request: request)));
}

Future<StatusMessageModel> completeRide({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('complete-riderequest',
          method: HttpMethod.POST, request: request)));
}

Future<StatusMessageModel> savePayment(Map request) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('save-payment',
          method: HttpMethod.POST, request: request)));
}

Future<StatusMessageModel> rideRequestResPond({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('riderequest-respond',
          method: HttpMethod.POST, request: request)));
}

Future<StatusMessageModel> rideRequestResPondSchedule(
    {required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('riderequest-respond-schedule',
          method: HttpMethod.POST, request: request)));
}

/// Get Notification List
Future<NotificationListModel> getNotification({required int page}) async {
  return NotificationListModel.fromJson(await handleResponse(
      await buildHttpResponse('notification-list?page=$page',
          method: HttpMethod.POST)));
}

Future<StatusMessageModel> deleteUser() async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('delete-user-account', method: HttpMethod.POST)));
}

Future<RideReportModel> earningList({Map? req}) async {
  return RideReportModel.fromJson(await handleResponse(await buildHttpResponse(
      'earning-list',
      method: HttpMethod.POST,
      request: req)));
}

Future<EarningReportResponseModel> reportPdf({Map? req}) async {
  return EarningReportResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('earning-list',
          method: HttpMethod.POST, request: req)));
}

Future updateProfileUid() async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('update-profile');
  multiPartRequest.fields['id'] = sharedPref.getInt(USER_ID).toString();
  multiPartRequest.fields['username'] =
      sharedPref.getString(USER_NAME).validate();
  multiPartRequest.fields['email'] =
      sharedPref.getString(USER_EMAIL).validate();
  multiPartRequest.fields['uid'] = sharedPref.getString(UID).toString();

  log('multipart request:${multiPartRequest.fields}');
  log(sharedPref.getString(UID).toString());

  await sendMultiPartRequest(
    multiPartRequest,
  ).onError((e, _) {
    GlobalMethods.toast(errorMessage);
  });
}

Future<WalletDetailModel> walletDetailApi() async {
  return WalletDetailModel.fromJson(await handleResponse(
      await buildHttpResponse('wallet-detail', method: HttpMethod.GET)));
}

Future<StatusMessageModel> complaintComment({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('save-complaintcomment',
          method: HttpMethod.POST, request: request)));
}

Future<ComplaintCommentModel> complaintList(
    {required int complaintId, required int currentPage}) async {
  return ComplaintCommentModel.fromJson(await handleResponse(
      await buildHttpResponse(
          'complaintcomment-list?complaint_id=$complaintId&page=$currentPage',
          method: HttpMethod.GET)));
}

Future<StatusMessageModel> logoutApi() async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('logout?clear=player_id',
          method: HttpMethod.GET)));
}

Future verifyEmailApi() async {
  return await handleResponse(await buildHttpResponse('sent-verification-email',
      method: HttpMethod.GET));
}

logOutSuccess() async {
  sharedPref.clear();
  GlobalMethods.stopServerTracking();

  IS_DARK_MODE_ON = false;
  appStore.setLoggedIn(false);
  appStore.setUserProfile("");
  GlobalState.driver_device_timer?.cancel();
  GlobalState.driver_server_timer?.cancel();
  GlobalState.isLoggingOut = false;
  GlobalState.isLoggedIn = false;

  GlobalMethods.pushAndRemoveAll(
      context: navigatorKey.currentContext!,
      screen: LoginScreen(),
      screenIdentifier: ScreenIdentifier.InitialScreen);
}

Future<AppSettingModel> getAppSettingApi() async {
  return AppSettingModel.fromJson(await handleResponse(
      await buildHttpResponse('appsetting', method: HttpMethod.GET)));
}

Future<dynamic> isUserExists(String? phoneNumber, String? email,
    String? googleId, String? facebookId, appleId) async {
  Response response = await buildHttpResponse('custom-login',
      request: {
        'contact_number': phoneNumber,
        'email': email,
        'google_id': googleId,
        'facebook_id': facebookId,
        'apple_id': appleId,
        "user_type": "driver",
      },
      method: HttpMethod.POST);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);

      return json;
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> validateOtpApi(
    {required String key, required String otp}) async {
  Response response = await buildHttpResponse('validate-otp',
      request: {
        'key': key,
        'otp': otp,
        'player_id': sharedPref.getString(PLAYER_ID),
        "user_type": "driver",
      },
      method: HttpMethod.POST);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);

      return json;
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> resendOtpApi({required String phone}) async {
  Response response = await buildHttpResponse('send-otp',
      request: {'contact_number': phone, "user_type": "driver"},
      method: HttpMethod.POST);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);

      return json;
    }
    return null;
  } else {
    return null;
  }
}

//new apis

Future<dynamic> sendEmailVerificationCode({
  required String email,
}) async {
  Response response =
      await buildHttpResponse('hello', method: HttpMethod.POST, request: {
    'email': email,
  });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> changeDriverEmail(
    {required String email, required String verificationCode}) async {
  Response response = await buildHttpResponse('update-email',
      method: HttpMethod.POST,
      request: {
        'email2': email,
        // 'code': verificationCode,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> sendMobileVerificationCode({
  required String mobile,
}) async {
  Response response = await buildHttpResponse('update-contact-number',
      method: HttpMethod.POST,
      request: {
        'contact_number2': mobile,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> changeRiderMobile(
    {required String mobile, required String verificationCode}) async {
  Response response = await buildHttpResponse('validate-otp-contact2',
      method: HttpMethod.POST,
      request: {
        'mobile': mobile,
        'otp': verificationCode,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<MapBoxSearchResponse?> searchWithMapBox(
    {required String search, required String sessionToken}) async {
  Response response = await buildHttpResponse(
      'https://api.mapbox.com/search/searchbox/v1/suggest?q=${search}&session_token=$sessionToken&access_token=${AppCred.mapBoxPublicTokenKey}&country=${GlobalState.driverRegionCode}',
      method: HttpMethod.GET);
  var json = jsonDecode(response.body);
  try {
    return MapBoxSearchResponse.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<MapBoxLocation?> getMapBoxLocation(
    {required String mapBoxId, required String sessionToken}) async {
  Response response = await buildHttpResponse(
      'https://api.mapbox.com/search/searchbox/v1/retrieve/${mapBoxId}?session_token=$sessionToken&access_token=${AppCred.mapBoxPublicTokenKey}&country=${GlobalState.driverRegionCode}',
      method: HttpMethod.GET);
  var json = jsonDecode(response.body);
  try {
    return MapBoxLocation.fromJson(json['features'][0]['properties']);
  } catch (e) {
    return null;
  }
}

// Future<GoogleMapSearchModel> searchAddressRequest(
//     {required String search}) async {
//   return GoogleMapSearchModel.fromJson(
//       await handleResponse(await buildHttpResponse(
//           "https://maps.googleapis.com/maps/api/place/autocomplete/json?input=" +
//               search +
//               "&key=" +
//               AppCred.googleMapAPIKey +
//               "&components=country:" +
//               //TODO: region code
//               GLOBAL_REGION_ID,
//           method: HttpMethod.GET)));
// }

// Future<GooglePlaceIdModel> searchAddressRequestPlaceId(
//     {required String placeId}) async {
//   return GooglePlaceIdModel.fromJson(await handleResponse(await buildHttpResponse(
//       "https://maps.googleapis.com/maps/api/place/details/json?place_id=$placeId&key=${AppCred.googleMapAPIKey}",
//       method: HttpMethod.GET)));
// }

// Future<dynamic> verifyEmail(
//     // required String email,
//     ) async {
//   Response response = await buildHttpResponse('sent-verification-email',
//       method: HttpMethod.GET);

//   if ((response.statusCode >= 200 && response.statusCode <= 206)) {
//     if (response.body.isJson()) {
//       return jsonDecode(response.body);
//     }
//     return null;
//   } else {
//     return null;
//   }
// }

Future<APIResponse<Null>> sendOTPToEmail({required String email}) async {
  Response response = await buildHttpResponse('send-otp-email',
      method: HttpMethod.POST,
      request: {
        'email': email,
        "user_type": "driver",
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var data = jsonDecode(response.body);
      return APIResponse(
        status: data["status"],
        message: data["message"],
        data: null,
      );
    }
    return APIResponse(status: false, message: "Server error", data: null);
  } else {
    return APIResponse(status: false, message: "Server error", data: null);
  }
}

Future<APIResponse<Null>> verifyEmailOTP(
    {required String email, required String otp}) async {
  Response response = await buildHttpResponse('verify-otp-email',
      method: HttpMethod.POST,
      request: {
        'email': email,
        'otp': otp,
        'user_type': "driver",
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var data = jsonDecode(response.body);
      return APIResponse(
        status: data["status"],
        message: data["message"],
        data: null,
      );
    }
    return APIResponse(status: false, message: "Server error", data: null);
  } else {
    return APIResponse(status: false, message: "Server error", data: null);
  }
}

class APIResponse<T> {
  bool status;
  String message;
  T? data;

  APIResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory APIResponse.fromMap(Map<String, dynamic> json) {
    return APIResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }
}

Future<StatusMessageModel> cancelOpportunity(
    {required Map request, int? rideId}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('driver-cancel-opportunity/$rideId',
          method: HttpMethod.POST, request: request)));
}

Future<CounterDataResponse> getCounterData() async {
  try {
    final response =
        await buildHttpResponse('notification-counter', method: HttpMethod.GET);
    final jsonData = await handleResponse(response);
    return CounterDataResponse.fromMap(jsonData);
  } catch (e) {
    return CounterDataResponse(
        status: false, message: "Error fetching counter data", data: null);
  }
}

Future<StatusMessageModel> notifyWaitingTimeStarted(
    {required int rideId}) async {
  try {
    final response = await buildHttpResponse('send-mqtt-driver-start-wait-time',
        method: HttpMethod.POST, request: {'ride_id': rideId});
    final jsonData = await handleResponse(response);
    return StatusMessageModel.fromJson(jsonData);
  } catch (e) {
    return StatusMessageModel(
        status: false, message: 'Error in updating waiting time');
  }
}

Future<RideHelp?> getPendingHelps({
  int page = 1,
}) async {
  Response response = await buildHttpResponse(
    'help-pending-list/?page=$page',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return RideHelp.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<RideHelp?> getCompletedHelps({
  int page = 1,
}) async {
  Response response = await buildHttpResponse(
    'help-complete-list/?page=$page',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return RideHelp.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<RideHelpDetails?> getRideHelpDetailsData({
  required int helpId,
  required int page,
}) async {
  Response response = await buildHttpResponse(
    'complaintcomment-list/?complaint_id=$helpId&page=$page',
    method: HttpMethod.GET,
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return RideHelpDetails.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<HelpCommentResponse?> saveRideHelpMessage(
    HelpCommentRequest request) async {
  Response response = await buildHttpResponse('helpcomment-store',
      method: HttpMethod.POST, request: request.toMap());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return HelpCommentResponse.fromMap(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<AccountDeletionResponse> checkAccountDeletionStatus() async {
  try {
    Response response = await buildHttpResponse(
      'check-account-deletion-status',
      method: HttpMethod.POST,
    );

    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      return AccountDeletionResponse.fromMap(json);
    }
    return AccountDeletionResponse(
        status: false, message: "Server error", data: null);
  } catch (e) {
    return AccountDeletionResponse(
        status: false, message: "Server error", data: null);
  }
}
