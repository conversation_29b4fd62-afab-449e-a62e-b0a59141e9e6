// import 'package:flutter/material.dart';
 
// class CallScreen extends StatefulWidget {
//   const CallScreen({super.key});

//   @override
//   State<CallScreen> createState() => _CallScreenState();
// }

// class _CallScreenState extends State<CallScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Center(
//         child: ElevatedButton(
//           onPressed: () => _createAudioRoom(),
//           child: const Text('Create an Audio Room'),
//         ),
//       ),
//     );
//   }

// Future<void> _createAudioRoom() async {
//   // Set up our call object


//   final result = await call.getOrCreate(); // Call object is created

//   if (result.isSuccess) {
//     await call.join(); // Our local app user can join and receive events
//     await call.goLive(); // Allow others to see and join the call (exit backstage mode)

//     Navigator.of(context).push(
//       MaterialPageRoute(
//         builder: (context) => AudioRoomScreen(
//           audioRoomCall: call,
//         ),
//       ),
//     );
//   } else {
//     debugPrint('Not able to create a call.');
//   }
// }

// }




// class AudioRoomScreen extends StatefulWidget {
//   const AudioRoomScreen({
//     super.key,
//     required this.audioRoomCall,
//   });

//   final Call audioRoomCall;

//   @override
//   State<AudioRoomScreen> createState() => _AudioRoomScreenState();
// }

// class _AudioRoomScreenState extends State<AudioRoomScreen> {
//   late CallState _callState;
//   var microphoneEnabled = false;

//   @override
//   void initState() {
//     super.initState();
//     _callState = widget.audioRoomCall.state.value;
//     widget.audioRoomCall.onPermissionRequest = (permissionRequest) {
//       widget.audioRoomCall.grantPermissions(
//         userId: permissionRequest.user.id,
//         permissions: permissionRequest.permissions.toList(),
//       );
//     };
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Audio Room: ${_callState.callId}'),
//         leading: IconButton(
//           onPressed: () async {
//             await widget.audioRoomCall.leave();
//             Navigator.of(context).pop();
//           },
//           icon: const Icon(
//             Icons.close,
//           ),
//         ),
//       ),
//       floatingActionButton: FloatingActionButton(
//         child: microphoneEnabled
//             ? const Icon(Icons.mic)
//             : const Icon(Icons.mic_off),
//         onPressed: () {
//           if (microphoneEnabled) {
//             widget.audioRoomCall.setMicrophoneEnabled(enabled: false);
//             setState(() {
//               microphoneEnabled = false;
//             });
//           } else {
//             if (!widget.audioRoomCall.hasPermission(CallPermission.sendAudio)) {
//               widget.audioRoomCall.requestPermissions(
//                 [CallPermission.sendAudio],
//               );
//             }
//             widget.audioRoomCall.setMicrophoneEnabled(enabled: true);
//             setState(() {
//               microphoneEnabled = true;
//             });
//           }
//         },
//       ),
//       body: StreamBuilder<CallState>(
//         initialData: _callState,
//         stream: widget.audioRoomCall.state.valueStream,
//         builder: (context, snapshot) {
//           if (snapshot.hasError) {
//             return const Center(
//               child: Text('Cannot fetch call state.'),
//             );
//           }
//           if (snapshot.hasData && !snapshot.hasError) {
//             var callState = snapshot.data!;

//             return GridView.builder(
//               itemBuilder: (BuildContext context, int index) {
//                 return Align(
//                   widthFactor: 0.8,
//                   child: StreamCallParticipant(
//                     call: widget.audioRoomCall,
//                     backgroundColor: Colors.transparent,
//                     participant: callState.callParticipants[index],
//                     showParticipantLabel: true,
//                     showConnectionQualityIndicator: false,
//                     userAvatarTheme: const StreamUserAvatarThemeData(
//                       constraints: BoxConstraints.expand(
//                         height: 100,
//                         width: 100,
//                       ),
//                     ),
//                   ),
//                 );
//               },
//               gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//                 crossAxisCount: 3,
//               ),
//               itemCount: callState.callParticipants.length,
//             );
//           }

//           return const Center(
//             child: CircularProgressIndicator(),
//           );
//         },
//       ),
//     );
//   }
// }