import 'package:rooo_driver/features/Refferals/model/referrals_model_responce.dart';
import 'package:rooo_driver/features/Refferals/model/refferal_offer_model_response.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

import '../../../model/refferal_offers/referral_offer_status_model_response.dart';

class ReferralsRepository {
  Future<RefferalOfferModelStatusResponse> getReferralOffersStatusApi() async {
    return RefferalOfferModelStatusResponse.fromJson(await handleResponse(
        await buildHttpResponse("get-offer-status", method: HttpMethod.GET)));
  }

  Future<RefferalModelResponse> getReferralApi(
      {required String offer_type}) async {
    Map request = {"referrals_type": offer_type};
    return RefferalModelResponse.fromJson(await handleResponse(
        await buildHttpResponse("referrals-list",
            request: request, method: HttpMethod.POST)));
  }

  Future<RefferalOfferModelResponse> getReferralOffersApi(
      {required String offer_type}) async {
    Map request = {"offer_type": offer_type};
    return RefferalOfferModelResponse.fromJson(await handleResponse(
        await buildHttpResponse("offer-list",
            request: request, method: HttpMethod.POST)));
  }
}

Future<RefferalOfferModelStatusResponse> offerStatusReferrals(
    {required Map request}) async {
  return RefferalOfferModelStatusResponse.fromJson(await handleResponse(
      await buildHttpResponse('', method: HttpMethod.GET, request: request)));
}
