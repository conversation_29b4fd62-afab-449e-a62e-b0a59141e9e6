import 'package:rooo_driver/model/PaginationModel.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model.dart';

class RefferalModelResponse {
  PaginationModel? pagination;
  List<RefferalOfferModel>? data;
  String? message;

  RefferalModelResponse({this.data, this.pagination, this.message});

  factory RefferalModelResponse.fromJson(Map<String, dynamic> json) {
    return RefferalModelResponse(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => RefferalOfferModel.fromJson(e))
                .toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["message"] = this.message;
    return datas;
  }
}
