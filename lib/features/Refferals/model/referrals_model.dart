class RefferalModel {
  int? id;
  String? title;
  String? sub_title;
  String? offer_type;
  String? amount;
  String? start_date;
  String? end_date;
  int? status;
  int? target_ride;
  String? apply_on;
  String? details;
  String? message;
  String ?message_link;
    String ?first_heading;
    String ? complete_heading;

  String ?share_url;

  String ?second_heading;


  String ?image_url;
  int ?total_count;
  int ?completed_count;

  

  RefferalModel({  this.id,
  this.message, 
this.first_heading,
this.share_url,
this.second_heading,
this.complete_heading,


  this.image_url,
  this.message_link,
      this.amount,
      this.title,
      this.sub_title,
      this.offer_type,
      this.start_date,
      this.end_date,
      this.completed_count, this.total_count,
      this.status,
      this.target_ride,
      this.apply_on,
      this.details});

  factory RefferalModel.fromJson(Map<String, dynamic> json) {
    return RefferalModel(



    id: json["id"],
      amount: json["amount"],
      title: json["title"],
      sub_title: json["sub_title"],
      offer_type: json["offer_type"],
      end_date: json["end_date"],
      status: json["status"],
      target_ride: json["target_ride"],
      apply_on: json["apply_on"],
      details: json["details"],
      start_date: json["start_date"],
      message: json["message"],
      message_link: json["message_link"],
      total_count: json["total_count"],
      completed_count: json["completed_count"],
      image_url: json["image_url"],
       first_heading: json["first_heading"],
       second_heading: json["second_heading"],
       share_url: json["share_url"],
              complete_heading: json["complete_heading"],

       




    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["id"] = this.id;
    datas["amount"] = this.amount;
    datas["title"] = this.title;
    datas["sub_title"] = this.sub_title;
    datas["offer_type"] = this.offer_type;
    datas["start_date"] = this.start_date;
    datas["end_date"] = this.end_date;
    datas["status"] = this.status;
    datas["target_ride"] = this.target_ride;
    datas["apply_on"] = this.apply_on;
    datas["details"] = this.details;
    datas["message"] =this.message;
    datas["title"] =this.message_link;
    datas["image_url"]=this.image_url;
    datas["completed_count"]=this.completed_count;
datas["first_heading"]=this.first_heading;
datas["second_heading"]=this.second_heading;

datas["share_url"]=this.share_url;
datas["complete_heading"]=this.complete_heading;



    return datas;
  }
}

