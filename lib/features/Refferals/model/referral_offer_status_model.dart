

class RefferalOfferStatusModel {
  int? id;
  String? title;
  String? sub_title;
  String? offer_type;
  String? amount;
  String? start_date;
  String? end_date;
  int? status;
  String? target;
  String? details;
  int? region_id;
  String? amount_type;
  int? target_ride;
  String? type;
  String terms_condition;
  RefferalOfferStatusModel({
    this.id,
    this.title,
    this.sub_title,
    this.offer_type,
    this.amount,
    this.start_date,
    this.end_date,
    this.status,
    this.target,
    this.details,
    this.region_id,
    this.amount_type,
    this.target_ride,
    this.type,
    required this.terms_condition,
  });

  factory RefferalOfferStatusModel.fromJson(Map<String, dynamic> map) {
    return RefferalOfferStatusModel(
      id: map["id"],
      title: map["title"],
      sub_title: map["sub_title"],
      offer_type: map["offer_type"],
      amount: map["amount"],
      start_date: map["start_date"],
      end_date: map["end_date"],
      status: map["status"],
      target: map["target"],
      details: map["details"],
      region_id: map["region_id"],
      amount_type: map["amount_type"],
      target_ride: map["target_ride"],
      type: map["type"],
      terms_condition: map["terms_condition"],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();

    datas["target"] = this.target;
    datas["region_id"] = this.region_id;

    datas["amount_type"] = this.amount_type;

    datas["type"] = this.type;
    datas["terms_condition"] = this.terms_condition;

    datas["id"] = this.id;
    datas["amount"] = this.amount;
    datas["title"] = this.title;
    datas["sub_title"] = this.sub_title;
    datas["offer_type"] = this.offer_type;
    datas["start_date"] = this.start_date;
    datas["end_date"] = this.end_date;
    datas["status"] = this.status;
    datas["target_ride"] = this.target_ride;
    datas["details"] = this.details;
    return datas;
  }





}
