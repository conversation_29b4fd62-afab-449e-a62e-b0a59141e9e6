import 'package:rooo_driver/model/PaginationModel.dart';
import 'package:rooo_driver/model/refferal_offers/referral_offer_status_model.dart';

class RefferalOfferModelStatusResponse {
  PaginationModel? pagination;
  List<RefferalOfferStatusModel>? data;
  int? offer_completed;
  String? you_made;

  RefferalOfferModelStatusResponse(
      {this.data, this.pagination, this.offer_completed, this.you_made});

  factory RefferalOfferModelStatusResponse.fromJson(Map<String, dynamic> json) {
    return RefferalOfferModelStatusResponse(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => RefferalOfferStatusModel.fromJson(e))
                .toList()
            : null,
        offer_completed: json["offer_completed"],
        you_made: json["you_made"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["offer_completed"] = this.offer_completed;
    datas["you_made"] = this.you_made;
    return datas;
  }
}
