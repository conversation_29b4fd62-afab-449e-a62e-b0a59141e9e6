import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_contact_picker/model/contact.dart';
import 'package:rooo_driver/features/Refferals/cubit/refferals_cubit.dart';
import 'package:rooo_driver/features/Refferals/model/referrals_model_responce.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/components/referral_offer_referral_card.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/refferal_offers/refferal_offer_model.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart' as contacts;
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';
import 'package:url_launcher/url_launcher.dart';

class ReferralOfferReferralScreen extends StatefulWidget {
  const ReferralOfferReferralScreen({super.key});

  State<ReferralOfferReferralScreen> createState() =>
      _ReferralOfferReferralScreenState();
}

class _ReferralOfferReferralScreenState
    extends State<ReferralOfferReferralScreen> {
  List<RefferalOfferModel> _refferalDataList = [];

  // final FlutterContactPicker _contactPicker = new FlutterContactPicker();

  final FlutterNativeContactPicker _contactPicker =
      new FlutterNativeContactPicker();
  // ignore: unused_field
  contacts.Contact? _contact;

  String _apimessage = '';

  void getReferralOffersData() async {
    appStore.setLoading(true);

    await getReferralOffersApi(offer_type: "referrals").then((value) {
      appStore.setLoading(false);

      _refferalDataList = value.data ?? [];

      _apimessage = value.message.toString();

      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  _getRefferalsdata() {
    BlocProvider.of<ReferralsCubit>(context)
        .getrefferalsdata(offerType: "referrals");
  }

  _onDataLoaded({required RefferalModelResponse refferalModelResponse}) {
    _refferalDataList = refferalModelResponse.data ?? [];
    _apimessage = refferalModelResponse.message ?? "";
  }

  _init() {
    _getRefferalsdata();
  }

  @override
  void initState() {
    getReferralOffersData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: BlocConsumer<ReferralsCubit, ReferralsState>(
            listener: (context, state) {
      if (state is ReferralsLoadedState2) {
        _onDataLoaded(refferalModelResponse: state.refferalModelResponse);
      }
      if (state is referralsErrorState) {
        GlobalMethods.errorToast(context, state.message);
      }

      if (state is ReferralsLoadedState2) {
        _getRefferalsdata();
      }
    }, builder: (context, state) {
      return ScreenBody(
        isLoading: state is ReferralsLoadingState,
        isEmpty: _refferalDataList.isEmpty,
        emptyMessage: _apimessage,
        child: Observer(builder: (_) {
          return Stack(
            alignment: Alignment.center,
            children: [
              height20,
              ListView.separated(
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return ReferralOfferReferralCard(
                        refferalOfferData: _refferalDataList[index]);
                  },
                  separatorBuilder: (context, index) {
                    return const Divider(
                      thickness: 3,
                    );
                  },
                  itemCount: _refferalDataList.length),
            ],
          );
        }),
      );
    }));
  }
}

//       Observer(builder: (_) {
//         return Stack(
//           alignment: Alignment.center,
//           children: [
//             height20,
//             SingleChildScrollView(
//               child: ListView.separated(
//                   physics: NeverScrollableScrollPhysics(),
//                   shrinkWrap: true,
//                   itemBuilder: (context, index) {
//                     return ReferralOfferReferralCard(
//                         refferalOfferData: _refferalDataList[index]);
//                   },
//                   separatorBuilder: (context, index) {
//                     return const Divider(
//                       thickness: 3,
//                     );
//                   },
//                   itemCount: _refferalDataList.length),
//             ),
//             Observer(
//               builder: (context) {
//                 return Visibility(
//                   visible: appStore.isLoading,
//                   child: loaderWidget(),
//                 );
//               },
//             ),
//             Observer(
//               builder: (context) {
//                 return Visibility(
//                   visible: _refferalDataList.isEmpty && !appStore.isLoading,
//                   child: emptyWidget(message: api_message.toString()),
//                 );
//               },
//             )
//           ],
//         );
//       }),
//     );
//   }
// }
