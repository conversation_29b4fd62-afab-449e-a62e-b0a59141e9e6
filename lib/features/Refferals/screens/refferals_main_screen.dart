import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/screens/referrals/referral_offer_referral_screen.dart';
import 'package:rooo_driver/screens/referrals/referral_status_screen.dart';
import 'package:rooo_driver/screens/refferal_offer_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';

class ReferralScreen extends StatefulWidget {
    final int ?screenIndex;

  const ReferralScreen({super.key, this.screenIndex});

  @override
  ReferralScreenState createState() => ReferralScreenState();
}

class ReferralScreenState extends State<ReferralScreen>
    with AutomaticKeepAliveClientMixin {
  int currentPage = 1;
  int totalPage = 1;
  List<String> riderStatus = [REFERRALS, REFERRAL_STATUS, REFERRAL_OFFER];

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //

    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return DefaultTabController(
            initialIndex: widget.screenIndex??0,

      length: riderStatus.length,
      child: Scaffold(
        appBar: RoooAppbar(
          title: language.referralTxt,
        ),
        body: Column(
          children: [
            tabContainer(tabs: riderStatus),
            Expanded(
              child: TabBarView(
                children: [
                  ReferralOfferReferralScreen(),
                  ReferralStatusScreen(),
                  ReferralOfferScreen(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
