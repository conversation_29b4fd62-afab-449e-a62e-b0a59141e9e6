import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/Refferals/model/referral_offer_status_model_response.dart';
import 'package:rooo_driver/features/Refferals/model/referrals_model_responce.dart';
import 'package:rooo_driver/features/Refferals/model/refferal_offer_model_response.dart';
import 'package:rooo_driver/features/Refferals/repository/refferals_repo.dart';

abstract class ReferralsState {}

class ReferralsInitState extends ReferralsState {}

class ReferralsLoadingState extends ReferralsState {}

class ReferralsReferralsState extends ReferralsState {
  final String message;

  ReferralsReferralsState({required this.message});
}

class ReferralsLoadedState2 extends ReferralsState {
  final RefferalModelResponse refferalModelResponse;

  ReferralsLoadedState2({required this.refferalModelResponse});
}

class ReferralsLoadedState extends ReferralsState {
  final RefferalOfferModelStatusResponse refferalOfferModelStatusResponse;

  ReferralsLoadedState({required this.refferalOfferModelStatusResponse});
}

class ReferralsLoadedState1 extends ReferralsState {
  final RefferalOfferModelResponse refferalOfferModelResponse;

  ReferralsLoadedState1({required this.refferalOfferModelResponse});
}

class referralsErrorState extends ReferralsState {
  final String message;

  referralsErrorState({required this.message});
}

class ReferralsCubit extends Cubit<ReferralsState> {
  ReferralsCubit() : super(ReferralsInitState());

  ReferralsRepository _referralsRepository = ReferralsRepository();

  getrefferalsdata({required String offerType}) async {
    emit(ReferralsLoadingState());
    await _referralsRepository
        .getReferralApi(offer_type: offerType)
        .then((value) {
      emit(ReferralsLoadedState2(refferalModelResponse: value));
    }).onError((error, stackTrace) {
      emit(referralsErrorState(message: "Server error"));
    });
  }
}
