class VehicleModelModel {
  int id;
  String name;
  int makeId;
    int seat;

  DateTime? createdAt;
  DateTime? updatedAt;

  VehicleModelModel({
    required this.id,
    required this.seat,
    required this.name,
    required this.makeId,
    this.createdAt,
    this.updatedAt,
  });

  // Factory constructor to create a Car from JSON
  factory VehicleModelModel.fromJson(Map<String, dynamic> json) {
    return VehicleModelModel(
      id: json['id'],
            seat: json['seat'],

      name: json['name'],
      makeId: json['make_id'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  // Method to convert a Car to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
            'seat': seat,

      'name': name,
      'make_id': makeId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}