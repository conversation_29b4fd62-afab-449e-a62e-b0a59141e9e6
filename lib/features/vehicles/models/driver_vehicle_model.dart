import 'package:rooo_driver/global/export/app_export.dart';

class DriverVehicleModel {
  int id;

  int riderId;
  // int serviceId;
  int make_id;
  int seats;

  int model_id;
  String model_name;

  List<String> serviceName;
  String? isDashCamAvailable;

  String? vehicleRegistrationStatus;
  String? vehicleInspectionStatus;
  String? vehicleRegistrationRejectionText;
  String? vehicleInspectionRejectionText;
  String? vehicleRegistration;
  String? vehicleInspectionReport;
  DateTime registrationDocExpirayDate;
  DateTime inspectionDocExpirayDate;

  String plateNumber;
  int productionYear;

  String transmission;
  String imageURL;
  bool isSelected;
  DateTime createdAt;
  DateTime updatedAt;

  DriverVehicleModel({
    required this.isSelected,
    required this.seats,
    required this.id,
    required this.model_id,
    required this.model_name,
    this.isDashCamAvailable,
    required this.productionYear,
    required this.vehicleInspectionRejectionText,
    required this.vehicleRegistrationRejectionText,
    required this.vehicleRegistrationStatus,
    required this.vehicleInspectionStatus,
    required this.riderId,
    // required this.serviceId,
    required this.make_id,
    required this.serviceName,
    required this.plateNumber,
    required this.transmission,
    required this.imageURL,
    required this.createdAt,
    required this.updatedAt,
    required this.vehicleInspectionReport,
    required this.vehicleRegistration,
    required this.registrationDocExpirayDate,
    required this.inspectionDocExpirayDate,
  });

  factory DriverVehicleModel.fromJson(Map<String, dynamic> json) {
    return DriverVehicleModel(
      id: json['id'],
      seats: json['seats'],
      model_name: json['model_name'],
      productionYear: json['production_year'],
      isDashCamAvailable: json['is_dash_cam_available'] != null
          ? json['is_dash_cam_available']
          : null,
      riderId: json['rider_id'],
      model_id: json['model_id'],
      // serviceId: json['service_id'],
      vehicleInspectionStatus: json['inspection_status'],
      vehicleRegistrationStatus: json['vehicle_registration_status'],
      vehicleRegistration: json['vehicle_registration'],
      vehicleInspectionReport: json['vehicle_inspection_report'],
      vehicleInspectionRejectionText: json['inspection_rejectionText'],
      vehicleRegistrationRejectionText:
          json['vehicle_registration_rejectionText'],
      make_id: json['make_id'],
      isSelected: json['is_selected'],
      serviceName: (json['service_name'] as List<dynamic>).map((e) => e.toString()).toList(),
      plateNumber: json['plateNumber'],
      transmission: json['transmission'],
      imageURL: json['imageURL'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      registrationDocExpirayDate:
          GlobalMethods.apiStringToDate(json['registration_expiry']),
      inspectionDocExpirayDate:
          GlobalMethods.apiStringToDate(json['inspection_expiry']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'production_year': productionYear,
      'rider_id': riderId,
      'model_name': model_name,
      'is_dash_cam_available': isDashCamAvailable.toString(),
      // 'service_id': serviceId,
      'is_active': isSelected,
      'inspection_status': vehicleInspectionStatus,
      'vehicle_registration_status': vehicleRegistrationStatus,
      'inspection_rejectionText': vehicleInspectionRejectionText,
      'vehicle_registration_rejectionText': vehicleRegistrationRejectionText,
      'make_id': make_id,
      'seats': seats,
      'model_id': model_id,
      'service_name': serviceName.toString(),
      'vehicle_registration': vehicleRegistration,
      'vehicle_inspection_report': vehicleInspectionReport,
      'plateNumber': plateNumber,
      'transmission': transmission,
      'imageURL': imageURL,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
