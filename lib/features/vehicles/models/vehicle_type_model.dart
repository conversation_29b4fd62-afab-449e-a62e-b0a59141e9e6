class VehicleTypeModel {
  int id;
  String service_image;
  String name;

  VehicleTypeModel({
    required this.id,
    required this.service_image,
    required this.name,
  });

  factory VehicleTypeModel.fromJson(Map<String, dynamic> json) {
    return VehicleTypeModel(
      id: json['id'],
      service_image: json['service_image'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'service_image': service_image,
      'name': name,
    };
  }
}
