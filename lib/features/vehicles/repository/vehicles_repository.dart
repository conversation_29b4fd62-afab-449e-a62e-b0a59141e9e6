import 'dart:async';
import 'dart:typed_data';

import 'package:http/http.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_brand_list_response_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_model_list_response_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_type_response_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicles_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class VehiclesRepository {

    Future<VehicleModelListResponseModel>getVehicleModelListAPi({required int vehicle_brand_id}) async {
  return VehicleModelListResponseModel.fromJson(
      await handleResponse(await buildHttpResponse(
    'models?make_id=${vehicle_brand_id}',
    method: HttpMethod.GET,
  )));
}



  Future<VehicleBrandListResponseModel> getVehicleBrandListAPi() async {
  return VehicleBrandListResponseModel.fromJson(
      await handleResponse(await buildHttpResponse(
    'makes',
    method: HttpMethod.GET,
  )));
}










  Future<DriverVehicleResponseModel> getVehicleListApi(
      {required int currentPage}) async {
    return DriverVehicleResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('vehicles', method: HttpMethod.GET)));
  }
    Future<VehicleTypeResponseModel> getVehicleTypeListApi(
      {required int regionId}) async {
    return VehicleTypeResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('service-list/?region_id=${regionId}', method: HttpMethod.GET)));
  }

      Future<StatusMessageModel> deleteVehicleApi(
      {required int vehicleId}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('vehicles/delete/' + vehicleId.toString(), method: HttpMethod.POST)));
  }
    Future<StatusMessageModel> activeVehicleApi(
      {required int vehicleId}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('vehicles/active/' + vehicleId.toString(), method: HttpMethod.POST)));
  }
  //   Future<WebviewDataModel> getAdvertisementDetailsApi({required  int id}) async {
  //   return WebviewDataModel.fromJson(await handleResponse(
  //       await buildHttpResponse('advertisement-details/' + id.toString(), method: HttpMethod.GET)));
  // }

  Future<StatusMessageModel> saveVehicleApi(
      {required MultipartRequest multiPartRequest}) async {
    return await StatusMessageModel.fromJson(await sendMultiPartApi(
        multiPartRequest: multiPartRequest, ));
  }
  

  Future sendMultiPartRequest(
    MultipartRequest multiPartRequest,
  ) async {
    String? result;
    multiPartRequest.headers.addAll(buildHeaderTokens());

    StreamedResponse response = await multiPartRequest.send();
    if (response.statusCode == 200) {
      Uint8List responseData = await response.stream.toBytes();
      result = String.fromCharCodes(responseData);
    }
    return result;
  }
}
