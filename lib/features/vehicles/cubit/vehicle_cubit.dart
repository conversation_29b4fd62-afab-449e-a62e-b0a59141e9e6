import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_brand_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_model_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_type_response_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicles_response_model.dart';
import 'package:rooo_driver/features/vehicles/repository/vehicles_repository.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class VehicleState {}

class VehicleInitState extends VehicleState {}

class VehicleLoadingState extends VehicleState {}

class VehicleDeletedState extends VehicleState {}

class VehicleActivatedState extends VehicleState {}

class VehicleDetailLoadedState extends VehicleState {
  final String message;

  VehicleDetailLoadedState({required this.message});
}

class VehicleSavedState extends VehicleState {}

class VehicleDeleteState extends VehicleState {}

class VehicleDetailLoaded extends VehicleState {
  final String data;

  VehicleDetailLoaded({required this.data});
}

class VehicleLoadedState extends VehicleState {
  final DriverVehicleResponseModel vehicleResponseModel;

  VehicleLoadedState({
    required this.vehicleResponseModel,
  });
}

class VehicleBrandLoadedState extends VehicleState {
  final List<VehicleBrandModel> vehicleBrandList;

  VehicleBrandLoadedState({
    required this.vehicleBrandList,
  });
}

class VehicleModelLoadedState extends VehicleState {
  final List<VehicleModelModel> vehicleModelList;

  VehicleModelLoadedState({
    required this.vehicleModelList,
  });
}

class VehicleTypeLoadedState extends VehicleState {
  final VehicleTypeResponseModel vehicleResponseModel;

  VehicleTypeLoadedState({
    required this.vehicleResponseModel,
  });
}

class VehicleErrorState extends VehicleState {
  final String? message;
  final String? unmessage;
  VehicleErrorState({this.message, this.unmessage});
}

class VehicleCubit extends Cubit<VehicleState> {
  VehicleCubit() : super(VehicleInitState());

  VehiclesRepository _vehicleRepository = VehiclesRepository();

  void deleteVehicle({required int vehicleId}) async {
    emit(VehicleLoadingState());
    await _vehicleRepository
        .deleteVehicleApi(vehicleId: vehicleId)
        .then((value) {
      if (value.status) {
        emit(VehicleDeletedState());
      } else {
        emit(VehicleErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(VehicleErrorState(message: "Server error"));
    });
  }

  void activateVehicle({required int vehicleId}) async {
    emit(VehicleLoadingState());
    await _vehicleRepository
        .activeVehicleApi(vehicleId: vehicleId)
        .then((value) {
      if (value.status) {
        emit(VehicleActivatedState());
      } else {
        emit(VehicleErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(VehicleErrorState(message: "Server error"));
    });
  }

  void getVehicle({required int currentPage}) async {
    emit(VehicleLoadingState());
    await _vehicleRepository
        .getVehicleListApi(currentPage: currentPage)
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(VehicleLoadedState(vehicleResponseModel: value));
        } else {
          emit(VehicleErrorState(message: serverErrorMessage));
        }
      } else {
        emit(VehicleErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(VehicleErrorState(message: "Server error"));
    });
  }

  void getVehicleType({required int regionId}) async {
    emit(VehicleLoadingState());
    await _vehicleRepository
        .getVehicleTypeListApi(regionId: regionId)
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(VehicleTypeLoadedState(vehicleResponseModel: value));
        } else {
          emit(VehicleErrorState(message: serverErrorMessage));
        }
      } else {
        emit(VehicleErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(VehicleErrorState(message: "Server error"));
    });
  }

  void saveVehicle({required MultipartRequest multiPartRequest}) async {
    emit(VehicleLoadingState());
    await _vehicleRepository
        .saveVehicleApi(
      multiPartRequest: multiPartRequest,
    )
        .then((value) {
      if (value.status) {
        emit(VehicleSavedState());
      } else {
        emit(VehicleErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(VehicleErrorState(message: "Server error"));
    });
  }

  getVehicleBrandList() async {
    emit(VehicleLoadingState());
    await _vehicleRepository.getVehicleBrandListAPi().then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(VehicleBrandLoadedState(vehicleBrandList: value.data!));
        } else {
          emit(VehicleErrorState(message: serverErrorMessage));
        }
      } else {
        emit(VehicleErrorState(message: value.message));
      }
      // if (value.status) {
      //     RegionListLoadedState(regionList: value.data!);
      // } else {
      // }
    }).onError((error, stackTrace) {
      emit(VehicleErrorState(message: "Server error"));
    });
  }

  getVehicleModelList({required int vehicle_brand_id}) async {
    emit(VehicleLoadingState());
    await _vehicleRepository
        .getVehicleModelListAPi(vehicle_brand_id: vehicle_brand_id)
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(VehicleModelLoadedState(vehicleModelList: value.data!));
        } else {
          emit(VehicleErrorState(message: serverErrorMessage));
        }
      } else {
        emit(VehicleErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(VehicleErrorState(message: "Server error"));
    });
  }

  //    getVehicleDetailsVehicle({required int id}) async {    emit(VehicleLoadingState());
  //   await VehicleRepository.getVehicleDetailsApi(id: id).then((value) {
  //     emit(VehicleDetailLoaded(data: value.description.toString()));
  //   }).onError((error, stackTrace) {
  //     emit(VehicleErrorState(error_message: "Server error"));

  //   });
  // }
}
