import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/vehicles/cubit/vehicle_cubit.dart';
import 'package:rooo_driver/features/vehicles/models/driver_vehicle_model.dart';
import 'package:rooo_driver/features/vehicles/models/vehicles_response_model.dart';
import 'package:rooo_driver/features/vehicles/screens/add_vehicle_screen.dart';
import 'package:rooo_driver/features/vehicles/widgets/driver_vehicle_card.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/utils/Constants.dart';

class VehiclesScreen extends StatefulWidget {
  const VehiclesScreen({super.key});

  @override
  State<VehiclesScreen> createState() => _VehiclesScreenState();
}

class _VehiclesScreenState extends State<VehiclesScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  List<DriverVehicleModel> _vehicleList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getVehicleList(currentPage: _currentPage);
        }
      }
    });
  }

  _deleteVehicle({required vehicleId}) {
    BlocProvider.of<VehicleCubit>(context).deleteVehicle(vehicleId: vehicleId);
  }

  _activateVehicle({required vehicleId}) {
    BlocProvider.of<VehicleCubit>(context)
        .activateVehicle(vehicleId: vehicleId);
  }

  _getVehicleList({required int currentPage}) {
    BlocProvider.of<VehicleCubit>(context).getVehicle(currentPage: currentPage);
  }

  _onDataLoaded({required DriverVehicleResponseModel vehicleResponseModel}) {
    _currentPage = vehicleResponseModel.pagination?.currentPage ?? 1;
    _totalPage = vehicleResponseModel.pagination?.totalPages ?? 1;

    _vehicleList = vehicleResponseModel.data as List<DriverVehicleModel> ?? [];
    _emptyMesssage = vehicleResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getVehicleList(currentPage: _currentPage);
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    GlobalMethods.removeSavedScreen();
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Vehicles"),
      floatingActionButton: FloatingActionButton(
          child: Icon(Icons.add),
          onPressed: () async {
            bool? reloadRequired = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AddVehiclesScreen(),
              ),
            );

            if (reloadRequired == true) {
              _getVehicleList(currentPage: 1);
            }
          }),
      body: BlocConsumer<VehicleCubit, VehicleState>(
        listener: (context, state) {
          if (state is VehicleLoadedState) {
            _onDataLoaded(vehicleResponseModel: state.vehicleResponseModel);
          } else if (state is VehicleErrorState) {
            if (state.message != null) {
              GlobalMethods.errorToast(context, state.message!);
            } else if (state.unmessage != null) {
              GlobalMethods.errorToast(context, state.unmessage!);
            }
          } else if (state is VehicleDeletedState) {
            GlobalMethods.succesToast(context, "Vehicle deleted succesfully");
            _init();
          } else if (state is VehicleActivatedState) {
            GlobalMethods.succesToast(context, "Vehicle activated succesfully");
            _init();
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is VehicleLoadingState,
              isEmpty: _vehicleList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: AnimationLimiter(
                child: ListView.separated(
                  padding: EdgeInsets.fromLTRB(screenPadding.left, screenPadding.left, screenPadding.left, screenPadding.left *8),
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => height10,
                  itemCount: _vehicleList.length,
                  itemBuilder: (BuildContext context, int index) {
                    DriverVehicleModel data = _vehicleList[index];

                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 1000),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                            child: DriverVehicleCard(
                                onActivate: () {
                                  GlobalMethods.showConfirmationDialog(
                                      context: context,
                                      onPositiveAction: () {
                                        _activateVehicle(vehicleId: data.id);
                                      },
                                      title:
                                          "Are you sure you want to activate this vehicle");
                                },
                                onEdit: () async {
                                  bool? reloadRequired = await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => AddVehiclesScreen(
                                        vehicleModel: data,
                                      ),
                                    ),
                                  );

                                  if (reloadRequired == true) {
                                    _getVehicleList(currentPage: 1);
                                  }
                                },
                                ondelete: () {
                                  GlobalMethods.showConfirmationDialog(
                                      context: context,
                                      onPositiveAction: () {
                                        _deleteVehicle(vehicleId: data.id);
                                      },
                                      title:
                                          "Are you sure you want to delete this vehicle");
                                },
                                data: data)),
                      ),
                    );
                  },
                ),
              ));
        },
      ),
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

