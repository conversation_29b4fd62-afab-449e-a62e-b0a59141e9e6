
class MapSettingModel {
  int id;
  String name;
  String status;
  String? key;
  DateTime? createdAt;
  DateTime? updatedAt;

  MapSettingModel({
    required this.id,
    required this.name,
    required this.status,
    this.key,
    this.createdAt,
    this.updatedAt,
  });

  // Convert JSON map to MapSettingModel object
  factory MapSettingModel.fromMap(Map<String, dynamic> json) => MapSettingModel(
        id: json["id"],
        name: json["name"],
        status: json["status"],
        key: json["key"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  // Convert MapSettingModel object to JSON map
  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "status": status,
        "key": key,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
