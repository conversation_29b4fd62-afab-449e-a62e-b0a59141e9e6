import 'package:rooo_driver/components/setting_black_container.dart';
import 'package:rooo_driver/features/documents/screens/vehicle_and_driver_document.dart';
import 'package:rooo_driver/features/faq/screens/faq_screen2.dart';
import 'package:rooo_driver/features/login/screens/roo_login_screen.dart';
import 'package:rooo_driver/features/privacy_center/screens/privacy_center_screen.dart';
import 'package:rooo_driver/features/rushed_area/screens/rushed_area_mapbox_screen.dart';
import 'package:rooo_driver/features/settings/map_setting/screens/app_setting_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/SettingModel.dart';
import 'package:rooo_driver/screens/AboutScreen.dart';
import 'package:rooo_driver/screens/BankAndWalletScreen.dart';
import 'package:rooo_driver/screens/TermsAndConditionsScreen.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

class AccountSettingScreen extends StatefulWidget {
  @override
  AccountSettingScreenState createState() => AccountSettingScreenState();
}

class AccountSettingScreenState extends State<AccountSettingScreen> {
  SettingModel settingModel = SettingModel();
  String? privacyPolicy;
  String? termsCondition;
  String? mHelpAndSupport;

  @override
  void initState() {
    super.initState();
    init();
  }

  // final WidgetStateProperty<Icon?> thumbIcon =
  //     WidgetStateProperty.resolveWith<Icon?>(
  //   (Set<WidgetState> states) {
  //     // Thumb icon when the switch is selected.
  //     if (states.contains(WidgetState.selected)) {
  //       return const Icon(Icons.check);
  //     }
  //     return const Icon(Icons.close);
  //   },
  // );

  void init() async {
    await driverDetail();
    await getAppSetting().then((value) {
      // if (value.settingModel!.helpSupportUrl != null)
      //   mHelpAndSupport = value.settingModel!.helpSupportUrl!;
      // settingModel = value.settingModel!;
      if (value.privacyPolicyModel!.value != null)
        privacyPolicy = value.privacyPolicyModel!.value!;
      if (value.termsCondition!.value != null)
        termsCondition = value.termsCondition!.value!;
      setState(() {});
    }).catchError((error) {
      log("Server error");
    });
  }

  Future<void> driverDetail() async {
    appStore.setLoading(true);
    await getUserDetail(userId: sharedPref.getInt(USER_ID)).then((value) {
      appStore.setLoading(false);
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.account),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // height20,
                //         blackContainer(
                //             title: language.changePassword,
                //             onTap: () {
                //               launchScreen(context, ChangePasswordScreen(),
                //                   pageRouteAnimation: PageRouteAnimation.Slide);
                //             }),
                //  height15,
                // SettingsBlackContainer(
                //     address: "language.png",
                //     title: language.language,
                //     onTap: () {
                //       GlobalMethods.pushScreen(
                //           context: context,
                //           screen: LanguageScreen(),
                //           screenIdentifier: ScreenIdentifier.LanguageScreen);
                //       // launchScreen(context, LanguageScreen(),
                //       //     pageRouteAnimation: PageRouteAnimation.Slide);
                //     }),
                SizedBox(),

                SettingsBlackContainer(
                    icon: Icon(Icons.file_copy),
                    title: language.document,
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: VehicleAndDriverDocumentScreen(),
                          screenIdentifier:
                              ScreenIdentifier.VehicleAndDriverDocumentScreen);
                      // launchScreen(context, PrivacyPolicyScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),
                SettingsBlackContainer(
                    icon: Icon(Icons.money),
                    title: language.bankInfo,
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: BankAndWalletScreen(
                            isWalletScreen: false,
                          ),
                          screenIdentifier:
                              ScreenIdentifier.VehicleAndDriverDocumentScreen);
                      // launchScreen(context, PrivacyPolicyScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),

                SettingsBlackContainer(
                    icon: Icon(Icons.file_present),
                    title: language.termsConditions,
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: TermsAndConditionsScreen(),
                          screenIdentifier:
                              ScreenIdentifier.TermsAndConditionsScreen);

                      // launchScreen(context, TermsAndConditionsScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),
                SettingsBlackContainer(
                    icon: Icon(Icons.info),
                    title: language.aboutUs,
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: AboutScreen(settingModel: settingModel),
                          screenIdentifier: ScreenIdentifier.AboutScreen);
                      // launchScreen(
                      //     context, AboutScreen(settingModel: settingModel),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),

                // SettingsBlackContainer(
                //     address: "nightmode.png",
                //     title: language.nightModeTxt,
                //     onTap: () {
                //       GlobalMethods.pushScreen(
                //           context: context,
                //           screen: DarkModeSettingsScreen(),
                //           screenIdentifier:
                //               ScreenIdentifier.DarkModeSettingsScreen);
                //       // launchScreen(context, DarkModeSettingsScreen(),
                //       //     pageRouteAnimation: PageRouteAnimation.Slide);
                //     }),

                SettingsBlackContainer(
                    icon: Icon(Icons.settings_applications_outlined),
                    title: "App setting",
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: AppSettingScreen(),
                          screenIdentifier: ScreenIdentifier.MapSettingScreen);
                      // launchScreen(context, DeleteAccountScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),

                SettingsBlackContainer(
                    icon: Icon(Icons.question_answer),
                    title: "FAQs",
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: FaqScreen(),
                          screenIdentifier: ScreenIdentifier.FaqScreen);
                      // launchScreen(context, DeleteAccountScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),
                SettingsBlackContainer(
                    icon: Icon(Icons.align_horizontal_left),
                    title: "Peak area",
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          // screen: RushedAreaScreeen(),
                          screen: RushedAreaMapboxScreen(),
                          screenIdentifier: ScreenIdentifier.FaqScreen);
                      // launchScreen(context, DeleteAccountScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),
                SettingsBlackContainer(
                    icon: Icon(Icons.privacy_tip),
                    title: "Privacy center",
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: PrivacyCenterScreen(),
                          screenIdentifier:
                              ScreenIdentifier.PrivacyCenterScreen);
                      // launchScreen(context, DeleteAccountScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),
                Padding(
                  padding: screenPadding,
                  child: InkWell(
                      onTap: () {
                        GlobalMethods.showConfirmationDialog(
                          context: context,
                          onPositiveAction: () {
                            _logOut();
                          },
                          title: language.areYouSureYouWantToLogoutThisApp,
                        );
                      },
                      child: Text(
                        "Log out",
                        style: AppTextStyles.header(color: Colors.red),
                      )),
                ),

                // settingItemWidget(Icons.lock_outline, language.changePassword,
                //     () {
                //   launchScreen(context, ChangePasswordScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(Icons.language, language.language, () {
                //   launchScreen(context, LanguageScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(
                //     Icons.assignment_outlined, language.privacyPolicy, () {
                //   launchScreen(context, PrivacyPolicyScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(Icons.help_outline, language.helpSupport,
                //     () {
                //   launchScreen(context, CareScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(
                //     Icons.assignment_outlined, language.termsConditions, () {
                //   launchScreen(context, TermsAndConditionsScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(
                //   Icons.info_outline,
                //   language.aboutUs,
                //   () {
                //     launchScreen(
                //         context, AboutScreen(settingModel: settingModel),
                //         pageRouteAnimation: PageRouteAnimation.Slide);
                //   },
                // ),
                // settingItemWidget(
                //     Icons.delete_outline, language.deleteAccount, () {
                //   launchScreen(context, DeleteAccountScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // ListTile(
                //   contentPadding: EdgeInsets.only(left: 16, right: 16),
                //   leading: Icon(Icons.offline_bolt_outlined,
                //       size: 25,
                //       color: isNightTime() ? Colors.white : primaryColor),
                //   title: Text(
                //       isAvailable
                //           ? language.available
                //           : language.notAvailable,
                //       style: primaryTextStyle()),
                //   trailing: Switch(
                //       thumbIcon: thumbIcon,
                //       thumbColor: isNightTime()
                //           ? MaterialStateColor.resolveWith((states) {
                //               if (states.contains(MaterialState.selected)) {
                //                 return Colors.white;
                //               }
                //               return Colors.white;
                //             })
                //           : null,
                //       trackColor: isNightTime()
                //           ? MaterialStateColor.resolveWith((states) {
                //               if (states.contains(MaterialState.selected)) {
                //                 return Colors.amber;
                //               }
                //               return Colors.grey;
                //             })
                //           : null,
                //       value: isAvailable,
                //       onChanged: (val) {
                //         setState(() {
                //           isAvailable = val;
                //         });
                //       }),
                //   onTap: () async {
                //     if (appStore.currentRiderRequest == null) {
                //       await showConfirmDialogCustom(
                //         context,
                //         title: !isAvailable
                //             ? language.youWillReceiveNewRidersAndNotifications
                //             : language
                //                 .youWillNotReceiveNewRidersAndNotifications,
                //         dialogType: DialogType.ACCEPT,
                //         positiveText: language.yes,
                //         negativeText: language.no,
                //         primaryColor: primaryColor,
                //         onAccept: (c) async {
                //           updateAvailable();
                //         },
                //       );
                //     } else {
                //      GlobalMethods.infoToast(context,  language
                //           .youCanNotThisActionsPerformBecauseYourCurrentRideIsNotCompleted);
                //     }
                //   },
                // ),
              ],
            ),
          ),
          Observer(builder: (context) {
            return Visibility(
              visible: appStore.isLoading,
              child: loaderWidget(),
            );
          })
        ],
      ),
    );
  }

  Widget settingItemWidget(IconData icon, String title, Function() onTap,
      {bool isLast = false, IconData? suffixIcon}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListTile(
          contentPadding: EdgeInsets.only(left: 16, right: 16),
          leading: Icon(
            icon,
            size: 25,
          ),
          title: Text(title, style: primaryTextStyle()),
          trailing: suffixIcon != null
              ? Icon(suffixIcon, color: Colors.green)
              : Icon(Icons.navigate_next,
                  color: IS_DARK_MODE_ON ? Colors.white : Colors.grey),
          onTap: onTap,
        ),
        if (!isLast) Divider(height: 0)
      ],
    );
  }

  Widget blackContainerWithToggle({required title, required Function() onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 25, horizontal: 10),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 15, color: Colors.white),
            ),

            Switch(
              value: IS_DARK_MODE_ON,
              onChanged: (value) {
                setState(() {
                  appStore.setLoading(true);
                });
                if (value == true) {
                  sharedPref.setBool(IS_DARK_MODE, value);
                } else {
                  sharedPref.remove(IS_DARK_MODE);
                }
                setState(() {
                  IS_DARK_MODE_ON = value;
                });

                Future.delayed(Duration(seconds: 3)).then((value) {
                  setState(() {
                    appStore.setLoading(false);
                  });

                  closeScreen(context);
                  closeScreen(context);
                });
              },
              activeColor: Colors.white,
              activeTrackColor: Colors.white,
              inactiveThumbColor: Colors.white,
              inactiveTrackColor: Colors.white,
            ),
            //     ),
            // Switch(

            //     thumbIcon: thumbIcon,
            //     thumbColor: isNightTime()
            //         ? MaterialStateColor.resolveWith((states) {
            //             if (states.contains(MaterialState.selected)) {
            //               return Colors.white;
            //             }
            //             return Colors.white;
            //           })
            //         : null,
            //     trackColor: isNightTime()
            //         ? MaterialStateColor.resolveWith((states) {
            //             if (states.contains(MaterialState.selected)) {
            //               return Colors.amber;
            //             }
            //             return Colors.white;
            //           })
            //         : null,
            //     value: isAvailable,
            //     onChanged: (val) {
            //       setState(() {
            //         isAvailable = val;
            //       });
            //     }),
          ],
        ),
      ),
    );
  }

  Future<void> _logOut() async {
    await Future.delayed(Duration(milliseconds: 200));
    GlobalMethods.showActivity(
      context: context,
      title: "Logging out...",
    );

    await driverStatus(
        status: 0, isLogoutOperation: true, driverStatusChangeCase: true);
    await Future.delayed(Duration(milliseconds: 500));
    await logout();
    logOutSuccess();

    GlobalState.mqttClient?.disconnect();
    GlobalMethods.pushAndRemoveAll(
        context: getContext,
        screen: LoginScreen(),
        screenIdentifier: ScreenIdentifier.LoginScreen);
  }

  Future<bool> driverStatus(
      {int? status,
      bool isLogoutOperation = false,
      required bool driverStatusChangeCase}) async {
    // appStore.setLoading(true);
    if (isLogoutOperation == true) {
      setState(() {});
    }
    Map req = {
      "status": "active",
    };

    if (driverStatusChangeCase) {
      req = {
        "status": "active",
        "is_online": status,
      };
    }

    return await updateDriverPlayerIdApi(req).then((value) {
      sharedPref.setInt(IS_ONLINE, value.data!.isOnline!);
      return true;
    }).catchError((error) {
      log("Server error");
      return false;
    });
  }
}
