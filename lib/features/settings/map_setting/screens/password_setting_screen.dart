
// import 'package:map_launcher/map_launcher.dart';
import 'package:map_launcher/map_launcher.dart' as map_launcher;

import 'package:rooo_driver/features/settings/map_setting/cubit/map_setting_cubit.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';


class PasswordSettingScreen extends StatefulWidget {
  final MapSettingModel? vehicleModel;
  const PasswordSettingScreen({super.key, this.vehicleModel});

  @override
  State<PasswordSettingScreen> createState() => _PasswordSettingScreenState();
}

class _PasswordSettingScreenState extends State<PasswordSettingScreen> {
  int _currentPage = 1;
  int _totalPage = 1;

  ScrollController _scrollController = ScrollController();

  ValueNotifier<List<map_launcher.AvailableMap>> _availableMaps =
      ValueNotifier([]);

  String _PASSWORD = "password";
  String _OTP = "otp";

  ValueNotifier<String> _selectedLoginSettting = ValueNotifier("Password");
  // String ?_selectedMap ;

  List<MyModel> _maps = [
    MyModel(title: "Apple Map", type: "Apple maps"),
    MyModel(title: "Google Map", type: "Google Maps"),
    MyModel(title: "Waze Map", type: "Waze")
  ];

  // ValueNotifier<MapSettingTypeModel> _isSelectedService =
  //     ValueNotifier(MapSettingTypeModel(id: -1, service_image: "", name: ""));

  // List<MapSettingTypeModel> _vehicleTypeList = [];

  _saveMapSeting({required String? selectedMapName}) {
    Map<String, dynamic> request = {"selected_map": selectedMapName};
    BlocProvider.of<MapSettingCubit>(context).saveMapSetting(request: request);
  }

    _saveLoginSeting({required String? selectedLoginSetting}) {
    Map<String, dynamic> request = {"login_screen": selectedLoginSetting};
    BlocProvider.of<MapSettingCubit>(context).saveMapSetting(request: request);
  }

  _getMapSetting() {
    BlocProvider.of<MapSettingCubit>(context)
        .getMapSetting(userId: sharedPref.getInt(USER_ID)!);
  }

  _onDataLoaded(
      {required MapSettingResponseModel data}) async {
    _currentPage = data.pagination?.currentPage ?? 1;
    _totalPage = data.pagination?.totalPages ?? 1;

    if(data.data!.login_screen=="password"){
      _selectedLoginSettting.value=_PASSWORD;

    }else if(data.data!.login_screen=="otp"){
            _selectedLoginSettting.value=_OTP;

    }

    if (data.data != null) {
      GlobalState.selectedMap = data.data?.selected_map;
      List<MyModel> maps =
          _maps.where((element) => element == GlobalState.selectedMap).toList();

      if (maps.isNotEmpty) {
        GlobalState.selectedMap = maps[0].type;
      }
    }


  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() async {
    // if (widget.vehicleModel != null) {
    //   _nameController.text = widget.vehicleModel!.name!;
    //   _transmissionController.text = widget.vehicleModel!.transmission;
    //   _plateNumberController.text = widget.vehicleModel!.transmission;
    //   _isSelectedService.value.id = widget.vehicleModel!.serviceId;
    // }
    _getMapSetting();
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MapSettingCubit, MapSettingState>(
      listener: (context, state) {
        if (state is MapSettingLoadedState) {
          _onDataLoaded(
              data: state.mapSettingResponseModel);
        }
        if (state is MapSettingErrorState) {
          GlobalMethods.errorToast(context, state.message);
        } else if (state is MapSettingSavedState) {
          _getMapSetting();
        }
        else if (state is LoginSettingSavedState) {
          _getMapSetting();
        }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: RoooAppbar(title: "Password Settings"),
            body: ScreenBody(
                onPullToRefresh: () async => await _onPullToRefresh(),
                isLoading: state is MapSettingLoadingState,
                isEmpty: false,
                emptyMessage: "No data",
                child: SingleChildScrollView(
                  child: Padding(
                    padding: screenPadding,
                    child: Column(
                      children: [


                        ValueListenableBuilder<String>(
                          valueListenable: _selectedLoginSettting,
                          builder: (context, value, child) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Select Login Method",
                                  style: AppTextStyles.title(),
                                ),
                                SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Left side - OTP
                                    Expanded(
                                      child: Column(
                                        children: [
                                          Text(
                                            "OTP",
                                            style: AppTextStyles.header().copyWith(
                                              color: value == _OTP
                                                ? AppColors.primaryColor(context)
                                                : Colors.grey,
                                              fontWeight: value == _OTP
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),

                                    // Center - Switch
                                    Switch(
                                      // activeColor: AppColors.primaryColor(context),
                                      inactiveTrackColor: Colors.white,
                                      // inactiveThumbColor: Colors.white,
                                      // trackOutlineColor: WidgetStateProperty.all(Colors.grey.shade300),
                                      value: value == _PASSWORD, // ON means Password is selected
                                      onChanged: (v) {
                                        if (v) { // Switch turned ON - select Password
                                          _selectedLoginSettting.value = _PASSWORD;
                                        } else { // Switch turned OFF - select OTP
                                          _selectedLoginSettting.value = _OTP;
                                        }
                                        _saveLoginSeting(selectedLoginSetting: _selectedLoginSettting.value);
                                      }
                                    ),

                                    // Right side - Password
                                    Expanded(
                                      child: Column(
                                        children: [
                                          Text(
                                            "Password",
                                            style: AppTextStyles.header().copyWith(
                                              color: value == _PASSWORD
                                                ? AppColors.primaryColor(context)
                                                : Colors.grey,
                                              fontWeight: value == _PASSWORD
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            );
                          },
                        ),
                        Divider(),

                      ],
                    ),
                  ),
                )));
      },
    );
  }
}

//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////
class MyModel {
  String title;
  String type;

  MyModel({required this.title, required this.type});

  // Factory method to create a model from a JSON object
  factory MyModel.fromJson(Map<String, dynamic> json) {
    return MyModel(
      title: json['title'],
      type: json['type'],
    );
  }

  // Method to convert a model instance to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'type': type,
    };
  }
}
