// import 'package:map_launcher/map_launcher.dart';
import 'package:map_launcher/map_launcher.dart' as map_launcher;

import 'package:rooo_driver/features/settings/map_setting/cubit/map_setting_cubit.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class MapSettingScreen extends StatefulWidget {
  final MapSettingModel? vehicleModel;
  const MapSettingScreen({super.key, this.vehicleModel});

  @override
  State<MapSettingScreen> createState() => _MapSettingScreenState();
}

class _MapSettingScreenState extends State<MapSettingScreen> {
  int _currentPage = 1;
  int _totalPage = 1;

  ScrollController _scrollController = ScrollController();

  ValueNotifier<List<map_launcher.AvailableMap>> _availableMaps =
      ValueNotifier([]);

  String _PASSWORD = "password";
  String _OTP = "otp";

  ValueNotifier<String> _selectedLoginSettting = ValueNotifier("Password");
  // String ?_selectedMap ;

  List<MyModel> _maps = [
    MyModel(title: "Apple Map", type: "Apple Maps"),
    MyModel(title: "Google Map", type: "Google Maps"),
    MyModel(title: "Waze Map", type: "Waze")
  ];

  // ValueNotifier<MapSettingTypeModel> _isSelectedService =
  //     ValueNotifier(MapSettingTypeModel(id: -1, service_image: "", name: ""));

  // List<MapSettingTypeModel> _vehicleTypeList = [];

  _saveMapSeting({required String? selectedMapName}) {
    Map<String, dynamic> request = {"selected_map": selectedMapName};
    BlocProvider.of<MapSettingCubit>(context).saveMapSetting(request: request);
  }

  _saveLoginSeting({required String? selectedLoginSetting}) {
    Map<String, dynamic> request = {"login_screen": selectedLoginSetting};
    BlocProvider.of<MapSettingCubit>(context).saveMapSetting(request: request);
  }

  _getMapSetting() {
    BlocProvider.of<MapSettingCubit>(context)
        .getMapSetting(userId: sharedPref.getInt(USER_ID)!);
  }

  _onDataLoaded({required MapSettingResponseModel data}) async {
    _currentPage = data.pagination?.currentPage ?? 1;
    _totalPage = data.pagination?.totalPages ?? 1;

    if (data.data != null) {
      GlobalState.selectedMap = data.data!.selected_map;
    }

    _availableMaps.value = await map_launcher.MapLauncher.installedMaps;

    if (GlobalState.selectedMap != null &&
        _availableMaps.value
            .where((element) => element.mapName == GlobalState.selectedMap)
            .isEmpty) {
      GlobalMethods.showInfoDialogNew(
          context: context,
          onClick: () {
            Navigator.of(context).pop();
          },
          title:
              "Your old selected Map is not available in current device, please update default map for you navigation");
    }
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() async {
    _getMapSetting();
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MapSettingCubit, MapSettingState>(
      listener: (context, state) {
        if (state is MapSettingLoadedState) {
          _onDataLoaded(data: state.mapSettingResponseModel);
        }
        if (state is MapSettingErrorState) {
          GlobalMethods.errorToast(context, state.message);
        } else if (state is MapSettingSavedState) {
          _getMapSetting();
        } else if (state is LoginSettingSavedState) {
          _getMapSetting();
        }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: RoooAppbar(title: "Map Settings"),
            body: ScreenBody(
                onPullToRefresh: () async => await _onPullToRefresh(),
                isLoading: state is MapSettingLoadingState,
                isEmpty: false,
                emptyMessage: "No data",
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      ValueListenableBuilder(
                        valueListenable: _availableMaps,
                        builder: (context, value, child) => ListView.builder(
                          shrinkWrap: true,
                          itemCount: _maps.length,
                          itemBuilder: (context, index) {
                            bool isAvailable = _availableMaps.value.any(
                                (element) =>
                                    element.mapName == _maps[index].type);

                            return Stack(
                              alignment: Alignment.center,
                              children: [
                                RadioListTile<String>(
                                  title: Text(_maps[index].title),
                                  value: _maps[index].type,
                                  groupValue: GlobalState.selectedMap,
                                  secondary: isAvailable
                                      ? Text(
                                          "Available",
                                          style: AppTextStyles.text(
                                              color: AppColors.greenColor),
                                        )
                                      : Text(
                                          "Not Available",
                                          style: AppTextStyles.text(
                                              color: Colors.red),
                                        ),
                                  onChanged: (value) {
                                    if (!isAvailable) {
                                      return;
                                    }

                                    List<map_launcher.AvailableMap> maps =
                                        _availableMaps.value
                                            .where((element) =>
                                                element.mapName ==
                                                _maps[index].type)
                                            .toList();

                                    _saveMapSeting(
                                        selectedMapName: maps[0].mapName);
                                  },
                                ),
                                Visibility(
                                  visible: !isAvailable,
                                  child: Container(
                                    height: 50,
                                    width: double.infinity,
                                    color: AppColors.whiteColor(context)
                                        .withOpacity(.3),
                                  ),
                                )
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                )));
      },
    );
  }
}

//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////
class MyModel {
  String title;
  String type;

  MyModel({required this.title, required this.type});

  // Factory method to create a model from a JSON object
  factory MyModel.fromJson(Map<String, dynamic> json) {
    return MyModel(
      title: json['title'],
      type: json['type'],
    );
  }

  // Method to convert a model instance to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'type': type,
    };
  }
}
