// import 'package:rooo_driver/global/export/app_export.dart';

// class DarkModeScreen extends StatefulWidget {
//   const DarkModeScreen({super.key});

//   @override
//   State<DarkModeScreen> createState() => _DarkModeScreenState();
// }

// class _DarkModeScreenState extends State<DarkModeScreen> {
// //  ValueNotifier< String> darkmodeSetting = ValueNotifier("");
//   String DARKMODE = "NIGHTMODE";
//   String LIGHTMODE = "LIGHTMODE";
//   String SYSTEMMODE = "SYSTEM";

//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//   }

//   init() {
//     // darkmodeSetting.value = sharedPref.getString(DARKMODE_SETTING) ?? "";
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: RoooAppbar(title: "Darkmode setting"),
//       body: Padding(
//           padding: screenPadding,
//           child: ValueListenableBuilder<ThemeMode>(
//             valueListenable: GlobalState.darkModeSetting,
//             builder: (context, value, child) {
//               return Column(
//                 children: [
//                   ListTile(
//                     title: Text("Dark mode"),
//                     trailing: Switch(
//                         value: value == ThemeMode.dark,
//                         onChanged: (v) {
//                           if (v) {

//                             GlobalState.darkModeSetting.value = ThemeMode.dark;
//                             sharedPref.setString(DARKMODE_SETTING, DARKMODE);
//                           }
//                         }),
//                   ),
//                   ListTile(
//                     title: Text("Light mode"),
//                     trailing: Switch(
//                         value: value == ThemeMode.light,
//                         onChanged: (v) {
//                           if (v) {
//                             GlobalState.darkModeSetting.value = ThemeMode.light;
//                             sharedPref.setString(DARKMODE_SETTING, LIGHTMODE);
//                           }
//                         }),
//                   ),
//                   ListTile(
//                     title: Text("System mode"),
//                     trailing: Switch(
//                         value: value == ThemeMode.system,
//                         onChanged: (v) {
//                           if (v) {
//                             GlobalState.darkModeSetting.value = ThemeMode.system;
//                             sharedPref.setString(DARKMODE_SETTING, SYSTEMMODE);
//                           }
//                         }),
//                   )
//                 ],
//               );
//             },
//           )),
//     );
//   }
// }
