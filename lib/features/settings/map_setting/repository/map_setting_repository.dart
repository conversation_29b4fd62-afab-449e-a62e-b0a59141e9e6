

import 'package:rooo_driver/global/export/app_export.dart';

class MapSettingsRepository {
  Future<MapSettingResponseModel> getMapSettingListApi({required int userId}
      ) async {
    return MapSettingResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('user-detail?id=$userId', method: HttpMethod.GET)));
  }
  //   Future<MapSettingTypeResponseModel> getMapSettingTypeListApi(
  //     {required int regionId}) async {
  //   return MapSettingTypeResponseModel.fromJson(await handleResponse(
  //       await buildHttpResponse('service-list/?region_id=${regionId}', method: HttpMethod.GET)));
  // }

  //     Future<StatusMessageModel> deleteMapSettingApi(
  //     {required int map_settingId}) async {
  //   return StatusMessageModel.fromJson(await handleResponse(
  //       await buildHttpResponse('map_settings/delete/' + map_settingId!.toString(), method: HttpMethod.POST)));
  // }
  //   Future<StatusMessageModel> activeMapSettingApi(
  //     {required int map_settingId}) async {
  //   return StatusMessageModel.fromJson(await handleResponse(
  //       await buildHttpResponse('map_settings/active/' + map_settingId!.toString(), method: HttpMethod.POST)));
  // }
    Future<StatusMessageModel> saveMapSettingApi({required  Map<String,dynamic>request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse("update-user-status", method: HttpMethod.POST,
        request: request)));
  }


  

  // Future sendMultiPartRequest(
  //   MultipartRequest multiPartRequest,
  // ) async {
  //   String? result;
  //   multiPartRequest.headers.addAll(buildHeaderTokens());

  //   StreamedResponse response = await multiPartRequest.send();
  //   if (response.statusCode == 200) {
  //     Uint8List responseData = await response.stream.toBytes();
  //     result = String.fromCharCodes(responseData);
  //   }
  //   return result;
  // }
}
