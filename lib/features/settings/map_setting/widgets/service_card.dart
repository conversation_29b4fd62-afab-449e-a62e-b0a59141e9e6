import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/features/vehicles/models/vehicle_type_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class VehicleTypeCard extends StatelessWidget {
  final VehicleTypeModel data;
  final bool isSelected;
  const VehicleTypeCard(
      {super.key, required this.data, required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 150,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: appRadius,
            border: Border.all(
                width: isSelected ? 2 : 1,
                color: isSelected
                    ? AppColors.greenColor
                    : AppColors.primaryBlackColor)),
        child: Column(
          children: [
            Expanded(
              child: CachedNetworkImage(
                fit: BoxFit.cover,
                imageUrl: data.service_image),
            ),
            width10,
            Expanded(
              child: Padding(
                padding: screenPadding / 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Divider(
                      color: Colors.black,
                    ),

                    CustomText(
                      data: data.name,
                      size: 15,
                      fontweight: FontWeight.bold,
                    ),
                    height5,
                    // CustomText(
                    //   data: data.plateNumber,
                    //   size: 12,
                    //   maxline: 4,
                    // ),
                    //   CustomText(
                    //   data: data.transmission,
                    //   size: 12,
                    //   maxline: 4,
                    // ),
                    //   CustomText(
                    //   data: data.serviceName,
                    //   size: 12,
                    //   maxline: 4,
                    // ),

                    // Row(

                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     IconButton(onPressed: (){}, icon: Icon(Icons.edit)),
                    //         IconButton(onPressed: (){}, icon: Icon(Icons.delete))

                    //   ],
                    // )                    // AppButtonWidget(
                    //   width: double.infinity,
                    //   onTap: () {},
                    //   text: "Buy now",
                    // )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
