
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/features/push_notification_pref/cubit/push_info_pref_cubit.dart';
import 'package:rooo_driver/features/push_notification_pref/models/push_notification_pref_model.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';


class PushNotificationPrefScreeen extends StatefulWidget {
  // final DriverPushNotificationPrefModel? PushNotificationPrefModel;
  // final PushNotificationPrefModel ?PushNotificationPrefModel;
  const PushNotificationPrefScreeen();

  @override
  State<PushNotificationPrefScreeen> createState() =>
      _PushNotificationPrefScreeenState();
}

class _PushNotificationPrefScreeenState
    extends State<PushNotificationPrefScreeen> {
  // ScrollController _scrollController = ScrollController();

  ValueNotifier<List<PushNotificationPrefModel>> _pushNotificationPrefList =
      ValueNotifier([]);

  String _emptyMesssage = "";

  _updatePushNotificationPref({required List<int> typeIds}) {
    String typeIdsString = typeIds.isEmpty ? "" : typeIds.toString();
    if (typeIdsString.isNotEmpty) {
      typeIdsString = typeIdsString.substring(1, typeIdsString.length - 1);
    }

    Map<String, dynamic> request = {
      "notif_type": typeIdsString,
    };

    BlocProvider.of<PushNotificationPrefCubit>(context)
        .updatePushNotificationPref(request: request);
  }

//   _onDataLoaded({required PushNotificationPrefResponseModel PushNotificationPrefTypeResponseModel}) {

// _bankNameController.text= widget.PushNotificationPrefModel?.bank_name??"";
// _accountNumberController.text= widget.PushNotificationPrefModel?.bank_name??"";

// _bsbNumberController.text= widget.PushNotificationPrefModel?.bank_name??"";

// _accountHolderNameController.text= widget.PushNotificationPrefModel?.bank_name??"";

//     // _currentPage = PushNotificationPrefTypeResponseModel.pagination?.currentPage ?? 1;
//     // _totalPage = PushNotificationPrefTypeResponseModel.pagination?.totalPages ?? 1;

//     // _PushNotificationPrefModel = PushNotificationPrefTypeResponseModel.data?.user_bank_account;
//     // _emptyMesssage = PushNotificationPrefTypeResponseModel.message.toString();
//   }

  _onPullToRefresh() {
    _init();
  }

  _init() {
// _bankNameController.text= widget.PushNotificationPrefModel?.bank_name??"";
// _accountNumberController.text= widget.PushNotificationPrefModel?.bank_name??"";

// _bsbNumberController.text= widget.PushNotificationPrefModel?.bank_name??"";

// _accountHolderNameController.text= widget.PushNotificationPrefModel?.bank_name??"";    // if (widget.PushNotificationPrefModel != null) {
    //   _nameController.text = widget.PushNotificationPrefModel!.name!;
    //   _selected_transmission.value = widget.PushNotificationPrefModel?.transmission??_tranmission_list[0];
    //   _plateNumberController.text = widget.PushNotificationPrefModel!.transmission;
    //   _isSelectedService.value.id=widget.PushNotificationPrefModel!.serviceId;
    // }

    _getPushNotificationPref();
  }

  _getPushNotificationPref() {
    BlocProvider.of<PushNotificationPrefCubit>(context)
        .getPushNotificationPref();
  }

  _dispose() {}

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PushNotificationPrefCubit, PushNotificationPrefState>(
      listener: (context, state) {
        if (state is PushNotificationPrefErrorState) {
          GlobalMethods.errorToast(context, state.message);
        } else if (state is PushNotificationPrefLoadedState) {
          _pushNotificationPrefList.value = state.pushNotificationPrefList;

          // if(widget.PushNotificationPrefModel?.bank_name==null||widget.PushNotificationPrefModel==null) {

          //   _selected_bank_id.value=_bankName_list[0].id!;

          // }else{

          //   _selected_bank_id.value=widget.PushNotificationPrefModel!.bank_id!;
          // }
        } else if (state is PushNotificationPrefSavedState) {
          GlobalMethods.succesToast(context, "Updated Successfully");
  _getPushNotificationPref();
        }
        // else if (state is PushNotificationPrefSavedState) {
        //   closeScreen(context);

        //   GlobalMethods.replaceScreen(
        //       context: context,
        //       screen: PushNotificationPrefsScreen(),
        //       screenIdentifier: ScreenIdentifier.PushNotificationPrefScreen);
        // }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: RoooAppbar(title: "Add PushNotifications "),
            body: ScreenBody(
                onPullToRefresh: () async => await _onPullToRefresh(),
                isLoading: state is PushNotificationPrefLoadingState,
                isEmpty: _pushNotificationPrefList.value.isEmpty,
                emptyMessage: _emptyMesssage,
                child: Padding(
                  padding: screenPadding,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ValueListenableBuilder<List<PushNotificationPrefModel>>(
                          valueListenable: _pushNotificationPrefList,
                          builder: (context, value, child) {
                            return AnimationLimiter(
                              child: ListView.separated(
                                physics: NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                separatorBuilder: (context, index) => height10,
                                itemCount: value.length,
                                itemBuilder: (BuildContext context, int index) {
                                  PushNotificationPrefModel data = value[index];

                                  return AnimationConfiguration.staggeredList(
                                    position: index,
                                    duration:
                                        const Duration(milliseconds: 1000),
                                    child: SlideAnimation(
                                      verticalOffset: 50.0,
                                      child: FadeInAnimation(
                                        child: SwitchListTile(
                                          value: data.isActive,
                                          title: Text(data.name),
                                          // tileColor: Colors.grey.shade200,
                                          onChanged: (value) {
                                            data.isActive = value;

                                            _pushNotificationPrefList
                                                .notifyListeners();
                                          },
                                        ),

                                        // InboxCard(
                                        //     data: data,
                                        //     onRead: () {
                                        //       _onReadInbox(id: data.id!);
                                        //     },
                                        //     onDelete: () {
                                        //       _onDeleteInbox(id: data.id!);
                                        //     })
                                      ),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
                        height20,

                        AppButton(
                            text: "Update",
                            onPressed: () async  {
                              _updatePushNotificationPref(
                                  typeIds: _pushNotificationPrefList.value
                                      .where((e) => e.isActive)
                                      .map((e) => e.id)
                                      .toList());
                              // if (_selected_bank_id.value != -1) {
                              //   if (_formKey.currentState?.validate() ??
                              //       false) {
                              //   }
                              // } else {
                              //   GlobalMethods.infoToast(
                              //       context, "Please select bank name");
                              // }
                            }),

                        //     Text(
                        //       "Bank name",
                        //       style: AppTextStyles.title(),
                        //     ),

                        //            ValueListenableBuilder<int>(
                        //             valueListenable: _selected_bank_id,
                        //             builder: (context, value, child) {
                        //               if (_bankName_list.isEmpty) {
                        //                 return SizedBox();
                        //               }
                        //               return Column(
                        //                 crossAxisAlignment: CrossAxisAlignment.start,
                        //                 children: [
                        //                   Container(
                        // padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                        // decoration: BoxDecoration(
                        //     // color: Colors.grey,
                        //     borderRadius: BorderRadius.circular(10),
                        //           border: Border.all(
                        //       width:1,
                        //         color: AppColors.primaryColor(context).withOpacity(.6)),),
                        // child: DropdownButtonHideUnderline(
                        //   child: DropdownButton<int?>(
                        //       isExpanded: true,
                        //       value: value == -1 ? _bankName_list[0].id : value,
                        //       items: _bankName_list.map((e) {
                        //         return DropdownMenuItem<int>(
                        //             value: e.id, child: Text(e.name.toString()));
                        //       }).toList(),
                        //       onChanged: (value) {
                        //         _selected_bank_id.value = value!;
                        //       }),
                        // ),
                        //                   ),
                        //                   //                     AppButton(
                        //                   //                       width: double.infinity,
                        //                   //                       text: "Save", onPressed: (){

                        //                   //                          if (widget.isFromDahboard) {
                        //                   //   if (_selected_province_id == -1 ||
                        //                   //       _selected_province_id == -1) {
                        //                   // GlobalMethods.infoToast    (context,  language.pleaseSelectProvinceAndRegionId);
                        //                   //   } else {
                        //                   //     _updateRegionId(request: {
                        //                   //       "type": "region",
                        //                   //       "region_id": _selected_region_id.value,
                        //                   //       "province_id": _selected_province_id.value
                        //                   //     });
                        //                   //   }
                        //                   // } else {
                        //                   //    _updateRegionId(request: {
                        //                   //     "type": "region",
                        //                   //     "region_id": _selected_region_id.value,
                        //                   //     "province_id": _selected_province_id.value
                        //                   //   });
                        //                   // }

                        //                   //                     })
                        //                 ],
                        //               );
                        //             },
                        //           ),

                        //     // AppTextField(
                        //     //     textFieldType: TextFieldType.NAME,
                        //     //     controller: _bankNameController,
                        //     //     keyboardType: TextInputType.number,
                        //     //     focus: _bankNameFocus,
                        //     //     nextFocus: _accountNumberFocus,
                        //     //     // focus: ageController,
                        //     //     // nextFocus: lastNameFocus,
                        //     //     validator: (value) {
                        //     //       if (value?.isEmpty ?? false) {
                        //     //         return "Please Enter valid information";
                        //     //       }
                        //     //       // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                        //     //       // if (!_yearRegex.hasMatch(value.toString())) {
                        //     //       //   return "Please enter valid car production year";
                        //     //       // } else {
                        //     //       //   return null;
                        //     //       // }
                        //     //     },
                        //     //     errorThisFieldRequired: language.thisFieldRequired,
                        //     //     decoration: InputDecoration(
                        //     //         hintText: "Bank name", labelText: "Bank name")),

                        // height20,
                        //        Text(
                        //   "Account number",
                        //   style: AppTextStyles.title(),
                        // ),
                        // AppTextField(
                        //     textFieldType: TextFieldType.PHONE,
                        //     controller: _accountNumberController,
                        //     keyboardType: TextInputType.number,
                        //     focus: _accountNumberFocus,
                        //     nextFocus: _bsbNumberFocus,
                        //     // focus: ageController,
                        //     // nextFocus: lastNameFocus,
                        //     validator: (value) {
                        //       if (value?.isEmpty ?? false) {
                        //         return "Please Enter valid information";
                        //       }
                        //       // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                        //       // if (!_yearRegex.hasMatch(value.toString())) {
                        //       //   return "Please enter valid car production year";
                        //       // } else {
                        //       //   return null;
                        //       // }
                        //     },
                        //     errorThisFieldRequired: language.thisFieldRequired,
                        //     decoration: InputDecoration(
                        //         hintText: "Account number", labelText: "Account number")),
                        // height20,
                        //        Text(
                        //   "Bsb number",
                        //   style: AppTextStyles.title(),
                        // ),
                        // AppTextField(
                        //     textFieldType: TextFieldType.PHONE,
                        //     controller: _bsbNumberController,
                        //     keyboardType: TextInputType.number,
                        //     focus: _bsbNumberFocus,
                        //     nextFocus: _accountHolderNameFocus,
                        //     // focus: ageController,
                        //     // nextFocus: lastNameFocus,
                        //     validator: (value) {
                        //       if (value?.isEmpty ?? false) {
                        //         return "Please Enter valid information";
                        //       }
                        //       // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                        //       // if (!_yearRegex.hasMatch(value.toString())) {
                        //       //   return "Please enter valid car production year";
                        //       // } else {
                        //       //   return null;
                        //       // }
                        //     },
                        //     errorThisFieldRequired: language.thisFieldRequired,
                        //     decoration: InputDecoration(
                        //         hintText: "Bsb number", labelText: "Bsb number")),
                        // height20,
                        //        Text(
                        //   "Account holder name",
                        //   style: AppTextStyles.title(),
                        // ),
                        // AppTextField(
                        //     textFieldType: TextFieldType.ADDRESS,
                        //     controller: _accountHolderNameController,
                        //     keyboardType: TextInputType.name,
                        //     focus: _accountHolderNameFocus,
                        //     nextFocus: _accountHolderNameFocus,
                        //     // focus: ageController,
                        //     // nextFocus: lastNameFocus,
                        //     validator: (value) {
                        //       if (value?.isEmpty ?? false) {
                        //         return "Please Enter valid information";
                        //       }
                        //       // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                        //       // if (!_yearRegex.hasMatch(value.toString())) {
                        //       //   return "Please enter valid car production year";
                        //       // } else {
                        //       //   return null;
                        //       // }
                        //     },
                        //     errorThisFieldRequired: language.thisFieldRequired,
                        //     decoration: InputDecoration(
                        //         hintText: "Account holder name", labelText: "Account holder name")),
                        //         height20,
                      ],
                    ),
                  ),
                )));
      },
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

