
import 'package:pinput/pinput.dart';
import 'package:rooo_driver/features/privacy_center/cubit/push_info_pref_cubit.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
// import 'package:slide_countdown/slide_countdown.dart';

class PrivacyCenterQuestionScreen extends StatefulWidget {
  const PrivacyCenterQuestionScreen({
    super.key,
  });

  @override
  State<PrivacyCenterQuestionScreen> createState() =>
      _PrivacyCenterQuestionScreenState();
}

class _PrivacyCenterQuestionScreenState
    extends State<PrivacyCenterQuestionScreen> {
  final TextEditingController _subjectController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();

  // String _pass = '';
  // UserData user = UserData();
  // String loginKey = "";

  ValueNotifier<bool> _showPassword = ValueNotifier(false);

  var _formkey = GlobalKey<FormState>();

  savePrivacyCenterquestion() {
    Map request = {
      'subject': _subjectController.text,
      'message': _messageController.text,
    };
    BlocProvider.of<PrivacyCenterCubit>(context).savePrivacyCenterQuestion(
      request: request,
    );
  }

  // resendOtp() {
  //   Map request = {
  //     'contact_number': widget.loginKey,
  //   };
  //   BlocProvider.of<VerifyOtpCubit>(context)
  //       .resendOtp(request: request, phone: widget.countryCode + widget.phone);
  // }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Question"),
      body: BlocConsumer<PrivacyCenterCubit, PrivacyCenterState>(
        listener: (context, state) async {
          if (state is PrivacyCenterErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is PrivacyCenterQuestionSavedState) {
            GlobalMethods.succesToast(context, state.message);
            _messageController.clear();
            _subjectController.clear();
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is PrivacyCenterLoadingState,
            isEmpty: false,
            emptyMessage: '',
            onPullToRefresh: () {
              return Future(() {});
            },
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Container(
                  //   height: MediaQuery.of(context).size.height / 2,
                  //   color: Colors.black,
                  //   child: Center(
                  //       child: Image.asset(
                  //     'images/rooo_logo.png',
                  //     width: MediaQuery.of(context).size.width - 100,
                  //     // height: Platform.isIOS
                  //     //     ? MediaQuery.of(context).size.height * .4
                  //     //     : MediaQuery.of(context).size.height * .50,
                  //   )),
                  // ),
                  Padding(
                    padding: screenPadding,
                    child: Form(
                      key: _formkey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                       
                          Text(
                            "Subject",
                            style: AppTextStyles.title(),
                          ),

                          TextFormField(
                            maxLength: 100,
                            autofocus: true,
                            enabled: true,
                            controller: _subjectController,
                            validator: (value) {
                              if (value!.length < 6) {
                                return "Please enter more than 6 characters";
                              }
                              return null;
                            },
                          ),
                          height10,

                          Text(
                            "Message",
                            style: AppTextStyles.title(),
                          ),

                          TextFormField(
                            autofocus: true,
                            maxLines: 4,
                            enabled: true,
                            controller: _messageController,
                            validator: (value) {
                              if (value!.length < 6) {
                                return "Please enter more than 6 characters";
                              }
                              return null;
                            },
                          ),
                          height10,

                       

                          height20,
                          AppButton(
                              width: double.infinity,
                              text: language.continueText,
                              onPressed: () async  {
                                if (_formkey.currentState?.validate() ??
                                    false) {
                                  if (_subjectController.length < 6) {
                                    GlobalMethods.infoToast(context,
                                        "Please enter more than 6 characters");
                                  } else if (_subjectController.length < 6) {
                                    GlobalMethods.infoToast(context,
                                        "Please enter more than 6 characters");
                                  } else {
                                    savePrivacyCenterquestion();
                                  }
                                }
                              })
                        ],
                      ),
                    ),
                  ),

                  // SlideCountdown(

                  //   duration: Duration(minutes: 1),
                  //   onDone: () {},
                  // )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
