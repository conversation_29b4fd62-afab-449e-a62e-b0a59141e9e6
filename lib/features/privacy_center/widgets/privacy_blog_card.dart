import 'package:rooo_driver/features/privacy_center/models/privacy_center_model.dart';
import 'package:rooo_driver/screens/blogsDetailScreen.dart';

import '../../../global/export/app_export.dart';

class PrivacyBlogCard extends StatelessWidget {

  final BlogModel data;
  const PrivacyBlogCard({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        GlobalMethods.pushScreen(
          context: context,
          screen: BlogsDetailScreen(
            appBarTitle: "Privacy detail",
            title: data.title,
            id: data.id,
          ),
          screenIdentifier: ScreenIdentifier.BlogsDetailScreen,
        );
      },
      child: Container(
        width: MediaQuery.of(context).size.width * .7,
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          color: Theme.of(context).cardColor,
          borderRadius: appRadius,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: MediaQuery.of(context).size.height * .2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(borderRadiusValue),
                  topRight: Radius.circular(borderRadiusValue),
                ),
                image: DecorationImage(
                  fit: BoxFit.cover,
                  image: NetworkImage(data.imageURL),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.title,
                    style: AppTextStyles.header().copyWith(
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  height10,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        "Open",
                        style: TextStyle(
                          color: AppColors.primaryColor(context),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      width5,
                      Icon(
                        Icons.arrow_forward,
                        size: 18,
                        color: AppColors.primaryColor(context),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
