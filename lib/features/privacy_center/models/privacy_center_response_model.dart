
import 'package:rooo_driver/features/privacy_center/models/privacy_center_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';





class PrivacyCenterResponseModel extends ResponseModel<PrivacyCeterModel> {
  PrivacyCenterResponseModel({
    required bool status,
    required String message,
    required PrivacyCeterModel? data,
    required PaginationModel? pagination
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory PrivacyCenterResponseModel.fromJson(Map<String, dynamic> json) {
    return PrivacyCenterResponseModel(
       pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
      status: json['status']!=null?json["status"]:true,
      message: json['message']!=null?json["message"]:"",
     data: json["data"] != null
            ? PrivacyCeterModel.fromJson(json["data"])
            : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(), // Convert each VehicleModel to JSON
      'pagination': pagination?.toJson(), // Convert PaginationModel to JSON if not null
    };
  }

}

