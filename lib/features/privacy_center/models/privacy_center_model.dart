
class BlogModel {
  final int id;
  final String title;
  final String userType;
  final String subTitle;
  final String bgColor;
  final String textColor;
  final String description;
  final String imageURL;
  final DateTime createdAt;
  final DateTime updatedAt;

  BlogModel({
    required this.id,
    required this.title,
    required this.userType,
    required this.subTitle,
    required this.bgColor,
    required this.textColor,
    required this.description,
    required this.imageURL,
    required this.createdAt,
    required this.updatedAt,
  });

  // From JSON (deserialization)
  factory BlogModel.fromJson(Map<String, dynamic> json) {
    return BlogModel(
      id: json['id'],
      title: json['title'],
      userType: json['user_type'],
      subTitle: json['sub_title'],
      bgColor: json['bg_color'],
      textColor: json['text_color'],
      description: json['description'],
      imageURL: json['imageURL'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // To JSON (serialization)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'user_type': userType,
      'sub_title': subTitle,
      'bg_color': bgColor,
      'text_color': textColor,
      'description': description,
      'imageURL': imageURL,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class PromoModel {
  final int id;
  final String title;
  final String subTitle;
  final String bgColor;
  final String textColor;
  final String description;
  final String userType;
  final int status;
  final int sequence;
  final DateTime createdAt;
  final DateTime updatedAt;

  PromoModel({
    required this.id,
    required this.title,
    required this.subTitle,
    required this.bgColor,
    required this.textColor,
    required this.description,
    required this.userType,
    required this.status,
    required this.sequence,
    required this.createdAt,
    required this.updatedAt,
  });

  // From JSON (deserialization)
  factory PromoModel.fromJson(Map<String, dynamic> json) {
    return PromoModel(
      id: json['id'],
      title: json['title'],
      subTitle: json['sub_title'],
      bgColor: json['bg_color'],
      textColor: json['text_color'],
      description: json['description'],
      userType: json['user_type'],
      status: json['status'],
      sequence: json['sequence'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // To JSON (serialization)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'sub_title': subTitle,
      'bg_color': bgColor,
      'text_color': textColor,
      'description': description,
      'user_type': userType,
      'status': status,
      'sequence': sequence,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class TrackingModel {
  final int id;
  final String title;
  final String subTitle;
  final String bgColor;
  final String textColor;
  final String description;
  final String userType;
  final int status;
  final int sequence;
  final DateTime createdAt;
  final DateTime updatedAt;

  TrackingModel({
    required this.id,
    required this.title,
    required this.subTitle,
    required this.bgColor,
    required this.textColor,
    required this.description,
    required this.userType,
    required this.status,
    required this.sequence,
    required this.createdAt,
    required this.updatedAt,
  });

  // From JSON (deserialization)
  factory TrackingModel.fromJson(Map<String, dynamic> json) {
    return TrackingModel(
      id: json['id'],
      title: json['title'],
      subTitle: json['sub_title'],
      bgColor: json['bg_color'],
      textColor: json['text_color'],
      description: json['description'],
      userType: json['user_type'],
      status: json['status'],
      sequence: json['sequence'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // To JSON (serialization)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'sub_title': subTitle,
      'bg_color': bgColor,
      'text_color': textColor,
      'description': description,
      'user_type': userType,
      'status': status,
      'sequence': sequence,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class PrivacyCeterModel {
  final List<BlogModel> blogs;
  final PromoModel promo;
  final TrackingModel tracking;

  PrivacyCeterModel({
    required this.blogs,
    required this.promo,
    required this.tracking,
  });

  // From JSON (deserialization)
  factory PrivacyCeterModel.fromJson(Map<String, dynamic> json) {
    return PrivacyCeterModel(
      blogs: (json['blogs'] as List).map((e) => BlogModel.fromJson(e)).toList(),
      promo: PromoModel.fromJson(json['promo']),
      tracking: TrackingModel.fromJson(json['tracking']),
    );
  }

  // To JSON (serialization)
  Map<String, dynamic> toJson() {
    return {
      'blogs': blogs.map((e) => e.toJson()).toList(),
      'promo': promo.toJson(),
      'tracking': tracking.toJson(),
    };
  }
}


