import 'dart:async';



import 'package:rooo_driver/features/privacy_center/models/privacy_center_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class PrivacyCentersRepository {
Future<PrivacyCenterResponseModel> getPrivacyCenterDataApi() async {
  return PrivacyCenterResponseModel.fromJson(await handleResponse(await buildHttpResponse(
      'privacy-detail',
      method: HttpMethod.POST)));
}





Future<StatusMessageModel> savePrivacyCenterQuestionApi({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("privacy-form-submit",
          method: HttpMethod.POST, request: request)));
}

  


}
