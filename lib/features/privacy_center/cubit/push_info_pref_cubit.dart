import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rooo_driver/features/privacy_center/models/privacy_center_response_model.dart';
import 'package:rooo_driver/features/privacy_center/repository/privacy_center_repository.dart';
import 'package:rooo_driver/model/BankListModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class PrivacyCenterState {}

class PrivacyCenterInitState extends PrivacyCenterState {}

class PrivacyCenterLoadingState extends PrivacyCenterState {}



class PrivacyCenterQuestionSavedState extends PrivacyCenterState {
  final String message;

  PrivacyCenterQuestionSavedState({required this.message});
}
class PrivacyCenterDeleteState extends PrivacyCenterState {}



class PrivacyCenterLoadedState extends PrivacyCenterState {
  final PrivacyCenterResponseModel privacyCenter;

  PrivacyCenterLoadedState({
    required this.privacyCenter,
  });
}
class BankNameListLoadedState extends PrivacyCenterState {
  final List<BankListModel> bankList;

  BankNameListLoadedState({
    required this.bankList,
  });
}



class PrivacyCenterErrorState extends PrivacyCenterState {
  final String message;
  PrivacyCenterErrorState({required this.message, });
}

class PrivacyCenterCubit extends Cubit<PrivacyCenterState> {
  PrivacyCenterCubit() : super(PrivacyCenterInitState());

  PrivacyCentersRepository PrivacyCenterRepository = PrivacyCentersRepository();
  

  void getPrivacyCenterData() async {
    emit(PrivacyCenterLoadingState());
    await PrivacyCenterRepository
        .getPrivacyCenterDataApi( )
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(PrivacyCenterLoadedState(privacyCenter: value));
        } else {
          emit(PrivacyCenterErrorState(message: serverErrorMessage));
        }
      } else {
        emit(PrivacyCenterErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(PrivacyCenterErrorState(message: "Server error"));
    });
  }


    void savePrivacyCenterQuestion({required Map<dynamic, dynamic> request }) async {
    emit(PrivacyCenterLoadingState());
    await PrivacyCenterRepository
        .savePrivacyCenterQuestionApi(request:request )
        .then((value) {
      if (value.status) {
          emit(PrivacyCenterQuestionSavedState(message: value.message));
        // if (value.data != null) {
        // } else {
        //   emit(PrivacyCenterErrorState());
        // }
      } else {
        emit(PrivacyCenterErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(PrivacyCenterErrorState(message: "Server error"));
    });
  }


  //  void getPrivacyCenterType({required int regionId}) async {
  //   emit(PrivacyCenterLoadingState());
  //   await PrivacyCenterRepository
  //       .updatePrivacyCenterApi(PrivacyCenterId:regionId )
  //       .then((value) {
  //     if (value.status) {
  //       if (value != null) {
  //         emit(PrivacyCenterTypeLoadedState(PrivacyCenterResponseModel: value));
  //       } else {
  //         emit(PrivacyCenterErrorState());
  //       }
  //     } else {
  //       emit(PrivacyCenterErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(PrivacyCenterErrorState(message: "Server error"));
  //   });
  // }



  //    getPrivacyCenterDetailsPrivacyCenter({required int id}) async {    emit(PrivacyCenterLoadingState());
  //   await PrivacyCenterRepository.getPrivacyCenterDetailsApi(id: id).then((value) {
  //     emit(PrivacyCenterDetailLoaded(data: value.description.toString()));
  //   }).onError((error, stackTrace) {
  //     emit(PrivacyCenterErrorState(error_message: "Server error"));

  //   });
  // }
}
