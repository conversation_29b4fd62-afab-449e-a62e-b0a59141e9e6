import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rooo_driver/features/bank_info/models/bank_info_response_model.dart';
import 'package:rooo_driver/features/bank_info/repository/bank_info_repository.dart';
import 'package:rooo_driver/model/BankListModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class BankInfoState {}

class BankInfoInitState extends BankInfoState {}

class BankInfoLoadingState extends BankInfoState {}



class BankInfoSavedState extends BankInfoState {}
class BankInfoDeleteState extends BankInfoState {}



class BankInfoLoadedState extends BankInfoState {
  final BankInfoResponseModel bankInfoResponseModel;

  BankInfoLoadedState({
    required this.bankInfoResponseModel,
  });
}
class BankNameListLoadedState extends BankInfoState {
  final List<BankListModel> bankList;

  BankNameListLoadedState({
    required this.bankList,
  });
}



class BankInfoErrorState extends BankInfoState {
  final String? message;
  final String? unmessage;
  BankInfoErrorState({this.message, this.unmessage});
}

class BankInfoCubit extends Cubit<BankInfoState> {
  BankInfoCubit() : super(BankInfoInitState());

  BankInfosRepository bankInfoRepository = BankInfosRepository();
  
  void getBankNameList() async {
    emit(BankInfoLoadingState());
    await bankInfoRepository
        .getBankNameListApi( )
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(BankNameListLoadedState(bankList: value.data!));
        }
      } else {
        emit(BankInfoErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(BankInfoErrorState(message: "Server error"));
    });
  }

  void getBankInfo({required int userId }) async {
    emit(BankInfoLoadingState());
    await bankInfoRepository
        .getUserDetail(userId:userId )
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(BankInfoLoadedState(bankInfoResponseModel: value));
        } else {
          emit(BankInfoErrorState(message: serverErrorMessage));
        }
      } else {
        emit(BankInfoErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(BankInfoErrorState(message: "Server error"));
    });
  }


    void updateBankInfo({required Map<dynamic, dynamic> request }) async {
    emit(BankInfoLoadingState());
    await bankInfoRepository
        .updateBankInfoApi(request:request )
        .then((value) {
      if (value.status) {
          emit(BankInfoSavedState());
        // if (value.data != null) {
        // } else {
        //   emit(BankInfoErrorState());
        // }
      } else {
        emit(BankInfoErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(BankInfoErrorState(message: "Server error"));
    });
  }


  //  void getBankInfoType({required int regionId}) async {
  //   emit(BankInfoLoadingState());
  //   await bankInfoRepository
  //       .updateBankInfoApi(bankInfoId:regionId )
  //       .then((value) {
  //     if (value.status) {
  //       if (value != null) {
  //         emit(BankInfoTypeLoadedState(bankInfoResponseModel: value));
  //       } else {
  //         emit(BankInfoErrorState());
  //       }
  //     } else {
  //       emit(BankInfoErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(BankInfoErrorState(message: "Server error"));
  //   });
  // }



  //    getBankInfoDetailsBankInfo({required int id}) async {    emit(BankInfoLoadingState());
  //   await BankInfoRepository.getBankInfoDetailsApi(id: id).then((value) {
  //     emit(BankInfoDetailLoaded(data: value.description.toString()));
  //   }).onError((error, stackTrace) {
  //     emit(BankInfoErrorState(error_message: "Server error"));

  //   });
  // }
}
