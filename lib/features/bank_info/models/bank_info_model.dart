class BankInfoModel {
  String? account_holder_name;
  String? account_number;
  String? bank_code;
  String? bank_name;
  String? created_at;
  int? id;
  String? updated_at;
  int? user_id;

  // String? bank_address;
  // String? bank_city;
  // String? postal_code;

  // String? dob;
  int? bank_id;
    String? type;

    String? bsb_number;

  // String? institution_number;
  // String? transit_number;

  BankInfoModel(
      {this.account_holder_name,
      this.account_number,
      this.bank_code,
      this.bank_name,
      this.created_at,
      this.id,
      this.updated_at,
      this.user_id,
      this.bsb_number,
      this.bank_id,
            this.type,

      // this.bank_address,
      // this.bank_city,
      // this.postal_code,
      // this.dob,
      // this.institution_number,
      // this.transit_number
      });

  factory BankInfoModel.fromJson(Map<String, dynamic> json) {
    return BankInfoModel(
        account_holder_name: json['account_holder_name'],
        account_number: json['account_number'],
        bank_code: json['bank_code'],
        bank_name: json['bank_name'],
        created_at: json['created_at'],
        id: json['id'],
        updated_at: json['updated_at'],
        user_id: json['user_id'],
                type: json['type'],

        bank_id: json["bank_id"],
        // bank_address: json["bank_address"],
        // bank_city: json["bank_city"],
        // postal_code: json["postal_code"],
        // dob: json["dob"],
        // institution_number: json["institution_number"],
        // transit_number: json["transit_number"]
        
        );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['account_holder_name'] = this.account_holder_name;
    data['account_number'] = this.account_number;
    data['bank_code'] = this.bank_code;
    data['bank_name'] = this.bank_name;
    data['created_at'] = this.created_at;
    data['id'] = this.id;
    data['updated_at'] = this.updated_at;
    data['user_id'] = this.user_id;
    data["bank_id"] =  this.bank_id;
        data["type"] =this. type;

    // data['bank_address'] = bank_address;
    // data["bank_city"] = bank_city;
    // data["postal_code"] = postal_code;
    // data["dob"] = dob;
    // data["institution_number"] = institution_number;
    // data["transit_number"] = transit_number;
    return data;
  }
}