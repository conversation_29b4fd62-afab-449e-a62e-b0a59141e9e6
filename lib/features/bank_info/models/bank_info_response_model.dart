
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class BankInfoResponseModel extends ResponseModel<UserData> {
  BankInfoResponseModel({
    required bool status,
    required String message,
    required UserData? data,
    // required PaginationModel? pagination
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory BankInfoResponseModel.fromJson(Map<String, dynamic> json) {
    return BankInfoResponseModel(
      //  pagination: json["pagination"] != null
      //       ? PaginationModel.fromJson(json["pagination"])
            // : null,
      status: json['status'],
      message: json['message'],
     data: json["data"] != null
            ? UserData.fromJson(json["data"])
            : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(), // Convert each MapSettingModel to JSON
      // 'pagination': pagination?.toJson(), // Convert PaginationModel to JSON if not null
    };
  }

}
