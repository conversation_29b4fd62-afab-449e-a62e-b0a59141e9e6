import 'package:rooo_driver/features/bank_info/cubit/bank_info_cubit.dart';
import 'package:rooo_driver/features/bank_info/models/bank_info_model.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

import 'package:rooo_driver/model/BankListModel.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class BankEditScreeen extends StatefulWidget {
  // final DriverBankInfoModel? bankInfoModel;
  final BankInfoModel? bankInfoModel;
  const BankEditScreeen({this.bankInfoModel});

  @override
  State<BankEditScreeen> createState() => _BankEditScreeenState();
}

class _BankEditScreeenState extends State<BankEditScreeen> {
  ScrollController _scrollController = ScrollController();

  ValueNotifier<int> _selected_bank_id = ValueNotifier(-1);
  ValueNotifier<String> _selected_bank_name = ValueNotifier("");

  List<BankListModel> _bankName_list = [];
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // ValueNotifier<BankInfoTypeModel> _isSelectedService =
  //     ValueNotifier(BankInfoTypeModel(id: -1, service_image: "", name: ""));

  // BankListResponseModel

  TextEditingController _bankNameController = TextEditingController();
  TextEditingController _accountNumberController = TextEditingController();

  TextEditingController _bsbNumberController = TextEditingController();
  TextEditingController _accountHolderNameController = TextEditingController();

  FocusNode? _accountNumberFocus;
  FocusNode? _bsbNumberFocus;
  FocusNode? _accountHolderNameFocus;

  // // TextEditingController _transmissionController = TextEditingController();
  // TextEditingController _plateNumberController = TextEditingController();

  // ValueNotifier<File> _bankInfoImage = ValueNotifier(File("path"));

  // _onScrolling() {
  //   _scrollController.addListener(() {
  //     if (_scrollController.position.pixels ==
  //         _scrollController.position.maxScrollExtent) {
  //       if (_currentPage < _totalPage) {
  //         _currentPage++;

  //         _getBankInfo();
  //       }
  //     }
  //   });
  // }

  // _saveBankInfo({required MultipartRequest multiPartRequest}) {
  //   BlocProvider.of<BankInfoCubit>(context)
  //       .saveBankInfo(multiPartRequest: multiPartRequest);
  // }

  _updateBankInfo() {
    Map<String, dynamic> req = {
      "username": sharedPref.getString(USER_NAME).toString(),
      "email": sharedPref.getString(USER_EMAIL).toString(),
      "user_bank_account": BankInfoModel(
              user_id: sharedPref.getInt(USER_ID),
              account_holder_name: _accountHolderNameController.text.trim(),
              account_number: _accountNumberController.text.trim(),
              bank_id: _selected_bank_id.value,
              bank_name: _selected_bank_name.value,
              bank_code: _bsbNumberController.text,
              type: "driver"

              // bank_address: adressController.text.trim(),
              // bank_city: cityController.text.trim(),
              // postal_code: bankPostalCodeController.text.trim(),
              // dob: dateTimeController.text.trim(),
              // bank_id: _selectedBank.name == "Others" ? null : _selectedBank.id,
              // institution_number: instituitionNumberController.text.trim(),
              // transit_number: transitNumberController.text.trim(),
              )
          .toJson()
    };
    print(req);
    BlocProvider.of<BankInfoCubit>(context).updateBankInfo(request: req);
  }

//   _onDataLoaded({required BankInfoResponseModel bankInfoTypeResponseModel}) {

// _bankNameController.text= widget.bankInfoModel?.bank_name??"";
// _accountNumberController.text= widget.bankInfoModel?.bank_name??"";

// _bsbNumberController.text= widget.bankInfoModel?.bank_name??"";

// _accountHolderNameController.text= widget.bankInfoModel?.bank_name??"";

//     // _currentPage = bankInfoTypeResponseModel.pagination?.currentPage ?? 1;
//     // _totalPage = bankInfoTypeResponseModel.pagination?.totalPages ?? 1;

//     // _bankInfoModel = bankInfoTypeResponseModel.data?.user_bank_account;
//     // _emptyMesssage = bankInfoTypeResponseModel.message.toString();
//   }

  _onPullToRefresh() {
    _init();
  }

  _init() {
    _bankNameController.text = widget.bankInfoModel?.bank_name ?? "";
    _accountNumberController.text = widget.bankInfoModel?.account_number ?? "";

    _bsbNumberController.text = widget.bankInfoModel?.bank_code ?? "";

    _accountHolderNameController.text =
        widget.bankInfoModel?.account_holder_name ??
            ""; // if (widget.bankInfoModel != null) {
    //   _nameController.text = widget.bankInfoModel!.name!;
    //   _selected_transmission.value = widget.bankInfoModel?.transmission??_tranmission_list[0];
    //   _plateNumberController.text = widget.bankInfoModel!.transmission;
    //   _isSelectedService.value.id=widget.bankInfoModel!.serviceId;
    // }

    _getBankNameList();
  }

  _getBankNameList() {
    BlocProvider.of<BankInfoCubit>(context).getBankNameList();
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BankInfoCubit, BankInfoState>(
      listener: (context, state) {
        if (state is BankInfoErrorState) {
          GlobalMethods.errorToast(context, state.message!);
        } else if (state is BankNameListLoadedState) {
          _bankName_list = state.bankList;

          if (widget.bankInfoModel?.bank_name == null ||
              widget.bankInfoModel == null) {
            _selected_bank_id.value = _bankName_list[0].id!;
            _selected_bank_name.value = _bankName_list[0].name!;
          } else {
            _selected_bank_id.value = widget.bankInfoModel!.bank_id!;
            _selected_bank_name.value = widget.bankInfoModel!.bank_name!;
          }
        } else if (state is BankInfoSavedState) {
          GlobalMethods.succesToast(context, "Updated Successfully");
          closeScreen(context);
          closeScreen(context);
        }
        // else if (state is BankInfoSavedState) {
        //   closeScreen(context);

        //   GlobalMethods.replaceScreen(
        //       context: context,
        //       screen: BankInfosScreen(),
        //       screenIdentifier: ScreenIdentifier.bankInfoScreen);
        // }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: RoooAppbar(title: "Add Bank information"),
            body: ScreenBody(
                onPullToRefresh: () async => await _onPullToRefresh(),
                isLoading: state is BankInfoLoadingState,
                isEmpty: false,
                emptyMessage: "",
                child: Padding(
                  padding: screenPadding,
                  child: SingleChildScrollView(
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Bank name",
                            style: AppTextStyles.title(),
                          ),

                          ValueListenableBuilder<int>(
                            valueListenable: _selected_bank_id,
                            builder: (context, value, child) {
                              if (_bankName_list.isEmpty) {
                                return SizedBox();
                              }
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 2),
                                    decoration: BoxDecoration(
                                      // color: Colors.grey,
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                          width: 1,
                                          color: AppColors.primaryMustardColr
                                              .withOpacity(.6)),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton<int?>(
                                          isExpanded: true,
                                          value: value == -1
                                              ? _bankName_list[0].id
                                              : value,
                                          items: _bankName_list.map((e) {
                                            return DropdownMenuItem<int>(
                                                value: e.id,
                                                child: Text(e.name.toString()));
                                          }).toList(),
                                          onChanged: (value) {
                                            _selected_bank_id.value = value!;
                                            _selected_bank_name.value =
                                                _bankName_list.firstWhere(
                                                    (e) => e.id == value,
                                                    orElse: () {
                                              return BankListModel(id: -1);
                                            }).name!;
                                          }),
                                    ),
                                  ),
                                  //                     AppButton(
                                  //                       width: double.infinity,
                                  //                       text: "Save", onPressed: (){

                                  //                          if (widget.isFromDahboard) {
                                  //   if (_selected_province_id == -1 ||
                                  //       _selected_province_id == -1) {
                                  // GlobalMethods.infoToast    (context,  language.pleaseSelectProvinceAndRegionId);
                                  //   } else {
                                  //     _updateRegionId(request: {
                                  //       "type": "region",
                                  //       "region_id": _selected_region_id.value,
                                  //       "province_id": _selected_province_id.value
                                  //     });
                                  //   }
                                  // } else {
                                  //    _updateRegionId(request: {
                                  //     "type": "region",
                                  //     "region_id": _selected_region_id.value,
                                  //     "province_id": _selected_province_id.value
                                  //   });
                                  // }

                                  //                     })
                                ],
                              );
                            },
                          ),

                          // AppTextField(
                          //     textFieldType: TextFieldType.NAME,
                          //     controller: _bankNameController,
                          //     keyboardType: TextInputType.number,
                          //     focus: _bankNameFocus,
                          //     nextFocus: _accountNumberFocus,
                          //     // focus: ageController,
                          //     // nextFocus: lastNameFocus,
                          //     validator: (value) {
                          //       if (value?.isEmpty ?? false) {
                          //         return "Please Enter valid information";
                          //       }
                          //       // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                          //       // if (!_yearRegex.hasMatch(value.toString())) {
                          //       //   return "Please enter valid car production year";
                          //       // } else {
                          //       //   return null;
                          //       // }
                          //     },
                          //     errorThisFieldRequired: language.thisFieldRequired,
                          //     decoration: InputDecoration(
                          //         hintText: "Bank name", labelText: "Bank name")),
                          height20,
                          Text(
                            "Account number",
                            style: AppTextStyles.title(),
                          ),
                          AppTextField(
                              textFieldType: TextFieldType.PHONE,
                              controller: _accountNumberController,
                              keyboardType: TextInputType.number,
                              focus: _accountNumberFocus,
                              nextFocus: _bsbNumberFocus,
                              // focus: ageController,
                              // nextFocus: lastNameFocus,
                              validator: (value) {
                                if (value?.isEmpty ?? false) {
                                  return "Please Enter valid information";
                                }
                                return null;
                                // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                                // if (!_yearRegex.hasMatch(value.toString())) {
                                //   return "Please enter valid car production year";
                                // } else {
                                //   return null;
                                // }
                              },
                              errorThisFieldRequired:
                                  language.thisFieldRequired,
                              decoration: InputDecoration(
                                hintText: "Account number",
                              )),
                          height20,
                          Text(
                            "Bsb number",
                            style: AppTextStyles.title(),
                          ),
                          AppTextField(
                              textFieldType: TextFieldType.PHONE,
                              controller: _bsbNumberController,
                              keyboardType: TextInputType.number,
                              focus: _bsbNumberFocus,
                              nextFocus: _accountHolderNameFocus,
                              // focus: ageController,
                              // nextFocus: lastNameFocus,
                              validator: (value) {
                                if (value?.isEmpty ?? false) {
                                  return "Please Enter valid information";
                                }
                                return null;
                                // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                                // if (!_yearRegex.hasMatch(value.toString())) {
                                //   return "Please enter valid car production year";
                                // } else {
                                //   return null;
                                // }
                              },
                              errorThisFieldRequired:
                                  language.thisFieldRequired,
                              decoration: InputDecoration(
                                hintText: "Bsb number",
                              )),
                          height20,
                          Text(
                            "Account holder name",
                            style: AppTextStyles.title(),
                          ),
                          AppTextField(
                              textFieldType: TextFieldType.ADDRESS,
                              controller: _accountHolderNameController,
                              keyboardType: TextInputType.name,
                              focus: _accountHolderNameFocus,
                              nextFocus: _accountHolderNameFocus,
                              // focus: ageController,
                              // nextFocus: lastNameFocus,
                              validator: (value) {
                                if (value?.isEmpty ?? false) {
                                  return "Please Enter valid information";
                                }
                                return null;
                                // final RegExp _yearRegex = RegExp(r'^\d{4}$');

                                // if (!_yearRegex.hasMatch(value.toString())) {
                                //   return "Please enter valid car production year";
                                // } else {
                                //   return null;
                                // }
                              },
                              errorThisFieldRequired:
                                  language.thisFieldRequired,
                              decoration: InputDecoration(
                                hintText: "Account holder name",
                              )),
                          height20,
                          AppButton(
                              text: "Update",
                              onPressed: () async {
                                if (_selected_bank_id.value != -1 ||
                                    _selected_bank_name != "") {
                                  if (_formKey.currentState?.validate() ??
                                      false) {
                                    _updateBankInfo();
                                  }
                                } else {
                                  GlobalMethods.infoToast(
                                      context, "Please select bank name");
                                }
                              }),
                        ],
                      ),
                    ),
                  ),
                )));
      },
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

