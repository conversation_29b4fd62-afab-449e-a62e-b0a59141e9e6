import 'package:rooo_driver/features/bank_info/cubit/bank_info_cubit.dart';
import 'package:rooo_driver/features/bank_info/models/bank_info_model.dart';
import 'package:rooo_driver/features/bank_info/models/bank_info_response_model.dart';
import 'package:rooo_driver/features/bank_info/screens/bank_edit_screen.dart';
import 'package:rooo_driver/features/help/screens/help_screen2.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class BankScreen extends StatefulWidget {
  // final DriverBankInfoModel? bankInfoModel;
  const BankScreen();

  @override
  State<BankScreen> createState() => _BankScreenState();
}

class _BankScreenState extends State<BankScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  // ValueNotifier<BankInfoTypeModel> _isSelectedService =
  //     ValueNotifier(BankInfoTypeModel(id: -1, service_image: "", name: ""));

  BankInfoModel? _bankInfoModel = BankInfoModel(account_number: "");
  String _emptyMesssage = "";

  String _bankName = "";
  String _accountNumber = "";
  String _bsbNumber = "";
  String _accountHolderName = "";

  // BankListResponseModel

  // TextEditingController _nameController = TextEditingController();
  // // TextEditingController _transmissionController = TextEditingController();
  // TextEditingController _plateNumberController = TextEditingController();

  // ValueNotifier<File> _bankInfoImage = ValueNotifier(File("path"));

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;
          _getBankInfo();
        }
      }
    });
  }

  // _saveBankInfo({required MultipartRequest multiPartRequest}) {
  //   BlocProvider.of<BankInfoCubit>(context)
  //       .saveBankInfo(multiPartRequest: multiPartRequest);
  // }

  _getBankInfo() {
    BlocProvider.of<BankInfoCubit>(context)
        .getBankInfo(userId: sharedPref.getInt(USER_ID) ?? 1);
  }

  _onDataLoaded({required BankInfoResponseModel bankInfoTypeResponseModel}) {
    _currentPage = bankInfoTypeResponseModel.pagination?.currentPage ?? 1;
    _totalPage = bankInfoTypeResponseModel.pagination?.totalPages ?? 1;

    _bankInfoModel = bankInfoTypeResponseModel.data?.user_bank_account;
    _bankName = _bankInfoModel?.bank_name ?? "N/A";
    _accountHolderName = _bankInfoModel?.account_holder_name ?? "N/A";
    _bsbNumber = _bankInfoModel?.bank_code ?? "N/A";
    _accountNumber = _bankInfoModel?.account_number ?? "N/A";
    _emptyMesssage = bankInfoTypeResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    // if (widget.bankInfoModel != null) {
    //   _nameController.text = widget.bankInfoModel!.name!;
    //   _selected_transmission.value = widget.bankInfoModel?.transmission??_tranmission_list[0];
    //   _plateNumberController.text = widget.bankInfoModel!.transmission;
    //   _isSelectedService.value.id=widget.bankInfoModel!.serviceId;
    // }
    _onScrolling();
    _getBankInfo();
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BankInfoCubit, BankInfoState>(
      listener: (context, state) {
        if (state is BankInfoLoadedState) {
          _onDataLoaded(bankInfoTypeResponseModel: state.bankInfoResponseModel);
        }
        if (state is BankInfoErrorState) {
          GlobalMethods.errorToast(
            context,
              state.message!,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
            bottomNavigationBar: BottomButton(
                text: "Edit Information",
                onPressed: () {
                    GlobalMethods.pushScreen(
                        context: context,
                        screen: BankEditScreeen(
                          bankInfoModel: _bankInfoModel,
                        ),
                        screenIdentifier: ScreenIdentifier.BankEditScreeen);
                },
                notVisible: false),
            body: ScreenBody(
                onPullToRefresh: () async => await _onPullToRefresh(),
                isLoading: state is BankInfoLoadingState,
                isEmpty: false,
                emptyMessage: _emptyMesssage,
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  padding: EdgeInsets.fromLTRB(16, 20, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: radius(),
                        ),
                        padding: EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Bank Details",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            height20,
                            _buildDetailRow("Bank Name", _bankName),
                            height15,
                            _buildDetailRow("Account Number", _accountNumber),
                            height15,
                            _buildDetailRow("BSB Number", _bsbNumber),
                            height15,
                            _buildDetailRow("Account Holder", _accountHolderName),
                          ],
                        ),
                      ),
                      height20,
                      Divider(color: Colors.grey[300]),
                      ListTile(
                        leading: Icon(Icons.help),
                        title: Text(
                          language.helpTxt,
                          style: TextStyle(),
                        ),
                        onTap: () {
                          GlobalMethods.pushScreen(
                              context: context,
                              screen: HelpScreen(),
                              screenIdentifier: ScreenIdentifier.HelpScreen);
                        },
                      ),
                      Divider(color: Colors.grey[300]),
                    ],
                  ),
                )));
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
