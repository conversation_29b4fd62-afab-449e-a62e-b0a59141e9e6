import 'dart:async';



import 'package:rooo_driver/features/bank_info/models/bank_info_response_model.dart';
import 'package:rooo_driver/features/bank_info/models/bank_name_list_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class BankInfosRepository {
Future<BankInfoResponseModel> getUserDetail({required int userId}) async {
  return BankInfoResponseModel.fromJson(await handleResponse(await buildHttpResponse(
      'user-detail?id=$userId',
      method: HttpMethod.GET)));
}

Future<BankNameListResponseModel> getBankNameListApi() async {
  return BankNameListResponseModel.fromJson(await handleResponse(
      await buildHttpResponse('bank-list', method: HttpMethod.GET)));
}




Future<StatusMessageModel> updateBankInfoApi({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("update-profile",
          method: HttpMethod.POST, request: request)));
}
  //   Future<WebviewDataModel> getAdvertisementDetailsApi({required  int id}) async {
  //   return WebviewDataModel.fromJson(await handleResponse(
  //       await buildHttpResponse('advertisement-details/' + id.toString(), method: HttpMethod.GET)));
  // }


  


}
