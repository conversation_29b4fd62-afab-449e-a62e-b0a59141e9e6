import 'package:rooo_driver/features/way_bill/models/home_route_response_model.dart';
import 'package:rooo_driver/features/way_bill/repository/way_bill_repository.dart';
import 'package:rooo_driver/global/export/app_export.dart';

abstract class WayBillState {}

class WayBillInitState extends WayBillState {}

class WayBillLoadingState extends WayBillState {}

class WayBillErrorState extends WayBillState {
  final String? message;
  final String? unmessage;

  WayBillErrorState({this.message, this.unmessage});
}

class WayBillLoadedState extends WayBillState {
  final WayBillResponseModel wayBillModel;

  WayBillLoadedState({required this.wayBillModel});
}

class WayBillAppliedState extends WayBillState {
  WayBillAppliedState();
}

class WayBillSavedState extends WayBillState {}

class HomeRoutAppliedState extends WayBillState {}

class WayBillDeletedState extends WayBillState {}

class SelectLocationLoadingState extends WayBillState {}

class SelectLocationAddressLoadedState extends WayBillState {
  final PlacesResponseModel googleMapSearchResult;

  SelectLocationAddressLoadedState({required this.googleMapSearchResult});
}

class WayBillCubit extends Cubit<WayBillState> {
  WayBillRepository _wayBillRepository = WayBillRepository();
  WayBillCubit() : super(WayBillInitState());

  getWayBill() {
    emit(WayBillLoadingState());

    _wayBillRepository.getWayBillApi().then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(WayBillLoadedState(wayBillModel: value));
        } else {
          emit(WayBillErrorState(message: serverErrorMessage));
        }
      } else {
        emit(WayBillErrorState(message: value.message));
      }
    }).onError((e, _) {
      emit(WayBillErrorState(message: e.toString()));
    });
  }




}
