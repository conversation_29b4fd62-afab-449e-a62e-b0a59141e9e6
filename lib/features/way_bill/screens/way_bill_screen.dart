import 'package:rooo_driver/features/way_bill/cubit/way_bill_cubit.dart';
import 'package:rooo_driver/features/way_bill/models/home_route_response_model.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

import 'package:url_launcher/url_launcher.dart';
// import 'package:webview_flutter/webview_flutter.dart';

class WayBillScreen extends StatefulWidget {
  const WayBillScreen({super.key});

  @override
  State<WayBillScreen> createState() => _WayBillScreenState();
}

class _WayBillScreenState extends State<WayBillScreen> {
  // OnRideRequest? _wayBill;
  String? _emptyMesssage;

  WayBillCubit? _wayBillCubit;

  bool _webPageLoaded = false;
  String? _url;

  // WebViewController _controller = WebViewController()
    // ..setJavaScriptMode(JavaScriptMode.unrestricted);

  _getWayBillr() {
    BlocProvider.of<WayBillCubit>(context).getWayBill();
  }

  // _applyWayBillr({required int id}) {
  //   BlocProvider.of<WayBillCubit>(context).applyWayBill(id: id);
  // }
  //   _deleteWayBillr({required int id}) {
  //   BlocProvider.of<WayBillCubit>(context).deleteWayBill(id: id);
  // }

  _onDataLoaded({required WayBillResponseModel wayBillResponseModel}) {
    closeScreen(context);
    launchUrl(Uri.parse(wayBillResponseModel.data!));



    //    _url = wayBillResponseModel.data;
    //     _controller.setNavigationDelegate(NavigationDelegate(
    //       onPageFinished: (url) {
        
    //       },
    //     ));
    //     _controller.loadRequest(Uri.parse(_url!));

    // // _wayBill = wayBillResponseModel.data as OnRideRequest;
    // _emptyMesssage = wayBillResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _init();
  }

  _init() {
    _getWayBillr();
  }

  _dispose() {
    _wayBillCubit?.close();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    GlobalMethods.removeSavedScreen();
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Way Bill"),
      // floatingActionButton: FloatingActionButton(
      //     child: Icon(Icons.add),
      //     onPressed: () async {
      //          bool reslut =
      //                                       await GlobalMethods.pushScreen(
      //                                           context: context,
      //                                           screen: AddWayBillScreen(),
      //                                           screenIdentifier:
      //                                               ScreenIdentifier
      //                                                   .AddWayBillScreen);

      //                                   if (reslut) {
      //                                     _init();
      //                                   }
      //     }),
      body: BlocConsumer<WayBillCubit, WayBillState>(
        bloc: _wayBillCubit,
        listener: (context, state) {
          if (state is WayBillLoadedState) {
            _onDataLoaded(wayBillResponseModel: state.wayBillModel);
          } else if (state is WayBillErrorState) {
            closeScreen(context);
            GlobalMethods.errorToast(context, state.message!);
          }
          
          // else if (state is HomeRoutAppliedState) {
          //   GlobalMethods.succesToast(context, "Applied successfully");
          //   _init();
          // }
          // else if (state is WayBillDeletedState) {
          //   GlobalMethods.succesToast(context, "Deleted successfully");
          //   _init();
          // }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is WayBillLoadingState,
              isEmpty: false,
              emptyMessage: _emptyMesssage ?? "No data",
              child:SizedBox(
          height: MediaQuery.sizeOf(context).height,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Stack(
              children: [
                // (_url == null)
                //     ? const SizedBox()
                //     : WebViewWidget(
                //         controller: _controller,
                //       ),
                // _webPageLoaded ? const SizedBox() : loaderWidget(),
              ],
            ),
          ),
        )
              // child: Column(
              //   children: [
              //     SingleChildScrollView(
              //       child: Stack(
              //         children: [
              //           Padding(
              //             padding: screenPadding,
              //             child: SingleChildScrollView(
              //               child: Column(
              //                 children: [
              //                   Card(
              //                     child: Container(
              //                       padding: screenPadding,
              //                       child: Column(
              //                         children: [
              //                           inkWellWidget(
              //                             onTap: () {},
              //                             child: HeaderkeyValue(
              //                                 key: language.PickupLocationTxt,
              //                                 value: _wayBill!.startAddress!),
              //                           ),

              //                           Divider(),
              //                           HeaderkeyValue(
              //                               key: language.destinationLocation,
              //                               value: _wayBill!.endAddress!),
              //                           Divider(),

              //                           keyValue(
              //                               key: language.TimeTxt,
              //                               value: _wayBill!.datetime!),
              //                           keyValue(
              //                               key: language.riderName,
              //                               value: _wayBill?.riderName ?? ""),
              //                           keyValue(
              //                               key: language.statusTxt,
              //                               value: _wayBill!.status
              //                                       .toString()[0]
              //                                       .toUpperCase() +
              //                                   _wayBill!.status
              //                                       .toString()
              //                                       .substring(1)),
              //                           keyValue(
              //                               key: language.distance,
              //                               value:
              //                                   _wayBill!.distance.toString() +
              //                                       _wayBill!.distanceUnit
              //                                           .toString()),

              //                           Divider(),

              //                           Container(
              //                             padding: EdgeInsets.symmetric(
              //                                 horizontal: 8),
              //                             decoration: BoxDecoration(
              //                                 borderRadius: appRadius,
              //                                 color:
              //                                     Colors.green.withOpacity(.3)),
              //                             child: Column(
              //                               children: [
              //                                 amountkeyValue(
              //                                     key: language.earning,
              //                                     value: (appStore
              //                                             .currencyCode) +
              //                                         (_wayBill
              //                                                 ?.driver_subtract_earning
              //                                                 ?.toDouble()
              //                                                 .toStringAsFixed(
              //                                                     2) ??
              //                                             ""))
              //                                 // (double.parse((data?.data
              //                                 //                 ?.driver_earning
              //                                 //                 ?.toStringAsFixed(
              //                                 //                     2) ??
              //                                 //             "0")) +
              //                                 //         double.parse((data?.data
              //                                 //                 ?.perDistanceCharge
              //                                 //                 ?.toStringAsFixed(
              //                                 //                     2) ??
              //                                 //             "0")) -
              //                                 //         ((data!.data?.tips ??
              //                                 //                 0.0) +
              //                                 //             (data!.data
              //                                 //                     ?.perMinuteWaitingCharge ??
              //                                 //                 0.0) +
              //                                 //             (data!.data?.tax ??
              //                                 //                 0.0)))
              //                                 //     .toStringAsFixed(2)),
              //                                 ,
              //                                 amountkeyValue(
              //                                     key: language.tip,
              //                                     value: (appStore
              //                                             .currencyCode) +
              //                                         (double.parse(_wayBill
              //                                                     ?.tips
              //                                                     ?.toStringAsFixed(
              //                                                         2) ??
              //                                                 "0"))
              //                                             .toString()),
              //                                 amountkeyValue(
              //                                     key: language.tax,
              //                                     value: (appStore
              //                                             .currencyCode) +
              //                                         (double.parse(_wayBill
              //                                                     ?.driver_tax
              //                                                     ?.toStringAsFixed(
              //                                                         2) ??
              //                                                 "0"))
              //                                             .toString()),
              //                                 amountkeyValue(
              //                                     key: language.waitingTime,
              //                                     value: (appStore
              //                                             .currencyCode) +
              //                                         (double.parse(_wayBill
              //                                                     ?.perMinuteWaitingCharge
              //                                                     ?.toStringAsFixed(
              //                                                         2) ??
              //                                                 "0"))
              //                                             .toString()),

              //                                 amountkeyValue(
              //                                     key: language.totalEarning,
              //                                     value: (appStore
              //                                             .currencyCode) +
              //                                         (double.parse((_wayBill
              //                                                     ?.driver_earning
              //                                                     ?.toStringAsFixed(
              //                                                         2) ??
              //                                                 "0"))
              //                                             // +
              //                                             // double.parse((data?.data
              //                                             //         ?.perDistanceCharge
              //                                             //         ?.toStringAsFixed(
              //                                             //             2) ??
              //                                             //     "0")
              //                                             //     )
              //                                             )
              //                                             .toStringAsFixed(2)),

              //                                 // amountkeyValue(
              //                                 //     key: language.total +
              //                                 //         " " +
              //                                 //         language.earning +
              //                                 //         "s",
              //                                 //     value: (appStore.currencyCode) +
              //                                 //         ((data!.data?.driver_earning ??
              //                                 //                     0.0) +
              //                                 //                 (data!.data
              //                                 //                         ?.perDistanceCharge ??
              //                                 //                     0.0)

              //                                 //                     )
              //                                 //             .toStringAsFixed(2)),

              //                                 // ,amountkeyValue(key: "Total", value: value)
              //                               ],
              //                             ),
              //                           ),
              //                           height4,

              //                           // Container(
              //                           //   decoration: BoxDecoration(

              //                           //     color: Colors.green.withOpacity(.3)
              //                           //   ),
              //                           //   child: Column(

              //                           //     children: [
              //                           //       amountkeyValue(key: key, value: data.data.wa)
              //                           //     ],
              //                           //   )

              //                           // )

              //                           // Container(
              //                           //   padding:
              //                           //       EdgeInsets.symmetric(horizontal: 8),
              //                           //   decoration: BoxDecoration(
              //                           //       color: Colors.red.withOpacity(.3),
              //                           //       borderRadius: radius()),
              //                           //   child: Column(
              //                           //     children: [
              //                           //       amountkeyValue(
              //                           //           key: "Cancellation charges",
              //                           //           value: data!
              //                           //               .data!.cancelationCharges
              //                           //               .toString()),
              //                           //       amountkeyValue(
              //                           //           key: "Tax",
              //                           //           value: data!.data?.tax.toString()      ??
              //                           //               "0"),

              //                           //     ],
              //                           //   ),
              //                           // ),

              //                           // // Row(
              //                           // //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //                           // //   children: [
              //                           // //     Expanded(child: CustomText(data: "Ride id")),
              //                           // //     Expanded(
              //                           // //       child: CustomText(
              //                           // //           data:
              //                           // //               ride_history_list[index].id.toString()),
              //                           // //     ),
              //                           // //   ],
              //                           // // ),
              //                           // // Divider(
              //                           // //   thickness: 1,
              //                           // // ),
              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data:
              //                           //                 language.PickupLocationTxt)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: _wayBill!.startAddress
              //                           //               .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // Divider(
              //                           //   thickness: 1,
              //                           // ),
              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data: language
              //                           //                 .destinationLocation)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: _wayBill!.endAddress
              //                           //               .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // Divider(
              //                           //   thickness: 1,
              //                           // ),
              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data: language.TimeTxt)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: _wayBill!.datetime
              //                           //               .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // height10,

              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child:
              //                           //             CustomText(data: language.ride)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: _wayBill!.riderName
              //                           //               .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // height10,

              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data: language.payment)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: data!.payment!.paymentStatus
              //                           //               .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // height10,

              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data: language.amount)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: data!.payment!.totalAmount
              //                           //               .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // height10,

              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data: language.paymentMethod)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: data!.payment!.paymentType
              //                           //               .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // height10,
              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data: language.DistanceTxt)),
              //                           //     Expanded(
              //                           //       child: CustomText(
              //                           //           data: _wayBill!.distance!
              //                           //                   .toString() +
              //                           //               " " +
              //                           //               _wayBill!.distanceUnit
              //                           //                   .toString()),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                           // height10,

              //                           // height20,
              //                           // Row(
              //                           //   mainAxisAlignment:
              //                           //       MainAxisAlignment.spaceBetween,
              //                           //   children: [
              //                           //     Expanded(
              //                           //         child: CustomText(
              //                           //             data: language.statusTxt)),
              //                           //     Expanded(
              //                           //       child: Container(
              //                           //         alignment: Alignment.center,
              //                           //         padding: EdgeInsets.all(5),
              //                           //         color:
              //                           //             _wayBill!.status == "cancelled"
              //                           //                 ? Colors.red
              //                           //                 : Colors.green,
              //                           //         child: CustomText(
              //                           //             color: Colors.white,
              //                           //             data: _wayBill!.status
              //                           //                 .toString()),
              //                           //       ),
              //                           //     ),
              //                           //   ],
              //                           // ),
              //                         ],
              //                       ),
              //                     ),
              //                   ),
              //                 ],
              //               ),
              //             ),
              //           ),
              //           // Observer(
              //           //   builder: (context) {
              //           //     return Visibility(
              //           //       visible: appStore.isLoading,
              //           //       child: Loader(),
              //           //     );
              //           //   },
              //           // )
              //         ],
              //       ),
              //     ),
              //     height20,
              //   ],
              // )
              );
        },
      ),
    );
  }

  HeaderkeyValue({required String key, required String value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
            child: CustomText(
          data: key,
          size: 20,
        )),
        width20,
        width20,
        Expanded(
          child: CustomText(
            size: 15,
            data: value,
            fontweight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  keyValue({required String key, required String value}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              child: CustomText(
            data: key,
            size: 12,
          )),
          width20,
          width20,
          Expanded(
            child: CustomText(
              size: 12,
              data: value,
              fontweight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  amountkeyValue({required String key, required String value}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              child: CustomText(
            data: key,
            size: 12,
          )),
          CustomText(
            size: 12,
            data: value,
            fontweight: FontWeight.bold,
          ),
        ],
      ),
    );
  }
}





//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

