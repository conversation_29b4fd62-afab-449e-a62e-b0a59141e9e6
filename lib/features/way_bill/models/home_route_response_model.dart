import 'package:rooo_driver/global/models/response_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class WayBillResponseModel extends ResponseModel<String> {
  WayBillResponseModel({
    required bool status,
    required String message,
    required String? data,
    required PaginationModel? pagination
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory WayBillResponseModel.fromJson(Map<String, dynamic> json) {
    return WayBillResponseModel(
       pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
      status: json['status'],
      message: json['message'],
     data: json["data"] != null
            ? json["data"]
            
            : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data, // Convert each VehicleModel to JSON
      'pagination': pagination?.toJson(), // Convert PaginationModel to JSON if not null
    };
  }

}
