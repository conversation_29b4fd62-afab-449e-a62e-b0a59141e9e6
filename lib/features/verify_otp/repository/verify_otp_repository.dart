
import 'package:rooo_driver/features/verify_otp/models/validat_otp_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class VerifyOtpRepository {
  Future<ValidateOtpResponseModel> validateOtpApi(
      {required Map request}) async {
    return ValidateOtpResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('validate-otp',
            request: request, method: HttpMethod.POST)));
  }
    Future<StatusMessageModel> forgotPassword(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('forget-password',
            request: request, method: HttpMethod.POST)));
  }

  Future<ValidateOtpResponseModel> sendOtpApi({required Map request}) async {
    return ValidateOtpResponseModel.from<PERSON>son(await handleResponse(
        await buildHttpResponse('send-otp',
            request: request, method: HttpMethod.POST)));
  }
    Future<ValidateOtpResponseModel> resendOtpApi({required Map request}) async {
    return ValidateOtpResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('resend-otp',
            request: request, method: HttpMethod.POST)));
  }
//   Future<dynamic> resendOtpApi({required String phone}) async {
//   Response response = await buildHttpResponse('send-otp',
//       request: {'contact_number': phone, "user_type": "driver"},
//       method: HttpMethod.POST);

//   if ((response.statusCode >= 200 && response.statusCode <= 206)) {
//     if (response.body.isJson()) {
//       var json = jsonDecode(response.body);

//       return json;
//     }
//     return null;
//   } else {
//     return null;
//   }
// }
}
