import 'package:rooo_driver/global/models/UserDetailModel.dart';

class ValidateOtpDataModel {
  final UserData? user;
  final String? key;
  final bool? isUserExists;

  ValidateOtpDataModel({
    this.user,
    this.key,
    this.isUserExists,
  });

  factory ValidateOtpDataModel.fromJson(Map<String, dynamic> json) {
    return ValidateOtpDataModel(
      user: json['user'] != null ? UserData.fromJson(json['user']) : null,
      key: json['key'],
      isUserExists: json['is_user_exists'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'user': user != null ? user!.toJson() : null,
      'key': key,
      'is_user_exists': isUserExists,
    };
    return data;
  }
}
