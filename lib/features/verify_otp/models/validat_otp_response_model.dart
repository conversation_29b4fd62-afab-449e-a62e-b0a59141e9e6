import 'package:rooo_driver/features/verify_otp/models/validate_otp_data_model.dart';


import 'package:rooo_driver/global/models/response_model.dart';

class ValidateOtpResponseModel extends ResponseModel<ValidateOtpDataModel> {
  ValidateOtpResponseModel({
    required bool status,
    required String message,
    required ValidateOtpDataModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory ValidateOtpResponseModel.fromJson(Map<String, dynamic> json) {
    return ValidateOtpResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? ValidateOtpDataModel.fromJson(json['data'])
          : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}





