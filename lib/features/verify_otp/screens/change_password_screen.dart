import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
// import 'package:slide_countdown/slide_countdown.dart';

class NewChangePasswordScreen extends StatefulWidget {
  const NewChangePasswordScreen({
    super.key,
  });

  @override
  State<NewChangePasswordScreen> createState() =>
      _NewChangePasswordScreenState();
}

class _NewChangePasswordScreenState extends State<NewChangePasswordScreen> {
  final TextEditingController _passwordController = TextEditingController();
  // String _pass = '';
  // UserData user = UserData();
  // String loginKey = "";

  forgotPassword({required String email}) {
    Map request = {
      'email': email,
      'user_type': "driver",
    };
    BlocProvider.of<VerifyOtpCubit>(context).forgotPassword(
      request: request,
    );
  }

  // resendOtp() {
  //   Map request = {
  //     'contact_number': widget.loginKey,
  //   };
  //   BlocProvider.of<VerifyOtpCubit>(context)
  //       .resendOtp(request: request, phone: widget.countryCode + widget.phone);
  // }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Change Password"),
      body: BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
        listener: (context, state) async {
          if (state is VerifyOtpErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is ForgotPasswordState) {
            GlobalMethods.succesToast(context, state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is VerifyOtpLoadingState,
            isEmpty: false,
            emptyMessage: '',
            onPullToRefresh: () {
              return Future(() {});
            },
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Container(
                  //   height: MediaQuery.of(context).size.height / 2,
                  //   color: Colors.black,
                  //   child: Center(
                  //       child: Image.asset(
                  //     'images/rooo_logo.png',
                  //     width: MediaQuery.of(context).size.width - 100,
                  //     // height: Platform.isIOS
                  //     //     ? MediaQuery.of(context).size.height * .4
                  //     //     : MediaQuery.of(context).size.height * .50,
                  //   )),
                  // ),
                  Padding(
                    padding: screenPadding,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Text(
                        //   "${language.WelcomeTxt}, ${user.firstName ?? "${language.UserTxt}"}.",
                        //   style: TextStyle(
                        //     fontSize: 20,
                        //   ),
                        // ),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Forgot your password ?",
                              style: AppTextStyles.title(),
                            ),
                            InkWell(
                                onTap: () {
                                  forgotPassword(email: appStore.userEmail);
                                },
                                child: Text(
                                  "Click here",
                                  style: AppTextStyles.title(
                                      color: AppColors.primaryMustardColr),
                                )),
                          ],
                        ),
                        // InkWell(
                        //   onTap: timerCountdown == 0 ? resendOtp : null,
                        //   child: Container(
                        //     padding: EdgeInsets.symmetric(
                        //         horizontal: 10, vertical: 10),
                        //     decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(20),
                        //
                        //     ),
                        //     child: timerCountdown == 0
                        //         ? Text(
                        //             language.resendOTPTxt,
                        //             style: TextStyle(
                        //               color: getOtpTimerTextColorWhentimerOff(),
                        //             ),
                        //           )
                        //         : Text(
                        //             "${language.resendOTPTxt} (0:${timerCountdown})",
                        //             style: TextStyle(
                        //               color: getOtpTimerTextColor(),
                        //             ),
                        //           ),
                        //   ),
                        // )
                      ],
                    ),
                  ),

                  // SlideCountdown(

                  //   duration: Duration(minutes: 1),
                  //   onDone: () {},
                  // )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
