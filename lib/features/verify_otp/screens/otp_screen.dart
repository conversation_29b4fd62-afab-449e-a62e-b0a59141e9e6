import 'package:pinput/pinput.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/registration/screens/new_registration_screen.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
// import 'package:slide_countdown/slide_countdown.dart';

class OtpScreen extends StatefulWidget {
  final String countryCode;
  final String phone;
  final String loginKey;
  final String countryIsoCode;
  final bool? isSocialValidation;

  const OtpScreen(
      {super.key,
      required this.countryCode,
      required this.phone,
      required this.loginKey,
      required this.countryIsoCode,
      this.isSocialValidation});

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  final TextEditingController otpController = TextEditingController();
  String _otp = '';
  UserData user = UserData();
  String loginKey = "";

  ValueNotifier<int> countdownTimer = ValueNotifier(60);
  late Timer _otpTimer;

  validateOtp({required bool isSocialValidation}) {
    Map request = {
      'key': loginKey,
      'otp': _otp,
      'player_id': sharedPref.getString(PLAYER_ID),
      "user_type": "driver",
    };
    BlocProvider.of<VerifyOtpCubit>(context)
        .velidateOtp(request: request, isSocialValidation: isSocialValidation);
  }

  resendOtp() {
    Map request = {
      'contact_number': widget.loginKey,
    };
    BlocProvider.of<VerifyOtpCubit>(context)
        .resendOtp(request: request, phone: widget.countryCode + widget.phone);
  }

  @override
  void initState() {
    super.initState();

    loginKey = widget.loginKey;
    _startTimer();
  }

  _startTimer() {
    _otpTimer = Timer.periodic(Duration(seconds: 1), (v) {
      countdownTimer.value = countdownTimer.value - 1;
      if (countdownTimer.value == 0) {
        _otpTimer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _otpTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Verification"),
      body: BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
        listener: (context, state) async {
          if (state is UserFoundState) {
            await GlobalMethods.saveDataLocally(user: state.user);
            if (state.user.isVerifiedDriver == 1) {
              GlobalMethods.pushAndRemoveAll(
                  context: context,
                  screen: RideScreen(),
                  screenIdentifier: ScreenIdentifier.InitialScreen);
            } else {
              GlobalMethods.pushAndRemoveAll(
                  context: context,
                  screen: RideScreen(),
                  screenIdentifier: ScreenIdentifier.InitialScreen);
            }
          } else if (state is UserNotFoundState) {
            // GlobalMethods.pushScreen(
            //   context: context,
            //   screen: EmailRegistrationScreen(
            //       countryCode: widget.countryCode,
            //       isFromPhone: true,
            //       countryIsoCode: widget.countryIsoCode,
            //       phone: widget.phone));

            GlobalMethods.pushScreen(
                context: context,
                screen: NewRegistrationScreen(
                  phone: widget.phone,
                  countryCode: widget.countryCode,
                  countryIsoCode: widget.countryIsoCode,
                  isFromPhone: true,
                ),
                screenIdentifier: ScreenIdentifier.RegistrationScreen);
            // launchScreen(
            //     context,
            //     NewRegistrationScreen(
            //       phone: widget.phone,
            //       countryCode: widget.countryCode,
            //       countryIsoCode: widget.countryIsoCode,
            //       isFromPhone: true,
            //     ));
          } else if (state is ResendOtpState) {
            GlobalMethods.infoToast(
                context, "OTP has been resent successfully.");
            loginKey = state.key;
            countdownTimer.value = 60;
            otpController.clear();
            _startTimer();
          } else if (state is OtpDoesNotMatchedState) {
            GlobalMethods.errorToast(context, state.message);
            _otp = "";
            otpController.clear();
          } else if (state is VerifyOtpErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is OtpVerifiedState) {
            Navigator.pop(context, true);
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is VerifyOtpLoadingState,
            isEmpty: false,
            emptyMessage: '',
            onPullToRefresh: () {
              return Future(() {});
            },
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Container(
                  //   height: MediaQuery.of(context).size.height / 2,
                  //   color: Colors.black,
                  //   child: Center(
                  //       child: Image.asset(
                  //     'images/rooo_logo.png',
                  //     width: MediaQuery.of(context).size.width - 100,
                  //     // height: Platform.isIOS
                  //     //     ? MediaQuery.of(context).size.height * .4
                  //     //     : MediaQuery.of(context).size.height * .50,
                  //   )),
                  // ),
                  Padding(
                    padding: screenPadding,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Text(
                        //   "${language.WelcomeTxt}, ${user.firstName ?? "${language.UserTxt}"}.",
                        //   style: TextStyle(
                        //     fontSize: 20,
                        //   ),
                        // ),
                        Text(
                          language.verifyOTPHeading,
                          style: TextStyle(),
                        ),
                        height10,
                        height20,
                        Pinput(
                          controller: otpController,
                          defaultPinTheme: PinTheme(
                              width: 55,
                              height: 55,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      width: .5,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary),
                                  borderRadius: BorderRadius.circular(10))),
                          length: 6,
                          onCompleted: (pin) {
                            _otp = pin;
                          },
                        ),
                        height20,
                        // InkWell(
                        //   onTap: timerCountdown == 0 ? resendOtp : null,
                        //   child: Container(
                        //     padding: EdgeInsets.symmetric(
                        //         horizontal: 10, vertical: 10),
                        //     decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(20),
                        //
                        //     ),
                        //     child: timerCountdown == 0
                        //         ? Text(
                        //             language.resendOTPTxt,
                        //             style: TextStyle(
                        //               color: getOtpTimerTextColorWhentimerOff(),
                        //             ),
                        //           )
                        //         : Text(
                        //             "${language.resendOTPTxt} (0:${timerCountdown})",
                        //             style: TextStyle(
                        //               color: getOtpTimerTextColor(),
                        //             ),
                        //           ),
                        //   ),
                        // )
                        BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
                          listener: (context, state) async {},
                          builder: (context, state) {
                            return Column(
                              children: [
                                ValueListenableBuilder<int>(
                                  valueListenable: countdownTimer,
                                  builder: (context, value, child) {
                                    if (value == 0) {
                                      return Row(
                                        children: [
                                          Text(
                                            language.resendOTPTxt,
                                            style: TextStyle(),
                                          ),
                                          width10,
                                          state is VerifyOtpResendingState
                                              ? SizedBox(
                                                  height: 30,
                                                  width: 30,
                                                  child: AppLoader(
                                                    size: 30,
                                                  ),
                                                )
                                              : InkWell(
                                                  onTap: () {
                                                    resendOtp();
                                                  },
                                                  child: CustomText(
                                                    data: language.resend,
                                                    color: Colors.red,
                                                  ))
                                        ],
                                      );
                                    } else {
                                      return Row(
                                        children: [
                                          Text(
                                            language.resendOTPTxt,
                                            style: TextStyle(),
                                          ),
                                          width10,
                                          ValueListenableBuilder(
                                            valueListenable: countdownTimer,
                                            builder: (context, value, child) {
                                              return Text(
                                                "00:00:" +
                                                    countdownTimer.value
                                                        .toString(),
                                                style: AppTextStyles.title(
                                                    color:
                                                        AppColors.primaryColor(
                                                            context)),
                                              );
                                            },
                                          )
                                        ],
                                      );
                                    }
                                  },
                                ),
                                height20,
                                AppButton(
                                    width: double.infinity,
                                    text: language.verifyOTP,
                                    onPressed: () async  {
                                      if (_otp.length != 6) {
                                        GlobalMethods.errorToast(
                                            context, language.enterOTP);
                                      } else if (loginKey.length == 0) {
                                        GlobalMethods.infoToast(
                                            context, language.ServerErrorTxt);
                                      } else {
                                        validateOtp(
                                            isSocialValidation:
                                                widget.isSocialValidation ??
                                                    false);
                                      }
                                    })
                                // AppButtonWidget(
                                //   width: double.infinity,
                                //   is_loading: state is VerifyOtpLoadingState,
                                //   text: language.verifyOTP,
                                //   onTap: () {
                                //     if (_otp.length != 6) {
                                //      GlobalMethods.infoToast(context,  language.enterOTP);
                                //     } else if (loginKey.length == 0) {
                                //      GlobalMethods.infoToast(context,  language.ServerErrorTxt);
                                //     } else {
                                //       validateOtp();
                                //     }
                                //   },
                                // ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  // SlideCountdown(

                  //   duration: Duration(minutes: 1),
                  //   onDone: () {},
                  // )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
