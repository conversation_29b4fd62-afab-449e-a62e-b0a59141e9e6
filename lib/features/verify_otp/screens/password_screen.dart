
import 'package:pinput/pinput.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/registration/screens/new_registration_screen.dart';
// import 'package:slide_countdown/slide_countdown.dart';

class PasswordScreen extends StatefulWidget {
  final String email;
  final String countryCode;
  final String phone;
  final String loginKey;
  final String countryIsoCode;
  final bool? isSocialValidation;

  const PasswordScreen(
      {super.key,
      required this.countryCode,
      required this.phone,
      required this.loginKey,
      required this.countryIsoCode,
      this.isSocialValidation,
      required this.email});

  @override
  State<PasswordScreen> createState() => _PasswordScreenState();
}

class _PasswordScreenState extends State<PasswordScreen> {
  final TextEditingController _passwordController = TextEditingController();
  // String _pass = '';
  // UserData user = UserData();
  // String loginKey = "";

  ValueNotifier<bool> _showPassword = ValueNotifier(false);

  validatePassword({required bool isSocialValidation}) {
    hideKeyboard(context);
    Map request = {
      'key': widget.loginKey,
      'password': _passwordController.text,
      'player_id': sharedPref.getString(PLAYER_ID),
      "user_type": "driver",
    };
    BlocProvider.of<VerifyOtpCubit>(context)
        .velidateOtp(request: request, isSocialValidation: isSocialValidation);
  }

  forgotPassword({required String email}) {
    Map request = {
      'email': email,
      'user_type': "driver",
    };
    BlocProvider.of<VerifyOtpCubit>(context).forgotPassword(
      request: request,
    );
  }

  // resendOtp() {
  //   Map request = {
  //     'contact_number': widget.loginKey,
  //   };
  //   BlocProvider.of<VerifyOtpCubit>(context)
  //       .resendOtp(request: request, phone: widget.countryCode + widget.phone);
  // }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Enter password"),
      body: BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
        listener: (context, state) async {
          if (state is UserFoundState) {
            await GlobalMethods.saveDataLocally(user: state.user);
            if (state.user.isVerifiedDriver == 1) {
              GlobalMethods.pushAndRemoveAll(
                  context: context,
                  screen: RideScreen(),
                  screenIdentifier: ScreenIdentifier.InitialScreen);
            } else {
              GlobalMethods.pushAndRemoveAll(
                  context: context,
                  screen: RideScreen(),
                  screenIdentifier: ScreenIdentifier.InitialScreen);
            }
          } else if (state is UserNotFoundState) {
       
            GlobalMethods.pushScreen(
                context: context,
                screen: NewRegistrationScreen(
                  phone: widget.phone,
                  countryCode: widget.countryCode,
                  countryIsoCode: widget.countryIsoCode,
                  isFromPhone: true,
                ),
                screenIdentifier: ScreenIdentifier.RegistrationScreen);
         
          } else if (state is OtpDoesNotMatchedState) {
            GlobalMethods.errorToast(context, state.message);
            _passwordController.clear();
          } else if (state is VerifyOtpErrorState) {

                        GlobalMethods.errorToast(context, state.message);

          } else if (state is ForgotPasswordState) {
            GlobalMethods.succesToast(context, state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is VerifyOtpLoadingState,
            isEmpty: false,
            emptyMessage: '',
            onPullToRefresh: () {
              return Future(() {});
            },
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Container(
                  //   height: MediaQuery.of(context).size.height / 2,
                  //   color: Colors.black,
                  //   child: Center(
                  //       child: Image.asset(
                  //     'images/rooo_logo.png',
                  //     width: MediaQuery.of(context).size.width - 100,
                  //     // height: Platform.isIOS
                  //     //     ? MediaQuery.of(context).size.height * .4
                  //     //     : MediaQuery.of(context).size.height * .50,
                  //   )),
                  // ),
                  Padding(
                    padding: screenPadding,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Text(
                        //   "${language.WelcomeTxt}, ${user.firstName ?? "${language.UserTxt}"}.",
                        //   style: TextStyle(
                        //     fontSize: 20,
                        //   ),
                        // ),
                        Text(
                          "Password",
                          style: AppTextStyles.title(),
                        ),
                        height10,

                        ValueListenableBuilder<bool>(
                          valueListenable: _showPassword,
                          builder: (context, value, child) {
                            return TextFormField(
                              autofocus: true,
                              enabled: true,
                              obscureText: !value,
                              controller: _passwordController,
                              decoration: InputDecoration(
                                  suffix: InkWell(
                                onTap: () {
                                  if (value) {
                                    _showPassword.value = false;
                                  } else {
                                    _showPassword.value = true;
                                  }
                                },
                                child: Icon(value
                                    ? Icons.remove_red_eye_outlined
                                    : Icons.remove_red_eye),
                              )),
                              validator: (value) {
                                if (value!.length < 6) {
                                  return "Password must be atleast 6 chars long";
                                }
                                return null;
                              },
                            );
                          },
                        ),
                        height10,
                        InkWell(
                            onTap: () {
                              forgotPassword(email: widget.email);
                            },
                            child: Text(
                              "Forgot your password ?",
                              style: AppTextStyles.title(
                                  color: AppColors.primaryMustardColr),
                            )),
                        // InkWell(
                        //   onTap: timerCountdown == 0 ? resendOtp : null,
                        //   child: Container(
                        //     padding: EdgeInsets.symmetric(
                        //         horizontal: 10, vertical: 10),
                        //     decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(20),
                        //
                        //     ),
                        //     child: timerCountdown == 0
                        //         ? Text(
                        //             language.resendOTPTxt,
                        //             style: TextStyle(
                        //               color: getOtpTimerTextColorWhentimerOff(),
                        //             ),
                        //           )
                        //         : Text(
                        //             "${language.resendOTPTxt} (0:${timerCountdown})",
                        //             style: TextStyle(
                        //               color: getOtpTimerTextColor(),
                        //             ),
                        //           ),
                        //   ),
                        // )

                        height20,
                        AppButton(
                            width: double.infinity,
                            text: language.continueText,
                            onPressed: () async  {
                              if (_passwordController.length < 6) {
                                GlobalMethods.errorToast(
                                    context, "Please enter pasword (min. 6 chars.)");
                              } else if (widget.loginKey.length == 0) {
                                GlobalMethods.infoToast(
                                    context, language.ServerErrorTxt);
                              } else {
                                validatePassword(
                                    isSocialValidation:
                                        widget.isSocialValidation ?? false);
                              }
                            })
                      ],
                    ),
                  ),

                  // SlideCountdown(

                  //   duration: Duration(minutes: 1),
                  //   onDone: () {},
                  // )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
