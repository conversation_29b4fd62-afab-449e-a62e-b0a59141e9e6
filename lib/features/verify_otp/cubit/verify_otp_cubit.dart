import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/verify_otp/repository/verify_otp_repository.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';

abstract class VerifyOtpState {}

class VerifyOtpInitState extends VerifyOtpState {}

class VerifyOtpLoadingState extends VerifyOtpState {}

class VerifyOtpResendingState extends VerifyOtpState {}

class ResendOtpState extends VerifyOtpState {
  final String key;

  ResendOtpState({required this.key});
}
class ForgotPasswordState extends VerifyOtpState {
  final String message;

  ForgotPasswordState({required this.message});
}
class OtpDoesNotMatchedState extends VerifyOtpState {
  final String message;

  OtpDoesNotMatchedState({required this.message});
}

class OtpVerifiedState extends VerifyOtpState {}

class UserFoundState extends VerifyOtpState {
  final UserData user;

  UserFoundState({required this.user});
}

class UserNotFoundState extends VerifyOtpState {}

class VerifyOtpDetailLoadedState extends VerifyOtpState {
  final String message;

  VerifyOtpDetailLoadedState({required this.message});
}

class VerifyOtpDetailLoaded extends VerifyOtpState {
  final String data;

  VerifyOtpDetailLoaded({required this.data});
}

class VerifyOtpLoadedState extends VerifyOtpState {
  // final VerifyOtpResponseModel verifyOtpResponseModel;

  // VerifyOtpLoadedState({
  //   required this.verifyOtpResponseModel,
  // });
}

class VerifyOtpErrorState extends VerifyOtpState {
 final String message;

  VerifyOtpErrorState({ required this.message,});
}

class VerifyOtpCubit extends Cubit<VerifyOtpState> {
  VerifyOtpCubit() : super(VerifyOtpInitState());

  VerifyOtpRepository verifyOtpRepository = VerifyOtpRepository();

  void velidateOtp(
      {required Map request, required bool isSocialValidation}) async {
    emit( VerifyOtpLoadingState());
    await verifyOtpRepository.validateOtpApi(request: request).then((value) {
      if (value.data == null) {
        emit(OtpDoesNotMatchedState(message: value.message.toString()));
      } else {
        if (isSocialValidation) {
          emit(OtpVerifiedState());
        } else {
          if (value.data?.user != null) {
            emit(UserFoundState(user: value.data!.user!));
          } else {
            emit(UserNotFoundState());
          }
        }
      }
    }).onError((error, stackTrace) {
      emit(VerifyOtpErrorState(message: "Server error"));
    });
  }

    void forgotPassword(
      {required Map request,}) async {
    emit( VerifyOtpLoadingState());
    await verifyOtpRepository.forgotPassword(request: request).then((value) {
   if(value.status){

    emit(ForgotPasswordState(message: value.message));

   }else{
          emit(VerifyOtpErrorState(message: value.message));


   }
    }).onError((error, stackTrace) {
      emit(VerifyOtpErrorState(message: "Server error"));
    });
  }

  resendOtp({required Map request, required phone}) async {
    emit(VerifyOtpResendingState());
    await verifyOtpRepository.resendOtpApi(request: request).then((value) {
      if(value.status){

      emit(ResendOtpState(key: value.data!.key!));
      }else{
              emit(VerifyOtpErrorState(message: value.message));


      }
    }).onError((error, stackTrace) {
      emit(VerifyOtpErrorState(message: "Server error"));
    });
  }
}
