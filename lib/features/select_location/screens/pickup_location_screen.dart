// import 'package:flutter/material.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';


// import '../../../components/rooo_appbar.dart';
// import '../../../global/constants/Colors.dart';
// import '../../../global/constants/constants.dart';
// import '../../../global/widgets/app_button.dart';
// import '../../../global/widgets/loader.dart';
// import '../../../screens/LocationPermissionScreen.dart';

// class PickupLocationScreen extends StatefulWidget {
//   final bool isDestination;

//   const PickupLocationScreen({super.key, required this.isDestination});

//   @override
//   State<PickupLocationScreen> createState() => _PickupLocationScreenState();
// }

// class _PickupLocationScreenState extends State<PickupLocationScreen> {
//   LatLng? _currentLocation;
//   ValueNotifier<PickResultModel> _selectedPlace =
//       ValueNotifier(PickResultModel());

//   Future<void> _getCurrentUserLocation() async {
//     try {
//       final geoPosition = await Geolocator.getCurrentPosition(
//           desiredAccuracy: LocationAccuracy.high);
//       setState(() {
//         _currentLocation = LatLng(geoPosition.latitude, geoPosition.longitude);
//       });
//     } catch (error) {
//       Navigator.push(context,
//           MaterialPageRoute(builder: (_) => LocationPermissionScreen()));
//     }
//   }

//   @override
//   void initState() {
//     super.initState();
//     _getCurrentUserLocation();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: RoooAppbar(
//         title: "Select location",
//       ),
//       body: Stack(
//         alignment: Alignment.center,
//         children: [
//           ValueListenableBuilder(
//             valueListenable: _selectedPlace,
//             builder: (context, value, child) {
//               return UltraMapPlacePicker(
//                 useCurrentLocation: true,
//                 googleApiKey: googleMapAPIKey,
//                 initialPosition: _currentLocation != null
//                     ? LocationModel(
//                         _currentLocation!.latitude, _currentLocation!.longitude)
//                     : LocationModel(-33.865143, 151.209900),
//                 mapTypes: (isHuaweiDevice) => isHuaweiDevice
//                     ? [UltraMapType.normal]
//                     : UltraMapType.values,
//                 myLocationButtonCooldown: 1,
//                 zoomControlsEnabled: false,
//                 resizeToAvoidBottomInset: false,
//                 pinBuilder: (context, state) {
//                   return Image.asset(
//                     widget.isDestination ? DestinationIcon : SourceIcon,
//                     height: 20,
//                     width: 20,
//                   );
//                 },
//                 onPlacePicked: (PickResultModel result) {
//                   _selectedPlace.value = result;
//                   Navigator.pop(context, _selectedPlace.value);
//                 },
//                 selectedPlaceWidgetBuilder:
//                     (context, selectedPlace, state, isSearchBarFocused) {
//                   return Positioned(
//                     bottom: screenPaddingValue,
//                     left: screenPaddingValue,
//                     right: screenPaddingValue,
//                     child: Container(
//                       height: 190,
//                       padding: screenPadding,
//                       child: state.name == 'Searching'
//                           ? Center(
//                               child: RooLoader(size: 25),
//                             )
//                           : Container(
//                               decoration: BoxDecoration(
//                                 borderRadius: appRadius,
//                                 color: AppColors.greenColor,
//                               ),
//                               padding: EdgeInsets.symmetric(
//                                   vertical: screenPaddingValue / 2,
//                                   horizontal: screenPaddingValue),
//                               child: Column(
//                                 mainAxisSize: MainAxisSize.min,
//                                 children: [
//                                   Expanded(
//                                     child: Text(
//                                       selectedPlace?.formattedAddress ?? '',
//                                       maxLines: 2,
//                                       overflow: TextOverflow.ellipsis,
//                                       style: TextStyle(
//                                         color: Colors.white,
//                                         fontWeight: FontWeight.bold,
//                                       ),
//                                     ),
//                                   ),
//                                   const SizedBox(height: 10),
//                                   AppButton(
//                                     text: "Select Place",
//                                     onPressed: () {
//                                       _selectedPlace.value =
//                                           selectedPlace ?? PickResultModel();
//                                       Navigator.pop(
//                                           context, _selectedPlace.value);
//                                     },
//                                   ),
//                                 ],
//                               ),
//                             ),
//                     ),
//                   );
//                 },
//               );
//             },
//           ),
//           // Positioned(
//           //   bottom: 20,
//           //   right: 20,
//           //   child: FloatingActionButton(
//           //     backgroundColor: AppColors.primaryColor(context),
//           //     onPressed: _getCurrentUserLocation,
//           //     child: Icon(Icons.my_location, color: Colors.white),
//           //   ),
//           // ),
//         ],
//       ),
//     );
//   }
// }
