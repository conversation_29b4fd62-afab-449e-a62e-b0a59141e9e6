
// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/features/ride_flow/model/change_destination_request_model.dart';
// import 'package:rooo_driver/features/select_location/cubit/select_location_cubit.dart';
// import 'package:rooo_driver/features/select_location/models/google_map_search_model.dart';
// import 'package:rooo_driver/global/constants/constants.dart';
// import 'package:rooo_driver/global/widgets/bottom_button.dart';
// import 'package:rooo_driver/global/widgets/loader.dart';
// import 'package:rooo_driver/utils/Constants.dart';
 
// class SelectDestinationScreen extends StatefulWidget {
//   final int rideId;
//   const SelectDestinationScreen({super.key, required this.rideId});

//   @override
//   State<SelectDestinationScreen> createState() => New_SelectScreenState();
// }

// class New_SelectScreenState extends State<SelectDestinationScreen> {
//   /////////////////////////////////////////////////////
//   // final _pickupController = TextEditingController();
//   // ValueNotifier<List<Prediction>> _pickupAddressList = ValueNotifier([]);
//   // ValueNotifier<String> _pickupAdress = ValueNotifier("");
//   // String _pickupLattitude = "";
//   // String _pickupLongitude = "";

//   // final _pickpFocus = FocusNode();
// /////////////////////////////////////////////////////
//   final _destinationController = TextEditingController();
//   ValueNotifier<String> _destinationAdress = ValueNotifier("");

//   ValueNotifier<List<Prediction>> _destinationAddressList = ValueNotifier([]);
//   final _destinationFocus = FocusNode();
//   String _destinationLattitude = "";
//   String _destinationLongitude = "";
// //////////////////////////////////////////////////////////////////
//   Timer? _debounceTimer;

//   _getSearchResults({required String searchText}) {
//     BlocProvider.of<SelectLocationCubit>(context)
//         .getAddressLocationList(searchText: searchText);
//   }

//   Future<void> _onSearching(BuildContext context, String searchText) async {
//     // Cancel the previous debounce timer if it exists to prevent extra calls
//     if (_debounceTimer != null && _debounceTimer!.isActive) {
//       _debounceTimer!.cancel();
//     }

//     // Start a new debounce timer
//     _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
//       _getSearchResults(searchText: searchText);
//       //Make API call or do something
//     });
//   }

//   // _onSelectPickup({required Prediction data}) async {
//   //   await searchAddressRequestPlaceId(placeId: data.placeId.toString())
//   //       .then((value) {
//   //     if (value.result != null) {
//   //       // _pickupLattitude = value.result!.geometry!.location!.lat.toString();
//   //       // _pickupLongitude = value.result!.geometry!.location!.lng.toString();

//   //       // _pickupAdress.value = data.description.toString();
//   //       // _pickupAddressList.value.clear();
//   //       // _pickupController.clear();
//   //       _destinationFocus.requestFocus();
//   //       // _pickupAddressList.notifyListeners();
//   //     }
//   //   });
//   // }

//   // _onSelectDestination({required Prediction data}) async {
//   //   await searchAddressRequestPlaceId(placeId: data.placeId.toString())
//   //       .then((value) {
//   //     if (value.result != null) {
//   //       _destinationLattitude =
//   //           value.result!.geometry!.location!.lat.toString();
//   //       _destinationLongitude =
//   //           value.result!.geometry!.location!.lng.toString();

//   //       _destinationAdress.value = data.description.toString();
//   //       _destinationAddressList.value.clear();
//   //       _destinationController.clear();
//   //       _destinationFocus.requestFocus();
//   //       _destinationAdress.notifyListeners();
//   //     }
//   //   });
//   // }

//   _dispose() {
//     _debounceTimer?.cancel();
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     _dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         bottomNavigationBar:
//             // ValueListenableBuilder(
//             //   valueListenable: _pickupAdress,
//             //   builder: (context, value, child) {
//             //     return
//             ValueListenableBuilder(
//                 valueListenable: _destinationAdress,
//                 builder: (context, value, child) {
//                   return BottomButton(
//                       text: "Selects",
//                       onPressed: () async {
//                         Position current_location =
//                             await Geolocator.getCurrentPosition();
//                         Navigator.pop(
//                             context,
//                             ChangeDestinationRequestModel(
//                                 currentLongitude:
//                                     current_location.longitude.toString(),
//                                 currentLatitude:
//                                     current_location.latitude.toString(),
//                                 rideRequestId: widget.rideId,
//                                 destinationLatitude: _destinationLattitude,
//                                 destinationLongitude: _destinationLongitude,
//                                 destinationAddress: _destinationAdress.value));
//                         // launchScreen(SelectServiceScreen(
//                         //   sourceAddress: _pickupAdress.value,
//                         //   destinationAddress: _destinationAdress.value,
//                         //     pickupLocation: LatLng(double.parse(_pickupLattitude),
//                         //         double.parse(_pickupLongitude)),
//                         //     destinlationLocation: LatLng(
//                         //         double.parse(_destinationLattitude),
//                         //         double.parse(_destinationLongitude)))
//                         //         );
//                       },
//                       notVisible: _destinationAdress.value.isEmpty ||
//                           _destinationAdress.value.isEmpty);
//                 }),
//         //   );
//         // },
//         // ),
//         appBar: RoooAppbar(title: "Change destination "),
//         body: Padding(
//             padding: screenPadding,
//             child: SingleChildScrollView(
//                 child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                   // Column(
//                   //   crossAxisAlignment: CrossAxisAlignment.start,
//                   //   children: [
//                   //     Text(
//                   //       "Pickup location",
//                   //       style: TextStyle(fontWeight: FontWeight.bold),
//                   //     ),
//                   //     ValueListenableBuilder(
//                   //       valueListenable: _pickupAdress,
//                   //       builder: (context, value, child) {
//                   //         return TextFormField(
//                   //           controller: _pickupController,
//                   //           focusNode: _pickpFocus,
//                   //           onChanged: (value) {
//                   //             if (value.length > 3) {
//                   //               _onSearching(context, value);
//                   //             }
//                   //           },
//                   //           decoration: InputDecoration(
//                   //               hintText: _pickupAdress.value,
//                   //               suffixIcon: BlocConsumer<SelectLocationCubit,
//                   //                   SelectLocationState>(
//                   //                 listener: (context, state) {
//                   //                   if (state
//                   //                       is SelectLocationAddressLoadedState) {
//                   //                     _pickupAddressList.value = state
//                   //                             .googleMapSearchResult
//                   //                             .predictions ??
//                   //                         [];
//                   //                   }
//                   //                 },
//                   //                 builder: (context, state) {
//                   //                   if (_pickpFocus.hasFocus) {
//                   //                     if (state is SelectLocationLoadingState) {
//                   //                       return SizedBox(
//                   //                         width: 20,
//                   //                         child: RooLoader(
//                   //                           size: 20,
//                   //                         ),
//                   //                       );
//                   //                     }
//                   //                   }
//                   //                   return Icon(Icons.search);
//                   //                 },
//                   //               )),
//                   //         );
//                   //       },
//                   //     ),
//                   //     width5,
//                   //     Row(
//                   //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   //       children: [
//                   //         TextButton(
//                   //             onPressed: () async {
//                   //               PickResult? selectedPlace = await launchScreen(
//                   //                   PickupLocationScreen(isDestination: false));
//                   //               if (selectedPlace != null) {
//                   //                 _pickupAdress.value =
//                   //                     selectedPlace.formattedAddress.toString();
//                   //                 _pickupLattitude = selectedPlace
//                   //                     .geometry!.location.lat
//                   //                     .toString();
//                   //                 _pickupLongitude = selectedPlace
//                   //                     .geometry!.location.lng
//                   //                     .toString();
//                   //               }
//                   //             },
//                   //             child: Row(
//                   //               children: [
//                   //                 Text("From map"),
//                   //                 Icon(Icons.location_on)
//                   //               ],
//                   //             )),
//                   //         TextButton(
//                   //             onPressed: () async {
//                   //               SavedPlace? savedPlace = await launchScreen(
//                   //                   SavedPlacesScreen(),
//                   //                   context: context);
//                   //               if (savedPlace != null) {
//                   //                 _pickupAdress.value = savedPlace.title;
//                   //                 _pickupLattitude = savedPlace.latitude;
//                   //                 _pickupLongitude = savedPlace.longitude;
//                   //               }
//                   //             },
//                   //             child: Row(
//                   //               children: [
//                   //                 Text("Favorites"),
//                   //                 Icon(Icons.flag)
//                   //               ],
//                   //             )),
//                   //       ],
//                   //     ),
//                   //     ValueListenableBuilder<List<Prediction>>(
//                   //       valueListenable: _pickupAddressList,
//                   //       builder: (context, value, child) {
//                   //         if (value.isEmpty || !_pickpFocus.hasFocus) {
//                   //           return SizedBox();
//                   //         }
//                   //         return SizedBox(
//                   //           height: 300,
//                   //           child: Card(
//                   //             child: ListView.builder(
//                   //               shrinkWrap: true,
//                   //               itemCount: value.length,
//                   //               itemBuilder: (context, index) {
//                   //                 Prediction data = value[index];
//                   //                 return ListTile(
//                   //                   onTap: () async {
//                   //                     _onSelectPickup(data: data);
//                   //                   },
//                   //                   trailing: Icon(Icons.my_location_outlined),
//                   //                   title: Text(data.description.toString()),
//                   //                 );
//                   //               },
//                   //             ),
//                   //           ),
//                   //         );
//                   //       },
//                   //     ),
//                   //   ],
//                   // ),
//                   // height10,
//                   // Divider(),
//                   height10,
//                   Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             "destination location",
//                             style: TextStyle(fontWeight: FontWeight.bold),
//                           ),
//                           ValueListenableBuilder(
//                             valueListenable: _destinationAdress,
//                             builder: (context, value, child) {
//                               return TextFormField(
//                                 controller: _destinationController,
//                                 focusNode: _destinationFocus,
//                                 onChanged: (value) {
//                                   if (value.length > 3) {
//                                     _onSearching(context, value);
//                                   }
//                                 },
//                                 decoration: InputDecoration(
//                                     hintText: _destinationAdress.value,
//                                     suffixIcon: BlocConsumer<
//                                         SelectLocationCubit,
//                                         SelectLocationState>(
//                                       listener: (context, state) {
//                                         if (state
//                                             is SelectLocationAddressLoadedState) {
//                                           _destinationAddressList.value = state
//                                                   .googleMapSearchResult
//                                                   .predictions ??
//                                               [];
//                                         }
//                                       },
//                                       builder: (context, state) {
//                                         if (_destinationFocus.hasFocus) {
//                                           if (state
//                                               is SelectLocationLoadingState) {
//                                             return SizedBox(
//                                               width: 20,
//                                               child: RooLoader(
//                                                 size: 20,
//                                               ),
//                                             );
//                                           }
//                                         }
//                                         return Icon(Icons.search);
//                                       },
//                                     )),
//                               );
//                             },
//                           ),
//                           width5,
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               TextButton(
//                                   onPressed: () async {
//                                     // PickResultModel? selectedPlace =
//                                     //     await launchScreen(PickupLocationScreen(
//                                     //         isDestination: true));
//                                     // if (selectedPlace != null) {
//                                     //   _destinationAdress.value = selectedPlace
//                                     //       .formattedAddress
//                                     //       .toString();
//                                     //   _destinationLattitude = selectedPlace
//                                     //       .geometry!.location.lat
//                                     //       .toString();
//                                     //   _destinationLongitude = selectedPlace
//                                     //       .geometry!.location.lng
//                                     //       .toString();
//                                     // }
//                                   },
//                                   child: Row(
//                                     children: [
//                                       Text("From map"),
//                                       Icon(Icons.location_on)
//                                     ],
//                                   )),
//                               TextButton(
//                                   onPressed: () async {
//                                     // SavedPlace? savedPlace = await launchScreen(
//                                     //     SavedPlacesScreen(),
//                                     //     context: context);
//                                     // if (savedPlace != null) {
//                                     //   _destinationAdress.value =
//                                     //       savedPlace.title;
//                                     //   _destinationLattitude =
//                                     //       savedPlace.latitude;
//                                     //   _destinationLongitude =
//                                     //       savedPlace.longitude;
//                                     // }
//                                   },
//                                   child: Row(
//                                     children: [
//                                       Text("Favorites"),
//                                       Icon(Icons.flag)
//                                     ],
//                                   )),
//                             ],
//                           ),
//                           ValueListenableBuilder<List<Prediction>>(
//                             valueListenable: _destinationAddressList,
//                             builder: (context, value, child) {
//                               if (value.isEmpty) {
//                                 return SizedBox();
//                               }
//                               return SizedBox(
//                                 height: 300,
//                                 child: Card(
//                                   child: ListView.builder(
//                                     shrinkWrap: true,
//                                     itemCount: value.length,
//                                     itemBuilder: (context, index) {
//                                       Prediction data = value[index];
//                                       return ListTile(
//                                         onTap: () {
//                                           // _onSelectDestination(data: data);
//                                         },
//                                         trailing:
//                                             Icon(Icons.my_location_outlined),
//                                         title:
//                                             Text(data.description.toString()),
//                                       );
//                                     },
//                                   ),
//                                 ),
//                               );
//                             },
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ]))));
//   }
// }
