
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:rooo_driver/features/select_location/models/google_map_search_model.dart';
// import 'package:rooo_driver/features/select_location/repository/select_location_repository.dart';

// abstract class SelectLocationState {}

// class SelectLocationInitialState extends SelectLocationState {}

// class SelectLocationLoadingState extends SelectLocationState {}

// class SelectLocationErrorState extends SelectLocationState {}



// class SelectLocationCubit extends Cubit<SelectLocationState> {
//   DestinationFilterRepository _selectLocationRepository =
//       DestinationFilterRepository();
//   SelectLocationCubit() : super(SelectLocationInitialState());


// }
