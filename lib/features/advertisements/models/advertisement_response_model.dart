import 'package:rooo_driver/features/advertisements/models/advertisement_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class AdvertisementResponseModel {
  PaginationModel? pagination;
  List<AdvertisementModel>? data;
  String? message;

  AdvertisementResponseModel({this.data, this.pagination, this.message});

  factory AdvertisementResponseModel.fromJson(Map<String, dynamic> json) {
    return AdvertisementResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => AdvertisementModel.fromJson(e))
                .toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["message"] = this.message;
    return datas;
  }
}
