class AdvertisementModel {
  int id;
  String name;
  String? desc;
  DateTime from;
  DateTime to;
  String imageURL;
  int status;
  String type;
  String webUrl;
  String page;
  DateTime createdAt;
  DateTime updatedAt;
  List<Media> media;

  AdvertisementModel({
    required this.id,
    required this.name,
    this.desc,
    required this.from,
    required this.to,
    required this.imageURL,
    required this.status,
    required this.type,
    required this.webUrl,
    required this.page,
    required this.createdAt,
    required this.updatedAt,
    required this.media,
  });

  // Factory method to create Data object from JSON
  factory AdvertisementModel.fromJson(Map<String, dynamic> json) {
    return AdvertisementModel(
      id: json['id'],
      name: json['name'],
      desc: json['desc'],
      from: DateTime.parse(json['from']),
      to: DateTime.parse(json['to']),
      imageURL: json['imageURL'],
      status: json['status'],
      type: json['type'],
      webUrl: json['web_url'],
      page: json['page'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      media: List<Media>.from(json['media'].map((x) => Media.fromJson(x))),
    );
  }

  // Method to convert Data object to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'desc': desc,
      'from': from.toIso8601String(),
      'to': to.toIso8601String(),
      'imageURL': imageURL,
      'status': status,
      'type': type,
      'web_url': webUrl,
      'page': page,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'media': List<dynamic>.from(media.map((x) => x.toJson())),
    };
  }
}

class Media {
  int id;
  String modelType;
  int modelId;
  String uuid;
  String collectionName;
  String name;
  String fileName;
  String mimeType;
  String disk;
  String conversionsDisk;
  int size;
  DateTime createdAt;
  DateTime updatedAt;
  String originalUrl;
  String previewUrl;

  Media({
    required this.id,
    required this.modelType,
    required this.modelId,
    required this.uuid,
    required this.collectionName,
    required this.name,
    required this.fileName,
    required this.mimeType,
    required this.disk,
    required this.conversionsDisk,
    required this.size,
    required this.createdAt,
    required this.updatedAt,
    required this.originalUrl,
    required this.previewUrl,
  });

  // Factory method to create Media object from JSON
  factory Media.fromJson(Map<String, dynamic> json) {
    return Media(
      id: json['id'],
      modelType: json['model_type'],
      modelId: json['model_id'],
      uuid: json['uuid'],
      collectionName: json['collection_name'],
      name: json['name'],
      fileName: json['file_name'],
      mimeType: json['mime_type'],
      disk: json['disk'],
      conversionsDisk: json['conversions_disk'],
      size: json['size'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      originalUrl: json['original_url'],
      previewUrl: json['preview_url'],
    );
  }

  // Method to convert Media object to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'model_type': modelType,
      'model_id': modelId,
      'uuid': uuid,
      'collection_name': collectionName,
      'name': name,
      'file_name': fileName,
      'mime_type': mimeType,
      'disk': disk,
      'conversions_disk': conversionsDisk,
      'size': size,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'original_url': originalUrl,
      'preview_url': previewUrl,
    };
  }
}