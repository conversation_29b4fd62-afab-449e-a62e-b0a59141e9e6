import 'package:rooo_driver/features/advertisements/models/advertisement_response_model.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class AdvertisementRepository {
  Future<AdvertisementResponseModel> getAdvertisementListApi(
      {required int currentPage}) async {
    return AdvertisementResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('advertisements', method: HttpMethod.GET)));
  }
  //   Future<WebviewDataModel> getAdvertisementDetailsApi({required  int id}) async {
  //   return WebviewDataModel.fromJson(await handleResponse(
  //       await buildHttpResponse('advertisement-details/' + id.toString(), method: HttpMethod.GET)));
  // }
}
