import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/advertisements/cubit/advertisement_cubit.dart';
import 'package:rooo_driver/features/advertisements/models/advertisement_model.dart';
import 'package:rooo_driver/features/advertisements/models/advertisement_response_model.dart';
import 'package:rooo_driver/features/advertisements/widgets/advertisement_card.dart';

import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/utils/Constants.dart';

class AdvertisementScreen extends StatefulWidget {
  const AdvertisementScreen({super.key});

  @override
  State<AdvertisementScreen> createState() => _AdvertisementScreenState();
}

class _AdvertisementScreenState extends State<AdvertisementScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  List<AdvertisementModel> _advertisementList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getAdvertisementList(currentPage: _currentPage);
        }
      }
    });
  }

  _getAdvertisementList({required int currentPage}) {
    BlocProvider.of<AdvertisementCubit>(context)
        .getAdvertisement(currentPage: currentPage);
  }

  _onDataLoaded(
      {required AdvertisementResponseModel advertisementResponseModel}) {
    _currentPage = advertisementResponseModel.pagination?.currentPage ?? 1;
    _totalPage = advertisementResponseModel.pagination?.totalPages ?? 1;

    _advertisementList = advertisementResponseModel.data ?? [];
    _emptyMesssage = advertisementResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getAdvertisementList(currentPage: _currentPage);
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Advertisement"),
      body: BlocConsumer<AdvertisementCubit, AdvertisementState>(
        listener: (context, state) {
          if (state is AdvertisementLoadedState) {
            _onDataLoaded(
                advertisementResponseModel: state.advertisementResponseModel);
          }
          if (state is AdvertisementErrorState) {
            GlobalMethods.errorToast(context, state.error_message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is AdvertisementLoadingState,
              isEmpty: _advertisementList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: AnimationLimiter(
                child: ListView.separated(
                  padding: screenPadding,
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => height10,
                  itemCount: _advertisementList.length,
                  itemBuilder: (BuildContext context, int index) {
                    AdvertisementModel data = _advertisementList[index];

                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 1000),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                            child: AdvertismentCard(data: data)),
                      ),
                    );
                  },
                ),
              ));
        },
      ),
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

