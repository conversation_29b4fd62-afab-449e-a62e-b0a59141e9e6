import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/advertisements/models/advertisement_response_model.dart';
import 'package:rooo_driver/features/advertisements/repository/advertisement_repository.dart';

abstract class AdvertisementState {}

class AdvertisementInitState extends AdvertisementState {}

class AdvertisementLoadingState extends AdvertisementState {}

class AdvertisementDeletedState extends AdvertisementState {}

class AdvertisementDetailLoadedState extends AdvertisementState {
  final String message;

  AdvertisementDetailLoadedState({required this.message});
}

class AdvertisementDetailLoaded extends AdvertisementState {
  final String data;

  AdvertisementDetailLoaded({required this.data});
}

class AdvertisementLoadedState extends AdvertisementState {
  final AdvertisementResponseModel advertisementResponseModel;

  AdvertisementLoadedState({
    required this.advertisementResponseModel,
  });
}

class AdvertisementErrorState extends AdvertisementState {
  final String error_message;

  AdvertisementErrorState({required this.error_message});
}

class AdvertisementCubit extends Cubit<AdvertisementState> {
  AdvertisementCubit() : super(AdvertisementInitState());

  AdvertisementRepository advertisementRepository = AdvertisementRepository();

  void getAdvertisement({required int currentPage}) async {
    emit(AdvertisementLoadingState());
    await advertisementRepository
        .getAdvertisementListApi(currentPage: currentPage)
        .then((value) {
      emit(AdvertisementLoadedState(advertisementResponseModel: value));
    }).onError((error, stackTrace) {
      emit(AdvertisementErrorState(error_message: "Server error"));
    });
  }

  //    getAdvertisementDetailsAdvertisement({required int id}) async {    emit(AdvertisementLoadingState());
  //   await advertisementRepository.getAdvertisementDetailsApi(id: id).then((value) {
  //     emit(AdvertisementDetailLoaded(data: value.description.toString()));
  //   }).onError((error, stackTrace) {
  //     emit(AdvertisementErrorState(error_message: "Server error"));

  //   });
  // }
}
