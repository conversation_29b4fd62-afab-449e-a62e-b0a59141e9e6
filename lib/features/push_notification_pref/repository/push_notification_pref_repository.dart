import 'dart:async';



import 'package:rooo_driver/features/push_notification_pref/models/push_notification_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class PushNotificationPrefsRepository {
Future<PushNotificationPrefResponseModel> getNotificationTypeApi() async {
  return PushNotificationPrefResponseModel.fromJson(await handleResponse(await buildHttpResponse(
      'notification-type',
      method: HttpMethod.GET)));
}





Future<StatusMessageModel> updatePushNotificationPrefApi({required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse("update-user-status",
          method: HttpMethod.POST, request: request)));
}

  


}
