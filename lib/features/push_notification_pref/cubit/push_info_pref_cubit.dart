import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rooo_driver/features/push_notification_pref/models/push_notification_pref_model.dart';
import 'package:rooo_driver/features/push_notification_pref/repository/push_notification_pref_repository.dart';
import 'package:rooo_driver/model/BankListModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class PushNotificationPrefState {}

class PushNotificationPrefInitState extends PushNotificationPrefState {}

class PushNotificationPrefLoadingState extends PushNotificationPrefState {}



class PushNotificationPrefSavedState extends PushNotificationPrefState {}
class PushNotificationPrefDeleteState extends PushNotificationPrefState {}



class PushNotificationPrefLoadedState extends PushNotificationPrefState {
  final List<PushNotificationPrefModel> pushNotificationPrefList;

  PushNotificationPrefLoadedState({
    required this.pushNotificationPrefList,
  });
}
class BankNameListLoadedState extends PushNotificationPrefState {
  final List<BankListModel> bankList;

  BankNameListLoadedState({
    required this.bankList,
  });
}



class PushNotificationPrefErrorState extends PushNotificationPrefState {
  final String message;
  PushNotificationPrefErrorState({required this.message, });
}

class PushNotificationPrefCubit extends Cubit<PushNotificationPrefState> {
  PushNotificationPrefCubit() : super(PushNotificationPrefInitState());

  PushNotificationPrefsRepository pushNotificationPrefRepository = PushNotificationPrefsRepository();
  

  void getPushNotificationPref() async {
    emit(PushNotificationPrefLoadingState());
    await pushNotificationPrefRepository
        .getNotificationTypeApi( )
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(PushNotificationPrefLoadedState(pushNotificationPrefList: value.data!));
        } else {
          emit(PushNotificationPrefErrorState(message: serverErrorMessage));
        }
      } else {
        emit(PushNotificationPrefErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(PushNotificationPrefErrorState(message: "Server error"));
    });
  }


    void updatePushNotificationPref({required Map<dynamic, dynamic> request }) async {
    emit(PushNotificationPrefLoadingState());
    await pushNotificationPrefRepository
        .updatePushNotificationPrefApi(request:request )
        .then((value) {
      if (value.status) {
          emit(PushNotificationPrefSavedState());
        // if (value.data != null) {
        // } else {
        //   emit(PushNotificationPrefErrorState());
        // }
      } else {
        emit(PushNotificationPrefErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(PushNotificationPrefErrorState(message: "Server error"));
    });
  }


  //  void getPushNotificationPrefType({required int regionId}) async {
  //   emit(PushNotificationPrefLoadingState());
  //   await PushNotificationPrefRepository
  //       .updatePushNotificationPrefApi(PushNotificationPrefId:regionId )
  //       .then((value) {
  //     if (value.status) {
  //       if (value != null) {
  //         emit(PushNotificationPrefTypeLoadedState(PushNotificationPrefResponseModel: value));
  //       } else {
  //         emit(PushNotificationPrefErrorState());
  //       }
  //     } else {
  //       emit(PushNotificationPrefErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(PushNotificationPrefErrorState(message: "Server error"));
  //   });
  // }



  //    getPushNotificationPrefDetailsPushNotificationPref({required int id}) async {    emit(PushNotificationPrefLoadingState());
  //   await PushNotificationPrefRepository.getPushNotificationPrefDetailsApi(id: id).then((value) {
  //     emit(PushNotificationPrefDetailLoaded(data: value.description.toString()));
  //   }).onError((error, stackTrace) {
  //     emit(PushNotificationPrefErrorState(error_message: "Server error"));

  //   });
  // }
}
