import 'package:rooo_driver/global/models/response_model.dart';
import 'package:rooo_driver/model/BankListModel.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class BankNameListResponseModel extends ResponseModel<List<BankListModel>> {
  BankNameListResponseModel({
    required bool status,
    required String message,
    required List<BankListModel>? data,
    required PaginationModel? pagination
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory BankNameListResponseModel.fromJson(Map<String, dynamic> json) {
    return BankNameListResponseModel(
       pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
      status: json['status']!=null?json["status"]:true,
      message: json['message']!=null?json["message"]:"",
     data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => BankListModel.fromJson(e))
                .toList()
            : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((e) => e.toJson()).toList(), // Convert each VehicleModel to JSON
      'pagination': pagination?.toJson(), // Convert PaginationModel to JSON if not null
    };
  }

}
