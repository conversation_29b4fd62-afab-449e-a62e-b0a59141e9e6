class PushNotificationPrefModel {
  int id;
  String name;
  bool isActive;

  PushNotificationPrefModel({
    required this.id,
    required this.name,
    required this.isActive,
  });

  // Factory constructor for creating a new instance from a map (JSON)
  factory PushNotificationPrefModel.fromJson(Map<String, dynamic> json) {
    return PushNotificationPrefModel(
      id: json['id'],
      name: json['name'],
      isActive: json['is_active'],
    );
  }

  // Method to convert an instance to a map (JSON)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'is_active': isActive,
    };
  }
}
