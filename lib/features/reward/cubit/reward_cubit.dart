import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/reward/models/reward_model.dart';

import 'package:rooo_driver/features/reward/models/reward_response_model.dart';
import 'package:rooo_driver/features/reward/repository/reward_repository.dart';
import 'package:rooo_driver/model/PaginationModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class RewardState {}

class RewardInitState extends RewardState {}

class RewardLoadingState extends RewardState {}

class RewardDeletedState extends RewardState {}

class RewardDetailLoadedState extends RewardState {
  final String message;

  RewardDetailLoadedState({required this.message});
}

class RewardSavedState extends RewardState {}

class RewardDeleteState extends RewardState {}

class RewardDetailLoaded extends RewardState {
  final String data;

  RewardDetailLoaded({required this.data});
}

class RewardLoadedState extends RewardState {
  final RewardResponseModel rewardResponseModel;

  RewardLoadedState({
    required this.rewardResponseModel,
  });
}
// class RewardLoadedState extends RewardState {
//   final RewardResponseModel rewardResponseModel;

//   RewardLoadedState({
//     required this.rewardResponseModel,
//   });
// }

class RewardErrorState extends RewardState {
  final String? message;
  final String? unmessage;
  RewardErrorState({this.message, this.unmessage});
}

class RewardCubit extends Cubit<RewardState> {
  RewardCubit() : super(RewardInitState());

  RewardRepository rewardRepository = RewardRepository();

  // void deleteReward({required int RewardId}) async {
  //   emit(RewardLoadingState());
  //   await RewardRepository
  //       .deleteRewardApi(RewardId: RewardId)
  //       .then((value) {
  //     if (value.status) {
  //         emit(RewardDeletedState());

  //     } else {
  //       emit(RewardErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(RewardErrorState(message: "Server error"));
  //   });
  // }
  void getRewardDummy({required int currentPage}) async {
    emit(RewardLoadingState());

    Future.delayed(Duration(seconds: 2)).then((value) {
List<RewardModel> rewards = [
    RewardModel(
      id: '001',
      code: 'WELCOME10',
      discountAmount: 10.0,
      expiryDate: DateTime.now().add(Duration(days: 15)),
      title: 'Welcome Discount',
      image: 'https://example.com/welcome.jpg',
    ),
    RewardModel(
      id: '002',
      code: 'SAVE20',
      discountAmount: 20.0,
      expiryDate: DateTime.now().add(Duration(days: 30)),
      title: 'Save 20%',
      image: 'https://example.com/save20.jpg',
    ),
    RewardModel(
      id: '003',
      code: 'FREESHIP',
      discountAmount: 0.0, // Free shipping
      expiryDate: DateTime.now().add(Duration(days: 10)),
      title: 'Free Shipping',
      image: 'https://example.com/freeship.jpg',
    ),
    RewardModel(
      id: '004',
      code: 'SPRING25',
      discountAmount: 25.0,
      expiryDate: DateTime.now().add(Duration(days: 45)),
      title: 'Spring Sale',
      image: 'https://example.com/spring25.jpg',
    ),
    RewardModel(
      id: '005',
      code: 'BLACKFRIDAY50',
      discountAmount: 50.0,
      expiryDate: DateTime.now().add(Duration(days: 60)),
      title: 'Black Friday Sale',
      image: 'https://example.com/blackfriday50.jpg',
    ),
  ];

      emit(RewardLoadedState(
          rewardResponseModel: RewardResponseModel(
              status: true,
              message: "loaded",
              data: rewards,
              pagination: PaginationModel(currentPage: 1))));
    }).onError((e, _) {});

    // await rewardRepository
    //     .getRewardListApi(currentPage: currentPage)
    //     .then((value) {
    //   if (value.status) {
    //     if (value.data != null) {
    //       emit(RewardLoadedState(rewardResponseModel: value));
    //     } else {
    //       emit(RewardErrorState());
    //     }
    //   } else {
    //     emit(RewardErrorState(message: value.message));
    //   }
    // }).onError((error, stackTrace) {
    //   emit(RewardErrorState(message: "Server error"));
    // });
  }

  void getReward({required int currentPage}) async {
    emit(RewardLoadingState());
    await rewardRepository
        .getRewardListApi(currentPage: currentPage)
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(RewardLoadedState(rewardResponseModel: value));
        } else {
          emit(RewardErrorState(message: serverErrorMessage));
        }
      } else {
        emit(RewardErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(RewardErrorState(message: "Server error"));
    });
  }

  //  void getReward({required int regionId}) async {
  //   emit(RewardLoadingState());
  //   await rewardRepository
  //       .getRewardListApi(regionId:regionId )
  //       .then((value) {
  //     if (value.status) {
  //       if (value.data != null) {
  //         emit(RewardLoadedState(RewardResponseModel: value));
  //       } else {
  //         emit(RewardErrorState());
  //       }
  //     } else {
  //       emit(RewardErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(RewardErrorState(message: "Server error"));
  //   });
  // }

  // void saveReward({required MultipartRequest multiPartRequest}) async {
  //   emit(RewardLoadingState());
  //   await RewardRepository
  //       .saveRewardApi(
  //     multiPartRequest: multiPartRequest,
  //   )
  //       .then((value) {
  //     if (value.status) {
  //       emit(RewardSavedState());
  //     } else {
  //       emit(RewardErrorState(message: value.message));
  //     }
  //   }).onError((error, stackTrace) {
  //     emit(RewardErrorState(message: "Server error"));
  //   });
  // }

  //    getRewardDetailsReward({required int id}) async {    emit(RewardLoadingState());
  //   await RewardRepository.getRewardDetailsApi(id: id).then((value) {
  //     emit(RewardDetailLoaded(data: value.description.toString()));
  //   }).onError((error, stackTrace) {
  //     emit(RewardErrorState(error_message: "Server error"));

  //   });
  // }
}
