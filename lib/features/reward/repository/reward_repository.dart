import 'dart:async';
import 'dart:typed_data';

import 'package:http/http.dart';

import 'package:rooo_driver/features/reward/models/reward_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class RewardRepository {
  Future<RewardResponseModel> getRewardListApi(
      {required int currentPage}) async {
    return RewardResponseModel.from<PERSON>son(await handleResponse(
        await buildHttpResponse('Rewards', method: HttpMethod.GET)));
  }
  //   Future<RewardTypeResponseModel> getRewardTypeListApi(
  //     {required int regionId}) async {
  //   return RewardTypeResponseModel.fromJson(await handleResponse(
  //       await buildHttpResponse('service-list/?region_id=${regionId}', method: HttpMethod.GET)));
  // }

      Future<StatusMessageModel> deleteRewardApi(
      {required int RewardId}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('Rewards/delete/' + RewardId.toString(), method: HttpMethod.POST)));
  }

  //   Future<WebviewDataModel> getAdvertisementDetailsApi({required  int id}) async {
  //   return WebviewDataModel.fromJson(await handleResponse(
  //       await buildHttpResponse('advertisement-details/' + id.toString(), method: HttpMethod.GET)));
  // }

  Future<StatusMessageModel> saveRewardApi(
      {required MultipartRequest multiPartRequest}) async {
    return await StatusMessageModel.fromJson(await sendMultiPartApi(
        multiPartRequest: multiPartRequest, ));
  }
  

  Future sendMultiPartRequest(
    MultipartRequest multiPartRequest,
  ) async {
    String? result;
    multiPartRequest.headers.addAll(buildHeaderTokens());

    StreamedResponse response = await multiPartRequest.send();
    if (response.statusCode == 200) {
      Uint8List responseData = await response.stream.toBytes();
      result = String.fromCharCodes(responseData);
    }
    return result;
  }
}
