class RewardModel {
  String id;
  String code;
  double discountAmount;
  DateTime expiryDate;
  String title;  // New property for the title
  String image;  // New property for the image URL

  RewardModel({
    required this.id,
    required this.code,
    required this.discountAmount,
    required this.expiryDate,
    required this.title,  // Include title in the constructor
    required this.image,   // Include image in the constructor
  });

  // Convert a RewardModel object into a Map (for JSON serialization)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'discountAmount': discountAmount,
      'expiryDate': expiryDate.toIso8601String(),
      'title': title,       // Serialize title
      'image': image,       // Serialize image
    };
  }

  // Convert a Map (JSON) into a RewardModel object
  factory RewardModel.fromJson(Map<String, dynamic> json) {
    return RewardModel(
      id: json['id'],
      code: json['code'],
      discountAmount: json['discountAmount'],
      expiryDate: DateTime.parse(json['expiryDate']),
      title: json['title'],  // Deserialize title
      image: json['image'],  // Deserialize image
    );
  }
}
