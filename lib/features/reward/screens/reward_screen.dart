// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/features/reward/cubit/reward_cubit.dart';
// import 'package:rooo_driver/features/reward/models/reward_model.dart';
// import 'package:rooo_driver/features/reward/models/reward_response_model.dart';
// import 'package:rooo_driver/features/reward/widgets/driver_vehicle_card.dart';

// import 'package:rooo_driver/global/constants/app_enums.dart';
// import 'package:rooo_driver/global/constants/constants.dart';
// import 'package:rooo_driver/global/widgets/screen_body.dart';

// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/utils/Constants.dart';

// class RewardScreen extends StatefulWidget {
//   const RewardScreen({super.key});

//   @override
//   State<RewardScreen> createState() => _RewardScreenState();
// }

// class _RewardScreenState extends State<RewardScreen> {
//   int _currentPage = 1;
//   int _totalPage = 1;
//   ScrollController _scrollController = ScrollController();

//   List<RewardModel> _RewardList = [];
//   String _emptyMesssage = "";

//   _onScrolling() {
//     _scrollController.addListener(() {
//       if (_scrollController.position.pixels ==
//           _scrollController.position.maxScrollExtent) {
//         if (_currentPage < _totalPage) {
//           _currentPage++;

//           _getRewardList(currentPage: _currentPage);
//         }
//       }
//     });
//   }



//   _getRewardList({required int currentPage}) {
//     BlocProvider.of<RewardCubit>(context).getRewardDummy(currentPage: currentPage);
//   }

//   _onDataLoaded({required RewardResponseModel RewardResponseModel}) {
//     _currentPage = RewardResponseModel.pagination?.currentPage ?? 1;
//     _totalPage = RewardResponseModel.pagination?.totalPages ?? 1;

//     _RewardList = RewardResponseModel.data as List<RewardModel> ?? [];
//     _emptyMesssage = RewardResponseModel.message.toString();
//   }

//   _onPullToRefresh() {
//     _currentPage = 1;
//     _init();
//   }

//   _init() {
//     _onScrolling();
//     _getRewardList(currentPage: _currentPage);
//   }

//   _dispose() {
//     _scrollController.dispose();
//   }

//   @override
//   void initState() {
//     super.initState();
//     _init();
//   }

//   @override
//   void dispose() {
//     GlobalMethods.removeSavedScreen();
//     super.dispose();
//     _dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: RoooAppbar(title: "Rewards"),
//       // floatingActionButton: FloatingActionButton(
//       //     child: Icon(Icons.add),
//       //     onPressed: () {
//       //       GlobalMethods.pushScreen(
//       //           context: context,
//       //           screen: AddRewardsScreen(),
//       //           screenIdentifier: ScreenIdentifier.addRewardScreen);
//       //     }),
//       body: BlocConsumer<RewardCubit, RewardState>(
//         listener: (context, state) {
//           if (state is RewardLoadedState) {
//             _onDataLoaded(RewardResponseModel: state.rewardResponseModel);
//           } else if (state is RewardErrorState) {
//             GlobalMethods.handleError(
//               context: context,
//             );
//           } else if (state is RewardDeletedState) {
//             GlobalMethods.succesToast(context, "Reward deleted succesfully");
//             _init();
//           }
//         },
//         builder: (context, state) {
//           return ScreenBody(
//               onPullToRefresh: () async => await _onPullToRefresh(),
//               isLoading: state is RewardLoadingState,
//               isEmpty: _RewardList.isEmpty,
//               emptyMessage: _emptyMesssage,
//               child: AnimationLimiter(
//                 child: ListView.separated(
//                   padding: screenPadding,
//                   shrinkWrap: true,
//                   separatorBuilder: (context, index) => height10,
//                   itemCount: _RewardList.length,
//                   itemBuilder: (BuildContext context, int index) {
//                     RewardModel data = _RewardList[index];

//                     return AnimationConfiguration.staggeredList(
//                       position: index,
//                       duration: const Duration(milliseconds: 1000),
//                       child: SlideAnimation(
//                         verticalOffset: 50.0,
//                         child: FadeInAnimation(
//                             child: RewardCard(
//                                 // onEdit: () {
//                                 //   GlobalMethods.pushScreen(
//                                 //       context: context,
//                                 //       screen: AddRewardsScreen(
//                                 //         RewardModel: data,
//                                 //       ),
//                                 //       screenIdentifier:
//                                 //           ScreenIdentifier.addRewardScreen);
//                                 // },
//                                 // ondelete: () {
//                                 //   GlobalMethods.showConfirmationDialog(
//                                 //       context: context,
//                                 //       positiveAction: () {
//                                 //         _deleteReward(RewardId: data.id);
//                                 //       },
//                                 //       title:
//                                 //           "Are you sure you want to delete this Reward");
//                                 // },
//                                 data: data)),
//                       ),
//                     );
//                   },
//                 ),
//               ));
//         },
//       ),
//     );
//   }
// }



// //////////////////////////////////////////////
// /////////////////////////////////////////////
// ////////////////////////////////////////////
// ///////////////////////////////////////////

