
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/help/models/help_response_model.dart';
import 'package:rooo_driver/features/help/repository/help_repository.dart';



abstract class HelpState {}

class HelpInitState extends HelpState {}

class HelpLoadingState extends HelpState {}

class HelpDeletedState extends HelpState {}

class HelpDetailLoadedState extends HelpState {
  final String message;

  HelpDetailLoadedState({required this.message});
}

class HelpDetailLoaded extends HelpState {
  final String data;

  HelpDetailLoaded({required this.data});
}

class HelpLoadedState extends HelpState {
  final WebViewDataResponseModel helpResponseModel;

  HelpLoadedState({
    required this.helpResponseModel,
  });
}

class HelpErrorState extends HelpState {
  final String message;

  HelpErrorState({required this.message});
}

class HelpCubit extends Cubit<HelpState> {
  HelpCubit() : super(HelpInitState());

  HelpRepository helpRepository = HelpRepository();

  void getHelp({required int current_page}) async {
    emit(HelpLoadingState());
    await helpRepository.getHelpListApi(page: current_page).then((value) {
      emit(HelpLoadedState(helpResponseModel: value));
    }).onError((error, stackTrace) {
      emit(HelpErrorState(message: "Server error"));
    });
  }

  Future<String> getHelpDetailsHelp({required int id}) async {
    String result = "";
    emit(HelpLoadingState());
    await helpRepository.getHelpDetailsApi(id: id).then((value) {
      result = value.toString();
      emit(HelpDetailLoaded(data: value.toString()));
    }).onError((error, stackTrace) {
      emit(HelpErrorState(message: "Server error"));
      result = "Server error";
    });
    return result;
  }
}
