import 'package:rooo_driver/features/help/models/help_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class WebViewDataResponseModel {
  PaginationModel? pagination;
  List<WebviewDataModel>? data;
  String? message;

  WebViewDataResponseModel({this.data, this.pagination, this.message});

  factory WebViewDataResponseModel.fromJson(Map<String, dynamic> json) {
    return WebViewDataResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => WebviewDataModel.fromJson(e))
                .toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["message"] = this.message;
    return datas;
  }
}
