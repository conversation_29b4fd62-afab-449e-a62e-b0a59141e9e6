import 'package:http/http.dart';

import 'package:rooo_driver/features/help/models/help_response_model.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class HelpRepository {
  Future<WebViewDataResponseModel> getHelpListApi({required int page}) async {
    return WebViewDataResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('help-list', method: HttpMethod.GET)));
  }

  Future<String?> getHelpDetailsApi({required int id}) async {
    Response response =
        await buildHttpResponse('help-details/' + id.toString());

    if ((response.statusCode >= 200 && response.statusCode <= 206)) {
      // if (response.body.isJson()) {
      //   var json = jsonDecode(response.body);
      //   try {
      //     return json;
      //   } catch (e) {
      //     return null;
      //   }
      // }
      // return null;
      return response.body;
    } else {
      return null;
    }
  }
}
