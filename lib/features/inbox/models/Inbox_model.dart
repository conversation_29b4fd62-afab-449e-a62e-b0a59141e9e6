import 'package:rooo_driver/features/inbox/models/inbox_response_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class InboxResponseModel {
  PaginationModel? pagination;
  List<InboxModel>? data;
  String? message;

  InboxResponseModel({this.data, this.pagination, this.message});

  factory InboxResponseModel.fromJson(Map<String, dynamic> json) {
    return InboxResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List).map((e) => InboxModel.fromJson(e)).toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["message"] = this.message;
    return datas;
  }
}

class RewardResponseModel {
  PaginationModel? pagination;
  List<RewardModel>? data;
  String? message;

  RewardResponseModel({this.data, this.pagination, this.message});

  factory RewardResponseModel.fromJson(Map<String, dynamic> json) {
    return RewardResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => RewardModel.fromJson(e))
                .toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["message"] = this.message;
    return datas;
  }
}

class RewardModel {
  final int id;
  final String title;
  final String description;
  final String userType;
  final String? userId;
  final String type;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  bool isRead;

  RewardModel({
    required this.id,
    required this.title,
    required this.description,
    required this.userType,
    this.userId,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.isRead,
  });

  // From JSON
  factory RewardModel.fromJson(Map<String, dynamic> json) {
    return RewardModel(
      id: json['id'],
      title: json['title'],
      isRead: json['is_read'] ?? false,
      description: json['description'],
      userType: json['user_type'],
      userId: json['user_id'],
      type: json['type'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      deletedAt: json['deleted_at'] != null
          ? DateTime.parse(json['deleted_at'])
          : null,
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'user_type': userType,
      'user_id': userId,
      'type': type,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }
}
