class InboxModel {

  int?id;
  String? description;
  bool?is_read;
  String? title;
    String? type;

  String ?imageURL;
  String ?created_at;

  InboxModel({this.description,this.is_read,    this.title, this.imageURL, this.created_at});

  InboxModel.fromJson(Map<String, dynamic> json) {
    title = json['title'];
        type = json['type'];

    imageURL = json['imageURL'];
    description = json['description'];
    id = json['id'];
    is_read = json['is_read'];
    created_at = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
        data['type'] = this.type;

    data['imageURL'] = this.imageURL;
    data['description'] = this.description;
    data['id'] = this.id;
    data['is_read'] = this.is_read;
    data['created_at'] = this.created_at;
    return data;
  }
}

