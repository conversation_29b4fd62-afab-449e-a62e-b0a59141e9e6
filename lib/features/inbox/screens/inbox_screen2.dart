import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/inbox/widgets/reward_card.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/inbox/cubit/inbox_cubit.dart';
import 'package:rooo_driver/features/inbox/models/Inbox_model.dart';
import 'package:rooo_driver/features/inbox/models/inbox_response_model.dart';
import 'package:rooo_driver/features/inbox/widgets/inbox_card.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Constants.dart';

class InboxScreen extends StatefulWidget {
  const InboxScreen({super.key});

  @override
  State<InboxScreen> createState() => _InboxScreenState();
}

class _InboxScreenState extends State<InboxScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  ValueNotifier<bool> _isGift = ValueNotifier(false);

  List<InboxModel> _inboxList = [];
  List<RewardModel> _rewardList = [];

  String _emptyMesssage = "";
  String type = "inbox";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getInboxList(currentPage: _currentPage);
        }
      }
    });
  }

  _getInboxList({required int currentPage}) {
    BlocProvider.of<InboxCubit>(context).getInbox(current_page: currentPage);
  }

  _getrewardList({required int currentPage}) {
    BlocProvider.of<InboxCubit>(context).getReward(current_page: currentPage);
  }

  _onDataLoaded({required InboxResponseModel inboxResponseModel}) {
    _currentPage = inboxResponseModel.pagination?.currentPage ?? 1;
    _totalPage = inboxResponseModel.pagination?.totalPages ?? 1;
    _inboxList = inboxResponseModel.data ?? [];
    _emptyMesssage = inboxResponseModel.message.toString();
  }

  _onRewardLoaded({required RewardResponseModel rewardResponseModel}) {
    _currentPage = rewardResponseModel.pagination?.currentPage ?? 1;
    _totalPage = rewardResponseModel.pagination?.totalPages ?? 1;
    _rewardList = rewardResponseModel.data ?? [];
    _emptyMesssage = rewardResponseModel.message.toString();
    _isGift.value = true;
  }

  _onReadInbox({required int id}) async {
    await BlocProvider.of<InboxCubit>(context).isRead(id: id);
    getAndApplyCounters();
  }

  _onRewardRead({required int id}) {
    BlocProvider.of<InboxCubit>(context).isRewardRead(id: id);
  }

  _onDeleteInbox({required int id}) {
    BlocProvider.of<InboxCubit>(context).deleteInbox(id: id);
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getInboxList(currentPage: _currentPage);
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: _isGift.value ? "Available Offers" : language.inBoxTxt,
        actionIconList: [
          Padding(
            padding: const EdgeInsets.only(right: 18),
            child: ValueListenableBuilder<bool>(
              valueListenable: _isGift,
              builder: (context, state, _) {
                return AppButton(
                  backgroundColor: state ? Colors.white : Colors.grey.shade300,
                  text: state ? "Inbox" : "Offers",
                  onPressed: ()  async  {
                    if (state) {
                      _getInboxList(currentPage: _currentPage);
                      _isGift.value = false;
                    } else {
                      _getrewardList(currentPage: _currentPage);
                      _isGift.value = true;
                    }
                    setState(() {});
                  },
                  icon: Icon(Icons.wallet_giftcard_outlined),
                );
              },
            ),
          )
        ],
      ),
      body: BlocConsumer<InboxCubit, InboxState>(
        listener: (context, state) {
          if (state is InboxLoadedState) {
            _onDataLoaded(inboxResponseModel: state.inboxResponseModel);
          }
          if (state is RewardLoadedState) {
            _onRewardLoaded(rewardResponseModel: state.rewardResponseModel);
          }
          if (state is InboxErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is InboxDeletedState) {
            GlobalMethods.succesToast(context, state.message);
            _getInboxList(currentPage: _currentPage);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is InboxLoadingState,
              isEmpty: _inboxList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ValueListenableBuilder<bool>(
                      valueListenable: _isGift,
                      builder: (context, value, child) {
                        if (value) {
                          return AnimationLimiter(
                            child: ListView.separated(
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              padding: EdgeInsets.only(top: 16),
                              separatorBuilder: (context, index) => height10,
                              itemCount: _rewardList.length,
                              itemBuilder: (BuildContext context, int index) {
                                RewardModel data = _rewardList[index];

                                return AnimationConfiguration.staggeredList(
                                  position: index,
                                  duration: const Duration(milliseconds: 1000),
                                  child: SlideAnimation(
                                    verticalOffset: 50.0,
                                    child: FadeInAnimation(
                                        child: RewardCard(
                                      data: data,
                                      onRead: () {
                                        setState(() {
                                          data.isRead = true;
                                        });
                                        _onRewardRead(id: data.id!);
                                      },
                                    )),
                                  ),
                                );
                              },
                            ),
                          );
                        }

                        return AnimationLimiter(
                          child: ListView.separated(
                            physics: NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            padding: EdgeInsets.only(top: 16),
                            separatorBuilder: (context, index) => height10,
                            itemCount: _inboxList.length,
                            itemBuilder: (BuildContext context, int index) {
                              InboxModel data = _inboxList[index];

                              return AnimationConfiguration.staggeredList(
                                position: index,
                                duration: const Duration(milliseconds: 1000),
                                child: SlideAnimation(
                                  verticalOffset: 50.0,
                                  child: FadeInAnimation(
                                      child: InboxCard(
                                          data: data,
                                          onRead: () {
                                            setState(() {
                                              data.is_read = true;
                                            });
                                            _onReadInbox(id: data.id!);
                                          },
                                          onDelete: () {
                                            _onDeleteInbox(id: data.id!);
                                          })),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    )

                    // AnimationLimiter(
                    //   child: ListView.separated(
                    //     physics: NeverScrollableScrollPhysics(),
                    //     shrinkWrap: true,
                    //     separatorBuilder: (context, index) => height10,
                    //     itemCount: _inboxList.length,
                    //     itemBuilder: (BuildContext context, int index) {
                    //       InboxModel data = _inboxList[index];

                    //       return AnimationConfiguration.staggeredList(
                    //         position: index,
                    //         duration: const Duration(milliseconds: 1000),
                    //         child: SlideAnimation(
                    //           verticalOffset: 50.0,
                    //           child: FadeInAnimation(
                    //               child: InboxCard(
                    //                   data: data,
                    //                   onRead: () {
                    //                     _onReadInbox(id: data.id!);
                    //                   },
                    //                   onDelete: () {
                    //                     _onDeleteInbox(id: data.id!);
                    //                   })),
                    //         ),
                    //       );
                    //     },
                    //   ),
                    // ),
                  ],
                ),
              ));
        },
      ),
    );
  }
}
