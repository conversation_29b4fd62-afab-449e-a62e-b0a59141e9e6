import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/features/inbox/models/Inbox_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/screens/inbox_detail_screen.dart';

class RewardCard extends StatelessWidget {
  final RewardModel data;
  final void Function() onRead;
  // final void Function() onDelete;

  const RewardCard({
    super.key,
    required this.data,
    required this.onRead,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onRead();
        GlobalMethods.pushScreen(
            context: context,
            screen: InboxDetailsScreen(
              id: data.id,
              title: data.title.toString(),
            ),
            screenIdentifier: ScreenIdentifier.InboxDetailsScreen);
        // launchScreen(
        //     context,
        //     InboxDetailsScreen(
        //       id: data.id!,
        //       title: data.title.toString(),
        //     ));
      },
      child: Container(
        decoration: BoxDecoration(
          color: data.isRead! ? Colors.grey.shade50 : Colors.orange[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: data.isRead! ? Colors.grey.shade200 : Colors.orange.shade200,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.03),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
        child: ListTile(
          contentPadding: EdgeInsets.all(12),
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: data.isRead! ? Colors.grey.shade100 : Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Icon(
                Icons.card_giftcard,
                color: data.isRead! ? Colors.grey.shade400 : Colors.orange,
                size: 24,
              ),
            ),
          ),
          title: Text(
            data.title,
            style: TextStyle(
              fontSize: 16,
              color: AppColors.primaryBlackColor,
              fontWeight: data.isRead! ? FontWeight.normal : FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _formatDateTime(data.createdAt.toString()),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          trailing: !data.isRead!
              ? Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.orange,
                  ),
                )
              : null,
        ),
      ),
    );
  }

  String _formatDateTime(String dateTimeStr) {
    try {
      final DateTime dateTime = DateTime.parse(dateTimeStr);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays == 0) {
        if (difference.inHours == 0) {
          return '${difference.inMinutes} min ago';
        }
        return '${difference.inHours}h ago';
      } else if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return dateTimeStr;
    }
  }
}
