import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/features/inbox/models/inbox_response_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/screens/inbox_detail_screen.dart';

class InboxCard extends StatelessWidget {
  final InboxModel data;
  final void Function() onRead;
  final void Function() onDelete;

  const InboxCard(
      {super.key,
      required this.data,
      required this.onRead,
      required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onRead();
        GlobalMethods.pushScreen(
            context: context,
            screen: InboxDetailsScreen(
              id: data.id!,
              title: data.title.toString(),
            ),
            screenIdentifier: ScreenIdentifier.InboxDetailsScreen);
        // launchScreen(
        //     context,
        //     InboxDetailsScreen(
        //       id: data.id!,
        //       title: data.title.toString(),
        //     ));
      },
      child: Container(
        decoration: BoxDecoration(
          color: data.is_read! ? Colors.grey.shade200 : Colors.green[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: data.is_read! ? Colors.grey.shade200 : Colors.green.shade200,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.03),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
        child: ListTile(
            contentPadding: EdgeInsets.all(12),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!data.is_read!)
                  Container(
                    width: 8,
                    height: 8,
                    margin: EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.green,
                    ),
                  ),
                InkWell(
                  onTap: onDelete,
                  child: Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
            leading: 
            
            
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: data.is_read! ? Colors.grey.shade100 : Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Icon(
                  data.is_read! ? Icons.mark_as_unread_outlined : Icons.mail,
                  color: data.is_read! ? Colors.grey.shade400 : Colors.green,
                  size: 24,
                ),
              ),
            ),
            title: Text(
              data.title!,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.primaryBlackColor,
                fontWeight: data.is_read! ? FontWeight.normal : FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                _formatDateTime(data.created_at!),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
        ),
      ),
    );
  }

  String _formatDateTime(String dateTimeStr) {
    try {
      final DateTime dateTime = DateTime.parse(dateTimeStr);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays == 0) {
        if (difference.inHours == 0) {
          if (difference.inMinutes == 0) {
            return 'Just now';
          }
          return '${difference.inMinutes} min ago';
        }
        return '${difference.inHours}h ago';
      } else if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return dateTimeStr;
    }
  }
}
