import 'package:http/http.dart';

import 'package:rooo_driver/features/inbox/models/Inbox_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class InboxRepository {
  Future<InboxResponseModel> getInboxListApi({required int page}) async {
    return InboxResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('get-inbox', method: HttpMethod.GET)));
  }

  Future<RewardResponseModel> getRewardListApi({required int page}) async {
    return RewardResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('reward', method: HttpMethod.GET)));
  }

  Future<StatusMessageModel> deleteInboxApi({required int id}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse("delete-inbox-message" + "/" + "$id",
            method: HttpMethod.POST)));
  }

  Future<StatusMessageModel> isReadInboxApi({required int id}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse("read-inbox-message" + "/" + "$id",
            method: HttpMethod.POST)));
  }

  Future<StatusMessageModel> isRewardReadApi({required int id}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse("read-inbox-message" + "/" + "$id",
            method: HttpMethod.POST)));
  }

  Future<String?> getInboxDetailsApi({required int id}) async {
    Response response =
        await buildHttpResponse('inbox-details/' + id.toString());

    if ((response.statusCode >= 200 && response.statusCode <= 206)) {
      // if (response.body.isJson()) {
      //   var json = jsonDecode(response.body);
      //   try {
      //     return json;
      //   } catch (e) {
      //     return null;
      //   }
      // }
      // return null;
      return response.body;
    } else {
      return null;
    }
  }
}
