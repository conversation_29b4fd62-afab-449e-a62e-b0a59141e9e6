import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/inbox/models/Inbox_model.dart';
import 'package:rooo_driver/features/inbox/repository/inbox_repository.dart';

abstract class InboxState {}

class InboxInitState extends InboxState {}

class InboxLoadingState extends InboxState {}

class InboxDeletedState extends InboxState {
  final String message;

  InboxDeletedState({required this.message});
}
// class InboxDetailLoadedState extends InboxState {
//   final String message;

//   InboxDetailLoadedState({required this.message});
// }

class InboxDetailLoaded extends InboxState {
  final String data;

  InboxDetailLoaded({required this.data});
}

class InboxLoadedState extends InboxState {
  final InboxResponseModel inboxResponseModel;

  InboxLoadedState({
    required this.inboxResponseModel,
  });
}

class RewardLoadedState extends InboxState {
  final RewardResponseModel rewardResponseModel;

  RewardLoadedState({
    required this.rewardResponseModel,
  });
}

class InboxErrorState extends InboxState {
  final String message;

  InboxErrorState({required  this.message,});
}

class InboxCubit extends Cubit<InboxState> {
  InboxCubit() : super(InboxInitState());

  InboxRepository inboxRepository = InboxRepository();

  void getInbox({required int current_page}) async {
    emit(InboxLoadingState());
    await inboxRepository.getInboxListApi(page: current_page).then((value) {
      emit(InboxLoadedState(inboxResponseModel: value));
    }).onError((error, stackTrace) {
      emit(InboxErrorState(message: "Server error"));
    });
  }

  void getReward({required int current_page}) async {
    emit(InboxLoadingState());
    await inboxRepository.getRewardListApi(page: current_page).then((value) {
      emit(RewardLoadedState(rewardResponseModel: value));
    }).onError((error, stackTrace) {
      emit(InboxErrorState(message: "Server error"));
    });
  }

  Future<void> isRead({required int id}) async {
    await inboxRepository
        .isReadInboxApi(id: id)
        .then((value) => null)
        .onError((error, stackTrace) {
      emit(InboxErrorState(message: "Server error"));
    });
  }

  isRewardRead({required int id}) {
    inboxRepository
        .isRewardReadApi(id: id)
        .then((value) => null)
        .onError((error, stackTrace) {
      emit(InboxErrorState(message: "Server error"));
    });
  }

  void deleteInbox({required int id}) async {
    emit(InboxLoadingState());
    await inboxRepository.deleteInboxApi(id: id).then((value) {
      if (value.status) {
        emit(InboxDeletedState(message: value.message));
      } else {
        emit(InboxErrorState(message: value.message.toString()));
      }
    }).onError((error, stackTrace) {
      emit(InboxErrorState(message: "Server error"));
    });
  }

  Future getInboxDetailsInbox({required int id}) async {
    emit(InboxLoadingState());
    await inboxRepository.getInboxDetailsApi(id: id).then((value) {
      emit(InboxDetailLoaded(data: value.toString()));
    }).onError((error, stackTrace) {
      emit(InboxErrorState(message: "Server error"));
    });
  }

  // Future getInboxDetailData({
  //   required int id,
  // }) async {
  //   emit(InboxLoadingState());
  //   inboxRepository.g(id).then((value) {

  //     if(value!=null){

  //          emit(InboxDetailData(data: value));
  //     }

  //   }).onError((error, stackTrace) {
  //     emit(InboxErrorState(error_message: "Server error"));
  //   });
  // }
}
