// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:rooo_driver/features/homepage/repository/homepage_repository.dart';
// import 'package:rooo_driver/global/models/UserDetailModel.dart';
// import 'package:rooo_driver/global/models/ride_model.dart';

// abstract class OnlineOfflineState {}

// class OnlineOfflineInitState extends OnlineOfflineState {}

// class OnlineOfflineLoadingState extends OnlineOfflineState {}

// class OnlineOfflineErrorState extends OnlineOfflineState {
//   final String? message;
//   final String? unmessage;

//   OnlineOfflineErrorState(
//       {this.message, this.unmessage});
// }

// class OnlineOfflineDataLoadedState extends OnlineOfflineState {
//   final UserDetailModel data;

//   OnlineOfflineDataLoadedState({required this.data});
// }

// class OnlineOfflineCubit extends Cubit<OnlineOfflineState> {
//   OnlineOfflineCubit() : super(OnlineOfflineInitState());

//   HomepageRepository _homePageRepository = HomepageRepository();

//   checkOnlineOfflineStatus() {
//     emit(OnlineOfflineLoadingState());
//     _homePageRepository.getUserDetailApi().then((value) {
//       if (value.status) {
//         if (value.data != null) {
//           emit(OnlineOfflineDataLoadedState(data: value.data!));
//         } else {
//           emit(OnlineOfflineErrorState());
//         }
//       } else {
//         emit(OnlineOfflineErrorState(message: value.message));
//       }
//     }).onError((e, _) {
//       emit(OnlineOfflineErrorState(message: e.toString()));
//     });
//   }


// }
