// import 'dart:convert';
// import 'dart:developer';

// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:mqtt_client/mqtt_client.dart';
// import 'package:mqtt_client/mqtt_server_client.dart';
// import 'package:rooo_driver/features/faq/cubit/help_cubit.dart';
// import 'package:rooo_driver/features/homepage/model/homepage_data_model.dart';
// import 'package:rooo_driver/features/homepage/repository/homepage_repository.dart';
// import 'package:rooo_driver/global/export/app_export.dart';
// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/global/models/UserDetailModel.dart';
// import 'package:rooo_driver/global/models/on_ride_request_model.dart';
// import 'package:rooo_driver/global/models/ride_model.dart';
// import 'package:rooo_driver/global/state/global_state.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/screens/dashboard/dBloc.dart';
// import 'package:rooo_driver/utils/Constants.dart';

// abstract class HomePageState {}

// class HomePageInitState extends HomePageState {}

// class HomePageLoadingState extends HomePageState {}

// class HomePageErrorState extends HomePageState {
//   final String? message;
//   final String? unmessage;

//   HomePageErrorState({this.message, this.unmessage});
// }



// class OnlineOfflineDataLoadedStatehomepage extends HomePageState {
//   final UserData data;

//   OnlineOfflineDataLoadedStatehomepage({required this.data});
// }

// class HomePageDataLoadedStatehomepage extends HomePageState {
//   final HomePageDataModel homePageData;

//   HomePageDataLoadedStatehomepage({required this.homePageData});
// }

// class UnverifiedDriverStatehomepage extends HomePageState {}




// class HomePageCubit extends Cubit<HomePageState> {

//   HomePageCubit() : super(HomePageInitState()) {
//     // mqttForUser();
//   }

//   HomepageRepository _homePageRepository = HomepageRepository();

//   getHomePageData() {
//     emit(HomePageLoadingState());
//     _homePageRepository.getHomePageDataApi().then((value) {
//       if (value.status) {
//         if (value.data != null) {
//           emit(HomePageDataLoadedStatehomepage(homePageData: value.data!));
//         } else {
//           emit(HomePageErrorState());
//         }
//       } else {
//         emit(HomePageErrorState(message: value.message));
//       }
//     }).onError((e, _) {
//       emit(HomePageErrorState(message: e.toString()));
//     });
//   }

//   checkOnlineOfflineStatus({required String userId}) {
//     emit(HomePageLoadingState());
//     _homePageRepository.getUserDetailApi(userId: userId).then((value) {
//       if (value.status) {
//         if (value.data != null) {
//           emit(OnlineOfflineDataLoadedStatehomepage(data: value.data!));
//         } else {
//           emit(HomePageErrorState());
//         }
//       } else {
//         emit(HomePageErrorState(message: value.message));
//       }
//     }).onError((e, _) {
//       emit(HomePageErrorState(message: e.toString()));
//     });
//   }

//   changeOnlineOfflineStatus(
//       {required int makeOnline, required String userId}) async {
//     emit(HomePageLoadingState());

//     if (makeOnline == 1) {
//       bool isVerified = false;
//       await _homePageRepository.getUserDetailApi(userId: userId).then((value) {
//         if (value.status) {
//           if (value.data != null) {
//             if (value.data?.isVerifiedDriver == 0) {
//               emit(UnverifiedDriverStatehomepage());
//             } else {
//               isVerified = true;
//             }
//           } else {
//             emit(HomePageErrorState());
//           }
//         } else {
//           emit(HomePageErrorState(message: value.message));
//         }
//       }).onError((e, _) {
//         emit(HomePageErrorState(message: e.toString()));
//       });

//       if (isVerified) {
//         await _homePageRepository
//             .updateDriverOnlineUpdateStatusApi(1)
//             .then((value) {
//           emit(OnlineState());
//           if(value.status){
//             emit(OnlineState());

//           }else{
//             emit(HomePageErrorState(message: value.message));
//           }
//         }).onError((e, _) {
//           emit(HomePageErrorState(message: e.toString()));
//         });
//       }
//     } else {
//       await _homePageRepository
//           .updateDriverOnlineUpdateStatusApi(0)
//           .then((value) {
//         emit(OfflineState());

//         if(value.status){
//           emit(OfflineState());

//         }else{
//           emit(HomePageErrorState(message: value.message));
//         }
//       }).onError((e, _) {
//         emit(HomePageErrorState(message: e.toString()));
//       });
//     }
//   }

//   // getCurrenRide() {
//   //   emit(HomePageLoadingState());
//   //   _homePageRepository.getCurrentRideRequestApi().then((value) {
//   //     if (value.status) {
//   //       OnRideRequest onRideRequest = OnRideRequest(
//   //           otp: "1234",
//   //           riderName: "Rider",
//   //           status: ARRIVING,
//   //           reached_otp: "4567",
//   //           driverName: "Driver",
//   //           id: 88,
//   //           regionId: 55,
//   //           endLatitude: "31.3260",
//   //           endLongitude: "75.5762",
//   //           startLatitude: "30.9010",
//   //           startLongitude: "75.8573",
//   //           endAddress: "jalandhar",
//   //           startAddress: "Ludhiana");
//   //       emit(RideLo(
//   //           rideModel: RideModel(onRideRequest: onRideRequest)));

//   //       // if (value.data != null) {
//   //       //   if (value.data!.onRideRequest!.status == ARRIVING) {
//   //       //     emit(RideArrivingState(rideModel: value.data!));
//   //       //   } else {
//   //       //     emit(HomePageErrorState());
//   //       //   }
//   //       // } else {
//   //       //   emit(HomePageErrorState());
//   //       // }
//   //     } else {
//   //       emit(HomePageErrorState(message: value.message));
//   //     }
//   //   }).onError((e, _) {
//   //     emit(HomePageErrorState());
//   //   });
//   // }

//   // timeOutNewRide({
//   //   required Map timeout_request,
//   // }) async {
//   //   emit(HomePageLoadingState());

//   //   await Future.delayed(Duration(seconds: 5)).then((value) {
//   //     emit(NewRideMissedState());
//   //   });

//   //   // await _homePageRepository
//   //   //     .acceptDeclineNewrideApi(request: timeout_request)
//   //   //     .then((value) {
//   //   //   if (value.status ?? false) {
//   //   //     emit(NewRideMissedState());
//   //   //   } else {
//   //   //     emit(HomePageErrorState(message: value.message));
//   //   //   }
//   //   // }).onError((error, stackTrace) {
//   //   //   emit(HomePageErrorState(message: "Server error"));
//   //   // });
//   // }

//   // accepDeclineNewRideREquest({
//   //   required Map request,
//   //   required bool is_decline,
//   // }) async {
//   //   emit(HomePageLoadingState());
//   //   await Future.delayed(Duration(seconds: 5)).then((value) {
//   //     if (true) {
//   //       if (is_decline) {
//   //         emit(NewRideDeclineState());
//   //       } else {
//   //         emit(NewRideAcceptState());
//   //       }
//   //     } else {
//   //       emit(NewRideErrorState(message: value.message));
//   //     }
//   //   });

//   //   // await _homePageRepository
//   //   //     .acceptDeclineNewrideApi(request: request)
//   //   //     .then((value) {
//   //   //   if (value.status ?? false) {
//   //   //     if (is_decline) {
//   //   //       emit(NewRideDeclineState());
//   //   //     } else {
//   //   //       emit(NewRideAcceptState());
//   //   //     }
//   //   //   } else {
//   //   //     emit(NewRideErrorState(message: value.message));
//   //   //   }
//   //   // }).onError((error, stackTrace) {
//   //   //   emit(NewRideErrorState(message: "Server error"));
//   //   // });
//   // }



//   @override
//   Future<void> close() {
  
//     // TODO: implement close
//     return super.close();
//   }
// }
