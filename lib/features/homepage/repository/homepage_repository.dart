import 'package:rooo_driver/features/homepage/model/homepage_response_model.dart';
import 'package:rooo_driver/features/homepage/model/user_detail_response_model.dart';
import 'package:rooo_driver/global/models/current_ride_response_model.dart';
import 'package:rooo_driver/model/LoginResponse.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class HomepageRepository {
  Future<CurrentRideResponseModel> getCurrentRideRequestApi() async {
    return CurrentRideResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('current-riderequest',
            method: HttpMethod.GET)));
  }

  Future<HomePageResponseModel> getHomePageDataApi() async {
    return HomePageResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('get-driver-dashboard',
            method: HttpMethod.GET)));
  }

  Future<UserDetailResponseModel> getUserDetailApi(
      {required String? userId}) async {
    return UserDetailResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('user-detail?id=$userId',
            method: HttpMethod.GET)));
  }

  Future<LoginResponse> updateDriverOnlineUpdateStatusApi(
      int status, bool driverStatusChangeCase) async {
    Map request;

    request = {
      "status": "active",
    };

    if (driverStatusChangeCase) {
      request = {
        "status": "active",
        "is_online": status,
      };
    }

    return LoginResponse.fromJson(await handleResponse(await buildHttpResponse(
        'update-user-status',
        method: HttpMethod.POST,
        request: request)));
  }
}
