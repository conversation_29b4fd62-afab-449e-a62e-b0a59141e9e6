import 'package:rooo_driver/features/advertisements/models/advertisement_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class AdvertismentResponseModel extends ResponseModel<AdvertisementModel> {
  AdvertismentResponseModel({
    required bool status,
    required String message,
    required AdvertisementModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory AdvertismentResponseModel.fromJson(Map<String, dynamic> json) {
    return AdvertismentResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? AdvertisementModel.fromJson(json['data'])
          : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
