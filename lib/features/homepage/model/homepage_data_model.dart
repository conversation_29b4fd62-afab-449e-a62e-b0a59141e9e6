import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/model/FAQ.dart';

class HomePageDataModel {
  num? rating;
  List<OnRideRequest>? scheduledRides;
  List<FAQ>? blogs;
  int? region_id;
  num? today_earning;
  int? ride_accept_decline_duration_for_driver_in_second;
  String? account_delete_instructions_for_driver;
  String? about_us_instruction_driver;
  bool? is_profile_complete;
  bool? is_live_photo_required;

  HomePageDataModel(
      {this.blogs,
      this.rating,
      this.about_us_instruction_driver,
      this.scheduledRides,
      this.region_id,
      this.is_live_photo_required,
      this.account_delete_instructions_for_driver,
      this.today_earning,
      this.ride_accept_decline_duration_for_driver_in_second,
      this.is_profile_complete});

  factory HomePageDataModel.fromJson(Map<String, dynamic> json) {
    return HomePageDataModel(
      scheduledRides: json["scheduledRides"] != null
          ? (json["scheduledRides"] as List)
              .map((e) => OnRideRequest.fromJson(e))
              .toList()
          : null,
      blogs: json["blogs"] != null
          ? (json["blogs"] as List).map((e) => FAQ.fromJson(e)).toList()
          : null,
      region_id: json["region_id"],
      is_profile_complete: json['is_profile_complete'],
      today_earning: json["today_earning"],
      account_delete_instructions_for_driver:
          json["account_delete_instructions_for_driver"],
      about_us_instruction_driver: json["about_us_instruction_driver"],
      rating: json["rating"],
      is_live_photo_required: json["is_live_photo_required"],
      ride_accept_decline_duration_for_driver_in_second:
          json["ride_accept_decline_duration_for_driver_in_second"],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["scheduledRides"] = this.scheduledRides;
    datas["blogs"] = this.blogs;
    datas["rating"] = this.rating;

    datas["region_id"] = this.region_id;
    datas["today_earning"] = this.today_earning;
    datas["is_live_photo_required"] = this.is_live_photo_required;

    datas["about_us_instruction_driver"] = this.about_us_instruction_driver;
    datas["account_delete_instructions_for_driver"] =
        this.account_delete_instructions_for_driver;
    datas["ride_accept_decline_duration_for_driver_in_second"] =
        this.ride_accept_decline_duration_for_driver_in_second;
    return datas;
  }
}

// class HomePageDataModel {
//   ScRidesBlogsModel? response;

//   HomePageDataModel({this.response});

//   factory HomePageDataModel.fromJson(Map<String, dynamic> json) {
//     return HomePageDataModel(
//       response: json["response"] != null
//           ? ScRidesBlogsModel.fromJson(json["response"])
//           : null,
//     );
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> datas = new Map<String, dynamic>();
//     datas["response"] = this.response;
//     return datas;
//   }
// }

class CountersData {
  int careCount;
  int notificationCount;
  int inboxCount;
  int opportunityCount;
  int rideIssuesCoount;

  CountersData({
    required this.careCount,
    required this.notificationCount,
    required this.inboxCount,
    required this.opportunityCount,
    required this.rideIssuesCoount,
  });

  factory CountersData.fromMap(Map<String, dynamic> map) {
    return CountersData(
      careCount: map['care_count'],
      notificationCount: map['notification_count'],
      inboxCount: map['inbox_count'],
      opportunityCount: map['opportunity_count'],
      rideIssuesCoount: map['ride_issues_count'],
    );
  }
}

class CounterDataResponse {
  bool status;
  String message;
  CountersData? data;

  CounterDataResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory CounterDataResponse.fromMap(Map<String, dynamic> json) {
    return CounterDataResponse(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? CountersData.fromMap(json['data']) : null,
    );
  }
}
