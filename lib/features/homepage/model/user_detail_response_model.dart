import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class UserDetailResponseModel extends ResponseModel<UserData> {
  UserDetailResponseModel({
    required bool status,
    required String message,
    required UserData? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory UserDetailResponseModel.fromJson(Map<String, dynamic> json) {
    return UserDetailResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? UserData.fromJson(json['data'])
          : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
