import 'package:rooo_driver/features/homepage/model/homepage_data_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class HomePageResponseModel extends ResponseModel<HomePageDataModel> {
  HomePageResponseModel({
    required bool status,
    required String message,
    required HomePageDataModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  factory HomePageResponseModel.fromJson(Map<String, dynamic> json) {
    return HomePageResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? HomePageDataModel.fromJson(json['data'])
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
