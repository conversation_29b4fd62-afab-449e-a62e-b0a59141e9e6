import 'package:rooo_driver/features/login/models/login_response_model.dart';
import 'package:rooo_driver/global/models/current_ride_response_model.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class InitialRepository{


   Future<CurrentRideResponseModel> getCurrentRideRequestApi() async {
    return CurrentRideResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('current-riderequest',
            method: HttpMethod.GET)));
  }


Future<LoginResponseModel> updateDriverStatusApi(Map request) async {
  return LoginResponseModel.fromJson(await handleResponse(await buildHttpResponse(
      'update-user-status',
      method: HttpMethod.POST,
      request: request)));
}
}