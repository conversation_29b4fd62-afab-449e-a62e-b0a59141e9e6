// import 'dart:async';
// import 'dart:convert';
// import 'dart:developer';

// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:mqtt_client/mqtt_client.dart';
// import 'package:mqtt_client/mqtt_server_client.dart';
// import 'package:rooo_driver/features/initial/repository/initial_repository.dart';
// import 'package:rooo_driver/features/ride_flow/model/multiple_stop_model.dart';
// import 'package:rooo_driver/global/constants/app_cred.dart';
// import 'package:rooo_driver/global/export/app_export.dart';
// import 'package:rooo_driver/global/models/ride_model.dart';
// import 'package:rooo_driver/global/state/global_state.dart';
// import 'package:rooo_driver/main.dart';
// import 'package:rooo_driver/model/AppSettingModel.dart';
// import 'package:rooo_driver/utils/Constants.dart';

// abstract class InitialState {}

// class InitState extends InitialState {}

// class InitialLoadingState extends InitialState {}

// class InitialErrorState extends InitialState {
//   final String? message;
//   final String? unmessage;

//   InitialErrorState({this.message, this.unmessage});
// }








// class InitialCubit extends Cubit<InitialState> {
//   InitialRepository _initialRepository = InitialRepository();
//   InitialCubit() : super(InitState()) {
//     // mqttForUser();
//   }




//   Future startDeviceTracking() async {
//     // GlobalMethods.infoToast(context,  "start Device tracking");
//     await get_device_current_location();

//     if (GlobalState.driver_device_timer == null) {
//       GlobalState.driver_device_timer =
//           Timer.periodic(Duration(seconds: 6), (timer) async {
//         await get_device_current_location();
//       });
//     }
//   }

//   // Future get_device_current_location() async {
//   //   await GlobalMethods.checkLocationPermission(
//   //     onPermissionGranted: () async {
//   //       await Geolocator.getCurrentPosition(
//   //         desiredAccuracy: LocationAccuracy.high,
//   //       ).then((value) {
//   //         GlobalState.driverPosition = LatLng(value.latitude, value.longitude);
//   //         emit(DeviceLocationUpdatedState(
//   //             location: GlobalState.driverPosition!));

//   //         // GlobalState.driverPosition=null;
//   //         // GlobalMethods.infoToast(context,  "Updated new location");
//   //       });
//   //     },
//   //   );
//   // }



//   @override
//   Future<void> close() {
//     // TODO: implement close
//     return super.close();
//   }
// }
