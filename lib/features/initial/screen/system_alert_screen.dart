import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';

class SystemAlertDeniedScreen extends StatefulWidget {
  @override
  SystemAlertDeniedScreenState createState() => SystemAlertDeniedScreenState();
}

class SystemAlertDeniedScreenState extends State<SystemAlertDeniedScreen>
    with WidgetsBindingObserver {
  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      PermissionStatus locationStatus = await Permission.systemAlertWindow.status;
      if (locationStatus != PermissionStatus.granted) {
        GlobalMethods.errorToast(context,   "Status is still " + locationStatus.name);
        return;
      } else {
        Navigator.pop(context, locationStatus);
      }
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.black,
      ),
      body: Padding(
        padding: screenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              'images/location_permission.gif',
              width: 130,
            ),
            const SizedBox(
              height: 20,
            ),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                  text:
                      "System alert permission is required for the app's functionality. Please allow location permission to \"",
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                  ),
                  children: [
                    TextSpan(
                      text: "Always",
                      style: TextStyle(fontWeight: FontWeight.w700),
                    ),
                    TextSpan(
                      text: "\" based on your device OS.",
                    ),
                  ]),
            ),
            ElevatedButton(
              onPressed: () {
                openAppSettings();
              },
              child: Text("Open settings"),
            ),
          ],
        ),
      ),
    );
  }
}
