// import 'package:flutter/material.dart';
// import 'package:permission_handler/permission_handler.dart';

// class PermissionsPage extends StatefulWidget {
//   @override
//   _PermissionsPageState createState() => _PermissionsPageState();
// }

// class _PermissionsPageState extends State<PermissionsPage> {
//   // Map each permission with its status (false = not granted, true = granted)
//   final Map<Permission, bool> _permissionsStatus = {
//     Permission.camera: false,
//     Permission.microphone: false,
//     Permission.location: false,
//     Permission.locationAlways: false, // for background location
//     Permission.bluetooth: false,
//     Permission.bluetoothConnect: false,
//     Permission.notification: false,
//     Permission.storage: false, // WRITE_EXTERNAL_STORAGE
//     Permission.phone: false, // READ_PHONE_STATE
//     Permission.bluetoothScan: false, // BLE scan
//     Permission.bluetoothAdvertise: false, // BLE advertise
//     Permission.contacts: false, // READ_CONTACTS
//     Permission.systemAlertWindow: false, // SYSTEM_ALERT_WINDOW
//   };

//   @override
//   void initState() {
//     super.initState();
//     _checkPermissions(); // Check permissions on page load
//   }

//   Future<void> _checkPermissions() async {
//     for (var permission in _permissionsStatus.keys) {
//       final status = await permission.status;
//       setState(() {
//         _permissionsStatus[permission] = status.isGranted;
//       });
//     }
//   }

//   Future<void> _requestPermission(Permission permission) async {
//     final status = await permission.request();
//     if (status.isDenied) {
//       _showPermissionDeniedDialog(permission); // Handle denied permissions
//     } else if (status.isPermanentlyDenied || status.isRestricted) {
//       _showSettingsDialog(); // Handle permanently denied/restricted permissions
//     } else {
//       _checkPermissions(); // Refresh permission status
//     }
//   }

//   void _showPermissionDeniedDialog(Permission permission) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: Text('Permission Denied'),
//           content: Text(
//             'The app needs access to ${permission.toString().split('.').last} to function properly. Please grant the permission.',
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: Text('Cancel'),
//             ),
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 _requestPermission(permission); // Retry permission request
//               },
//               child: Text('Retry'),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   void _showSettingsDialog() {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: Text('Permission Permanently Denied'),
//           content: Text(
//             'You have permanently denied this permission. Please go to settings to grant the permission manually.',
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: Text('Cancel'),
//             ),
//             TextButton(
//               onPressed: () {
//                 openAppSettings(); // Open app settings
//                 Navigator.of(context).pop();
//               },
//               child: Text('Open Settings'),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Permissions Status'),
//       ),
//       body: ListView(
//         children: _permissionsStatus.entries.map((entry) {
//           return ListTile(
//             title: Text(entry.key.toString().split('.').last.toUpperCase()),
//             trailing: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Icon(
//                   entry.value ? Icons.check : Icons.close,
//                   color: entry.value ? Colors.green : Colors.red,
//                 ),
//                 if (!entry.value)
//                   IconButton(
//                     icon: Icon(Icons.refresh),
//                     onPressed: () {
//                       _requestPermission(entry.key); // Request permission
//                     },
//                   ),
//               ],
//             ),
//           );
//         }).toList(),
//       ),
//     );
//   }
// }
