import 'package:rooo_driver/features/reward/models/reward_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class RewardResponseModel extends ResponseModel<List<RewardModel>> {
  RewardResponseModel({
    required bool status,
    required String message,
    required List<RewardModel>? data,
    required PaginationModel? pagination
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory RewardResponseModel.fromJson(Map<String, dynamic> json) {
    return RewardResponseModel(
       pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
      status: json['status'],
      message: json['message'],
     data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => RewardModel.fromJson(e))
                .toList()
            : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((e) => e.toJson()).toList(), // Convert each VehicleModel to JSON
      'pagination': pagination?.toJson(), // Convert PaginationModel to JSON if not null
    };
  }

}
