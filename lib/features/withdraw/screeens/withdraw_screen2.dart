// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
// import 'package:rooo_driver/components/rooo_appbar.dart';
// import 'package:rooo_driver/features/WithDraw/cubit/WithDraw_cubit.dart';
// import 'package:rooo_driver/features/WithDraw/models/WithDraw_model.dart';
// import 'package:rooo_driver/features/WithDraw/models/WithDraw_response_model.dart';
// import 'package:rooo_driver/features/WithDraw/widgets/driver_vehicle_card.dart';

// import 'package:rooo_driver/global/constants/app_enums.dart';
// import 'package:rooo_driver/global/constants/constants.dart';
// import 'package:rooo_driver/global/widgets/screen_body.dart';

// import 'package:rooo_driver/global/globalMethods/global_method.dart';
// import 'package:rooo_driver/utils/Constants.dart';

// class WithdrawScreen2 extends StatefulWidget {
//   const WithdrawScreen2({super.key});

//   @override
//   State<WithdrawScreen2> createState() => _WithdrawScreen2State();
// }

// class _WithdrawScreen2State extends State<WithdrawScreen2> {
//   int _currentPage = 1;
//   int _totalPage = 1;
//   ScrollController _scrollController = ScrollController();

//   List<WithDrawModel> _WithDrawList = [];
//   String _emptyMesssage = "";

//   _onScrolling() {
//     _scrollController.addListener(() {
//       if (_scrollController.position.pixels ==
//           _scrollController.position.maxScrollExtent) {
//         if (_currentPage < _totalPage) {
//           _currentPage++;

//           _getWithDrawList(currentPage: _currentPage);
//         }
//       }
//     });
//   }



//   _getWithDrawList({required int currentPage}) {
//     BlocProvider.of<WithDrawCubit>(context).getWithDrawDummy(currentPage: currentPage);
//   }

//   _onDataLoaded({required Withdawres withDrawResponseModel}) {
//     _currentPage = WithDrawResponseModel.pagination?.currentPage ?? 1;
//     _totalPage = WithDrawResponseModel.pagination?.totalPages ?? 1;

//     _WithDrawList = WithDrawResponseModel.data as List<WithDrawModel> ?? [];
//     _emptyMesssage = WithDrawResponseModel.message.toString();
//   }

//   _onPullToRefresh() {
//     _currentPage = 1;
//     _init();
//   }

//   _init() {
//     _onScrolling();
//     _getWithDrawList(currentPage: _currentPage);
//   }

//   _dispose() {
//     _scrollController.dispose();
//   }

//   @override
//   void initState() {
//     super.initState();
//     _init();
//   }

//   @override
//   void dispose() {
//     GlobalMethods.removeSavedScreen();
//     super.dispose();
//     _dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: RoooAppbar(title: "WithDraws"),
//       // floatingActionButton: FloatingActionButton(
//       //     child: Icon(Icons.add),
//       //     onPressed: () {
//       //       GlobalMethods.pushScreen(
//       //           context: context,
//       //           screen: AddWithDrawsScreen(),
//       //           screenIdentifier: ScreenIdentifier.addWithDrawScreen);
//       //     }),
//       body: BlocConsumer<WithDrawCubit, WithDrawState>(
//         listener: (context, state) {
//           if (state is WithDrawLoadedState) {
//             _onDataLoaded(WithDrawResponseModel: state.WithDrawResponseModel);
//           } else if (state is WithDrawErrorState) {
//             GlobalMethods.handleError(
//               context: context,
//             );
//           } else if (state is WithDrawDeletedState) {
//             GlobalMethods.succesToast(context, "WithDraw deleted succesfully");
//             _init();
//           }
//         },
//         builder: (context, state) {
//           return ScreenBody(
//               onPullToRefresh: () async => await _onPullToRefresh(),
//               isLoading: state is WithDrawLoadingState,
//               isEmpty: _WithDrawList.isEmpty,
//               emptyMessage: _emptyMesssage,
//               child: AnimationLimiter(
//                 child: ListView.separated(
//                   padding: screenPadding,
//                   shrinkWrap: true,
//                   separatorBuilder: (context, index) => height10,
//                   itemCount: _WithDrawList.length,
//                   itemBuilder: (BuildContext context, int index) {
//                     WithDrawModel data = _WithDrawList[index];

//                     return AnimationConfiguration.staggeredList(
//                       position: index,
//                       duration: const Duration(milliseconds: 1000),
//                       child: SlideAnimation(
//                         verticalOffset: 50.0,
//                         child: FadeInAnimation(
//                             child: WithDrawCard(
//                                 // onEdit: () {
//                                 //   GlobalMethods.pushScreen(
//                                 //       context: context,
//                                 //       screen: AddWithDrawsScreen(
//                                 //         WithDrawModel: data,
//                                 //       ),
//                                 //       screenIdentifier:
//                                 //           ScreenIdentifier.addWithDrawScreen);
//                                 // },
//                                 // ondelete: () {
//                                 //   GlobalMethods.showConfirmationDialog(
//                                 //       context: context,
//                                 //       positiveAction: () {
//                                 //         _deleteWithDraw(WithDrawId: data.id);
//                                 //       },
//                                 //       title:
//                                 //           "Are you sure you want to delete this WithDraw");
//                                 // },
//                                 data: data)),
//                       ),
//                     );
//                   },
//                 ),
//               ));
//         },
//       ),
//     );
//   }
// }



// //////////////////////////////////////////////
// /////////////////////////////////////////////
// ////////////////////////////////////////////
// ///////////////////////////////////////////

