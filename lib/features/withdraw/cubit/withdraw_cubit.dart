// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:http/http.dart';

// import 'package:rooo_driver/model/PaginationModel.dart';

// abstract class WithDrawState {}

// class WithDrawInitState extends WithDrawState {}

// class WithDrawLoadingState extends WithDrawState {}

// class WithDrawDeletedState extends WithDrawState {}

// class WithDrawDetailLoadedState extends WithDrawState {
//   final String message;

//   WithDrawDetailLoadedState({required this.message});
// }

// class WithDrawSavedState extends WithDrawState {}

// class WithDrawDeleteState extends WithDrawState {}

// class WithDrawDetailLoaded extends WithDrawState {
//   final String data;

//   WithDrawDetailLoaded({required this.data});
// }

// class WithDrawLoadedState extends WithDrawState {
//   final WithDrawResponseModel WithDrawResponseModel;

//   WithDrawLoadedState({
//     required this.WithDrawResponseModel,
//   });
// }
// // class WithDrawLoadedState extends WithDrawState {
// //   final WithDrawResponseModel WithDrawResponseModel;

// //   WithDrawLoadedState({
// //     required this.WithDrawResponseModel,
// //   });
// // }

// class WithDrawErrorState extends WithDrawState {
//   final String? message;
//   final String? unmessage;
//   WithDrawErrorState({this.message, this.unmessage});
// }

// class WithDrawCubit extends Cubit<WithDrawState> {
//   WithDrawCubit() : super(WithDrawInitState());

//   WithDrawRepository WithDrawRepository = WithDrawRepository();

//   // void deleteWithDraw({required int WithDrawId}) async {
//   //   emit(WithDrawLoadingState());
//   //   await WithDrawRepository
//   //       .deleteWithDrawApi(WithDrawId: WithDrawId)
//   //       .then((value) {
//   //     if (value.status) {
//   //         emit(WithDrawDeletedState());

//   //     } else {
//   //       emit(WithDrawErrorState(message: value.message));
//   //     }
//   //   }).onError((error, stackTrace) {
//   //     emit(WithDrawErrorState(message: "Server error"));
//   //   });
//   // }
//   void getWithDrawDummy({required int currentPage}) async {
//     emit(WithDrawLoadingState());

//     Future.delayed(Duration(seconds: 2)).then((value) {
// List<WithDrawModel> WithDraws = [
//     WithDrawModel(
//       id: '001',
//       code: 'WELCOME10',
//       discountAmount: 10.0,
//       expiryDate: DateTime.now().add(Duration(days: 15)),
//       title: 'Welcome Discount',
//       image: 'https://example.com/welcome.jpg',
//     ),
//     WithDrawModel(
//       id: '002',
//       code: 'SAVE20',
//       discountAmount: 20.0,
//       expiryDate: DateTime.now().add(Duration(days: 30)),
//       title: 'Save 20%',
//       image: 'https://example.com/save20.jpg',
//     ),
//     WithDrawModel(
//       id: '003',
//       code: 'FREESHIP',
//       discountAmount: 0.0, // Free shipping
//       expiryDate: DateTime.now().add(Duration(days: 10)),
//       title: 'Free Shipping',
//       image: 'https://example.com/freeship.jpg',
//     ),
//     WithDrawModel(
//       id: '004',
//       code: 'SPRING25',
//       discountAmount: 25.0,
//       expiryDate: DateTime.now().add(Duration(days: 45)),
//       title: 'Spring Sale',
//       image: 'https://example.com/spring25.jpg',
//     ),
//     WithDrawModel(
//       id: '005',
//       code: 'BLACKFRIDAY50',
//       discountAmount: 50.0,
//       expiryDate: DateTime.now().add(Duration(days: 60)),
//       title: 'Black Friday Sale',
//       image: 'https://example.com/blackfriday50.jpg',
//     ),
//   ];

//       emit(WithDrawLoadedState(
//           WithDrawResponseModel: WithDrawResponseModel(
//               status: true,
//               message: "loaded",
//               data: WithDraws,
//               pagination: PaginationModel(currentPage: 1))));
//     }).onError((e, _) {});

//     // await WithDrawRepository
//     //     .getWithDrawListApi(currentPage: currentPage)
//     //     .then((value) {
//     //   if (value.status) {
//     //     if (value.data != null) {
//     //       emit(WithDrawLoadedState(WithDrawResponseModel: value));
//     //     } else {
//     //       emit(WithDrawErrorState());
//     //     }
//     //   } else {
//     //     emit(WithDrawErrorState(message: value.message));
//     //   }
//     // }).onError((error, stackTrace) {
//     //   emit(WithDrawErrorState(message: "Server error"));
//     // });
//   }

//   void getWithDraw({required int currentPage}) async {
//     emit(WithDrawLoadingState());
//     await WithDrawRepository
//         .getWithDrawListApi(currentPage: currentPage)
//         .then((value) {
//       if (value.status) {
//         if (value.data != null) {
//           emit(WithDrawLoadedState(WithDrawResponseModel: value));
//         } else {
//           emit(WithDrawErrorState());
//         }
//       } else {
//         emit(WithDrawErrorState(message: value.message));
//       }
//     }).onError((error, stackTrace) {
//       emit(WithDrawErrorState(message: "Server error"));
//     });
//   }

//   //  void getWithDraw({required int regionId}) async {
//   //   emit(WithDrawLoadingState());
//   //   await WithDrawRepository
//   //       .getWithDrawListApi(regionId:regionId )
//   //       .then((value) {
//   //     if (value.status) {
//   //       if (value.data != null) {
//   //         emit(WithDrawLoadedState(WithDrawResponseModel: value));
//   //       } else {
//   //         emit(WithDrawErrorState());
//   //       }
//   //     } else {
//   //       emit(WithDrawErrorState(message: value.message));
//   //     }
//   //   }).onError((error, stackTrace) {
//   //     emit(WithDrawErrorState(message: "Server error"));
//   //   });
//   // }

//   // void saveWithDraw({required MultipartRequest multiPartRequest}) async {
//   //   emit(WithDrawLoadingState());
//   //   await WithDrawRepository
//   //       .saveWithDrawApi(
//   //     multiPartRequest: multiPartRequest,
//   //   )
//   //       .then((value) {
//   //     if (value.status) {
//   //       emit(WithDrawSavedState());
//   //     } else {
//   //       emit(WithDrawErrorState(message: value.message));
//   //     }
//   //   }).onError((error, stackTrace) {
//   //     emit(WithDrawErrorState(message: "Server error"));
//   //   });
//   // }

//   //    getWithDrawDetailsWithDraw({required int id}) async {    emit(WithDrawLoadingState());
//   //   await WithDrawRepository.getWithDrawDetailsApi(id: id).then((value) {
//   //     emit(WithDrawDetailLoaded(data: value.description.toString()));
//   //   }).onError((error, stackTrace) {
//   //     emit(WithDrawErrorState(error_message: "Server error"));

//   //   });
//   // }
// }
