
import 'package:rooo_driver/model/EarningListModelWeek.dart';

class EarningModel {
  final String? todayDate;
  final String ?fromDate;
  final String? toDate;
  final List<WeekReport>? weekReport; // Make weekReport nullable
  final int ?totalRideCount;
  final double ?totalRideFare;
  final int ?totalOfferReferral;
  final num ?totalEarnings;
  final num ?today_earnings;

  EarningModel( {
    this.today_earnings,
     this.todayDate,
     this.fromDate,
     this.toDate,
    this.weekReport, // Update weekReport to be nullable
     this.totalRideCount,
     this.totalRideFare,
     this.totalOfferReferral,
     this.totalEarnings,
  });

  factory EarningModel.fromJson(Map<String, dynamic> json) {
    var weekReportList = json['week_report'] as List?;
    List<WeekReport>? weekReport;
    if (weekReportList != null) {
      weekReport = weekReportList.map((report) => WeekReport.fromJson(report)).toList();
    }

    return EarningModel(
      today_earnings:json["today_earnings"],
      todayDate: json['today_date'],
      fromDate: json['from_date'],
      toDate: json['to_date'],
      weekReport: weekReport,
      totalRideCount: json['total_ride_count'],
      totalRideFare: json['total_ride_fare'].toDouble(),
      totalOfferReferral: json['total_offer_referral'],
      totalEarnings: json['total_earnings'],
    );
  }
}