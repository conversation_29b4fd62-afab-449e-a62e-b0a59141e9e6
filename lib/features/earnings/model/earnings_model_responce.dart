class EarningListModelWeek {

  num? total_ride_fare;
  num? total_offer_referral;
  num? today_earnings;
  num? today_ride_request;
  num?total_earnings;
  String? today;

  // List<WeekReport>? weekReport;

  EarningListModelWeek({
    this.total_ride_fare,
    this.total_offer_referral,
    this.today_earnings,
    this.today_ride_request,
    this.today,this.total_earnings
 
  });

  factory EarningListModelWeek.fromJson(Map<String, dynamic> json) {
    return EarningListModelWeek(
      total_ride_fare: json['total_ride_fare'],
      total_offer_referral: json['total_offer_referral'],
      today_earnings: json['today_earnings'],
      today_ride_request: json['today_ride_request'],
      today: json['today'],
      total_earnings: json['total_earnings'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total_ride_fare'] = this.total_ride_fare;
    data['total_offer_referral'] = this.total_offer_referral;
    data['today_earnings'] = this.today_earnings;
    data['today_ride_request'] = this.today_ride_request;
    data['today'] = this.today;
    data['total_earnings'] = this.total_earnings;

    return data;
  }
}

// class WeekReport {
//   num? amount;
//   String? date;
//   String? day;

//   WeekReport({this.amount, this.date, this.day});

//   factory WeekReport.fromJson(Map<String, dynamic> json) {
//     return WeekReport(
//       amount: json['amount'],
//       date: json['date'],
//       day: json['day'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['amount'] = this.amount;
//     data['date'] = this.date;
//     data['day'] = this.day;
//     return data;
//   }
// }
