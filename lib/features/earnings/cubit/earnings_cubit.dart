// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:rooo_driver/model/EarningListModelWeek.dart';

// abstract class EarningsState{}

// class EarningsInitState extends EarningsState{}

// class EarningsLoadingState extends EarningsState{}

// class EarningsTodayState extends EarningsState{

// }


// class EarningsLoadedState extends EarningsState{
//   final EarningListModelWeek earningsResponseModel;

//   EarningsLoadedState({
//     required this.earningsResponseModel
//   });
// }

// class EarningsErrorState extends EarningsState {
//   final String error_message;

//   EarningsErrorState({required this.error_message});
// }

// class EarningsCubit extends Cubit<EarningsState> {

//   EarningsCubit() : super (EarningsInitState());

//   earning


// }