// import 'package:map_launcher/map_launcher.dart';

import 'package:rooo_driver/components/setting_black_container.dart';
import 'package:rooo_driver/features/earnings/screens/earning_screen.dart';
import 'package:rooo_driver/features/ride_history/screens/ride_history_screen.dart';
import 'package:rooo_driver/features/way_bill/screens/way_bill_screen.dart';
import 'package:rooo_driver/features/withdraw/screeens/withdraw_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class EarningSettingScreen extends StatefulWidget {
  const EarningSettingScreen();

  @override
  State<EarningSettingScreen> createState() => _EarningSettingScreenState();
}

class _EarningSettingScreenState extends State<EarningSettingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: RoooAppbar(title: "Earnings"),
        body: ScreenBody(
            isLoading: false,
            isEmpty: false,
            emptyMessage: "No data",
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SettingsBlackContainer(
                      icon: Icon(Icons.history_toggle_off_sharp),
                      title: "Ride history",
                      onTap: () {
                        GlobalMethods.pushScreen(
                            context: context,
                            screen: RideHistoryScreen(),
                            screenIdentifier:
                                ScreenIdentifier.RideHistoryScreen);

                        // launchScreen(context, TermsAndConditionsScreen(),
                        //     pageRouteAnimation: PageRouteAnimation.Slide);
                      }),
                  SettingsBlackContainer(
                      icon: Icon(Icons.attach_money),
                      title: "Withdraw",
                      onTap: () {
                        GlobalMethods.pushScreen(
                            screen: WithDrawScreen(
                              onTap: () {},
                            ),
                            context: context,
                            screenIdentifier:
                                ScreenIdentifier.MapSettingScreen);

                        // launchScreen(context, TermsAndConditionsScreen(),
                        //     pageRouteAnimation: PageRouteAnimation.Slide);
                      }),
                  SettingsBlackContainer(
                      icon: Icon(Icons.receipt_long_sharp),
                      title: "Waybill",
                      onTap: () {
                        GlobalMethods.pushScreen(
                            context: context,
                            screen: WayBillScreen(),
                            screenIdentifier:
                                ScreenIdentifier.PasswordSettingScreen);

                        // launchScreen(context, TermsAndConditionsScreen(),
                        //     pageRouteAnimation: PageRouteAnimation.Slide);
                      }),
                  SettingsBlackContainer(
                      icon: Icon(Icons.currency_exchange_sharp),
                      title: "Earnings",
                      onTap: () {
                        GlobalMethods.pushScreen(
                            context: context,
                            screen: EarningScreen(),
                            screenIdentifier:
                                ScreenIdentifier.PasswordSettingScreen);

                        // launchScreen(context, TermsAndConditionsScreen(),
                        //     pageRouteAnimation: PageRouteAnimation.Slide);
                      }),
                ],
              ),
            )));
  }
}

//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////
class MyModel {
  String title;
  String type;

  MyModel({required this.title, required this.type});

  // Factory method to create a model from a JSON object
  factory MyModel.fromJson(Map<String, dynamic> json) {
    return MyModel(
      title: json['title'],
      type: json['type'],
    );
  }

  // Method to convert a model instance to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'type': type,
    };
  }
}
