import 'package:rooo_driver/features/earnings/screens/earning_report_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class EarningReportResponseModel extends ResponseModel<EarningReportModel> {
  EarningReportResponseModel({
    required bool status,
    required String message,
    required EarningReportModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory EarningReportResponseModel.fromJson(Map<String, dynamic> json) {
    return EarningReportResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? EarningReportModel.fromJson(json['data'])
          : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.to<PERSON><PERSON>(),
    };
  }
}
