import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/ios_padding.dart';
import 'package:rooo_driver/screens/BankAndWalletScreen.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';
import 'package:url_launcher/url_launcher.dart';

class ReportEarningScreen extends StatefulWidget {
  @override
  ReportEarningScreenState createState() => ReportEarningScreenState();
}

class ReportEarningScreenState extends State<ReportEarningScreen> {
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();

  DateTime? fromDate, toDate;
  // num? totalRide;
  num? totalReferrals;
  num? TotalEarnings;
  @override
  void initState() {
    super.initState();
  }

  void init() async {
    if (fromDateController.text.isNotEmpty &&
        toDateController.text.isNotEmpty) {
      appStore.setLoading(true);
      Map req = {
        "type": "report",
        "from_date": fromDateController.text.toString(),
        "to_date": toDateController.text.toString(),
      };
      await reportPdf(req: req).then((value) {
        appStore.setLoading(false);
        // totalRide = value.totalRideFare ?? 0.0;
        // totalReferrals = value.total_offer_referral ?? 0.0;
        // TotalEarnings = value.total_earnings ?? 0.0;

        if (value.status) {
          if (value.data != null) {
            launchUrl(Uri.parse(value.data!.link));
          }
        } else {
          GlobalMethods.errorToast(context, value.message);
        }

        setState(() {});
      }).catchError((error) {
        appStore.setLoading(false);

        log("Server error");
      });
    } else {
      GlobalMethods.infoToast(context, 'Please select "From" and "To" dates');
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) {
        return Stack(
          children: [
            SingleChildScrollView(
              padding: screenPadding,
              child: Column(
                children: [
                  Row(
                    children: [
                      SizedBox(
                        width: 50,
                        child: Text(
                          language.from,
                        ),
                      ),
                      SizedBox(width: 16),

                      Flexible(
                        child: AppTextField(
                          onTap: () async {
                            DateTime? FromPickedDate = await showDatePicker(
                              builder: IS_DARK_MODE_ON
                                  ? (context, child) {
                                      return Theme(
                                        data: Theme.of(context).copyWith(
                                          dialogBackgroundColor: Colors.white,
                                          colorScheme: ColorScheme.light(
                                            primary: Colors
                                                .black, // header background color
                                            onPrimary: Colors
                                                .white, // header text color
                                            onSurface:
                                                Colors.black, // body text color
                                          ),
                                          textButtonTheme: TextButtonThemeData(
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors
                                                  .black, // button text color
                                            ),
                                          ),
                                        ),
                                        child: child!,
                                      );
                                    }
                                  : null,
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(2010),
                              lastDate: DateTime.now(),
                            );
                            fromDate =
                                DateTime.parse(FromPickedDate.toString());
                            fromDateController.text =
                                dateToInfoString(FromPickedDate!)
                                    .toString()
                                    .substring(0, 10);
                            setState(() {});
                          },
                          controller: fromDateController,
                          textFieldType: TextFieldType.OTHER,
                          errorThisFieldRequired: language.thisFieldRequired,
                          decoration: inputDecoration(context,
                              hintTextStyle: TextStyle(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                              hint: language.fromDate,
                              suffixIcon: Icon(
                                Icons.calendar_today,
                                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                              )),
                        ),
                      ),

                      // Expanded(
                      //   child: DateTimePicker(

                      //     controller: fromDateController,
                      //     type: DateTimePickerType.date,
                      //     lastDate: DateTime.now(),
                      //     firstDate: DateTime(2010),
                      //     onChanged: (value) {
                      //       fromDate = DateTime.parse(value);
                      //       fromDateController.text = value;
                      //       setState(() {});
                      //     },
                      //     decoration: inputDecoration(context,
                      //              fillColor: colorSelector(darkModeColor: cementColor, lightmodeColor: extraLightGreyColor),
                      //       hintTextStyle: TextStyle(color: Colors.black),
                      //     hint: language.fromDate, suffixIcon: Icon(Icons.calendar_today,color: Colors.black,)),
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Row(
                    children: [
                      SizedBox(
                        width: 50,
                        child: Text(
                          language.to,
                        ),
                      ),
                      SizedBox(width: 16),

                      Flexible(
                        child: AppTextField(
                          onTap: () async {
                            DateTime? toPickedDate = await showDatePicker(
                              builder: IS_DARK_MODE_ON
                                  ? (context, child) {
                                      return Theme(
                                        data: Theme.of(context).copyWith(
                                          hintColor: Colors.black,
                                          dialogBackgroundColor: Colors.white,
                                          colorScheme: ColorScheme.light(
                                            primary: Colors
                                                .black, // header background color
                                            onPrimary: Colors
                                                .white, // header text color
                                            onSurface:
                                                Colors.black, // body text color
                                          ),
                                          textButtonTheme: TextButtonThemeData(
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors
                                                  .black, // button text color
                                            ),
                                          ),
                                        ),
                                        child: child!,
                                      );
                                    }
                                  : null,
                              context: context,
                              lastDate: DateTime.now(),
                              firstDate: fromDate ?? DateTime.now(),
                              initialDate: fromDate ?? DateTime.now(),
                            );
                            toDate = DateTime.parse(toPickedDate.toString());
                            toDateController.text =
                                dateToInfoString(toPickedDate!)
                                    .toString()
                                    .substring(0, 10);
                            // DateFormat("dd-MM-yyyy").parse(toPickedDate.toString()).toString();
                            // toPickedDate.toString().substring(0, 10);
                            setState(() {});
                          },
                          controller: toDateController,
                          textFieldType: TextFieldType.OTHER,
                          errorThisFieldRequired: language.thisFieldRequired,
                          decoration: inputDecoration(context,
                              hintTextStyle: TextStyle(color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black),
                              hint: language.toDate,
                              suffixIcon: Icon(
                                Icons.calendar_today,
                                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                              )),
                        ),
                      ),
                      // Expanded(
                      //   child: DateTimePicker(
                      //     controller: toDateController,
                      //     type: DateTimePickerType.date,
                      // lastDate: DateTime.now(),
                      // firstDate: fromDate ?? DateTime.now(),
                      //     onChanged: (value) {
                      //       toDate = DateTime.parse(value);
                      //       toDateController.text = value;
                      //       setState(() {});
                      //     },
                      //     decoration: inputDecoration(context,
                      //         hint: language.toDate,
                      //         fillColor: colorSelector(
                      //             darkModeColor: cementColor,
                      //             lightmodeColor: extraLightGreyColor),
                      //         hintTextStyle: TextStyle(color: Colors.black),
                      //         suffixIcon: Icon(
                      //           Icons.calendar_today,
                      //           color: Colors.black,
                      //         )),
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(height: 16),
                  AppButton(
                    width: MediaQuery.of(context).size.width,
                    text: language.confirm,
                    onPressed: () async {
                      init();
                    },
                  ),

                  (totalReferrals == null || TotalEarnings == null)
                      ? SizedBox()
                      : Column(
                          children: [
                            height20,
                            if (fromDateController.text.isNotEmpty &&
                                toDateController.text.isNotEmpty)
                              Text(
                                  '${fromDateController.text}   to  ${toDateController.text}',
                                  style: boldTextStyle()),
                            SizedBox(height: 16),
                            earningText(
                                title: language.totalRide,
                                amount: TotalEarnings),
                            SizedBox(height: 16),
                            earningText(
                                title: language.totalReferralsTxt,
                                amount: totalReferrals),
                            // SizedBox(height: 16),
                            // Divider(color: getWhiteColor()),
                            // earningText(
                            //     title: language.totalEarning,
                            //     amount: TotalEarnings),
                            // Divider(color: getWhiteColor()),
                            SizedBox(height: 16),
                          ],
                        ),
                  SizedBox(height: 16),

                  // Align(
                  //   alignment: Alignment.bottomCenter,
                  //   child: AppButtonWidget(
                  //     shapeBorder: RoundedRectangleBorder(
                  //         borderRadius: BorderRadius.circular(10)),
                  //     padding: EdgeInsets.symmetric(horizontal: 40),
                  //     text: language.cashOutTxt,
                  //     onTap: () {
                  //       launchScreen(
                  //           context,
                  //           BankAndWalletScreen(
                  //             isWalletScreen: true,
                  //           ));
                  //     },
                  //   ),
                  // )
                ],
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: IosPadding(
                child: AppButton(
                  text: language.cashOutTxt,
                  onPressed: () async  {
                    GlobalMethods.pushScreen(
                        context: context,
                        screen: BankAndWalletScreen(isWalletScreen: true),
                        screenIdentifier: ScreenIdentifier.BankAndWalletScreen);
                  },
                ),
              ),
            ),
            Visibility(
              visible: appStore.isLoading,
              child: Loader(),
            )
          ],
        );
      },
    );
  }
}
