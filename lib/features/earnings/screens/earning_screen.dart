
import 'package:flutter/material.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/earnings/model/earning_model.dart';
import 'package:rooo_driver/features/earnings/screens/report_earnings_screen.dart';
import 'package:rooo_driver/features/earnings/screens/today_earnigs_screen.dart';
import 'package:rooo_driver/features/earnings/screens/weekly_earnings_screen.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/EarningListModelWeek.dart';
import 'package:rooo_driver/utils/Common.dart';



class EarningScreen extends StatefulWidget {
  @override
  EarningScreenState createState() => EarningScreenState();
}

class EarningScreenState extends State<EarningScreen> with AutomaticKeepAliveClientMixin {
  EarningModel? earningListModelWeek;
  List<WeekReport> weekReport = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: RoooAppbar(
          title: language.earning,
        ),
        body: Column(
          children: [


            tabContainer(tabs: [
                  language.today,
                  language.weekly,
                  language.report,
                  ]),
           
            Expanded(
              child: TabBarView(
                children: [
                  TodayEarningScreen(),
                  WeeklyEarningScreen(),
                  ReportEarningScreen(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
