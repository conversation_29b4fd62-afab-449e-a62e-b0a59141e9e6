import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/EarningListModelWeek.dart';
import 'package:rooo_driver/screens/BankAndWalletScreen.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';

class TodayEarningScreen extends StatefulWidget {
  @override
  TodayEarningScreenState createState() => TodayEarningScreenState();
}

class TodayEarningScreenState extends State<TodayEarningScreen> {
  // num totalRide = 0;
  num totalReferrals = 0;
  num _todayEarnings = 0;
  List<RideRequestDetailsModel> _riderequestDetail = [];

  @override
  void initState() {
    appStore.setLoading(true);
    super.initState();
    init();
  }

  void init() async {
    Map req = {
      "type": "today",
    };
    await earningList(req: req).then((value) {
      appStore.setLoading(false);

      totalReferrals = value.totalOfferReferral ?? 0.0;
      _todayEarnings = value.todayEarnings ?? 0.0;
      _riderequestDetail = value.todayRideRequestDetails ?? [];

      setState(() {
        appStore.setLoading(false);
      });
    }).catchError((error) {
      appStore.setLoading(false);

      log("Server error");
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: screenPadding,
          child: SingleChildScrollView(
            child: Column(
              children: [
                Text('${printDate('${DateTime.now()}')}', style: TextStyle()),
                SizedBox(height: 16),
                SizedBox(height: 16),
                // earningText(title: language.totalRide, amount: totalRide),
                // SizedBox(height: 16),
                earningText(
                    title: language.totalReferralsTxt, amount: totalReferrals),
                SizedBox(height: 16),
                Divider(),
                earningText(
                    title: language.totalEarning, amount: _todayEarnings),
                Divider(),

                SizedBox(height: 16),

                ListView.separated(
                  physics: NeverScrollableScrollPhysics(),
                  separatorBuilder: (context, index) => Divider(),
                  shrinkWrap: true,
                  itemCount: _riderequestDetail.length,
                  itemBuilder: (context, index) {
                    RideRequestDetailsModel data = _riderequestDetail[index];
                    return Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Pickup"),
                            width20,
                            width20,
                            SizedBox(
                                width: 200,
                                child: Text(
                                  data.startAddress ?? "",
                                  style: AppTextStyles.text(),
                                ))
                          ],
                        ),
                        height10,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Drop"),
                            width20,
                            width20,
                            SizedBox(
                                width: 200,
                                child: Text(
                                  data.endAddress ?? "",
                                  style: AppTextStyles.text(),
                                ))
                          ],
                        ),
                        height10,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Earnings"),
                            width20,
                            width20,
                            SizedBox(
                                width: 200,
                                child: Text(
                                  "AUD " +
                                      data.payment!.driverCommission!
                                          .toStringAsFixed(2),
                                  style: AppTextStyles.text(),
                                ))
                          ],
                        )
                      ],
                    );
                  },
                ),
                height20,
                AppButton(
                  text: language.cashOutTxt,
                  onPressed: () async  {
                    GlobalMethods.pushScreen(
                        context: context,
                        screen: BankAndWalletScreen(isWalletScreen: true),
                        screenIdentifier: ScreenIdentifier.BankAndWalletScreen);
                  },
                )
              ],
            ),
          ),
        ),
        Visibility(
          visible: appStore.isLoading,
          child: loaderWidget(),
        )
      ],
    );
  }
}
