// import 'package:rooo_driver/features/earnings/model/earnings_model_responce.dart';
// import 'package:rooo_driver/network/NetworkUtils.dart';

// class EarningsRepository {
//   Future<EarningListModelWeek> getTodayEarningApi(
//     {required int currentpage,required String status  }
//   ) async{
//     return EarningListModelWeek.fromJson(await handleResponse(
//       await buildHttpResponse('earningToday-list?type=${status}&page=$currentpage',
//       method: HttpMethod.GET)
//     ));
//   }

//   Future<EarningListModelWeek> getWeeklyEarnings (
//     {required Map request}
//   ) async {
//     return EarningListModelWeek.fromJson(await handleResponse(
//       await bui
//     ))
//   }
// }