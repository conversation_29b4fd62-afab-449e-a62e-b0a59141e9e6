import 'dart:async';



import 'package:rooo_driver/features/online_offline_status/models/bank_info_response_model.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class OnlineOfflineTimesRepository {
Future<OnlineOfflineTimeResponseModel> getOnlineOfflineTimeApi() async {
  return OnlineOfflineTimeResponseModel.fromJson(await handleResponse(await buildHttpResponse(
      'online-time',
      method: HttpMethod.GET)));
}


  //   Future<WebviewDataModel> getAdvertisementDetailsApi({required  int id}) async {
  //   return WebviewDataModel.fromJson(await handleResponse(
  //       await buildHttpResponse('advertisement-details/' + id.toString(), method: HttpMethod.GET)));
  // }


  


}
