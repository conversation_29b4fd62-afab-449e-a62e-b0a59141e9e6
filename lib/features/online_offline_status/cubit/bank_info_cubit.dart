import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rooo_driver/features/online_offline_status/models/bank_info_response_model.dart';
import 'package:rooo_driver/features/online_offline_status/repository/bank_info_repository.dart';
import 'package:rooo_driver/model/BankListModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class OnlineOfflineTimeState {}

class OnlineOfflineTimeInitState extends OnlineOfflineTimeState {}

class OnlineOfflineTimeLoadingState extends OnlineOfflineTimeState {}



class OnlineOfflineTimeSavedState extends OnlineOfflineTimeState {}
class OnlineOfflineTimeDeleteState extends OnlineOfflineTimeState {}



class OnlineOfflineTimeLoadedState extends OnlineOfflineTimeState {
  final OnlineOfflineTimeResponseModel onlineOfflineTimeResponseModel;

  OnlineOfflineTimeLoadedState({
    required this.onlineOfflineTimeResponseModel,
  });
}
class BankNameListLoadedState extends OnlineOfflineTimeState {
  final List<BankListModel> bankList;

  BankNameListLoadedState({
    required this.bankList,
  });
}



class OnlineOfflineTimeErrorState extends OnlineOfflineTimeState {
  final String message;
  OnlineOfflineTimeErrorState({required this.message, });
}

class OnlineOfflineTimeCubit extends Cubit<OnlineOfflineTimeState> {
  OnlineOfflineTimeCubit() : super(OnlineOfflineTimeInitState());

  OnlineOfflineTimesRepository OnlineOfflineTimeRepository = OnlineOfflineTimesRepository();
  


  void getOnlineOfflineTime() async {
    emit(OnlineOfflineTimeLoadingState());
    await OnlineOfflineTimeRepository
        .getOnlineOfflineTimeApi( )
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(OnlineOfflineTimeLoadedState(onlineOfflineTimeResponseModel: value));
        } else {
          emit(OnlineOfflineTimeErrorState(message: serverErrorMessage));
        }
      } else {
        emit(OnlineOfflineTimeErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(OnlineOfflineTimeErrorState(message: "Server error"));
    });
  }




}
