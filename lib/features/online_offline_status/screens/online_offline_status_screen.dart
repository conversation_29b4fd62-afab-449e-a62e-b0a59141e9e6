import 'package:rooo_driver/features/online_offline_status/cubit/bank_info_cubit.dart';
import 'package:rooo_driver/features/online_offline_status/models/bank_info_response_model.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class OnlineOfflineStatusScreen extends StatefulWidget {
  // final DriverOnlineOfflineTimeModel? OnlineOfflineTimeModel;
  const OnlineOfflineStatusScreen();

  @override
  State<OnlineOfflineStatusScreen> createState() =>
      _OnlineOfflineStatusScreenState();
}

class _OnlineOfflineStatusScreenState extends State<OnlineOfflineStatusScreen> {
  String _emptyMesssage = "";

  bool _isError = false;

  String _onlineTime = "";
  String _offlineTime = "";
  String _remainigOnlineime = "";
  bool _isOnline = false;


  _getOnlineOfflineTime() {
    BlocProvider.of<OnlineOfflineTimeCubit>(context).getOnlineOfflineTime();
  }

  _onDataLoaded(
      {required OnlineOfflineTimeResponseModel online_offline_response_model}) {
    _onlineTime = online_offline_response_model.data!.totalOnlineHours;
    _offlineTime = online_offline_response_model.data!.totalOfflineHours;
    _remainigOnlineime = online_offline_response_model.data!.remainingOnlineHours;
    setState(() {
      _isOnline = online_offline_response_model.data!.type.toLowerCase() == "online";
    });
  }

  _onPullToRefresh() {
    _init();
  }

  _init() {
    
    _getOnlineOfflineTime();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OnlineOfflineTimeCubit, OnlineOfflineTimeState>(
      listener: (context, state) {
        if (state is OnlineOfflineTimeLoadedState) {
          _onDataLoaded(
              online_offline_response_model:
                  state.onlineOfflineTimeResponseModel);
        }
        if (state is OnlineOfflineTimeErrorState) {
          _isError = true;
           GlobalMethods.errorToast(context,  state.message ); 
        }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: RoooAppbar(title: "Online/Offline Time"),
            body: ScreenBody(
                onPullToRefresh: () async => await _onPullToRefresh(),
                isLoading: state is OnlineOfflineTimeLoadingState || _isError,
                isEmpty: false,
                emptyMessage: _emptyMesssage,
                child: Padding(
                  padding: screenPadding,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Status",
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          _isOnline ? "ONLINE" : "OFFLINE",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                            color:
                                !_isOnline ? Colors.red : Colors.green,
                          ),
                        ),
                        height20,
                        Text(
                          _isOnline
                              ? "Total Online time"
                              : "Total Offline time",
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        Text(
                         
                         _isOnline ?
                          _onlineTime :
                          _offlineTime,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                            color:
                                !_isOnline ? Colors.red : Colors.green,
                          ),
                        ),
                          height20,
                        Text("Remaining Online time",
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          _remainigOnlineime,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                            color:
                                 Colors.yellow,
                          ),
                        ),
                      ],
                    ),
                  ),
                )));
      },
    );
  }
}
