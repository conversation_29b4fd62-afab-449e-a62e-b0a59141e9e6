class OnlineOfflineTimeModel {
  final String type;
  final String totalOnlineHours;
  final String totalOfflineHours;
  final String remainingOnlineHours;

  // Constructor
  OnlineOfflineTimeModel({
    required this.type,
    required this.totalOnlineHours,
    required this.totalOfflineHours,
    required this.remainingOnlineHours,
  });

  // Factory method to create a UserStatus object from a JSON map
  factory OnlineOfflineTimeModel.fromJson(Map<String, dynamic> json) {
    return OnlineOfflineTimeModel(
      type: json['type'],
      totalOnlineHours: json['total_online_hours'],
      totalOfflineHours: json['total_offline_hours'],
      remainingOnlineHours: json['remaining_online_hours'],
    );
  }

}
