
import 'package:rooo_driver/features/online_offline_status/models/bank_info_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class OnlineOfflineTimeResponseModel extends ResponseModel<OnlineOfflineTimeModel> {
  OnlineOfflineTimeResponseModel({
    required bool status,
    required String message,
    required OnlineOfflineTimeModel? data,
    // required PaginationModel? pagination
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory OnlineOfflineTimeResponseModel.fromJson(Map<String, dynamic> json) {
    return OnlineOfflineTimeResponseModel(
      //  pagination: json["pagination"] != null
      //       ? PaginationModel.fromJson(json["pagination"])
            // : null,
      status: json['status'],
      message: json['message'],
     data: json["data"] != null
            ? OnlineOfflineTimeModel.fromJson(json["data"])
            : null,
    );
  }

 

}
