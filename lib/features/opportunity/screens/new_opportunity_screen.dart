
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/components/ScheduledRideCard.dart';
import 'package:rooo_driver/features/drawer/screens/driver_drawer.dart';
import 'package:rooo_driver/features/opportunity/utils/opportunity_mqtt_util.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/global/state/global_state.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/opportunity/cubit/opportunity_cubit.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/OpposrtunityResponseModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

class PendingOpportunityScreen extends StatefulWidget {
  const PendingOpportunityScreen({
    super.key,
  });

  @override
  State<PendingOpportunityScreen> createState() =>
      _PendingOpportunityScreenState();
}

class _PendingOpportunityScreenState extends State<PendingOpportunityScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();
  OpportunityMqttUtil? _mqttUtil;

  List<OnRideRequest> _opportunityList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getOpportunityList(
            currentPage: _currentPage,
          );
        }
      }
    });
  }

  _getOpportunityList({required int currentPage}) {
    BlocProvider.of<OpportunityCubit>(context)
        .getOpportunity(currentPage: currentPage, status: "new");
  }

  acceptOpportunity({required int id, required int driverId}) {
    Map request = {
      "id": id,
      "driver_id": driverId,
      "is_accept": "1",
    };
    BlocProvider.of<OpportunityCubit>(context)
        .acceptOpportunity(request: request);
  }

  _onDataLoaded({required OpportunityResponseModel opportunityResponseModel}) {
    _currentPage = opportunityResponseModel.pagination?.currentPage ?? 1;
    _totalPage = opportunityResponseModel.pagination?.totalPages ?? 1;
    _opportunityList = opportunityResponseModel.data ?? [];
    _emptyMesssage = opportunityResponseModel.messasge.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getOpportunityList(
      currentPage: _currentPage,
    );
    _initMqtt();
  }

  _initMqtt() async {
    final userId = sharedPref.getInt(USER_ID);
    if (userId != null) {
      _mqttUtil = OpportunityMqttUtil(
        userId: userId,
        onOpportunityAlreadyAccepted: _onOpportunityAlreadyAccepted,
      );
      await _mqttUtil?.initialize();
    }
  }

  _onOpportunityAlreadyAccepted() {
    _getOpportunityList(currentPage: 1);
  }

  _dispose() {
    _scrollController.dispose();
    _mqttUtil?.dispose();
    _mqttUtil = null;
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    _dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<OpportunityCubit, OpportunityState>(
        listener: (context, state) {
          if (state is OpportunityLoadedState) {
            getAndApplyCounters();
            _onDataLoaded(
                opportunityResponseModel: state.opportunityResponseModel);
          }
          if (state is OpportunityErrorState) {
            GlobalMethods.errorToast(context, state.message);
          }
          if (state is OpportunityAcceptedState) {
            _getOpportunityList(currentPage: _currentPage);
            GlobalMethods.succesToast(context, "Ride accepted successfully");
            GlobalState.homePageDataRefresher();
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is OpportunityLoadingState,
              isEmpty: _opportunityList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: AnimationLimiter(
                child: ListView.separated(
                  padding: screenPadding,
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => height10,
                  itemCount: _opportunityList.length,
                  itemBuilder: (BuildContext context, int index) {
                    OnRideRequest data = _opportunityList[index];

                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 1000),
                      child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: OpportunityRideCard(
                              text: language.accept,
                              datetime: data.datetime,
                              endAddress: data.endAddress.toString(),
                              startAddress: data.startAddress.toString(),
                              isButton: true,
                              onPressed: () {
                                acceptOpportunity(
                                    id: data.id!,
                                    driverId: sharedPref.getInt(USER_ID)!);
                              },
                            ),
                          )),
                    );
                  },
                ),
              ));
        },
      ),
    );
  }
}
