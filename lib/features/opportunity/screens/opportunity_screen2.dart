import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/opportunity/screens/accepted_opportunity_screen.dart';
import 'package:rooo_driver/features/opportunity/screens/new_opportunity_screen.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OpportunityScreen extends StatefulWidget {
  final bool fromDashboard;
  const OpportunityScreen({super.key,  this.fromDashboard = false});

  @override
  OpportunityScreenState createState() => OpportunityScreenState();
}

class OpportunityScreenState extends State<OpportunityScreen>
    with AutomaticKeepAliveClientMixin {
  int currentPage = 1;
  int totalPage = 1;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //

    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return DefaultTabController(
      initialIndex: widget.fromDashboard ? 1 : 0,
      length: 2,
      child: Scaffold(
        appBar: RoooAppbar(
          title: language.opportunityTxt,
        ),
        body: Column(
          children: [
            tabContainer(
                tabs: [language.NewTripText, language.AcceptedTripText]),
            Expanded(
              child: TabBarView(
                children: [
                  PendingOpportunityScreen(),
                  AcceptedOpportunityScreen(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
