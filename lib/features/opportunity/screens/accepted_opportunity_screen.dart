// }

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/components/ScheduledRideCard.dart';
import 'package:rooo_driver/features/drawer/screens/driver_drawer.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/global/state/global_state.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/opportunity/cubit/opportunity_cubit.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/OpposrtunityResponseModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

class AcceptedOpportunityScreen extends StatefulWidget {
  const AcceptedOpportunityScreen({
    super.key,
  });

  @override
  State<AcceptedOpportunityScreen> createState() =>
      _AcceptedOpportunityScreenState();
}

class _AcceptedOpportunityScreenState extends State<AcceptedOpportunityScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  List<OnRideRequest> _opportunityList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getOpportunityList(
            currentPage: _currentPage,
          );
        }
      }
    });
  }

  _getOpportunityList({required int currentPage}) {
    BlocProvider.of<OpportunityCubit>(context)
        .getOpportunity(currentPage: currentPage, status: "accepted");
  }

  Future<bool?> checkCurrentlyRiding() async {
    bool? isCurrentlyRiding =
        await BlocProvider.of<OpportunityCubit>(context).checkCurrentlyRiding();
    return isCurrentlyRiding;
  }

  startOpportunityRide({required int id}) {
    Map request = {
      "id": id,
      "status": ARRIVING,
    };
    BlocProvider.of<OpportunityCubit>(context)
        .startOpportunityRide(request: request, rideId: id);
  }

  _onDataLoaded({required OpportunityResponseModel opportunityResponseModel}) {
    _currentPage = opportunityResponseModel.pagination?.currentPage ?? 1;
    _totalPage = opportunityResponseModel.pagination?.totalPages ?? 1;
    _opportunityList = opportunityResponseModel.data ?? [];
    _emptyMesssage = opportunityResponseModel.messasge.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getOpportunityList(
      currentPage: _currentPage,
    );
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  void confirmCancelRide(int id) {
    GlobalMethods.showConfirmationDialog(
        context: context,
        title: "Are you sure you want to cancel this ride?",
        positiveText: "Yes",
        negativeText: "No",
        onPositiveAction: () {
          cancelRide(id, flag: true);
        });
  }

  Future<void> cancelRide(int id, {required bool flag}) async {
    Map req = {
      "id": id,
      "cancel_by": 'driver',
      "status": "canceled",
      'is_flag': flag,
    };
    BlocProvider.of<OpportunityCubit>(context)
        .cancelOpportunityRide(request: req);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<OpportunityCubit, OpportunityState>(
        listener: (context, state) {
          if (state is OpportunityLoadedState) {
            getAndApplyCounters();
            _onDataLoaded(
                opportunityResponseModel: state.opportunityResponseModel);
          } else if (state is OpportunityErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is StartOpportunityRideState) {
            GlobalMethods.pushAndRemoveAll(
                context: context,
                screen: RideScreen(),
                screenIdentifier: ScreenIdentifier.InitialScreen);
            // launchScreen(context, InitialScreen(), isNewTask: true);
          } else if (state is OpportunityCanceledState) {
            GlobalMethods.succesToast(context, state.message);
            _getOpportunityList(currentPage: _currentPage);
            GlobalState.homePageDataRefresher();
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is OpportunityLoadingState,
              isEmpty: _opportunityList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: AnimationLimiter(
                child: ListView.separated(
                  padding: screenPadding,
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => height10,
                  itemCount: _opportunityList.length,
                  itemBuilder: (BuildContext context, int index) {
                    OnRideRequest data = _opportunityList[index];

                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 1000),
                      child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: OpportunityRideCard(
                              text: language.StartRideText,
                              datetime: data.datetime,
                              endAddress: data.endAddress.toString(),
                              startAddress: data.startAddress.toString(),
                              isButton: true,
                              onPressed: () async {
                                bool? isCurrentlyRiding =
                                    await checkCurrentlyRiding();

                                if (isCurrentlyRiding == null) {
                                  GlobalMethods.errorToast(
                                      context, errorMessage);
                                } else {
                                  if (!isCurrentlyRiding) {
                                    startOpportunityRide(id: data.id!);
                                  } else {
                                    GlobalMethods.infoToast(context,
                                        "You are already in ride please wait...");
                                  }
                                }
                              },
                              onCancel: () {
                                confirmCancelRide(data.id!);
                              },
                            ),
                          )),
                    );
                  },
                ),
              ));
        },
      ),
    );
  }
}
