import 'package:rooo_driver/global/models/current_ride_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/model/OpposrtunityResponseModel.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class OpportunityRepository {
  Future<OpportunityResponseModel> getNewOpportunityListApi(
      {required int currentPage, required String status}) async {
    return OpportunityResponseModel.fromJson(await handleResponse(
        await buildHttpResponse(
            'opportunity-list?type=${status}&page=$currentPage',
            method: HttpMethod.GET)));
  }

  Future<OpportunityResponseModel> acceptOpportunity(
      {required Map request}) async {
    return OpportunityResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('riderequest-respond-schedule',
            method: HttpMethod.POST, request: request)));
  }

  Future<CurrentRideResponseModel> checkCurrentlyRiding() async {
    return CurrentRideResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('current-riderequest',
            method: HttpMethod.GET)));
  }

  Future<StatusMessageModel> startOpportunityRide(
      {required Map request, required int rideId}) async {
    return StatusMessageModel.fromJson(await handleResponse(await buildHttpResponse(
        'riderequest-update/$rideId',
        method: HttpMethod.POST,
        request: request)));
  }
}
