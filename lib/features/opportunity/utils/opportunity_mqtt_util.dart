import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:rooo_driver/global/constants/app_cred.dart';
import 'package:flutter/foundation.dart';

class OpportunityMqttUtil {
  MqttServerClient? _mqttClient;
  final int _userId;
  final VoidCallback _onOpportunityAlreadyAccepted;
  OpportunityMqttUtil({
    required int userId,
    required VoidCallback onOpportunityAlreadyAccepted,
  }) : _userId = userId,
       _onOpportunityAlreadyAccepted = onOpportunityAlreadyAccepted;

  Future<void> initialize() async {
    final clientId = "rooo_driver_" + _userId.toString();

    _mqttClient = MqttServerClient.withPort(
      AppCred.mqttHost,
      clientId,
      8883,
      maxConnectionAttempts: 1
    );

    _mqttClient?.setProtocolV311();
    _mqttClient?.secure = true;
    _mqttClient?.securityContext = SecurityContext.defaultContext;
    _mqttClient?.logging(on: true);
    _mqttClient?.keepAlivePeriod = 60; 
    _mqttClient?.autoReconnect = true;

    try {
      await _mqttClient?.connect(AppCred.mqttUserName, AppCred.mqttPassword);
    } catch (e) {
      log('OpportunityMqttUtil -> Connection error: ${e.toString()}');
      return;
    }

    if (_mqttClient?.connectionStatus?.state == MqttConnectionState.connected) {
      log('OpportunityMqttUtil -> Connected successfully');

      final topic = 'opportunity_ride_already_accept_${_userId}';
      _mqttClient?.subscribe(topic, MqttQos.atLeastOnce);

      _mqttClient?.updates?.listen(_onMessage);

      _mqttClient?.onDisconnected = _onDisconnected;
    } else {
      log('OpportunityMqttUtil -> Failed to connect: ${_mqttClient?.connectionStatus?.state}');
    }
  }

  void _onMessage(List<MqttReceivedMessage<MqttMessage?>>? messages) {
    if (messages == null || messages.isEmpty) return;

    final MqttPublishMessage recMess = messages[0].payload as MqttPublishMessage;
    final payload = MqttPublishPayload.bytesToStringAsString(recMess.payload.message);
    try {
      final data = jsonDecode(payload);

      if (data['success_type'] == 'opportunity_already_accepted') {
        _onOpportunityAlreadyAccepted();
      }
    } catch (e) {
      log('OpportunityMqttUtil -> Error parsing message: ${e.toString()}');
    }
  }

  void _onDisconnected() {
    log('OpportunityMqttUtil -> Disconnected');
  }

  void dispose() {
    if (_mqttClient != null && _mqttClient!.connectionStatus!.state == MqttConnectionState.connected) {
      final topic = 'opportunity_ride_already_accept_${_userId}';
      _mqttClient?.unsubscribe(topic);

      _mqttClient?.disconnect();
      log('OpportunityMqttUtil -> Disposed');
    }

    _mqttClient = null;
  }
}
