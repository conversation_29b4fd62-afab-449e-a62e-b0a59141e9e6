
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/WalletListModel.dart';
import 'package:rooo_driver/screens/ride_history_details_screen.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';

class RidePaymentHistoryScreen extends StatefulWidget {
  final 
  List<WalletModel> walletData ;
  const RidePaymentHistoryScreen({super.key, required this.walletData});

  @override
  State<RidePaymentHistoryScreen> createState() => _RidePaymentHistoryScreenState();
}

class _RidePaymentHistoryScreenState extends State<RidePaymentHistoryScreen> {
 

  @override
  void initState() {
    //  : implement initState
    super.initState();
    // getWalletList();
  }


  Widget build(BuildContext context) {
    return Scaffold(
      body: Observer(builder: (context) {
        return Stack(
          children: [
            SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                padding: EdgeInsets.fromLTRB(16, 20, 16, 16),
                child:widget. walletData.isEmpty&&!appStore.isLoading
                    ? Center(
                        child: CustomText(data: language.notAvailable),
                      )
                    : ListView.separated(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                         WalletModel data=widget.walletData[index];
                          return inkWellWidget(
                            onTap: () {

                              if(data.rideRequestId!=null){
                                GlobalMethods.pushScreen(context: context, screen: RideHistoryDetailScreen(id: data.rideRequestId!), screenIdentifier: ScreenIdentifier.RideHistoryDetailScreen);

                 
                              }
                            },
                            child: Card(
                              
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10, horizontal: 10),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(data: language.amount,color: Colors.black),
                                        CustomText(color: Colors.black,
                                            fontweight: FontWeight.bold,
                                            data: appStore.currencyCode
                                                    .toString() +
                                                data.amount
                                                    .toString()),
                                      ],
                                    ),
                                    height5,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(data: language.TimeTxt,color:Colors.black),
                                        CustomText(color: Colors.black,
                                            fontweight: FontWeight.bold,
                                            data: data
                                                    .datetime
                                                    .toString()
                                                    .substring(0, 10) +
                                                " at " +
                                                data
                                                    .datetime
                                                    .toString()
                                                    .substring(10, 16)),
                                      ],
                                    ),
                                    height5,
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        CustomText(
                                            fontweight: FontWeight.bold,
                                            color: 
                                                Colors.green,
                                            data:language.TripEarning),
                                            width10,

                                            Icon(Icons.arrow_forward_ios,size: 20,color: Colors.black,)
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (context, index) {
                          return height10;
                        },
                        itemCount: widget.walletData.length)),
            Visibility(
              visible: appStore.isLoading,
              child: Loader(),
            ),
            // !appStore.isLoading && walletData.isEmpty
            //     ? emptyWidget()
            //     : SizedBox(),
          ],
        );
      }),
    );
  }
}
