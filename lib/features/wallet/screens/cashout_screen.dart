import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/WalletListModel.dart';
import 'package:rooo_driver/screens/ride_history_details_screen.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';

class CashoutHistoryScreen extends StatefulWidget {
  final List<WalletModel> walletData;
  const CashoutHistoryScreen({super.key, required this.walletData});

  @override
  State<CashoutHistoryScreen> createState() => _CashoutHistoryScreenState();
}

class _CashoutHistoryScreenState extends State<CashoutHistoryScreen> {
  Widget build(BuildContext context) {
    return Scaffold(
      body: Observer(builder: (context) {
        return Stack(
          children: [
            SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                padding: EdgeInsets.fromLTRB(16, 20, 16, 16),
                child: widget.walletData.isEmpty && !appStore.isLoading
                    ? Center(
                        child: CustomText(data: language.notAvailable),
                      )
                    : ListView.separated(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          WalletModel data = widget.walletData[index];
                          return inkWellWidget(
                            onTap: () {

                              if (data.rideRequestId != null) {
                              GlobalMethods.pushScreen(
                                  context: context,
                                  screen: RideHistoryDetailScreen(
                                      id: data.rideRequestId!),
                                  screenIdentifier:
                                      ScreenIdentifier.RideHistoryDetailScreen);
                             
                              }
                            },
                            child: Card(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10, horizontal: 10),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                          data: language.amount,
                                          color: Colors.black,
                                        ),
                                        CustomText(
                                            color: Colors.black,
                                            fontweight: FontWeight.bold,
                                            data: appStore.currencyCode
                                                    .toString() +
                                                data.amount.toString()),
                                      ],
                                    ),
                                    height5,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                            data: language.TimeTxt,
                                            color: Colors.black),
                                        CustomText(
                                            color: Colors.black,
                                            fontweight: FontWeight.bold,
                                            data: data.datetime
                                                    .toString()
                                                    .substring(0, 10) +
                                                " at " +
                                                data.datetime
                                                    .toString()
                                                    .substring(10, 16)),
                                      ],
                                    ),
                                    height5,
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        CustomText(
                                            fontweight: FontWeight.bold,
                                            color: Colors.blue,
                                            data: language.withDraw),
                                        width10,
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (context, index) {
                          return height10;
                        },
                        itemCount: widget.walletData.length)),
            Visibility(
              visible: appStore.isLoading,
              child: Loader(),
            ),
            // !appStore.isLoading && walletData.isEmpty
            //     ? emptyWidget()
            //     : SizedBox(),
          ],
        );
      }),
    );
  }
}
