
import 'package:rooo_driver/features/wallet/screens/cashout_screen.dart';
import 'package:rooo_driver/features/wallet/screens/ride_payment_history_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/WalletListModel.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';

class WalletHistoryScreen extends StatefulWidget {
  @override
  WalletHistoryScreenState createState() => WalletHistoryScreenState();
}

class WalletHistoryScreenState extends State<WalletHistoryScreen>
    with AutomaticKeepAliveClientMixin {
  int currentPage = 1;
  int totalPage = 1;

  int currentIndex = -1;

  List<WalletModel> walletData = [];

  int totalAmount = 0;

  @override
  void initState() {
    super.initState();
    getWalletList();
  }

  void getWalletList() async {
    setState(() {
      appStore.setLoading(true);
    });
    await getWalletListApi(pageData: currentPage).then((value) {
   
      currentPage = value.pagination!.currentPage!;
      totalPage = value.pagination!.totalPages!;
      totalAmount = value.walletBalance!.totalAmount!.toInt();
      if (currentPage == 1) {
        walletData.clear();
      }
      walletData = value.data ?? [];
 setState(() {
        appStore.setLoading(false);
      });    }).catchError((error) {
      setState(() {
        appStore.setLoading(false);
      });
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: RoooAppbar(
          title: language.WalletHistoryTxt,
        ),
        body:appStore.isLoading? Loader():
             Column(
                children: [
                  tabContainer(tabs: [language.ride, language.cashoutTxt]),
                  Expanded(
                    child: TabBarView(
                      children: [
                        RidePaymentHistoryScreen(
                          walletData: walletData
                              .where((element) => element.type == "credit")
                              .toList(),
                        ),
                        CashoutHistoryScreen(
                          walletData: walletData
                              .where((element) => element.type == "debit")
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
