import 'package:flutter/material.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';

class DrawerContainer extends StatelessWidget {
  final double? height;
  final double? width;
  final String name;
  final IconData iconData;
  final Widget? subTitle;
  final bool is_profile_completed;
  final MainAxisAlignment? mainAxisAlignment;

  final dynamic screenName;
  final ScreenIdentifier screenIdentifier;
  const DrawerContainer(
      {super.key,
      required this.name,
      this.mainAxisAlignment,
      required this.iconData,
      this.screenName,
      required this.screenIdentifier,
      this.subTitle,
      required this.is_profile_completed,
      this.height,
      this.width});

  @override
  Widget build(BuildContext context) {
    return inkWellWidget(
      onTap: () {
        GlobalMethods.handleInCompleteProfile(
            context: context,
            is_profile_completed: is_profile_completed,
            positiveAction: () {
              Navigator.of(context).pop();
              GlobalMethods.pushScreen(
                  context: context,
                  screen: screenName,
                  screenIdentifier: screenIdentifier);
            });

        // launchScreen(context, screenName);
      },
      child: Stack(
        children: [
          Container(
            width: width,
            padding: screenPadding / 2,
            height: height,
            decoration:
                BoxDecoration(color: Colors.white, borderRadius: radius()),
            child: Row(
              mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
              children: [
                Icon(
                  iconData,
                  color: AppColors.primaryBlackColor,
                ),
                width10,
                CustomText(
                  data: name,
                  size: 20,
                  fontweight: FontWeight.bold,
                  color: Colors.black,
                )
              ],
            ),
          ),
          if (subTitle != null)
            Positioned(
              right: 16,
              top: 0,
              bottom: 0,
              child: Center(child: subTitle!),
            ),
        ],
      ),
    );
  }
}
