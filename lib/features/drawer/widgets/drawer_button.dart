import 'package:rooo_driver/utils/Extensions/app_common.dart';
import 'package:flutter/material.dart';

class DrawerButton extends StatefulWidget {
  final String title;
  final Widget? subTitle;
  final Function() onTap;

  DrawerButton({required this.title, required this.onTap, this.subTitle});

  @override
  DrawerButtonState createState() => DrawerButtonState();
}

class DrawerButtonState extends State<DrawerButton> {
  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: widget.onTap,
                child: Container(
                  margin: EdgeInsets.only(top: 8, bottom: 8),
                  child: Text(widget.title, style: drawerTextStyleLarge()),
                ),
              ),
            ),
            widget.subTitle ?? const SizedBox(),
          ],
        ),
      ],
    );
  }
}
