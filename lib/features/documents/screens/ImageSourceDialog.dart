import 'package:flutter/material.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';

class ImageSourceDialog extends StatefulWidget {
  final Function()? onGallery;
  final Function()? onCamera;
  final Function()? onFile;
  final bool isFile;

  ImageSourceDialog(
      {this.onGallery, this.onCamera, this.onFile, this.isFile = false});

  @override
  State<ImageSourceDialog> createState() => _ImageSourceDialogState();
}

class _ImageSourceDialogState extends State<ImageSourceDialog> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("language.selectSources", style: boldTextStyle(size: 18)),
              InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(Icons.cancel_outlined)),
            ],
          ),
          inkWellWidget(
            onTap: widget.onCamera ?? () {},
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.all(8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.camera),
                  SizedBox(width: 8),
                  Text("camera", style: TextStyle()),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
