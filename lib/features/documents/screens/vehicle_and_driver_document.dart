import 'package:rooo_driver/components/setting_black_container.dart';
import 'package:rooo_driver/features/vehicles/screens/vehicle_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:flutter_mobx/flutter_mobx.dart';



class VehicleAndDriverDocumentScreen extends StatefulWidget {
  @override
  VehicleAndDriverDocumentScreenState createState() => VehicleAndDriverDocumentScreenState();
}

class VehicleAndDriverDocumentScreenState extends State<VehicleAndDriverDocumentScreen> {
  

  // final WidgetStateProperty<Icon?> thumbIcon =
  //     WidgetStateProperty.resolveWith<Icon?>(
  //   (Set<WidgetState> states) {
  //     // Thumb icon when the switch is selected.
  //     if (states.contains(WidgetState.selected)) {
  //       return const Icon(Icons.check);
  //     }
  //     return const Icon(Icons.close);
  //   },
  // );







  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.settingTxt),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.only(bottom: 16),
            child: Column(
              children: [
                // height20,
                //         blackContainer(
                //             title: language.changePassword,
                //             onTap: () {
                //               launchScreen(context, ChangePasswordScreen(),
                //                   pageRouteAnimation: PageRouteAnimation.Slide);
                //             }),
                //  height15,
                // SettingsBlackContainer(
                //     address: "language.png",
                //     title: language.language,
                //     onTap: () {
                //       GlobalMethods.pushScreen(
                //           context: context,
                //           screen: LanguageScreen(),
                //           screenIdentifier: ScreenIdentifier.LanguageScreen);
                //       // launchScreen(context, LanguageScreen(),
                //       //     pageRouteAnimation: PageRouteAnimation.Slide);
                //     }),
                SizedBox(),

                     SettingsBlackContainer(
                      icon: Icon(Icons.document_scanner_sharp),
                    title: language.document,
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: DocumentScreen(canGoToDashboard: false,),
                          screenIdentifier:
                              ScreenIdentifier.PrivacyPolicyScreen);
                      // launchScreen(context, PrivacyPolicyScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),
                SettingsBlackContainer(
                    icon: Icon(Icons.car_repair),
                    title: language.vehicleDetail,
                    onTap: () {
                      GlobalMethods.pushScreen(
                          context: context,
                          screen: VehiclesScreen(),
                          screenIdentifier:
                              ScreenIdentifier.PrivacyPolicyScreen);
                      // launchScreen(context, PrivacyPolicyScreen(),
                      //     pageRouteAnimation: PageRouteAnimation.Slide);
                    }),
         

               
                // settingItemWidget(Icons.lock_outline, language.changePassword,
                //     () {
                //   launchScreen(context, ChangePasswordScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(Icons.language, language.language, () {
                //   launchScreen(context, LanguageScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(
                //     Icons.assignment_outlined, language.privacyPolicy, () {
                //   launchScreen(context, PrivacyPolicyScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(Icons.help_outline, language.helpSupport,
                //     () {
                //   launchScreen(context, CareScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(
                //     Icons.assignment_outlined, language.termsConditions, () {
                //   launchScreen(context, TermsAndConditionsScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // settingItemWidget(
                //   Icons.info_outline,
                //   language.aboutUs,
                //   () {
                //     launchScreen(
                //         context, AboutScreen(settingModel: settingModel),
                //         pageRouteAnimation: PageRouteAnimation.Slide);
                //   },
                // ),
                // settingItemWidget(
                //     Icons.delete_outline, language.deleteAccount, () {
                //   launchScreen(context, DeleteAccountScreen(),
                //       pageRouteAnimation: PageRouteAnimation.Slide);
                // }),
                // ListTile(
                //   contentPadding: EdgeInsets.only(left: 16, right: 16),
                //   leading: Icon(Icons.offline_bolt_outlined,
                //       size: 25,
                //       color: isNightTime() ? Colors.white : primaryColor),
                //   title: Text(
                //       isAvailable
                //           ? language.available
                //           : language.notAvailable,
                //       style: primaryTextStyle()),
                //   trailing: Switch(
                //       thumbIcon: thumbIcon,
                //       thumbColor: isNightTime()
                //           ? MaterialStateColor.resolveWith((states) {
                //               if (states.contains(MaterialState.selected)) {
                //                 return Colors.white;
                //               }
                //               return Colors.white;
                //             })
                //           : null,
                //       trackColor: isNightTime()
                //           ? MaterialStateColor.resolveWith((states) {
                //               if (states.contains(MaterialState.selected)) {
                //                 return Colors.amber;
                //               }
                //               return Colors.grey;
                //             })
                //           : null,
                //       value: isAvailable,
                //       onChanged: (val) {
                //         setState(() {
                //           isAvailable = val;
                //         });
                //       }),
                //   onTap: () async {
                //     if (appStore.currentRiderRequest == null) {
                //       await showConfirmDialogCustom(
                //         context,
                //         title: !isAvailable
                //             ? language.youWillReceiveNewRidersAndNotifications
                //             : language
                //                 .youWillNotReceiveNewRidersAndNotifications,
                //         dialogType: DialogType.ACCEPT,
                //         positiveText: language.yes,
                //         negativeText: language.no,
                //         primaryColor: primaryColor,
                //         onAccept: (c) async {
                //           updateAvailable();
                //         },
                //       );
                //     } else {
                //      GlobalMethods.infoToast(context,  language
                //           .youCanNotThisActionsPerformBecauseYourCurrentRideIsNotCompleted);
                //     }
                //   },
                // ),
              ],
            ),
          ),
          Observer(builder: (context) {
            return Visibility(
              visible: appStore.isLoading,
              child: loaderWidget(),
            );
          })
        ],
      ),
    );
  }

  Widget settingItemWidget(IconData icon, String title, Function() onTap,
      {bool isLast = false, IconData? suffixIcon}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListTile(
          contentPadding: EdgeInsets.only(left: 16, right: 16),
          leading: Icon(
            icon,
            size: 25,
          ),
          title: Text(title, style: primaryTextStyle()),
          trailing: suffixIcon != null
              ? Icon(suffixIcon, color: Colors.green)
              : Icon(Icons.navigate_next,
                  color: IS_DARK_MODE_ON ? Colors.white : Colors.grey),
          onTap: onTap,
        ),
        if (!isLast) Divider(height: 0)
      ],
    );
  }

  Widget blackContainerWithToggle({required title, required Function() onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 25, horizontal: 10),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 15, color: Colors.white),
            ),

            Switch(
              value: IS_DARK_MODE_ON,
              onChanged: (value) {
                setState(() {
                  appStore.setLoading(true);
                });
                if (value == true) {
                  sharedPref.setBool(IS_DARK_MODE, value);
                } else {
                  sharedPref.remove(IS_DARK_MODE);
                }
                setState(() {
                  IS_DARK_MODE_ON = value;
                });

                Future.delayed(Duration(seconds: 3)).then((value) {
                  setState(() {
                    appStore.setLoading(false);
                  });

                  closeScreen(context);
                  closeScreen(context);
                });
              },
              activeColor: Colors.white,
              activeTrackColor: Colors.white,
              inactiveThumbColor: Colors.white,
              inactiveTrackColor: Colors.white,
            ),
            //     ),
            // Switch(

            //     thumbIcon: thumbIcon,
            //     thumbColor: isNightTime()
            //         ? MaterialStateColor.resolveWith((states) {
            //             if (states.contains(MaterialState.selected)) {
            //               return Colors.white;
            //             }
            //             return Colors.white;
            //           })
            //         : null,
            //     trackColor: isNightTime()
            //         ? MaterialStateColor.resolveWith((states) {
            //             if (states.contains(MaterialState.selected)) {
            //               return Colors.amber;
            //             }
            //             return Colors.white;
            //           })
            //         : null,
            //     value: isAvailable,
            //     onChanged: (val) {
            //       setState(() {
            //         isAvailable = val;
            //       });
            //     }),
          ],
        ),
      ),
    );
  }
}
