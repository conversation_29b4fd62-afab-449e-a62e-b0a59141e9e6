import 'package:rooo_driver/model/PaginationModel.dart';

class DocumentListModel {
  List<DocumentModel>? data;
  PaginationModel? pagination;

  DocumentListModel({this.data, this.pagination});

  factory DocumentListModel.fromJson(Map<String, dynamic> json) {
    return DocumentListModel(
      data: json['data'] != null
          ? (json['data'] as List)
              .map((i) => DocumentModel.fromJson(i))
              .toList()
          : null,
      pagination: json['pagination'] != null
          ? PaginationModel.fromJson(json['pagination'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.pagination != null) {
      data['pagination'] = this.pagination!.toJson();
    }
    return data;
  }
}

enum DRIVER_DOCUMENT_TYPE {
  image,
  text,
}

class DocumentModel {
  String? createdAt;
  int? hasExpiryDate;
  int? id;
  int? isRequired;
  String? name;
  String? description;
  String? imageURL;
  int? status;
  String? updatedAt;

  int? totalRequiredDocs;
  List<DocumentModel>? children;

  DRIVER_DOCUMENT_TYPE type;
  String? docText;
  String? expiryDate;

  DocumentModel({
    this.createdAt,
    this.hasExpiryDate,
    this.id,
    this.isRequired,
    this.name,
    this.status,
    this.updatedAt,
    this.description,
    this.imageURL,
    this.children,
    this.totalRequiredDocs,
    this.type = DRIVER_DOCUMENT_TYPE.image,
    this.docText,
    this.expiryDate,
  });

  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    List<DocumentModel>? data = [];
    if (json['children'] != null) {
      json['children'].forEach((e) {
        data.add(DocumentModel.fromJson(e));
      });
    }
    return DocumentModel(
      createdAt: json['created_at'],
      hasExpiryDate: json['has_expiry_date'],
      id: json['id'],
      isRequired: json['is_required'],
      name: json['name'],
      status: json['status'],
      updatedAt: json['updated_at'],
      description: json['description'],
      totalRequiredDocs: json['totalRequiredDocs'],
      imageURL: json['imageURL'],
      children: data,
      type: json['document_type'] == "text"
          ? DRIVER_DOCUMENT_TYPE.text
          : DRIVER_DOCUMENT_TYPE.image,
      docText: json["doc_text"],
      expiryDate: json['expire_date'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['created_at'] = this.createdAt;
    data['has_expiry_date'] = this.hasExpiryDate;
    data['id'] = this.id;
    data['is_required'] = this.isRequired;
    data['name'] = this.name;
    data['status'] = this.status;
    data['updated_at'] = this.updatedAt;
    data['description'] = this.description;
    data['imageURL'] = this.imageURL;
    data['totalRequiredDocs'] = this.totalRequiredDocs;
    data["doc_text"] = this.docText;
    return data;
  }
}
