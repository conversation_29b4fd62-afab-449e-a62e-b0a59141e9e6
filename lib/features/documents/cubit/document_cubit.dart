import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/documents/models/document_model.dart';
import 'package:rooo_driver/features/documents/repository/document_repository.dart';
import 'package:rooo_driver/features/documents/models/driver_document_model.dart';

abstract class DocumentState {}

class DocumentInitialState extends DocumentState {}

class DocumentLoadingState extends DocumentState {}

class DocumentDeleltedState extends DocumentState {}

class DocumentErrorState extends DocumentState {
  final String? message;
  final String? unmessage;

  DocumentErrorState({this.message, this.unmessage});
}

class DocumentLoadedState extends DocumentState {
  final List<DocumentModel> documentList;

  DocumentLoadedState({required this.documentList});
}

class UploadedDocumentLoadedState extends DocumentState {
  final List<DriverDocumentModel> documentList;

  UploadedDocumentLoadedState({required this.documentList});
}

class DocumentUploadedState extends DocumentState {
  DocumentUploadedState();
}

class DocumentCubit extends Cubit<DocumentState> {
  DocumentRepository _documentRepository = DocumentRepository();
  DocumentCubit() : super(DocumentInitialState());

  getDocumentList() async {
    emit(DocumentLoadingState());

    await _documentRepository.getDocumentList().then((value) {
      emit(DocumentLoadedState(documentList: value.data!));
    }).onError((error, stackTrace) {
      emit(DocumentErrorState(message: "Server error"));
    });
  }

  getUploadedDocumentList() async {
    emit(DocumentLoadingState());

    await _documentRepository.getDriverDocumentList().then((value) {
      emit(UploadedDocumentLoadedState(documentList: value.data!));
    }).onError((error, stackTrace) {
      emit(DocumentErrorState(message: "Server error"));
    });
  }

  deleteDocument({required int id}) async {
    emit(DocumentLoadingState());

    await _documentRepository.deleteDocument(id: id).then((value) {
      emit(DocumentDeleltedState());
    }).onError((error, stackTrace) {
      emit(DocumentErrorState(message: "Server error"));
    });
  }

  uploadDocument({
    required documentId,
    required String userId,
    required String? imagePath,
    required String? expiryDate,
    required String? docText,
  }) async {
    emit(DocumentLoadingState());

    await _documentRepository
        .addNewDocument(
      docId: documentId,
      userId: userId,
      imagePath: imagePath,
      expiryDate: expiryDate,
      docText: docText,
    )
        .then((value) {
      emit(DocumentUploadedState());
    }).onError((error, stackTrace) {
      emit(DocumentErrorState(message: "Server error"));
    });
  }

  updateDocument({
    required documentId,
    required String userId,
    required String? imagePath,
    required String? expiryDate,
    required String? docText,
  }) async {
    emit(DocumentLoadingState());

    await _documentRepository
        .updateDocument(
      docId: documentId,
      userId: userId,
      imagePath: imagePath,
      expiryDate: expiryDate,
      docText: docText,
    )
        .then((value) {
      emit(DocumentUploadedState());
    }).onError((error, stackTrace) {
      emit(DocumentErrorState(message: "Server error"));
    });
  }
}
