import 'package:http/http.dart';
import 'package:rooo_driver/features/documents/models/document_model.dart';
import 'package:rooo_driver/features/documents/models/driver_document_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';
import 'package:rooo_driver/network/RestApis.dart';

class DocumentRepository {
  Future<DocumentListModel> getDocumentList() async {
    return DocumentListModel.fromJson(await handleResponse(
        await buildHttpResponse('document-list', method: HttpMethod.GET)));
  }

  Future<DriverDocumentList> getDriverDocumentList() async {
    return DriverDocumentList.fromJson(await handleResponse(
        await buildHttpResponse('driver-document-list',
            method: HttpMethod.GET)));
  }

  Future<StatusMessageModel> deleteDocument({required int id}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('driver-document-delete/$id',
            method: HttpMethod.POST)));
  }

  Future addNewDocument({
    required int docId,
    required String userId,
    required String? imagePath,
    required String? expiryDate,
    required String? docText,
  }) async {
    MultipartRequest multiPartRequest =
        await getMultiPartRequest('driver-document-save');

    multiPartRequest.fields['document_id'] = docId.toString();
    multiPartRequest.fields['driver_id'] = userId;
    if (docText != null) {
      multiPartRequest.fields['doc_text'] = docText;
    }
    if (expiryDate != null) {
      multiPartRequest.fields['expire_date'] = expiryDate;
    }
    if (imagePath != null && imagePath.isNotEmpty) {
      multiPartRequest.files
          .add(await MultipartFile.fromPath("driver_document", imagePath));
    }

    multiPartRequest.headers.addAll(buildHeaderTokens());
    await chuckerHttpClient.send(
      multiPartRequest,
    );
  }

  Future updateDocument({
    required int docId,
    required String userId,
    required String? imagePath,
    required String? expiryDate,
    required String? docText,
  }) async {
    MultipartRequest multiPartRequest =
        await getMultiPartRequest('driver-document-update/$docId');
    multiPartRequest.fields['driver_id'] = userId;
    if (docText != null) {
      multiPartRequest.fields['doc_text'] = docText;
    }
    if (expiryDate != null) {
      multiPartRequest.fields['expire_date'] = expiryDate;
    }
    if (imagePath != null && imagePath.isNotEmpty) {
      multiPartRequest.files
          .add(await MultipartFile.fromPath("driver_document", imagePath));
    }
    multiPartRequest.headers.addAll(buildHeaderTokens());
    await chuckerHttpClient.send(
      multiPartRequest,
    );
  }
}
