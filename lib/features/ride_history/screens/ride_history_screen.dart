
import 'package:rooo_driver/features/ride_history/screens/ride_history_completed_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';



class RideHistoryScreen extends StatefulWidget {
  @override
  RideHistoryScreenState createState() => RideHistoryScreenState();
}

class RideHistoryScreenState extends State<RideHistoryScreen> {
  int currentPage = 1;
  int totalPage = 1;

  @override
  void initState() {
    super.initState();  
    init();
  }

  void init() async {
    //
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length:2,
      child: Scaffold(                  
        appBar: RoooAppbar(
          title: language.myRides,
        ),
        body: Column(   
          children: [
            tabContainer(tabs:[language.completed,language.cancelled]),
            Expanded(
              child: Tab<PERSON><PERSON><PERSON><PERSON><PERSON>(children: [
                RideHistoryCompleted(status: "completed"),
                RideHistoryCompleted(status: "canceled"),
              ]),
            ),
          ],
        ),
      ),
    );
  }
}
