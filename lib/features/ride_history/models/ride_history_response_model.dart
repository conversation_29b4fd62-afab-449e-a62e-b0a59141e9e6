

import 'package:rooo_driver/global/models/ride_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class RideHistoryResponseModel {
  PaginationModel? pagination;
  List<OnRideRequest>? data;
  String ? message;

  RideHistoryResponseModel({this.data, this.pagination, this.message});

  factory RideHistoryResponseModel.fromJson(Map<String, dynamic> json) {
    return RideHistoryResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List).map((e) => OnRideRequest.fromJson(e)).toList()
            : null,
            message: json["message"]
            );
            
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas["pagination"] = this.pagination;
    datas["data"] = this.data;
    datas["message"]= this.message;
    return datas;
  }
}
