import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/components/faq_card.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/faq/cubit/help_cubit.dart';
import 'package:rooo_driver/features/faq/screens/faq_detail_screen2.dart';
import 'package:rooo_driver/features/help/models/help_model.dart';
import 'package:rooo_driver/features/help/models/help_response_model.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Constants.dart';

class FaqScreen extends StatefulWidget {
  const FaqScreen({super.key});

  @override
  State<FaqScreen> createState() => _FaqScreenState();
}

class _FaqScreenState extends State<FaqScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();

  List<WebviewDataModel> _faqList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getFaqList(currentPage: _currentPage);
        }
      }
    });
  }

  _getFaqList({required int currentPage}) {
    BlocProvider.of<FaqCubit>(context).getFaq(current_page: currentPage);
  }

  _onDataLoaded({required WebViewDataResponseModel faqResponseModel}) {
    _currentPage = faqResponseModel.pagination?.currentPage ?? 1;
    _totalPage = faqResponseModel.pagination?.totalPages ?? 1;

    _faqList = faqResponseModel.data ?? [];
    _emptyMesssage = faqResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getFaqList(currentPage: _currentPage);
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.faqsTxt),
      body: BlocConsumer<FaqCubit, FaqState>(
        listener: (context, state) {
          if (state is FaqLoadedState) {
            _onDataLoaded(faqResponseModel: state.faqResponseModel);
          }
          if (state is FaqErrorState) {
            GlobalMethods.errorToast(context, state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is FaqLoadingState,
              isEmpty: _faqList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: AnimationLimiter(
                child: ListView.separated(
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => height10,
                  itemCount: _faqList.length,
                  itemBuilder: (BuildContext context, int index) {
                    WebviewDataModel data = _faqList[index];

                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 1000),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: WebViewCard(
                            onTap: () {

                              GlobalMethods.pushScreen(context: context, screen: FaqDetailsScreen(
                                      title: data.title, id: data.id), screenIdentifier: ScreenIdentifier.FaqDetailsScreen);
                              // launchScreen(
                              //     context,
                              //     FaqDetailsScreen(
                              //         title: data.title, id: data.id));
                            },
                            data: data,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ));
        },
      ),
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

