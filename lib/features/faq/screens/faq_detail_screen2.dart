import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/faq/cubit/help_cubit.dart';
import 'package:rooo_driver/features/inbox/cubit/inbox_cubit.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:webview_flutter/webview_flutter.dart';

class FaqDetailsScreen extends StatefulWidget {
  final String title;
  final int id;
  const FaqDetailsScreen({super.key, required this.title, required this.id});

  @override
  State<FaqDetailsScreen> createState() => _FaqDetailsScreenState();
}

class _FaqDetailsScreenState extends State<FaqDetailsScreen> {
  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _getFaqDetail(id: widget.id);
  }

  String _description = "";
  String _emptyMessage = "";

  _getFaqDetail({required int id}) async {
    controller.setBackgroundColor(Colors.white);

    await BlocProvider.of<FaqCubit>(context).getFaqDetailsFaq(id: id);
  }

  _onPullToRefresh() {
    _getFaqDetail(id: widget.id);
  }

  _onDataLoaded({required String data}) {
    controller.loadHtmlString(data);
    _description = data;
    controller
        .setNavigationDelegate(NavigationDelegate(onPageFinished: (url) {
            if (Theme.of(context).brightness == Brightness.dark) {
          controller.runJavaScript('''
            document.body.style.backgroundColor = '#121212';
            document.body.style.color = '#FFFFFF';
            document.querySelectorAll('*').forEach(element => {
              if (element.tagName === 'A') {
                element.style.color = '#90CAF9';
              }
              if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3' || 
                  element.tagName === 'H4' || element.tagName === 'H5' || element.tagName === 'H6') {
                element.style.color = '#FFFFFF';
              }
              if (element.tagName === 'P' || element.tagName === 'SPAN' || element.tagName === 'DIV') {
                element.style.color = '#E0E0E0';
              }
            });
          ''');
        }
        }));
    ;
  }

  Widget build(BuildContext context) {
    return Scaffold(
        appBar: RoooAppbar(title: "Faq details"),
        body: BlocConsumer<FaqCubit, FaqState>(
          listener: (context, state) {
            if (state is FaqDetailLoaded) {
              _onDataLoaded(data: state.data);
            } else if (state is InboxErrorState) {
              GlobalMethods.infoToast(context,  errorMessage);
            }
          },
          builder: (context, state) {
            return ScreenBody(
                onPullToRefresh: () async => await _onPullToRefresh(),
                isLoading: state is FaqLoadingState,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                    
                      SizedBox(
                          height: MediaQuery.of(context).size.height,
                          child: WebViewWidget(controller: controller)),
                    ],
                  ),
                ),
                isEmpty:
                    state is FaqDetailLoadedState && _description.isEmpty,
                emptyMessage: errorMessage);
          },
        ));
    ;
  }
}
