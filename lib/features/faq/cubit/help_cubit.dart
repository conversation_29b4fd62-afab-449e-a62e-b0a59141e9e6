
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/faq/repository/help_repository.dart';
import 'package:rooo_driver/features/help/models/help_response_model.dart';


abstract class FaqState {}

class FaqInitState extends FaqState {}

class FaqLoadingState extends FaqState {}

class FaqDeletedState extends FaqState {}

class FaqDetailLoadedState extends FaqState {
  final String message;

  FaqDetailLoadedState({required this.message});
}

class FaqDetailLoaded extends FaqState {
  final String data;

  FaqDetailLoaded({required this.data});
}

class FaqLoadedState extends FaqState {
  final WebViewDataResponseModel faqResponseModel;

  FaqLoadedState({
    required this.faqResponseModel,
  });
}

class FaqErrorState extends FaqState {


  final String message;
  FaqErrorState({ required this.message});
}

class FaqCubit extends Cubit<FaqState> {
  FaqCubit() : super(FaqInitState());

  FaqRepository faqRepository = FaqRepository();

  void getFaq({required int current_page}) async {
    emit(FaqLoadingState());
    await faqRepository.getFaqListApi(page: current_page).then((value) {
      emit(FaqLoadedState(faqResponseModel: value));
    }).onError((error, stackTrace) {
      emit(FaqErrorState(message: "Server error"));
    });
  }

  getFaqDetailsFaq({required int id}) async {
    emit(FaqLoadingState());
    await faqRepository.getFaqDetailsApi(id: id).then((value) {
      emit(FaqDetailLoaded(data: value.description.toString()));
    }).onError((error, stackTrace) {
      emit(FaqErrorState(message: "Server error"));
    });
  }
}
