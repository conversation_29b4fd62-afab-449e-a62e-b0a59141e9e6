
import 'package:rooo_driver/features/help/models/help_model.dart';
import 'package:rooo_driver/features/help/models/help_response_model.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class FaqRepository {
  Future<WebViewDataResponseModel> getFaqListApi({required int page}) async {
    return WebViewDataResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('faq-list', method: HttpMethod.GET)));
  }

  Future<WebviewDataModel> getFaqDetailsApi({required int id}) async {
    return WebviewDataModel.fromJson(await handleResponse(
        await buildHttpResponse('faq-details/' + id.toString(),
            method: HttpMethod.GET)));
  }
}
