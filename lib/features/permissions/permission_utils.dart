import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/features/permissions/screens/location_permission_screen.dart';
import 'package:rooo_driver/features/permissions/screens/notification_permission_screen.dart';

class PermissionUtils {
  static Future<bool> checkAndRequestNotificationPermission(BuildContext context) async {
    PermissionStatus status = await Permission.notification.status;
    if (status.isGranted) return true;

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (context) => NotificationPermissionScreen()),
    );
    return result ?? false;
  }

  static Future<bool> checkAndRequestLocationPermission(BuildContext context) async {
    PermissionStatus status = await Permission.locationAlways.status;
    if (status.isGranted) return true;

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (context) => LocationPermissionScreen()),
    );
    return result ?? false;
  }
}