import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class LocationPermissionScreen extends StatefulWidget {
  @override
  LocationPermissionScreenState createState() =>
      LocationPermissionScreenState();
}

class LocationPermissionScreenState extends State<LocationPermissionScreen>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      PermissionStatus status = await Permission.locationAlways.status;
      if (status.isGranted) {
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Location"),
      body: Padding(
        padding: screenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.location_on, size: 100),
            const SizedBox(height: 20),
            Text(
              "To ensure you never miss a ride request, location permissions are required:",
              style: AppTextStyles.title(),
            ),
            height10,
            Text(
              "• ROOO will send any new nearby ride requests\n• ROOO will calculate the waiting time for your ride",
              style: AppTextStyles.subtitle(),
            ),
            height10,
            Text(
              "Based on your device OS settings, please allow location permission to \"Always\"",
              style: AppTextStyles.subtitle(),
            ),
            height20,
            AppButton(
              width: double.infinity,
              onPressed: () async {
                var status = await Permission.locationWhenInUse.request();
                if (status.isGranted) {
                  status = await Permission.locationAlways.request();
                }
                if (status.isPermanentlyDenied) {
                  await openAppSettings();
                }
              },
              text: "Allow Location Access",
            ),
          ],
        ),
      ),
    );
  }
}
