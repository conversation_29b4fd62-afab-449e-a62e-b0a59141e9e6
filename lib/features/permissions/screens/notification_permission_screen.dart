import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class NotificationPermissionScreen extends StatefulWidget {
  @override
  NotificationPermissionScreenState createState() =>
      NotificationPermissionScreenState();
}

class NotificationPermissionScreenState
    extends State<NotificationPermissionScreen> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      PermissionStatus status = await Permission.notification.status;
      if (status.isGranted) {
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Notifications"),
      body: Padding(
        padding: screenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.notification_important_rounded, size: 100),
            const SizedBox(height: 20),
            Text(
              "To ensure you never miss important updates, notifications are required:",
              style: AppTextStyles.title(),
            ),
            height10,
            Text(
              "• ROOO will send scheduled ride-related notifications, account updates, and promotions\n• You'll receive any new messages from the driver during the chat",
              style: AppTextStyles.subtitle(),
            ),
            height20,
            AppButton(
              width: double.infinity,
              onPressed: () async {
                await Permission.notification.request();
                
              },
              text: "Allow Notifications",
            ),
          ],
        ),
      ),
    );
  }
}
