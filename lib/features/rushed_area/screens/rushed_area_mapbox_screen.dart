import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mb;
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:rooo_driver/features/permissions/screens/location_permission_screen.dart';
import 'package:rooo_driver/features/rushed_area/cubit/rushed_area_cubit.dart';
import 'package:rooo_driver/features/rushed_area/models/rushed_area_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class RushedAreaMapboxScreen extends StatefulWidget {
  const RushedAreaMapboxScreen();

  @override
  State<RushedAreaMapboxScreen> createState() => _RushedAreaMapboxScreenState();
}

class _RushedAreaMapboxScreenState extends State<RushedAreaMapboxScreen>
    with WidgetsBindingObserver {
  mb.MapboxMap? mapboxMap;
  Position? _currentPosition;
  mb.PointAnnotationManager? _pointAnnotationManager;
  mb.CircleAnnotationManager? _circleAnnotationManager;

  ValueNotifier<List<RushedAreaModel>> _rushedAreaList = ValueNotifier([]);
  String _emptyMessage = "";
  bool _isLoadingCurrentLocation = true;
  bool _isCreatingCircles = false;

  Future<void> _createRushedAreaCircles(List<RushedAreaModel> areas) async {
    if (_isCreatingCircles) return;
    _isCreatingCircles = true;
    await _circleAnnotationManager?.deleteAll();

    double currentZoom =
        await mapboxMap?.getCameraState().then((state) => state.zoom) ?? 12.0;

    double baseZoom = 12.0;
    double scaleFactor = pow(2, baseZoom - currentZoom).toDouble();

    for (var area in areas) {
      double radiusInKm = area.displayRadius.toDouble();
      if (radiusInKm != 0) {
        double screenRadius = (radiusInKm * 1000) / (scaleFactor);

        screenRadius = screenRadius / 2;
        if (screenRadius < 5) {
          screenRadius = 5;
        }

        await _circleAnnotationManager?.create(
          mb.CircleAnnotationOptions(
            geometry: mb.Point(
              coordinates: mb.Position(
                area.averageLng,
                area.averageLat,
              ),
            ),
            circleRadius: screenRadius,
            circleColor: Colors.red.value,
            circleOpacity: 0.2,
            circleStrokeWidth: 2,
            circleStrokeColor: Colors.red.value,
          ),
        );
      }
    }
    setState(() {
      _isCreatingCircles = false;
    });
  }

  // Add camera change listener
  void _onCameraChanged(
      mb.CameraChangedEventData cameraChangedEventData) async {
    if (_rushedAreaList.value.isNotEmpty) {
      Future.delayed(Duration(seconds: 1), () {
        _createRushedAreaCircles(_rushedAreaList.value);
      });
    }
  }

  _onMapCreated(mb.MapboxMap mapboxMap) async {
    this.mapboxMap = mapboxMap;
    this.mapboxMap?.scaleBar.updateSettings(
        mb.ScaleBarSettings(enabled: false));

    _pointAnnotationManager =
        await mapboxMap.annotations.createPointAnnotationManager();
    _circleAnnotationManager =
        await mapboxMap.annotations.createCircleAnnotationManager();

    await _getCurrentLocation();
    if (_currentPosition != null) {
      await mapboxMap.flyTo(
          mb.CameraOptions(
            center: mb.Point(
              coordinates: mb.Position(
                _currentPosition!.longitude,
                _currentPosition!.latitude,
              ),
            ),
            zoom: 12.0,
          ),
          mb.MapAnimationOptions());
    }
  }

  Future<void> _checkLocationPermission() async {
    PermissionStatus status = await Permission.locationWhenInUse.status;
    if (status == PermissionStatus.denied) {
      Navigator.of(context).pop();
      await GlobalMethods.pushScreen(
        context: context,
        screen: LocationPermissionScreen(),
        screenIdentifier: ScreenIdentifier.LocationPermissionScreen,
      );
    }
  }

  Future<void> _getCurrentLocation() async {
    if (GlobalState.lastKnownLocation == null) {
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } else {
      _currentPosition = Position.fromMap({
        "latitude": GlobalState.lastKnownLocation!.latitude,
        "longitude": GlobalState.lastKnownLocation!.longitude,
        "accuracy": GlobalState.lastKnownLocation!.accuracy,
        "altitude": GlobalState.lastKnownLocation!.altitude,
        "heading": GlobalState.lastKnownLocation!.heading,
        "speed": GlobalState.lastKnownLocation!.speed,
        "speedAccuracy": GlobalState.lastKnownLocation!.speedAccuracy,
      });
    }

    if (_currentPosition != null) {
      await _createDriverMarker();
    }

    setState(() {
      _isLoadingCurrentLocation = false;
    });
  }

  Future<void> _createDriverMarker() async {
    final ByteData bytes = await rootBundle.load('images/driver.png');
    final Uint8List list = bytes.buffer.asUint8List();

    await _pointAnnotationManager?.create(
      mb.PointAnnotationOptions(
        geometry: mb.Point(
          coordinates: mb.Position(
            _currentPosition!.longitude,
            _currentPosition!.latitude,
          ),
        ),
        image: list,
        iconSize: 0.5,
      ),
    );
  }

  _getRushedArea() {
    BlocProvider.of<RushedAreaCubit>(context).getRushedArea();
  }

  _onPullToRefresh() async {
    await _checkLocationPermission();
    _getRushedArea();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _init();
    super.initState();
  }

  @override
  void didChangePlatformBrightness() {
    this.mapboxMap?.loadStyleURI(Theme.of(context).brightness != Brightness.dark
        ? mb.MapboxStyles.DARK
        : mb.MapboxStyles.STANDARD);
    super.didChangePlatformBrightness();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  _init() async {
    await _checkLocationPermission();
    _getRushedArea();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RushedAreaCubit, RushedAreaState>(
      listener: (context, state) {
        if (state is RushedAreaErrorState) {
          GlobalMethods.errorToast(context, state.message);
        } else if (state is RushedAreaLoadedState) {
          _rushedAreaList.value = state.rushedAreaList;

          _createRushedAreaCircles(state.rushedAreaList);
        } else if (state is RushedAreaSavedState) {
          GlobalMethods.succesToast(context, "Updated Successfully");
          _getRushedArea();
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: RoooAppbar(title: "Peak Areas"),
          body: ScreenBody(
            onPullToRefresh: () async => await _onPullToRefresh(),
            isLoading: state is RushedAreaLoadingState,
            isEmpty: false,
            emptyMessage: _emptyMessage,
            child: mb.MapWidget(
              key: ValueKey("peak_area_map"),
              styleUri: Theme.of(context).brightness == Brightness.dark
                  ? mb.MapboxStyles.DARK
                  : mb.MapboxStyles.STANDARD,
              onMapCreated: _onMapCreated,
              onCameraChangeListener: _onCameraChanged,
              gestureRecognizers: {
                Factory<OneSequenceGestureRecognizer>(
                    () => EagerGestureRecognizer()),
              },
            ),
          ),
        );
      },
    );
  }
}
