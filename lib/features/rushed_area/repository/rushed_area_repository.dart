import 'dart:async';

import 'package:rooo_driver/features/rushed_area/models/rushed_area_response_model.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class RushedAreaRepository {
  Future<RushedAreaResponseModel> getRushedAreaApi() async {
    return RushedAreaResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('peak-hour-data', method: HttpMethod.GET)));
  }
}
