class RushedAreaModel {
  final int id;
  final double averageLat;
  final double averageLng;
  final int totalRides;
  final num displayRadius;
  final int isSame;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  // Constructor
  RushedAreaModel({
    required this.id,
    required this.averageLat,
    required this.averageLng,
    required this.totalRides,
    required this.displayRadius,
    required this.isSame,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  // Factory method to create an object from JSON
  factory RushedAreaModel.fromJson(Map<String, dynamic> json) {
    return RushedAreaModel(
      id: json['id'],
      averageLat: json['average_lat'].toDouble(),
      averageLng: json['average_lng'].toDouble(),
      totalRides: json['total_rides'],
      displayRadius: json['display_radius'],
      isSame: json['is_same'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      deletedAt: json['deleted_at'] != null ? DateTime.parse(json['deleted_at']) : null,
    );
  }

  // Method to convert the object to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'average_lat': averageLat,
      'average_lng': averageLng,
      'total_rides': totalRides,
      'display_radius': displayRadius,
      'is_same': isSame,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }
}
