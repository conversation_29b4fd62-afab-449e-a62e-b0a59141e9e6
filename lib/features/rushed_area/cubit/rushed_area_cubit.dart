import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rooo_driver/features/rushed_area/models/rushed_area_model.dart';
import 'package:rooo_driver/features/rushed_area/repository/rushed_area_repository.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class RushedAreaState {}

class RushedAreaInitState extends RushedAreaState {}

class RushedAreaLoadingState extends RushedAreaState {}



class RushedAreaSavedState extends RushedAreaState {}
class RushedAreaDeleteState extends RushedAreaState {}



class RushedAreaLoadedState extends RushedAreaState {
  final List<RushedAreaModel> rushedAreaList;

  RushedAreaLoadedState({
    required this.rushedAreaList,
  });
}




class RushedAreaErrorState extends RushedAreaState {
  final String message;
  RushedAreaErrorState({  required this.message,});
}

class RushedAreaCubit extends Cubit<RushedAreaState> {
  RushedAreaCubit() : super(RushedAreaInitState());

  RushedAreaRepository rushedAreaRepository = RushedAreaRepository();
  

  void getRushedArea() async {
    emit(RushedAreaLoadingState());
    await rushedAreaRepository
        .getRushedAreaApi( )
        .then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(RushedAreaLoadedState(rushedAreaList: value.data!));
        } else {
          emit(RushedAreaErrorState(message: serverErrorMessage));
        }
      } else {
        emit(RushedAreaErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(RushedAreaErrorState(message: "Server error"));
    });
  }




}
