// import 'package:flutter/widgets.dart';
// import 'package:rooo_driver/features/registration/cubit/registration_cubit.dart';
// import 'package:rooo_driver/features/registration/screens/new_registration_screen.dart';
// import 'package:rooo_driver/global/export/app_export.dart';
// import 'package:rooo_driver/global/widgets/screen_body.dart';

// class EmailRegistrationScreen extends StatefulWidget {
//     final bool isFromPhone;
//       final String countryIsoCode;
//         final String phone;
//           final String countryCode;




//   const EmailRegistrationScreen({super.key, required this.isFromPhone, required this.countryIsoCode,required this.phone, required  this.countryCode});

//   @override
//   State<EmailRegistrationScreen> createState() =>
//       _EmailRegistrationScreenState();
// }

// class _EmailRegistrationScreenState extends State<EmailRegistrationScreen> {
//   _registerEmail({required String email}) {
//     BlocProvider.of<RegistrationCubit>(context).registerEmail(email: email);
//   }

//   TextEditingController _emailController = TextEditingController();
//     final _formKey = GlobalKey<FormState>();

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: RoooAppbar(title: "Email registration "),
//       body: BlocConsumer<RegistrationCubit, RegistrationState>(
//         listener: (context, state) {
//           if (state is EmailRegisteredState) {

//             GlobalMethods.succesToast(context, "email regstered successfully");

//                launchScreen(
//                 context,
//                 NewRegistrationScreen(
//                   email: state.user.user!.email!,
//                   phone: widget.phone,
//                   countryCode: widget.countryCode,
//                   countryIsoCode: widget.countryIsoCode,
//                   isFromPhone: true,
//                 ));
//           } else if (state is RegistrationErrorState) {
//             GlobalMethods.hanldeError(
//                 context: context,
//                 message: state.message,
//                 message: state.unmessage);
//           }
//           // TODO: implement listener
//         },
//         builder: (context, state) {
//           return ScreenBody(
//             isEmpty: false,
//             emptyMessage: "",
//             isLoading: state is RegistrationLoadingState,
//             child: Padding(
//       padding: EdgeInsets.all(16.0),
//       child: Form(
//         key: _formKey,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text("Email",style: AppTextStyles.title,),
//             TextFormField(

//               controller: _emailController,
//               // decoration: InputDecoration(
//               //   labelText: 'Email',
//               //   border: OutlineInputBorder(),
//               // ),
//               validator: validateEmail,
//               keyboardType: TextInputType.emailAddress,
//             ),
// height20,height20,height20,            AppButton(
//               width: double.infinity,
//               onPressed: () {
//                 if (_formKey.currentState?.validate() ?? false) {

//                   _registerEmail(email: _emailController.text);
//                   // Process the data if the form is valid
//                   // ScaffoldMessenger.of(context).showSnackBar(
//                   //   SnackBar(content: Text('Processing Data')),
//                   // );
//                 }
//               },
//               text: "Next",
//             ),
//           ],
//         ),
//       ),
//     )
//           );
//         },
//       ),
//     );
//   }
//   String? validateEmail(String? value) {
//   // Define a regex pattern for email validation
//   final RegExp emailRegex = RegExp(
//     r'^[^@]+@[^@]+\.[^@]+$',
//     caseSensitive: false,
//     multiLine: false,
//   );

//   // Check if the email is empty
//   if (value == null || value.isEmpty) {
//     return 'Email cannot be empty';
//   }

//   // Check if the email matches the regex pattern
//   if (!emailRegex.hasMatch(value)) {
//     return 'Please enter a valid email address';
//   }

//   // Return null if the email is valid
//   return null;
// }
// }
