import 'dart:async';
import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pinput/pinput.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/registration/models/vehicle_response_model.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/global/constants/app_enums.dart';
import 'package:rooo_driver/global/constants/app_text_styles.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/registration/cubit/registration_cubit.dart';
import 'package:rooo_driver/features/registration/models/vehicle_model.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/screens/PrivacyPolicyScreen.dart';
import 'package:rooo_driver/screens/TermsAndConditionsScreen.dart';
import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';

import 'package:rooo_driver/utils/Extensions/StringExtensions.dart';
import 'package:rooo_driver/utils/Extensions/app_common.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class NewRegistrationScreen extends StatefulWidget {
  final String countryIsoCode;
  final bool socialLogin;
  final String? userName;
  final bool isOtp;
  final String? countryCode;
  final String? privacyPolicyUrl;
  final String? termsConditionUrl;
  final String? phone;
  final String? name;
  final String? email;
  final bool isFromPhone;

  NewRegistrationScreen({
    this.socialLogin = false,
    this.userName,
    this.name,
    this.email,
    this.isOtp = false,
    this.countryCode,
    this.privacyPolicyUrl,
    this.termsConditionUrl,
    this.phone,
    this.isFromPhone = false,
    required this.countryIsoCode,
  });

  @override
  State<NewRegistrationScreen> createState() => _NewRegistrationScreenState();
}

class _NewRegistrationScreenState extends State<NewRegistrationScreen> {
  RegistrationCubit? bloc;

  ValueNotifier<int> currentIndex = ValueNotifier(0);

  ValueNotifier<bool> _isReferralCodeVerified = ValueNotifier(false);

  ValueNotifier<bool> _isUserWantsToEnterReferralCode = ValueNotifier(false);

  ProvinceModel? _selectedProvince;

  List<ProvinceModel> _provinceData = [];
  List<ProvinceModel> _filtereedProvinceData = [];

  ValueNotifier<int> selectVehicleIndex = ValueNotifier(-1);
  ValueNotifier<bool> isAcceptedTermsAndConditions = ValueNotifier(false);

  TextEditingController _provinceInputController = TextEditingController();
  FocusNode _provinceInputFocusNode = FocusNode();

  int _selectedVehicleId = -1;

  List<int> filledIndexes = [];

  VehicleResponseModel? vehicleResponseModel;
  /////////////////////////////////////////////////////////
//gender and terms and conditions
  List<String> genderList = [SELECT, MALE, FEMALE, PREFER_NOT_TO_SAY, OTHER];
  ValueNotifier<String> _selectedGender = ValueNotifier(SELECT);
  String _otherGender = "";

  ValueNotifier<XFile> profileImage = ValueNotifier(XFile(""));

  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  // final _userNameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  String? _countryCode;

  final _carModelController = TextEditingController();

  final carProductionYearController = TextEditingController();

  final _carPlateNumberController = TextEditingController();

  final carColorController = TextEditingController();

  final dobController = TextEditingController();
  final _referralController = TextEditingController();

  final FocusNode _referralFocusNode = FocusNode();

  List<GlobalKey<FormState>> formKeys = [
    GlobalKey<FormState>(), //personalDetailsForm
    GlobalKey<FormState>(), //_emailAndSecurity
    GlobalKey<FormState>(),
  ];

  bool _isEmailOTPSent = false;
  bool _isEmailOTPVerified = false;
  TextEditingController _emailOTPController = TextEditingController();
  Timer? _emailVerificationTimer;
  int _emailOTPCountdownTimer = 60;
  bool _isNotifiedForTheEmailVerification = false;

  bool _isSelectingDOB = false;
  bool _wasDOBSelected = false;

  @override
  void dispose() {
    super.dispose();
    bloc?.close();
    _emailVerificationTimer?.cancel();
  }

  @override
  void initState() {
    super.initState();
    _phoneNumberController.text = widget.phone.toString();
    // dobController.text = GlobalMethods.dateToUIString(lastDOB);

    _isEmailOTPVerified = widget.socialLogin;

    _countryCode = widget.countryCode;

    // Add focus listener for DOB field
    dobFocus.addListener(() {
      if (dobFocus.hasFocus && !_isSelectingDOB) {
        if (!_wasDOBSelected) {
          _getDOB();
        }
      }
    });

    _referralFocusNode.addListener(() {
      if (_referralFocusNode.hasFocus) {
        Scrollable.ensureVisible(
          _referralFocusNode.context!,
          alignment: 0.1,
          duration: Duration(milliseconds: 300),
        );
      }
    });

    _provinceInputFocusNode.addListener(() {
      if (!_provinceInputFocusNode.hasFocus) {
        setState(() {
          _filtereedProvinceData = [];
        });
      } else {
        Scrollable.ensureVisible(
          _provinceInputFocusNode.context!,
          alignment: 0.1,
          duration: Duration(milliseconds: 300),
        );

        if (_selectedProvince == null) {
          _filtereedProvinceData = _getFilteredProvinceData("");

          setState(() {
            _selectedProvince = null;
          });
        }
      }
    });

    _getProvinceData();
  }

  _verifyCoupon({required String referral_code}) {
    hideKeyboard(context);
    Map<String, dynamic> request = {"referral_code": referral_code};
    BlocProvider.of<RegistrationCubit>(context)
        .verifyReferral(request: request);
  }

  _getProvinceData() {
    BlocProvider.of<RegistrationCubit>(context).getprovinceList();
  }

  register({required MultipartRequest multiPartRequest}) {
    BlocProvider.of<RegistrationCubit>(context).completeProfile(
      multiPartRequest: multiPartRequest,
    );
  }

  FocusNode firstNameFocus = FocusNode();
  FocusNode lastNameFocus = FocusNode();

  FocusNode emailFocus = FocusNode();
  FocusNode passwordFocus = FocusNode();

  // FocusNode userNameFocus = FocusNode();
  FocusNode dobFocus = FocusNode();

  // carModel focusnode only
  FocusNode carModel = FocusNode();
  FocusNode carProductionYear = FocusNode();
  FocusNode carPlateNumber = FocusNode();
  FocusNode carColor = FocusNode();

  DateTime firstDOB = DateTime.now().subtract(Duration(days: 365 * 90));
  DateTime lastDOB = DateTime.now().subtract(Duration(days: 365 * 20));
  DateTime selectedDOB = DateTime.now().subtract(Duration(days: 365 * 20));

  void _getDOB() {
    if (_isSelectingDOB) return;
    _isSelectingDOB = true;

    showDatePicker(
      context: context,
      initialDate: selectedDOB,
      firstDate: firstDOB,
      lastDate: lastDOB,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Colors.white,
              surface: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[900]!
                  : Colors.white,
              onSurface: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            ),
            dialogBackgroundColor:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[900]
                    : Colors.white,
          ),
          child: child!,
        );
      },
    ).then((value) {
      _isSelectingDOB = false;
      if (value != null) {
        selectedDOB = value;
        _wasDOBSelected = true;
        setState(() {
          dobController.text = GlobalMethods.dateToUIString(selectedDOB);
        });
      }
    });
  }

  titleText({required String title}) {
    return Text(
      title,
      style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
    );
  }

  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.signUp),
      body: BlocConsumer<RegistrationCubit, RegistrationState>(
        bloc: bloc,
        listener: (context, state) {
          if (state is RegistrationErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is RegisterationCompletedState) {
            GlobalMethods.pushAndRemoveAll(
                context: context,
                screen: RideScreen(
                  isNewSignUp: true,
                ),
                screenIdentifier: ScreenIdentifier.InitialScreen);
          } else if (state is ReferralCodeVerifiedState) {
            _isReferralCodeVerified.value = true;
          } else if (state is ProvinceListLoadedState) {
            _provinceData = state.provinceList;
            _selectedProvince = state.provinceList[0];
            _provinceInputController.text = _selectedProvince!.provinceName;
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is RegistrationLoadingState,
            isEmpty: false,
            emptyMessage: '',
            onPullToRefresh: () {
              return Future.value();
            },
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 100.0),
                child: Column(
                  children: [
                    ValueListenableBuilder<int>(
                      valueListenable: currentIndex,
                      builder: (context, state, child) {
                        return Stepper(
                            physics: NeverScrollableScrollPhysics(),
                            controlsBuilder: (stepIndex, stepState) {
                              if (stepState.currentStep == 4) {
                                return AppButton(
                                    width: double.infinity,
                                    text: "Register",
                                    onPressed: () async {
                                      if (!isAcceptedTermsAndConditions.value) {
                                        GlobalMethods.errorToast(context,
                                            "Please accept the terms and conditions");
                                        return;
                                      }

                                      MultipartRequest multiPartRequest =
                                          await getMultiPartRequest(
                                              'driver-signup');
                                      multiPartRequest.fields['first_name'] =
                                          _firstNameController.text;
                                      multiPartRequest.fields['last_name'] =
                                          _lastNameController.text;
                                      multiPartRequest.fields['email'] =
                                          _emailController.text;
                                      multiPartRequest.fields['password'] =
                                          _passwordController.text;
                                      multiPartRequest.fields['dob'] =
                                          GlobalMethods.dateToAPIString(
                                              selectedDOB);
                                      multiPartRequest.fields['user_type'] =
                                          'driver';
                                      multiPartRequest
                                              .fields['contact_number'] =
                                          _countryCode! +
                                              _phoneNumberController.text;

                                      multiPartRequest.fields['player_id'] =
                                          sharedPref
                                              .getString(PLAYER_ID)
                                              .toString();

                                      multiPartRequest.fields['region_id'] =
                                          _selectedProvince!.regionId
                                              .toString();
                                      multiPartRequest.fields['province_id'] =
                                          _selectedProvince!.id.toString();

                                      multiPartRequest.fields['gender'] =
                                          _selectedGender.value;
                                      if (_selectedGender.value == OTHER) {
                                        multiPartRequest
                                                .fields['other_gender_text'] =
                                            _otherGender;
                                      }
                                      if (_isReferralCodeVerified.value &&
                                          _isUserWantsToEnterReferralCode
                                              .value) {
                                        multiPartRequest
                                                .fields["referral_code"] =
                                            _referralController.text;
                                      }
                                      multiPartRequest.fields["carCategory"] =
                                          _selectedVehicleId.toString();
                                      multiPartRequest.fields["carModel"] =
                                          _carModelController.text;

                                      multiPartRequest
                                              .fields["carPlateNumber"] =
                                          _carPlateNumberController.text;

                                      multiPartRequest
                                              .fields["carProductionYear"] =
                                          carProductionYearController.text;

                                      multiPartRequest.files.add(
                                          await MultipartFile.fromPath(
                                              'profile_image[]',
                                              profileImage.value.path));

                                      register(
                                          multiPartRequest: multiPartRequest);
                                    });
                              } else {
                                return Row(
                                  children: [
                                    AppButton(
                                        text: language.continueText,
                                        onPressed: () async {
                                          if (currentIndex.value == 0) {
                                            if (_selectedGender.value ==
                                                SELECT) {
                                              GlobalMethods.errorToast(context,
                                                  "Please select gender");
                                              return;
                                            }
                                            if (formKeys[0]
                                                    .currentState
                                                    ?.validate() ??
                                                false) {
                                              incrementCurrentIndex(
                                                  index: currentIndex.value);
                                            }
                                          } else if (currentIndex.value == 1) {
                                            if (profileImage.value.path == '') {
                                              GlobalMethods.errorToast(context,
                                                  "Please upload image");
                                            } else {
                                              incrementCurrentIndex(
                                                  index: currentIndex.value);
                                            }
                                          } else if (currentIndex.value == 2) {
                                            // related to email and security
                                            if (formKeys[1]
                                                    .currentState
                                                    ?.validate() ??
                                                false) {
                                              if (!_isEmailOTPSent) {
                                                _showEmailVerificationDialog();
                                              } else if (_emailOTPController
                                                      .text
                                                      .trim()
                                                      .length <
                                                  6) {
                                                GlobalMethods.errorToast(
                                                    context,
                                                    "Please enter email verification code");
                                              } else if (!_isEmailOTPVerified) {
                                                await _verifyEmailOtp(
                                                  email: _emailController.text
                                                      .trim(),
                                                  otp: _emailOTPController.text
                                                      .trim(),
                                                );
                                              } else {
                                                incrementCurrentIndex(
                                                    index: currentIndex.value);
                                              }
                                            }
                                          } else if (currentIndex.value == 3) {
                                            if (_isUserWantsToEnterReferralCode
                                                .value) {
                                              if (formKeys[2]
                                                      .currentState
                                                      ?.validate() ??
                                                  false) {
                                                if (_isReferralCodeVerified
                                                    .value) {
                                                  incrementCurrentIndex(
                                                      index:
                                                          currentIndex.value);
                                                } else {
                                                  await _verifyCoupon(
                                                      referral_code:
                                                          _referralController
                                                              .text);
                                                }
                                              }
                                            } else {
                                              incrementCurrentIndex(
                                                  index: currentIndex.value);
                                            }
                                          } else if (currentIndex.value == 4) {
                                            if (isAcceptedTermsAndConditions
                                                .value) {
                                            } else {
                                              GlobalMethods.errorToast(context,
                                                  "Please accept the terms and conditions");
                                            }
                                          }
                                        }),
                                    width20,
                                    AppButton(
                                        text: language.cancelTxt,
                                        onPressed: () async {
                                          if (currentIndex.value > 0) {
                                            currentIndex.value =
                                                currentIndex.value - 1;
                                          }
                                        })
                                  ],
                                );
                              }
                            },
                            onStepTapped: (value) {
                              if (filledIndexes.contains(value)) {
                                currentIndex.value = value;
                              }
                            },
                            currentStep: state,
                            steps: [
                              Step(
                                  title: titleText(title: "Details"),
                                  content: personalDetailsForm()),
                              Step(
                                  title: titleText(title: "Picture"),
                                  content: _profileImage()),
                              Step(
                                  title: titleText(title: "Email and Security"),
                                  content: _emailAndSecurity()),
                              Step(
                                  title:
                                      titleText(title: "Select Referral Code"),
                                  content: referralWidget()),
                              Step(
                                  title:
                                      titleText(title: "Terms and Conditions"),
                                  content: termsAndCondition()),
                            ]);
                      },
                    ),
                   
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  //  Widget carDetails() {
  //   return Form(
  //     key: formKeys[1],
  //     child: Column(
  //       children: [
  //         height10,
  //         AppTextField(
  //             textFieldType: TextFieldType.OTHER,
  //             controller: _carModelController,
  //             focus: carModel,
  //             nextFocus: carProductionYear,
  //             // focus: ageController,
  //             // nextFocus: lastNameFocus,
  //             validator: (value) {
  //               if (value?.isEmpty ?? false || value == null) {
  //                 return "Please enter valid car model";
  //               } else {
  //                 return null;
  //               }
  //             },
  //             errorThisFieldRequired: language.thisFieldRequired,
  //             decoration: InputDecoration(
  //                 hintText: "Car model", labelText: "Car Model")),
  //         height10,
  //         AppTextField(
  //             textFieldType: TextFieldType.PHONE,
  //             controller: carProductionYearController,
  //             keyboardType: TextInputType.number,
  //             focus: carProductionYear,
  //             nextFocus: carPlateNumber,
  //             // focus: ageController,
  //             // nextFocus: lastNameFocus,
  //             validator: (value) {
  //               final RegExp _yearRegex = RegExp(r'^\d{4}$');

  //               if (!_yearRegex.hasMatch(value.toString())) {
  //                 return "Please enter valid car production year";
  //               } else {
  //                 return null;
  //               }
  //             },
  //             errorThisFieldRequired: language.thisFieldRequired,
  //             decoration: InputDecoration(
  //                 hintText: "Car production year",
  //                 labelText: "Car production year")),
  //         height10,
  //         AppTextField(
  //             textFieldType: TextFieldType.OTHER,
  //             controller: _carPlateNumberController,
  //             focus: carPlateNumber,
  //             nextFocus: carColor,
  //             // focus: ageController,
  //             // nextFocus: lastNameFocus,
  //             validator: (value) {
  //               if (value?.isEmpty ?? false || value == null) {
  //                 return "Please enter valid car plate number";
  //               } else {
  //                 return null;
  //               }
  //             },
  //             errorThisFieldRequired: language.thisFieldRequired,
  //             decoration: InputDecoration(
  //                 hintText: "Car plate Number", labelText: "Car plate number")),
  //         height10,
  //         AppTextField(
  //             textFieldType: TextFieldType.OTHER,
  //             controller: carColorController,
  //             focus: carColor,
  //             // focus: ageController,
  //             // nextFocus: lastNameFocus,
  //             validator: (value) {
  //               if (value?.isEmpty ?? false || value == null) {
  //                 return "Please enter valid car color";
  //               } else {
  //                 return null;
  //               }
  //             },
  //             errorThisFieldRequired: language.thisFieldRequired,
  //             decoration: InputDecoration(
  //                 hintText: "Car Color", labelText: "Car color")),
  //       ],
  //     ),
  //   );
  // }

  Widget personalDetailsForm() {
    return Form(
      key: formKeys[0],
      child: Column(
        children: [
          height10,
          AppTextField(
              textFieldType: TextFieldType.NAME,
              controller: _firstNameController,
              focus: firstNameFocus,
              nextFocus: lastNameFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: (value) {
                FocusScope.of(context).requestFocus(lastNameFocus);
              },
              validator: (value) {
                if (value?.isEmpty ?? false || value == null) {
                  return "Please enter valid name";
                } else {
                  return null;
                }
              },
              errorThisFieldRequired: language.thisFieldRequired,
              decoration: InputDecoration(
                  hintText: language.firstName, labelText: language.firstName)),
          height10,
          AppTextField(
              textFieldType: TextFieldType.NAME,
              controller: _lastNameController,
              focus: lastNameFocus,
              nextFocus: dobFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: (value) {
                // Instead of directly focusing DOB, we'll let the focus listener handle it
                FocusScope.of(context).requestFocus(dobFocus);
              },
              validator: (value) {
                if (value?.isEmpty ?? false || value == null) {
                  return "Please enter valid name";
                } else {
                  return null;
                }
              },
              errorThisFieldRequired: language.thisFieldRequired,
              decoration: InputDecoration(
                  hintText: language.lastName, labelText: language.lastName)),
          height10,
          AppTextField(
            textFieldType: TextFieldType.OTHER,
            controller: dobController,
            focus: dobFocus,
            textInputAction: TextInputAction.done,
            onFieldSubmitted: (value) {
              dobFocus.unfocus();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "Please select DOB";
              }
              return null;
            },
            errorThisFieldRequired: language.thisFieldRequired,
            decoration: InputDecoration(hintText: "DOB", labelText: "DOB"),
            readOnly: true,
            onTap: () {
              _wasDOBSelected = false;
              _getDOB();
            },
          ),
          height10,
          ValueListenableBuilder<String>(
            valueListenable: _selectedGender,
            builder: (context, value, child) {
              if (genderList.isEmpty || _selectedGender.value == -1) {
                return SizedBox();
              }
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Gender", style: AppTextStyles.title()),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                          width: 1,
                          color:
                              AppColors.primaryColor(context).withOpacity(.6)),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String?>(
                          isExpanded: true,
                          value: value == -1 ? genderList[0] : value,
                          items: genderList.map((e) {
                            return DropdownMenuItem<String>(
                                value: e, child: Text(getTitleCase(e)));
                          }).toList(),
                          onChanged: (value) {
                            _selectedGender.value = value!;
                          }),
                    ),
                  ),
                  _selectedGender.value != "other"
                      ? const SizedBox()
                      : Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: AppTextField(
                            textFieldType: TextFieldType.OTHER,
                            label: "Please enter",
                            initialValue: _otherGender,
                            onChanged: (value) {
                              _otherGender = value;
                            },
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return "Please enter";
                              }
                              return null;
                            },
                          ),
                        ),
                ],
              );
            },
          ),
          height10,
          TextFormField(
            focusNode: _provinceInputFocusNode,
            decoration: InputDecoration(
              label: Text("State"),
              hintText: "Type to search...",
              suffixIcon: Icon(
                Icons.search,
              ),
            ),
            controller: _provinceInputController,
            validator: (value) {
              if (_selectedProvince == null) {
                return "Please select a valid state";
              }
              return null;
            },
            onChanged: (value) {
              _filtereedProvinceData = _getFilteredProvinceData(value);
              if (_filtereedProvinceData.isEmpty) {
                _filtereedProvinceData.add(
                  ProvinceModel(
                    id: -1,
                    regionId: -1,
                    regionName: "",
                    provinceName: "No state found",
                  ),
                );
              }
              setState(() {
                _selectedProvince = null;
              });
            },
          ),
          Card(
            child: Container(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade400
                  : Colors.grey.shade100,
              height: _filtereedProvinceData.length > 0 ? 200 : 0,
              child: ListView.separated(
                // shrinkWrap: true,
                // physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(
                      _filtereedProvinceData[index].provinceName,
                      style: TextStyle(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black),
                    ),
                    subtitle: Text(
                      _filtereedProvinceData[index].regionName,
                      style: TextStyle(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey.shade300
                              : Colors.black),
                    ),
                    onTap: () {
                      if (_filtereedProvinceData[index].id == -1) {
                        return;
                      }
                      _selectedProvince = _filtereedProvinceData[index];
                      _provinceInputController.text =
                          _selectedProvince!.provinceName;
                      setState(() {
                        _filtereedProvinceData = [];
                      });
                    },
                  );
                },
                separatorBuilder: (context, index) => Divider(),
                itemCount: _filtereedProvinceData.length,
              ),
            ),
          ),
          height10,
          TextFormField(
            controller: _phoneNumberController,
            autofocus: true,
            readOnly: true,
            maxLength: 10,
            validator: (value) {
              if ((value?.length ?? 0) < 9) {
                return "Please enter valid mobile number";
              }
              return null;
            },
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
            ),
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            decoration: InputDecoration(
                counterText: "",
                prefixIcon: Padding(
                  padding: const EdgeInsets.all(14),
                  child: Text(
                    "+61",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
                border: OutlineInputBorder()),
          ),
          height10,
        ],
      ),
    );
  }

  Widget _emailAndSecurity() {
    return Form(
      key: formKeys[1],
      child: Column(
        children: [
          height10,
          AppTextField(
              textFieldType: TextFieldType.EMAIL,
              controller: _emailController,
              focus: emailFocus,
              nextFocus: passwordFocus,
              validator: (value) {
                final RegExp _emailRegex = RegExp(
                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                );
                if (!_emailRegex.hasMatch(value.toString())) {
                  return "Please enter valid email";
                } else {
                  return null;
                }
              },
              errorThisFieldRequired: language.thisFieldRequired,
              readOnly: _isEmailOTPVerified,
              decoration: InputDecoration(
                hintText: language.email,
                labelText: language.email,
                suffixIcon: _isEmailOTPVerified
                    ? Icon(
                        Icons.check,
                        color: Colors.green,
                      )
                    : null,
              )),
          height10,
          !_isEmailOTPSent || _isEmailOTPVerified
              ? const SizedBox()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Please enter the verification code"),
                    height10,
                    Pinput(
                      controller: _emailOTPController,
                      defaultPinTheme: PinTheme(
                        width: 55,
                        height: 55,
                        decoration: BoxDecoration(
                          border: Border.all(
                              width: .5,
                              color: Theme.of(context).colorScheme.primary),
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      length: 6,
                      onCompleted: (pin) {
                        // _otp = pin;
                      },
                    ),
                    height20,
                    _timerView(),
                  ],
                ),
          AppTextField(
            textFieldType: TextFieldType.PASSWORD,
            controller: _passwordController,
            focus: passwordFocus,
            label: "Password",
            validator: (value) {
              if (value!.length < 6) {
                return "Password must be atleast 6 chars. long";
              } else {
                return null;
              }
            },
            errorThisFieldRequired: language.thisFieldRequired,
          ),
        ],
      ),
    );
  }

  // Widget AgeWidget() {
  //   return AppTextField(
  //
  //     textFieldType: TextFieldType.NAME,
  //     controller: ageController,
  //     // focus: ageController,
  //     // nextFocus: lastNameFocus,
  //     errorThisFieldRequired: language.thisFieldRequired,
  //     decoration: inputDecoration(context,
  //
  //         hint
  //         hint: language.email),
  //   );
  // }

  Widget referralWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Do you have referral code?"),
        Row(
          children: [
            Text("No"),
            width10,
            Switch(
              value: _isUserWantsToEnterReferralCode.value,
              onChanged: (value) {
                setState(() {
                  _isUserWantsToEnterReferralCode.value = value;
                });
              },
            ),
            width10,
            Text("Yes"),
          ],
        ),
        !_isUserWantsToEnterReferralCode.value
            ? const SizedBox()
            : Row(
                children: [
                  Expanded(
                    child: Form(
                      key: formKeys[2],
                      child: TextFormField(
                          focusNode: _referralFocusNode,
                          controller: _referralController,
                          onChanged: (v) {
                            _isReferralCodeVerified.value = false;
                          },
                          validator: (value) {
                            if (value?.isEmpty ?? false || value == null) {
                              return "Please enter valid referralcode";
                            } else {
                              return null;
                            }
                          },
                          decoration: InputDecoration(
                            hintText: "Referral Code",
                          )),
                    ),
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: _isReferralCodeVerified,
                    builder: (context, value, child) {
                      if (value) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: CircleAvatar(
                              radius: 15,
                              backgroundColor: AppColors.greenColor,
                              child: Icon(
                                Icons.check,
                                color: Colors.white,
                              )),
                        );
                      }
                      return SizedBox();
                    },
                  )
                ],
              ),
      ],
    );
  }

  Widget SelectVehicleDropDown() {
    if (vehicleResponseModel == null) {
      return SizedBox();
    }
    return ListView.separated(
        separatorBuilder: (context, index) => height10,
        itemCount: vehicleResponseModel!.data!.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          VehicleModel data = vehicleResponseModel!.data![index];
          // _selectedVehicleId =
          //     state.vehicleResponseModel.data?[0].id ?? -1;
          return ValueListenableBuilder(
            valueListenable: selectVehicleIndex,
            builder: (context, state, _) {
              return InkWell(
                onTap: () {
                  selectVehicleIndex.value = index;
                  _selectedVehicleId = data.id ?? -1;
                },
                child: Container(
                  padding: screenPadding / 4,
                  decoration: BoxDecoration(
                      borderRadius: radius(),
                      border: Border.all(
                          width: .5,
                          color: selectVehicleIndex.value == index
                              ? AppColors.greenColor
                              : AppColors.blackColor(context))),
                  child: Row(
                    children: [
                      commonCachedNetworkImage(data.serviceImage,
                          fit: BoxFit.contain, height: 50, width: 50),
                      SizedBox(width: 16),
                      Expanded(
                        child:
                            Text(data.name.validate(), style: boldTextStyle()),
                      ),
                      Visibility(
                        visible: selectVehicleIndex.value == index,
                        child: Icon(Icons.check_circle_outline,
                            color: Colors.green),
                      )
                    ],
                  ),
                ),
              );
            },
          );
        });
  }

  Widget termsAndCondition() {
    return Column(
      children: [
        ValueListenableBuilder<bool>(
          valueListenable: isAcceptedTermsAndConditions,
          builder: (context, value, child) {
            return CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              controlAffinity: ListTileControlAffinity.leading,
              title: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${language.agreeToThe} ',
                      style: TextStyle(color: AppColors.blackColor(context)),
                    ),
                    TextSpan(
                      text: language.termsConditions,
                      style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryColor(context)),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          GlobalMethods.pushScreen(
                              context: context,
                              screen: TermsAndConditionsScreen(),
                              screenIdentifier:
                                  ScreenIdentifier.TermsAndConditionsScreen);

                          // launchScreen(context, TermsAndConditionsScreen(),
                          //     pageRouteAnimation: PageRouteAnimation.Slide);
                        },
                    ),
                    TextSpan(
                      text: ' & ',
                      style: TextStyle(color: AppColors.blackColor(context)),
                    ),
                    TextSpan(
                      text: language.privacyPolicy,
                      style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryColor(context)),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          GlobalMethods.pushScreen(
                              context: context,
                              screen: PrivacyPolicyScreen(),
                              screenIdentifier:
                                  ScreenIdentifier.PrivacyPolicyScreen);
                          // launchScreen(context, PrivacyPolicyScreen(),
                          //     pageRouteAnimation: PageRouteAnimation.Slide);
                        },
                    ),
                  ],
                ),
                textAlign: TextAlign.left,
              ),
              value: value,
              onChanged: (val) async {
                isAcceptedTermsAndConditions.value = val!;
                filledIndexes.add(5);
              },
            );
          },
        ),
      ],
    );
  }

  Future<void> getImage() async {
    XFile? selectedImage = await ImagePicker()
        .pickImage(source: ImageSource.camera, imageQuality: 50);

    if (selectedImage != null) {
      bool value = await GlobalMethods.checkImageSize(
          result: selectedImage, context: context);
      if (value) {
        profileImage.value = selectedImage;
      }
    }
  }

  Widget _profileImage() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Stack(
          alignment: Alignment.bottomRight,
          children: [
            SizedBox(
              height: 100,
              width: 100,
              child: ValueListenableBuilder<XFile>(
                valueListenable: profileImage,
                builder: (context, value, child) {
                  if (value.path != "") {
                    return Center(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: Image.file(File(value.path),
                            height: 100,
                            width: 100,
                            fit: BoxFit.cover,
                            alignment: Alignment.center),
                      ),
                    );
                  }
                  {
                    return Center(
                      child: Padding(
                        padding: EdgeInsets.only(left: 4, bottom: 4),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(100),
                          child: Image.asset('images/ic_person.jpg',
                              height: 90, width: 90),
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
            inkWellWidget(
              onTap: () {
                getImage();
              },
              child: Container(
                  padding: screenPadding / 3,
                  decoration: BoxDecoration(
                      color: Colors.black,
                      shape: BoxShape.circle,
                      border: Border.all()),
                  child: Icon(
                    Icons.edit,
                    color: Colors.white,
                  )),
            )
          ],
        ),
      ],
    );
  }

  incrementCurrentIndex({required int index}) {
    currentIndex.value = currentIndex.value + 1;
    filledIndexes.add(index);
  }

  List<ProvinceModel> _getFilteredProvinceData(String text) {
    return _provinceData
        .where((o) => o.provinceName.toLowerCase().contains(text.toLowerCase()))
        .toList();
  }

  void _showEmailVerificationDialog() {
    if (_isNotifiedForTheEmailVerification) {
      _sendEmailOtp(email: _emailController.text.trim(), resendOTP: true);
      return;
    }
    GlobalMethods.showInfoDialogNew(
      context: context,
      onClick: () {
        _isNotifiedForTheEmailVerification = true;
        _sendEmailOtp(email: _emailController.text.trim(), resendOTP: false);
      },
      title:
          "A verification code will be sent to your email. Please verify your email address to continue",
    );
  }

  Future<void> _sendEmailOtp({
    required String email,
    required bool resendOTP,
  }) async {
    if (!resendOTP) {
      Navigator.of(context).pop();
    }
    _showSendingEmailOTP();
    var response = await sendOTPToEmail(email: email);
    Navigator.of(context).pop();
    if (!response.status) {
      GlobalMethods.errorToast(context, response.message);
      return;
    }
    setState(() {
      _isEmailOTPSent = true;
    });
    _emailOTPCountdownTimer = 60;
    _emailVerificationTimer = Timer.periodic(Duration(seconds: 1), (v) {
      if (_emailOTPCountdownTimer == 0) {
        _emailVerificationTimer?.cancel();
      } else {
        setState(() {
          _emailOTPCountdownTimer = _emailOTPCountdownTimer - 1;
        });
      }
    });
  }

  void _showSendingEmailOTP() {
    GlobalMethods.showActivity(
      context: context,
      title: "Sending email verification code. Please wait...",
    );
  }

  void _showVerifyingEmail() {
    GlobalMethods.showActivity(
      context: context,
      title:
          "We are verifying your email. Please wait while we verify your email address.",
    );
  }

  Future<void> _verifyEmailOtp({
    required String email,
    required String otp,
  }) async {
    _showVerifyingEmail();
    var response = await verifyEmailOTP(
      email: email,
      otp: _emailOTPController.text.trim(),
    );
    Navigator.of(context).pop();
    if (!response.status) {
      GlobalMethods.errorToast(context, response.message, seconds: 6);
      return;
    }
    setState(() {
      _isEmailOTPVerified = true;
    });
  }

  Widget _timerView() {
    return Column(
      children: [
        Row(
          children: [
            Text(
              "Code not received?",
              style: TextStyle(),
            ),
            width10,
            _emailOTPCountdownTimer == 0
                ? AppButton(
                    text: "Resend",
                    onPressed: () async {
                      _sendEmailOtp(
                          email: _emailController.text.trim(), resendOTP: true);
                    },
                  )
                : Text(
                    _emailOTPCountdownTimer.toString(),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ],
        ),
        height10,
      ],
    );
  }
}
