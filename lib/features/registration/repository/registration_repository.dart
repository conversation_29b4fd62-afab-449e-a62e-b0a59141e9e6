import 'package:rooo_driver/Services/UserServices.dart';
import 'package:rooo_driver/features/edit_profile/model/region_response_model.dart';
import 'package:rooo_driver/features/registration/models/user_data_response_model.dart';

import 'package:rooo_driver/features/registration/services/registration_service.dart';
import 'package:rooo_driver/features/verify_otp/models/validat_otp_response_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/model/LoginResponse.dart';
import 'package:http/http.dart' as http;

class RegistrationRepository {
  RegistrationService _registrationService = RegistrationService();
  FirebaseAuth _auth = FirebaseAuth.instance;
  UserService userService = UserService();

  // Future<VehicleResponseModel> getVehicleCategoryListByRegionIdApi(
  //     {required String regionId}) async {
  //   return await _registrationService.getVehicleCategoryListByRegionApi(
  //       regionId: regionId);
  // }
  //  Future<VehicleResponseModel> getVehicleCategoryListByCountryCodeApi(
  //     {required String countryCode}) async {
  //   return await _registrationService.getVehicleCategoryListByCountryCodeApi(
  //       countryCode: countryCode);
  // }

  Future<ProvinceModelResponse> getProvinceListAPi() async {
    return ProvinceModelResponse.fromJson(
        await handleResponse(await buildHttpResponse(
      'province-list',
      method: HttpMethod.GET,
    )));
  }

  Future<RegionResponseModel> getRegionListAPi() async {
    return RegionResponseModel.fromJson(
        await handleResponse(await buildHttpResponse(
      'region-list',
      method: HttpMethod.GET,
    )));
  }

  Future<StatusMessageModel> verifyReferralAPi(
      {required Map<String, dynamic> request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse("verify-referral-code",
            method: HttpMethod.POST, request: request)));
  }

  Future<ValidateOtpResponseModel> sendOptApi(
      {required Map<String, dynamic> request}) async {
    return await _registrationService.sendOtp(request: request);
  }

  Future<UserDataResponseModel?> registerApi(
      {required MultipartRequest multiPartRequest}) async {
    multiPartRequest.headers.addAll(buildHeaderTokens());

    try {
      var bytes = await chuckerHttpClient.send(
        multiPartRequest,
      );

      var response = await http.Response.fromStream(bytes);
      return await UserDataResponseModel.fromJson(jsonDecode(response.body));
    } catch (e) {
      return null;
    }
  }

  Future<UserDataResponseModel?> completeProfile(
      {required MultipartRequest multiPartRequest}) async {
    // multiPartRequest.headers.addAll(buildHeaderTokens());
    // return await UserDataResponseModel.fromJson(await sendMultiPartApi(
    //   multiPartRequest: multiPartRequest,
    // ));

    multiPartRequest.headers.addAll(buildHeaderTokens());

    try {
      var bytes = await chuckerHttpClient.send(
        multiPartRequest,
      );

      var response = await http.Response.fromStream(bytes);
      return await UserDataResponseModel.fromJson(jsonDecode(response.body));
    } catch (e) {
      return null;
    }
  }
}

Future saveResponseLoccally({required LoginResponse loginResponse}) async {
  await sharedPref.setString(TOKEN, loginResponse.data!.apiToken.validate());
  await sharedPref.setString(
      USER_TYPE, loginResponse.data!.userType.validate());
  await sharedPref.setString(
      FIRST_NAME, loginResponse.data!.firstName.validate());
  await sharedPref.setString(
      LAST_NAME, loginResponse.data!.lastName.validate());
  await sharedPref.setString(
      CONTACT_NUMBER, loginResponse.data!.contactNumber.validate());
  await sharedPref.setString(USER_EMAIL, loginResponse.data!.email.validate());
  
  await sharedPref.setInt(USER_ID, loginResponse.data!.id ?? 0);
  await sharedPref.setString(
      USER_PROFILE_PHOTO, loginResponse.data!.profileImage.validate());
  await sharedPref.setString(GENDER, loginResponse.data!.gender.validate());
  if (loginResponse.data!.isOnline != null)
    await sharedPref.setInt(IS_ONLINE, loginResponse.data!.isOnline ?? 0);
  await sharedPref.setInt(
      IS_Verified_Driver, loginResponse.data!.isVerifiedDriver ?? 0);
  if (loginResponse.data!.uid != null)
    await sharedPref.setString(UID, loginResponse.data!.uid.validate());
  await sharedPref.setString(LOGIN_TYPE, loginResponse.data!.email.validate());

  await appStore.setLoggedIn(true);
  await appStore.setUserEmail(loginResponse.data!.email.validate());
  await appStore.setUserProfile(loginResponse.data!.profileImage.validate());

  // if (sharedPref.getInt(IS_Verified_Driver) == 1) {
  //   launchScreen(context, InitialScreen(), isNewTask: true);
  // } else {
  //   launchScreen(
  //       context, VerifyDeliveryPersonScreen(isShow: true),
  //       pageRouteAnimation: PageRouteAnimation.Slide,
  //       isNewTask: true);
  // }
}
                  // print('nct-> sending reg mul error' + e.toString());

                  // GlobalMethods.infoToast(context,  data['message']);
                
  




  // Future<void> signUpWithEmailPassword(
  //   context, {
  //   String? name,
  //   String? email,
  //   String? password,
  //   String? mobileNumber,
  //   String? fName,
  //   String? lName,
  //   String? userName,
  //   bool socialLoginName = false,
  //   String? userType,
  //   String? uID,
  //   bool isOtp = false,
  //   UserDetail? userDetail,
  //   required Iterable<int> serviceIds,
  //   bool? nightDrivingPreference,
  //   String? securityNumber,
  //   String? age,
  //   String? gender,
  //   String? referralCode,
  //   File? file,
  //   bool isExist = true,
  // }) async {
  //   UserCredential? userCredential = await _auth.createUserWithEmailAndPassword(
  //       email: email!, password: password!);
  //   if (userCredential.user != null) {
  //     try {
  //       await FirebaseAuth.instance
  //           .signInWithEmailAndPassword(email: email, password: password)
  //           .then((value) {})
  //           .onError((error, stackTrace) {});
  //     } catch (e) {
  //       print(e.toString());
  //     }
  //     User currentUser = userCredential.user!;

  //     UserData userModel = UserData();

  //     /// Create user
  //     userModel.uid = currentUser.uid.validate();
  //     userModel.email = email;
  //     userModel.referralCode = referralCode;

  //     userModel.contactNumber = mobileNumber.validate();

  //     userModel.username = userName.validate();

  //     userModel.userType = userType.validate();
  //     userModel.displayName = fName.validate() + " " + lName.validate();
  //     userModel.firstName = fName.validate();
  //     userModel.lastName = lName.validate();
  //     userModel.createdAt = Timestamp.now().toDate().toString();
  //     userModel.updatedAt = Timestamp.now().toDate().toString();
  //     userModel.playerId = sharedPref.getString(PLAYER_ID).validate();
  //     sharedPref.setString(UID, userCredential.user!.uid.validate());

  //     userModel.userDetail = UserDetail(
  //       nightDrivingPreference: nightDrivingPreference,
  //       age: int.parse(age!),
  //       socialSecurityNumber: securityNumber,
  //     );

  //     await userService
  //         .addDocumentWithCustomId(currentUser.uid, userModel.toJson())
  //         .then((value) async {
  //       MultipartRequest multiPartRequest =
  //           await getMultiPartRequest('driver-signup');
  //       multiPartRequest.fields['username'] = userName!;
  //       multiPartRequest.fields['first_name'] = fName!;
  //       multiPartRequest.fields['last_name'] = lName!;
  //       multiPartRequest.fields['email'] = email;
  //       multiPartRequest.fields['user_type'] = 'driver';
  //       multiPartRequest.fields['contact_number'] = mobileNumber!;
  //       multiPartRequest.fields['password'] = password;
  //       multiPartRequest.fields['player_id'] =
  //           sharedPref.getString(PLAYER_ID).validate();
  //       multiPartRequest.fields['uid'] = userModel.uid!;
  //       multiPartRequest.fields['gender'] = gender!;
  //       multiPartRequest.fields["referral_code"] = referralCode!;

  //       String servicesData = "";
  //       for (var element in serviceIds) {
  //         servicesData += "," + element.toString();
  //       }
  //       servicesData = servicesData.substring(1);

  //       multiPartRequest.fields['service_ids'] = servicesData;

  //       multiPartRequest.fields['user_detail'] = jsonEncode(UserDetail(
  //         age: int.parse(age),
  //         nightDrivingPreference: nightDrivingPreference,
  //         socialSecurityNumber: securityNumber,
  //       ).toJson());

  //       if (file != null) {
  //         multiPartRequest.files
  //             .add(await MultipartFile.fromPath('profile_image', file.path));
  //       }

  //       // Map req = {
  //       //   'first_name': fName,
  //       //   'last_name': lName,
  //       //   'username': userName,
  //       //   'email': email,
  //       //   "user_type": "driver",
  //       //   "contact_number": mobileNumber,
  //       //   'password': password,
  //       //   "player_id": sharedPref.getString(PLAYER_ID).validate(),
  //       //   "uid": userModel.uid,
  //       //   "gender": gender,
  //       //   if (socialLoginName) 'login_type': 'mobile',
  //       //   'service_ids': serviceIds,
  //       //   'age': age,
  //       //   'socialSecurityNumber': securityNumber,
  //       //   'nightDrivingPreference': nightDrivingPreference,
  //       //   // "user_detail": {
  //       //   //   'car_model': userDetail!.carModel.validate(),
  //       //   //   'car_color': userDetail.carColor.validate(),
  //       //   //   'car_plate_number': userDetail.carPlateNumber.validate(),
  //       //   //   'car_production_year': userDetail.carProductionYear.validate(),
  //       //   // },
  //       //   //updated service_id to service_ids as a Set<int>
  //       // };

  //       if (!isExist) {
  //         updateProfileUid();
  //         if (sharedPref.getInt(IS_Verified_Driver) == 1) {
  //           launchScreen(context, InitialScreen(), isNewTask: true);
  //         } else {
  //           launchScreen(context, VerifyDeliveryPersonScreen(isShow: true),
  //               pageRouteAnimation: PageRouteAnimation.Slide, isNewTask: true);
  //         }
  //       } else {
  //         // await signUpApi(req).then((value) {
  //         print('nct-> sending reg mul ');
  //         await sendMultiPartRequest(
  //           multiPartRequest,
  //           onSuccess: (data) async {
  //             print('nct-> reg mul done ');
  //             print('nct-> ' + data.toString());

  //             if (data != null) {
  //               if (data['status'] == false) {
  //                 GlobalMethods.infoToast(context,  data['message']);
  //                 await userService
  //                     .removeDocument(userModel.uid!)
  //                     .then((value) async {
  //                   AuthServices authService = AuthServices();

  //                   await authService
  //                       .deleteUserFirebase()
  //                       .then((value) async {})
  //                       .catchError((error) {});
  //                 }).catchError((error) {});
  //                 return;
  //               }
          
  //             } else {
  //               print('nct-> sending reg mul error 2');

  //               GlobalMethods.infoToast(context,  errorMessage);
  //             }
  //           },
  //           onError: (e) {
  //             print('nct-> sending reg mul error 3' + e.toString());

  //             GlobalMethods.infoToast(context,  e);
  //           },
  //         ).catchError((error) {
  //           print('nct-> sending reg mul error 4' + "Server error");

  //           GlobalMethods.infoToast(context,  "Server error");
  //           log('${"Server error"}');
  //         });
  //       }

  //       appStore.setLoading(false);
  //     }).catchError((e) {
  //       appStore.setLoading(false);
  //       GlobalMethods.infoToast(context,  '${e.toString()}');
  //       log('${e.toString()}');
  //     });
  //   } else {
  //     throw "errorSomethingWentWrong";
  //   }

