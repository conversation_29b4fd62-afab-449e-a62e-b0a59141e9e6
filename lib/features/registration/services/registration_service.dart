import 'package:rooo_driver/features/registration/models/vehicle_response_model.dart';
import 'package:rooo_driver/features/verify_otp/models/validat_otp_response_model.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class RegistrationService {
  Future<VehicleResponseModel> getVehicleCategoryListByCountryCodeApi(
      {required String countryCode }) async {
    return VehicleResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('service-list?country_code=$countryCode',
            method: HttpMethod.GET)));
  }
   Future<VehicleResponseModel> getVehicleCategoryListByRegionApi(
      {required String regionId }) async {
    return VehicleResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('service-list?region_id=$regionId',
            method: HttpMethod.GET)));
  }

  Future<VehicleResponseModel> registerApi(
      {required String countryCode}) async {
    return VehicleResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('service-list?country_code=$countryCode',
            method: HttpMethod.GET)));
  }
   Future<ValidateOtpResponseModel> sendOtp(
      {required Map<String,dynamic> request}) async {
    return ValidateOtpResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('send-otp',
        request: request,
            method: HttpMethod.POST)));
  }
}
