import 'package:rooo_driver/model/AppSettingModel.dart';

class VehicleModel {
  num? adminCommission;
  num? baseFare;
  num? cancellationFee;
  num? capacity;
  String? commissionType;
  String? createdAt;
  num? fleetCommission;
  int? id;
  num? minimumDistance;
  num? minimumFare;
  String? name;
  String? paymentMethod;
  num? perDistance;
  num? perDistancePriorCancel;
  num? perMinuteDrive;
  num? perMinutePriorCancel;
  num? perMinuteWait;
  ProvinceModel? region;
  num? regionId;
  String? serviceImage;
  num? status;
  String? updatedAt;
  num? waitingTimeLimit;

  ///local
  bool isSelect;

  VehicleModel({
    this.adminCommission,
    this.baseFare,
    this.cancellationFee,
    this.capacity,
    this.commissionType,
    this.createdAt,
    this.fleetCommission,
    this.id,
    this.minimumDistance,
    this.minimumFare,
    this.name,
    this.paymentMethod,
    this.perDistance,
    this.perDistancePriorCancel,
    this.perMinuteDrive,
    this.perMinutePriorCancel,
    this.perMinuteWait,
    this.region,
    this.regionId,
    this.serviceImage,
    this.status,
    this.updatedAt,
    this.waitingTimeLimit,
    this.isSelect = false,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      adminCommission: json['admin_commission'],
      baseFare: json['base_fare'],
      cancellationFee: json['cancellation_fee'],
      capacity: json['capacity'],
      commissionType: json['commission_type'],
      createdAt: json['created_at'],
      fleetCommission: json['fleet_commission'],
      id: json['id'],
      minimumDistance: json['minimum_distance'],
      minimumFare: json['minimum_fare'],
      name: json['name'],
      paymentMethod: json['payment_method'],
      perDistance: json['per_distance'],
      perDistancePriorCancel: json['per_distance_prior_cancel'],
      perMinuteDrive: json['per_minute_drive'],
      perMinutePriorCancel: json['per_minute_prior_cancel'],
      perMinuteWait: json['per_minute_wait'],
      region: json['region'] != null
          ? ProvinceModel.fromJson(json['region'])
          : null,
      regionId: json['region_id'],
      serviceImage: json['service_image'],
      status: json['status'],
      updatedAt: json['updated_at'],
      waitingTimeLimit: json['waiting_time_limit'],
    );
  }
}
