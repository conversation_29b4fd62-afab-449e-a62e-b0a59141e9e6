import 'package:rooo_driver/features/registration/models/vehicle_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class VehicleResponseModel {
  List<VehicleModel>? data;
  PaginationModel? pagination;

  String? message;

  VehicleResponseModel({this.data, this.pagination, this.message});

  factory VehicleResponseModel.fromJson(Map<String, dynamic> json) {
    return VehicleResponseModel(
        data: json['data'] != null
            ? (json['data'] as List)
                .map((i) => VehicleModel.fromJson(i))
                .toList()
            : null,
        pagination: json['pagination'] != null
            ? PaginationModel.fromJson(json['pagination'])
            : null,
        message: json["message"]);
  }
}
