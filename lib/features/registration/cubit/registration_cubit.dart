import 'package:rooo_driver/features/registration/repository/registration_repository.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';

abstract class RegistrationState {}

class RegistrationInitState extends RegistrationState {}

class RegistrationLoadingState extends RegistrationState {}

class RegistrationDeletedState extends RegistrationState {}

class RegisterationCompletedState extends RegistrationState {}

class EmailRegisteredState extends RegistrationState {
  final UserCredential user;

  EmailRegisteredState({required this.user});
}

class RegionListLoadedState extends RegistrationState {
  final List<ProvinceModel> regionList;

  RegionListLoadedState({required this.regionList});
}

class ReferralCodeVerifiedState extends RegistrationState {
  ReferralCodeVerifiedState();
}

class ProvinceListLoadedState extends RegistrationState {
  final List<ProvinceModel> provinceList;

  ProvinceListLoadedState({required this.provinceList});
}

// class VehicleCategoryLoadedState extends RegistrationState {
//   final VehicleResponseModel vehicleResponseModel;

//   VehicleCategoryLoadedState({required this.vehicleResponseModel});
// }

class RegistrationOtpSentState extends RegistrationState {
  final String key;

  RegistrationOtpSentState({required this.key});
}

class UserNotFoundstate extends RegistrationState {}

// class RegistrationLoadedState extends RegistrationState {
//   final RegistrationResponseModel registrationResponseModel;

//   RegistrationLoadedState({
//     required this.registrationResponseModel,
//   });
// }

class RegistrationErrorState extends RegistrationState {
  final String message;

  RegistrationErrorState(
      {required this.message, });
}

class RegistrationCubit extends Cubit<RegistrationState> {
  RegistrationCubit() : super(RegistrationInitState());

  RegistrationRepository _registrationRepository = RegistrationRepository();

  // void getVehicleCategoryListByRegionId({required String regionId}) async {
  //   emit(RegistrationLoadingState());

  //   await _registrationRepository
  //       .getVehicleCategoryListByRegionIdApi(regionId: regionId)
  //       .then((value) {
  //     emit(VehicleCategoryLoadedState(vehicleResponseModel: value));
  //   }).onError((error, stackTrace) {
  //     emit(RegistrationErrorState(message: "Server error"));
  //     print(error);
  //   });
  // }
  //   void getVehicleCategoryListByCountryCode({required String countryCode}) async {
  //   emit(RegistrationLoadingState());

  //   await _registrationRepository
  //       .getVehicleCategoryListByCountryCodeApi(countryCode: countryCode)
  //       .then((value) {
  //     emit(VehicleCategoryLoadedState(vehicleResponseModel: value));
  //   }).onError((error, stackTrace) {
  //     emit(RegistrationErrorState(message: "Server error"));
  //     print(error);
  //   });
  // }

  void sendOtp({required Map<String, dynamic> request}) async {
    emit(RegistrationLoadingState());

    await _registrationRepository.sendOptApi(request: request).then((value) {
      if (value.status) {
        emit(RegistrationOtpSentState(key: value.data!.key!));
      } else {
        emit(RegistrationErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(RegistrationErrorState(message: "Server error"));
      print(error);
    });
  }

  getRegionList() async {
    emit(RegistrationLoadingState());
    await _registrationRepository.getRegionListAPi().then((value) {
      if (value.data != null) {
        emit(RegionListLoadedState(regionList: value.data!));
      } else {
        emit(RegistrationErrorState(message: serverErrorMessage));
      }
      // if (value.status) {
      //     RegionListLoadedState(regionList: value.data!);
      // } else {
      //   emit(EditProfileErrorState(message: value.message));
      // }
    }).onError((error, stackTrace) {
      emit(RegistrationErrorState(message: "Server error"));
    });
  }

  verifyReferral({required Map<String, dynamic> request}) async {
    emit(RegistrationLoadingState());
    await _registrationRepository
        .verifyReferralAPi(request: request)
        .then((value) {
      if (value.status) {
        emit(ReferralCodeVerifiedState());
      } else {
        emit(RegistrationErrorState(message: value.message));
      }
      // if (value.status) {
      //     RegionListLoadedState(regionList: value.data!);
      // } else {
      //   emit(EditProfileErrorState(message: value.message));
      // }
    }).onError((error, stackTrace) {
      emit(RegistrationErrorState(message: "Server error"));
    });
  }

  getprovinceList() async {
    emit(RegistrationLoadingState());
    await _registrationRepository.getProvinceListAPi().then((value) {
      if (value.status && value.data != null) {
        emit(ProvinceListLoadedState(provinceList: value.data!));
      } else {
        emit(RegistrationErrorState(message: serverErrorMessage));
      }
      // if (value.status) {
      // } else {
      //   emit(EditProfileErrorState(message: value.message));
      // }
    }).onError((error, stackTrace) {
      emit(RegistrationErrorState(message: "Server error"));
    });
  }

  void register2({
    required String email,
    required String password,
    required String userName,
    required String firstName,
    required String lastName,
    required String contactNumber,
    required int age,
    required String gender,
    required int carCategoryId,
    required String carModel,
    required String carProductionYear,
    required String carPlateNumber,
    required String carColor,
    required String referralCode,
    required File file,
  }) {}

  void completeProfile({required MultipartRequest multiPartRequest}) async {
    emit(RegistrationLoadingState());
    await _registrationRepository
        .completeProfile(multiPartRequest: multiPartRequest)
        .then((value) async {
      if (value == null) {
        emit(RegistrationErrorState(
            message: "Something went wrong"));
      } else if (value.status) {
        if (value.data != null) {
          await GlobalMethods.saveResponseLoccally(data: value.data!);
          emit(RegisterationCompletedState());
        } else {
          emit(RegistrationErrorState(message: serverErrorMessage));
        }
      } else {
        emit(RegistrationErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(RegistrationErrorState(message: "Server error"));
      print(error);
    });
  }

// registerEmail({required  String  email }) async {
//   emit(RegistrationLoadingState());
//   await _registrationRepository.registerUserToFirebase(email: email, password: "jhfcd51sd5").then((value){
//     if(value==null){
//           emit(RegistrationErrorState());

//     }
//     else{
//    emit( EmailRegisteredState(user: value!));

//     }

//   }).onError((e,_){

//     emit(RegistrationErrorState(message: GlobalMethods.handleFirebaseError(e)));

//   });

// }
}
