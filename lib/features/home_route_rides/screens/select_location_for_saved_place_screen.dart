// import 'package:rooo_driver/global/constants/app_cred.dart';
// import 'package:rooo_driver/global/export/app_export.dart';
// import 'package:rooo_driver/global/widgets/app_loader.dart';
// import 'package:ultra_map_place_picker/ultra_map_place_picker.dart';

// class SelectLocationForSavedPlaceScreen extends StatefulWidget {
//   final bool isFromSavedPlace;

//   const SelectLocationForSavedPlaceScreen({
//     super.key,
//     required this.isFromSavedPlace,
//   });

//   @override
//   State<SelectLocationForSavedPlaceScreen> createState() =>
//       _SelectLocationForSavedPlaceScreenState();
// }

// class _SelectLocationForSavedPlaceScreenState
//     extends State<SelectLocationForSavedPlaceScreen> {
//   GlobalKey<FormState> _formKey = GlobalKey<FormState>();
//   String _title = "";

//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: const RoooAppbar(
//         title: "Select locatio n",
//       ),
//       body: Stack(
//         alignment: Alignment.center,
//         children: [
//           UltraMapPlacePicker(
//             useCurrentLocation: true,
//             googleApiKey:AppCred.mapBoxPublicTokenKey,
//             initialPosition: UltraLocationModel(-33.865143, 151.209900),
//             mapTypes: (isHuaweiDevice) =>
//                 isHuaweiDevice ? [UltraMapType.normal] : UltraMapType.values,
//             myLocationButtonCooldown: 10,
//             zoomControlsEnabled: false,
//             resizeToAvoidBottomInset: false,
//             pinBuilder: (context, state) {
//               return Image.asset(
//                 MyCurrentLocationIcon,
//                 height: 90,
//                 // width: 20,
//               );
//             },
//             onPlacePicked: (PickResultModel result) {
//               _consumePlace(result);
//             },
//             selectedPlaceWidgetBuilder:
//                 (context, selectedPlace, state, isSearchBarFocused) {
//               return Positioned(
//                 bottom: 10,
//                 left: 10,
//                 right: 10,
//                 child: SizedBox(
//                   height: 140,
//                   child: state == SearchingState.searching
//                       ? const Center(
//                           child: AppLoader(size: 25),
//                         )
//                       : Container(
//                           decoration: BoxDecoration(
//                               // borderRadius: 20,
//                               color: AppColors.primaryWhiteColor.withOpacity(0.7),
//                               ),
//                           padding: const EdgeInsets.all(10),
//                           child: Column(
//                             mainAxisSize: MainAxisSize.min,
//                             children: [
//                               Expanded(
//                                 child: Text(
//                                   selectedPlace?.formattedAddress ?? '',
//                                   maxLines: 2,
//                                   overflow: TextOverflow.ellipsis,
//                                   style: const TextStyle(
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                 ),
//                               ),
//                               const SizedBox(height: 10),
//                               AppButton(
//                                 text: "Select Place",
//                                 onPressed: () {
//                                   if (selectedPlace != null) {
//                                     _consumePlace(selectedPlace);
//                                   }
//                                 },
//                               ),
//                             ],
//                           ),
//                         ),
//                 ),
//               );
//             },
//           ),
//         ],
//       ),
//     );
//   }

//   void _consumePlace(PickResultModel result) {
//     if (!widget.isFromSavedPlace) {
//       Navigator.of(context).pop(result);
//       return;
//     }
//     _title = result.formattedAddress ?? "";
//     showGeneralDialog(
//       context: context,
//       pageBuilder: (context, animation, secondaryAnimation) {
//         return AlertDialog(
//           title: Text("Place title?"),
//           content: Form(
//             key: _formKey,
//             child: TextFormField(
//               validator: (value) {
//                 if ((value ?? "").trim().isEmpty) {
//                   return "required";
//                 }
//                 return null;
//               },
//               initialValue: _title,
//               onChanged: (value) {
//                 _title = value.trim();
//               },
//             ),
//           ),
//           actions: [
//             AppButton(
//                 text: "Cancel",
//                 // backgoundColor: Colors.grey.shade200,
//                 onPressed: () {
//                   Navigator.of(context).pop();
//                 }),
//             AppButton(
//                 text: "Save",
//                 onPressed: () {
//                   if (_formKey.currentState!.validate()) {
//                     Navigator.of(context).pop();
//                     Navigator.of(context).pop(
//                       PickedPlaceResult(place: result, title: _title),
//                     );
//                   }
//                 })
//           ],
//         );
//       },
//     );
//   }
// }

// class PickedPlaceResult {
//   String title;
//   PickResultModel place;
//   PickedPlaceResult({required this.title, required this.place});
// }
