import 'package:rooo_driver/features/home_route_rides/models/map_box_location_model.dart';
import 'package:rooo_driver/features/home_route_rides/screens/map_picker_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:uuid/uuid.dart';

class AddHomeRouteScreen extends StatefulWidget {
  const AddHomeRouteScreen({
    super.key,
  });

  @override
  State<AddHomeRouteScreen> createState() => New_SelectScreenState();
}

class New_SelectScreenState extends State<AddHomeRouteScreen> {
  final _destinationController = TextEditingController();
  ValueNotifier<String> _destinationAdress = ValueNotifier("");

  ValueNotifier<List<MapBoxSuggestion>> _destinationAddressList =
      ValueNotifier([]);
  final _destinationFocus = FocusNode();
  String _destinationLattitude = "";
  String _destinationLongitude = "";
//////////////////////////////////////////////////////////////////
  Timer? _debounceTimer;

  bool _isDoingTask = false;
  String _sessionToken = const Uuid().v4();

  _getSearchResults({required String searchText}) async {
    setState(() {
      _isDoingTask = true;
    });
    await BlocProvider.of<HomeRouteCubit>(context)
        .getLocationSuggestions(text: searchText, sessionToken: _sessionToken);
    setState(() {
      _isDoingTask = false;
    });
  }

  Future<void> _onSearching(BuildContext context, String searchText) async {
    // Cancel the previous debounce timer if it exists to prevent extra calls
    if (_debounceTimer != null && _debounceTimer!.isActive) {
      _debounceTimer!.cancel();
    }

    // Start a new debounce timer
    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      _getSearchResults(searchText: searchText);
      //Make API call or do something
    });
  }

  _saveHomeRoute() async {
    setState(() {
      _isDoingTask = true;
    });
    Map<String, dynamic> request = {
      "title": _destinationAdress.value,
      "lat": _destinationLattitude,
      "lng": _destinationLongitude
    };
    await BlocProvider.of<HomeRouteCubit>(context)
        .saveHomeRoute(request: request);
    setState(() {
      _isDoingTask = false;
    });
  }

  _onSelectDestination({required MapBoxLocation value}) async {
    hideKeyboard(context);
    _destinationLattitude = value.latitude.toString();
    _destinationLongitude = value.longitude.toString();

    _destinationAdress.value = value.name;
    _destinationAddressList.value.clear();

    setState(() {
      
    _destinationController.text = value.name + ", " + value.fullAddress;
    });
  }

  HomeRouteCubit? _homeRouteCubit;

  _dispose() {
    _debounceTimer?.cancel();
    _homeRouteCubit?.close();
  }

  @override
  void initState() {
    super.initState();
    _destinationFocus.addListener(() {
      if (_destinationFocus.hasFocus) {
        _destinationController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _destinationController.text.length,
        );
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        bottomNavigationBar:
            // ValueListenableBuilder(
            //   valueListenable: _pickupAdress,
            //   builder: (context, value, child) {
            //     return
            ValueListenableBuilder(
                valueListenable: _destinationAdress,
                builder: (context, value, child) {
                  return BottomButton(
                      text: "Save",
                      onPressed: () async {
                        _saveHomeRoute();
                      },
                      notVisible: _destinationAdress.value.isEmpty ||
                          _destinationAdress.value.isEmpty);
                }),
        //   );
        // },
        // ),
        appBar: RoooAppbar(title: "Add route match"),
        body: Stack(
          children: [
            Padding(
              padding: screenPadding,
              child: SingleChildScrollView(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                    height10,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Home",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            ValueListenableBuilder(
                              valueListenable: _destinationAdress,
                              builder: (context, value, child) {
                                return TextFormField(
                                  controller: _destinationController,
                                  focusNode: _destinationFocus,
                                  onChanged: (value) {
                                    if (value.length > 3) {
                                      _onSearching(context, value);
                                    }
                                  },
                                  decoration: InputDecoration(
                                      suffixIcon: BlocConsumer<HomeRouteCubit,
                                          HomeRouteState>(
                                    listener: (context, state) {
                                      if (state
                                          is MapBoxSuggestionsLoadedState) {
                                        _destinationAddressList.value.clear();
                                        for (var element in state.data) {
                                          if (element.fullAddress.isNotEmpty) {
                                            _destinationAddressList.value
                                                .add(element);
                                          }
                                        }
                                        setState(() {});
                                      } else if (state
                                          is MapBoxLocationDetailsState) {
                                        _onSelectDestination(value: state.data);
                                      } else if (state is HomeRouteSavedState) {
                                        Navigator.pop(context, true);
                                      } else if (state is HomeRouteErrorState) {
                                        GlobalMethods.errorToast(
                                            context, state.message);
                                      }
                                    },
                                    bloc: _homeRouteCubit,
                                    builder: (context, state) {
                                      if (_destinationFocus.hasFocus) {
                                        if (state
                                            is MapBoxSuggestionLoadingState) {
                                          return SizedBox(
                                            width: 20,
                                            child: AppLoader(
                                              size: 20,
                                            ),
                                          );
                                        }
                                      }
                                      return Icon(Icons.search);
                                    },
                                  )),
                                );
                              },
                            ),
                            width5,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                TextButton(
                                    onPressed: () async {
                                      MapBoxLocationModel? selectedPlace =
                                          await GlobalMethods.pushScreen(
                                              context: context,
                                              screen: MapPickupScreen(),
                                              screenIdentifier: ScreenIdentifier
                                                  .MapPickupScreen);

                                      if (selectedPlace != null) {
                                        _destinationAdress.value =
                                            selectedPlace.address;

                                        _destinationLattitude = selectedPlace
                                            .point.latitude
                                            .toString();

                                        _destinationLongitude = selectedPlace
                                            .point.longitude
                                            .toString();

                                        setState(() {
                                          _destinationController.text =
                                              selectedPlace.address;
                                        });
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        Text("From map"),
                                        Icon(Icons.location_on)
                                      ],
                                    )),
                              ],
                            ),
                            ValueListenableBuilder<List<MapBoxSuggestion>>(
                              valueListenable: _destinationAddressList,
                              builder: (context, value, child) {
                                if (value.isEmpty) {
                                  return SizedBox();
                                }
                                return SizedBox(
                                  height: 300,
                                  child: Card(
                                    child: ListView.builder(
                                      shrinkWrap: true,
                                      itemCount: value.length,
                                      itemBuilder: (context, index) {
                                        var data = value[index];
                                        return ListTile(
                                          onTap: () {
                                            BlocProvider.of<HomeRouteCubit>(
                                                    context)
                                                .getLocationDetails(
                                              mapBoxId: data.mapBoxId,
                                              sessionToken: _sessionToken,
                                            );
                                          },
                                          trailing:
                                              Icon(Icons.my_location_outlined),
                                          title: Text(data.name.toString()),
                                          subtitle:
                                              Text(data.fullAddress.toString()),
                                        );
                                      },
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ])),
            ),
            _isDoingTask ? loaderWidget() : const SizedBox()
          ],
        ));
  }
}
