import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:location/location.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mb;
import 'package:rooo_driver/features/home_route_rides/models/map_box_location_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:http/http.dart' as http;

class MapPickupScreen extends StatefulWidget {
  const MapPickupScreen({super.key});

  @override
  State<MapPickupScreen> createState() => _MapPickupScreenState();
}

class _MapPickupScreenState extends State<MapPickupScreen> with WidgetsBindingObserver {
  bool _isInitial = true;
  mb.MapboxMap? createdMap;

  Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory<OneSequenceGestureRecognizer>(
        () => EagerGestureRecognizer()), // For zooming gestures
  };

  ValueNotifier<MapBoxLocationModel> _address = ValueNotifier(
      MapBoxLocationModel(
          point: MapBoxPoint(latitude: 0, longitude: 0), address: ""));


  _onMapCreated(mb.MapboxMap mapboxMap) async {
    this.createdMap = mapboxMap;
    this.createdMap?.scaleBar.updateSettings(
        mb.ScaleBarSettings(enabled: false));

    GlobalMethods.showActivity(context: context, title: "Loading map...");

    late geo.Position currentLocation;

    if (GlobalState.lastKnownLocation != null) {
      currentLocation = geo.Position.fromMap({
        "latitude": GlobalState.lastKnownLocation!.latitude,
        "longitude": GlobalState.lastKnownLocation!.longitude,
        "accuracy": GlobalState.lastKnownLocation!.accuracy,
        "altitude": GlobalState.lastKnownLocation!.altitude,
        "heading": GlobalState.lastKnownLocation!.heading,
        "speed": GlobalState.lastKnownLocation!.speed,
        "speedAccuracy": GlobalState.lastKnownLocation!.speedAccuracy,
      });
    } else {
      currentLocation = await geo.Geolocator.getCurrentPosition();
      GlobalState.lastKnownLocation = LocationData.fromMap({
        "latitude": currentLocation.latitude,
        "longitude": currentLocation.longitude,
        "accuracy": currentLocation.accuracy,
        "altitude": currentLocation.altitude,
        "heading": currentLocation.heading,
        "speed": currentLocation.speed,
        "speedAccuracy": currentLocation.speedAccuracy,
      });
    }

    await mapboxMap.location.updateSettings(
        mb.LocationComponentSettings(enabled: true, pulsingEnabled: true));

    await Future.delayed(Duration(seconds: 1));
    await mapboxMap.flyTo(
      mb.CameraOptions(
          zoom: 16,
          center: mb.Point(
            coordinates: mb.Position(
                currentLocation.longitude, currentLocation.latitude),
          )),
      null,
    );
    await Future.delayed(Duration(seconds: 1));

    Navigator.pop(context);
  }

  int createRandomColor() {
    var random = Random();
    return Color.fromARGB(
            255, random.nextInt(255), random.nextInt(255), random.nextInt(255))
        .value;
  }

  Timer? _debounce;

  // This function will be called to make the API request

  Future<void> _reverseGeocode(
      mb.CameraChangedEventData cameraChangedEventData) async {
    _address.value = MapBoxLocationModel(
        point: MapBoxPoint(longitude: -1, latitude: -1), address: "-1");
    double lat =
        cameraChangedEventData.cameraState.center.coordinates.lat.toDouble();
    double lng =
        cameraChangedEventData.cameraState.center.coordinates.lng.toDouble();
    final String url =
        'https://api.mapbox.com/geocoding/v5/mapbox.places/$lng,$lat.json?access_token=${AppCred.mapBoxPublicTokenKey}&language=en';

    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['features'] != null && data['features'].isNotEmpty) {
          _address.value = MapBoxLocationModel(
              point: MapBoxPoint(
                  longitude: data['features'][0]['center'][0],
                  latitude: data['features'][0]['center'][1]),
              address: data['features'][0]['place_name']);
        } else {
          _address.value = MapBoxLocationModel(
              point: MapBoxPoint(longitude: -2, latitude: -2), address: "-2");
        }
      } else {
        _address.value = MapBoxLocationModel(
            point: MapBoxPoint(longitude: -3, latitude: -3), address: "-3");
      }
    } catch (e) {
      GlobalMethods.errorToast(context, errorMessage);
    }
  }

  void _onChanged(mb.CameraChangedEventData cameraChangedEventData) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel(); // Cancel the previous timer if it's still active
    }

    _debounce = Timer(const Duration(milliseconds: 400), () {
      if (_isInitial) {
        _isInitial = false;
        return;
      }

      _reverseGeocode(cameraChangedEventData);
    });
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _debounce?.cancel();
    _address.dispose();
    super.dispose();
  }
  @override
  void didChangePlatformBrightness() {
    this.createdMap?.loadStyleURI(Theme.of(context).brightness != Brightness.dark
        ? mb.MapboxStyles.DARK
        : mb.MapboxStyles.STANDARD);
    super.didChangePlatformBrightness();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: RoooAppbar(title: "Pick from map"),
      body: Stack(
        alignment: Alignment.center,
        children: [
          mb.MapWidget(
            styleUri: Theme.of(context).brightness == Brightness.dark
                ? mb.MapboxStyles.DARK
                : mb.MapboxStyles.STANDARD,
            onCameraChangeListener: _onChanged,
            gestureRecognizers: gestureRecognizers,
            key: ValueKey("mapWidget"),
            onMapCreated: _onMapCreated,
          ),
          Container(
            height: 40,
            width: 20,
            child: Image.asset('images/destination.png'),
          ),
          Positioned(
            top: 20,
            right: 20,
            child: FloatingActionButton(
              backgroundColor: Colors.white,
              onPressed: () async {
                await createdMap?.flyTo(
                  mb.CameraOptions(
                    zoom: 16,
                    center: mb.Point(
                      coordinates: mb.Position(
                        GlobalState.lastKnownLocation!.longitude!.toDouble(),
                        GlobalState.lastKnownLocation!.latitude!.toDouble(),
                      ),
                    ),
                  ),
                  null,
                );
                await Future.delayed(Duration(seconds: 2));
              },
              child: Icon(Icons.my_location, color: Colors.black),
            ),
          ),
          Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: ValueListenableBuilder<MapBoxLocationModel>(
                valueListenable: _address,
                builder: (context, value, child) {
                
                  if (value.point.latitude == -1 || value.point.longitude == 0) {
                    return Container(
                        padding: screenPadding,
                        color: Colors.black,
                        // decoration: BoxDecoration(
                        //     borderRadius: appRadius,
                        //     color: Colors.black.withOpacity(.7)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Loading....',
                              style: TextStyle(color: Colors.white),
                            ),
                            CircularProgressIndicator()
                          ],
                        ));
                  }
                  if (value.point.latitude == -2) {
                    return Text("No Address found");
                  }
                  if (value.point.latitude == -3) {
                    return Text(errorMessage);
                  }
                  return Container(
                      padding: EdgeInsets.fromLTRB(
                          16, 16, 16, Platform.isIOS ? 50 : 30),
                      color: Colors.black,
                      // decoration: BoxDecoration(
                      //     borderRadius: appRadius,
                      //     color: Colors.black.withOpacity(.7)),
                      child: Column(
                        children: [
                          Text(
                            "Address: ${_address.value.address}",
                            style: TextStyle(color: Colors.white),
                          ),
                          AppButton(
                              text: "Select",
                              onPressed: () async {
                                Navigator.pop(context, value);
                              })
                        ],
                      ));
                },
              )),
        ],
      ),
    );
  }
}
