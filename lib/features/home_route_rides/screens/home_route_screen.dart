import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/features/home_route_rides/models/home_route_response_model.dart';
import 'package:rooo_driver/features/home_route_rides/screens/add_home_route_screen.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class HomeRouteScreen extends StatefulWidget {
  const HomeRouteScreen({super.key});

  @override
  State<HomeRouteScreen> createState() => _HomeRouteScreenState();
}

class _HomeRouteScreenState extends State<HomeRouteScreen> {
  List<HomeRouteModel> _homeRoute = [];
  String? _emptyMesssage;

  HomeRouteCubit? _homeRouteCubit;

  _getHomeRouter() {
    BlocProvider.of<HomeRouteCubit>(context).getHomeRoute();
  }

  _applyHomeRouter({required int id}) {
    BlocProvider.of<HomeRouteCubit>(context).applyHomeRoute(id: id);
  }

  _deleteHomeRouter({required int id}) {
    BlocProvider.of<HomeRouteCubit>(context).deleteHomeRoute(id: id);
  }

  _onDataLoaded({required HomeRouteResponseModel homeRouteResponseModel}) {
    _homeRoute = homeRouteResponseModel.data as List<HomeRouteModel>;

    
    _emptyMesssage = homeRouteResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _init();
  }

  _init() {
    _getHomeRouter();
  }

  _dispose() {
    _homeRouteCubit?.close();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    GlobalMethods.removeSavedScreen();
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Route Match"),
      floatingActionButton: FloatingActionButton(
          child: Icon(Icons.add),
          onPressed: () async {
            bool? reslut = await GlobalMethods.pushScreen(
                context: context,
                screen: AddHomeRouteScreen(),
                screenIdentifier: ScreenIdentifier.AddHomeRouteScreen);

            if (reslut ?? false) {
              _init();
            }
          }),
      body: BlocConsumer<HomeRouteCubit, HomeRouteState>(
        bloc: _homeRouteCubit,
        listener: (context, state) {
          if (state is HomeRouteLoadedState) {
            _onDataLoaded(homeRouteResponseModel: state.homeRouteModel);
          } else if (state is HomeRouteErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is HomeRoutAppliedState) {
            GlobalMethods.succesToast(context, "Applied successfully");
            _init();
          } else if (state is HomeRouteDeletedState) {
            GlobalMethods.succesToast(context, "Deleted successfully");
            _init();
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is HomeRouteLoadingState,
              isEmpty: _homeRoute.isEmpty,
              emptyMessage: _emptyMesssage ?? "No data",
              child: ListView.separated(
                padding: EdgeInsets.fromLTRB(screenPadding.left, screenPadding.left, screenPadding.left, screenPadding.left +60),
                shrinkWrap: true,
                separatorBuilder: (context, index) => height10,
                itemCount: _homeRoute.length,
                itemBuilder: (BuildContext context, int index) {
                  HomeRouteModel data = _homeRoute[index];
                            
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 1000),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                          child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.whiteColor(context),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 1,
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                          border: Border.all(
                            width: data.isActive == 1 ? 2 : 1,
                            color: data.isActive == 1
                                ? AppColors.greenColor
                                : Colors.grey.withOpacity(0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Route " + (index + 1).toString(),
                                  style: AppTextStyles.header(
                                    color: AppColors.blackColor(context),
                                  ).copyWith(fontSize: 16),
                                ),
                                if (data.isActive == 1)
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: AppColors.greenColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.check_circle, size: 16, color: AppColors.greenColor),
                                        SizedBox(width: 4),
                                        Text(
                                          "Active",
                                          style: TextStyle(
                                            color: AppColors.greenColor,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Text(
                              data.title,
                              style: AppTextStyles.header(
                                color: AppColors.greenColor,
                              ).copyWith(fontSize: 18),
                            ),
                            SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: AppButton(
                                    text: "Delete",
                                    icon: Icon(Icons.delete_outline, size: 20),
                                    backgroundColor: Colors.red.withOpacity(0.1),
                                    onPressed: () async {
                                      GlobalMethods.showConfirmationDialog(
                                        context: context,
                                        onPositiveAction: () {
                                          _deleteHomeRouter(id: data.id);
                                        },
                                        title: "Are you sure you want to delete this route?",
                                      );
                                    },
                                  ),
                                ),
                                if (data.isActive != 1) ...[
                                  SizedBox(width: 12),
                                  Expanded(
                                    child: AppButton(
                                      text: "Apply",
                                      icon: Icon(Icons.check_circle_outline, size: 20),
                                      onPressed: () async  {
                                        _applyHomeRouter(id: data.id);
                                      },
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      )),
                    ),
                  );
                },
              ));
        },
      ),
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

