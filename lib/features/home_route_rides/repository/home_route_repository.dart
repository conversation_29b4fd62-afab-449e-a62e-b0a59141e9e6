import 'package:rooo_driver/features/home_route_rides/models/home_route_response_model.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class HomeRouteRepository {
  Future<HomeRouteResponseModel> getHomeRouteApi() async {
    return HomeRouteResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('get-destination-filter',
            method: HttpMethod.GET)));
  }

  Future<StatusMessageModel> saveHomeRouteApi(
      {required Map<String, dynamic> request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('save-destination-filter',
            method: HttpMethod.POST, request: request)));
  }

  Future<StatusMessageModel> applyHomeRouteApi({required int id}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('apply-destination-filter',
            method: HttpMethod.POST, request: {"id": id})));
  }

  Future<StatusMessageModel> deleteHomeRouteApi({required int id}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('delete-destination-filter',
            method: HttpMethod.POST, request: {"id": id})));
  }



  Future<StatusMessageModel> getCurrentRideRequestApi() async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('save-destination-filter',
            method: HttpMethod.GET)));
  }

  // Future<GooglePlaceIdModel> searchAddressRequestPlaceId(
  //     {required String placeId}) async {
  //   return GooglePlaceIdModel.fromJson(await handleResponse(await buildHttpResponse(
  //       'https://api.mapbox.com/geocoding/v5/mapbox.places/$placeId.json?access_token=${AppCred.mapBoxPublicTokenKey}&autocomplete=true&limit=5',
  //       method: HttpMethod.GET)));
  // }
}
