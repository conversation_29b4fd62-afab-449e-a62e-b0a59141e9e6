import 'package:rooo_driver/features/home_route_rides/models/home_route_response_model.dart';
import 'package:rooo_driver/features/home_route_rides/repository/home_route_repository.dart';
import 'package:rooo_driver/global/export/app_export.dart';

// Main Model Class
class MapBoxSearchResponse {
  List<MapBoxSuggestion> suggestions;

  MapBoxSearchResponse({
    required this.suggestions,
  });

  factory MapBoxSearchResponse.fromJson(Map<String, dynamic> json) {
    return MapBoxSearchResponse(
      suggestions: List<MapBoxSuggestion>.from(
          json['suggestions'].map((x) => MapBoxSuggestion.fromJson(x))),
    );
  }
}

// Feature Class
class MapBoxSuggestion {
  String mapBoxId;
  String name;
  String fullAddress;

  MapBoxSuggestion({
    required this.mapBoxId,
    required this.name,
    required this.fullAddress,
  });

  factory MapBoxSuggestion.fromJson(Map<String, dynamic> json) {
    return MapBoxSuggestion(
      mapBoxId: json['mapbox_id'],
      name: json['name'],
      fullAddress: json['full_address'] ?? "",
    );
  }
}

class MapBoxLocation {
  String name;
  String fullAddress;
  double latitude;
  double longitude;

  MapBoxLocation({
    required this.name,
    required this.fullAddress,
    required this.latitude,
    required this.longitude,
  });

  factory MapBoxLocation.fromJson(Map<String, dynamic> json) {
    return MapBoxLocation(
      name: json['name'],
      fullAddress: json['full_address'],
      latitude: json['coordinates']['latitude'],
      longitude: json['coordinates']['longitude'],
    );
  }
}

abstract class HomeRouteState {}

class HomeRouteInitState extends HomeRouteState {}

class HomeRouteLoadingState extends HomeRouteState {}

class HomeRouteErrorState extends HomeRouteState {
  final String message;

  HomeRouteErrorState({required this.message});
}

class HomeRouteLoadedState extends HomeRouteState {
  final HomeRouteResponseModel homeRouteModel;

  HomeRouteLoadedState({required this.homeRouteModel});
}

class HomeRouteAppliedState extends HomeRouteState {
  HomeRouteAppliedState();
}

class HomeRouteSavedState extends HomeRouteState {}

class HomeRoutAppliedState extends HomeRouteState {}

class HomeRouteDeletedState extends HomeRouteState {}

class MapBoxSuggestionLoadingState extends HomeRouteState {}

class MapBoxSuggestionsLoadedState extends HomeRouteState {
  final List<MapBoxSuggestion> data;
  MapBoxSuggestionsLoadedState({required this.data});
}

class MapBoxLocationDetailsState extends HomeRouteState {
  final MapBoxLocation data;
  MapBoxLocationDetailsState({required this.data});
}

class HomeRouteCubit extends Cubit<HomeRouteState> {
  HomeRouteRepository _homeRouteRepository = HomeRouteRepository();
  HomeRouteCubit() : super(HomeRouteInitState());

  getHomeRoute() {
    emit(HomeRouteLoadingState());

    _homeRouteRepository.getHomeRouteApi().then((value) {
      if (value.status) {
        if (value.data != null) {
          emit(HomeRouteLoadedState(homeRouteModel: value));
        } else {
          emit(HomeRouteErrorState(message: serverErrorMessage));
        }
      } else {
        emit(HomeRouteErrorState(message: value.message));
      }
    }).onError((e, _) {
      emit(HomeRouteErrorState(message: e.toString()));
    });
  }

  deleteHomeRoute({required int id}) {
    emit(HomeRouteLoadingState());

    _homeRouteRepository.deleteHomeRouteApi(id: id).then((value) {
      if (value.status) {
        emit(HomeRouteDeletedState());
      } else {
        emit(HomeRouteErrorState(message: value.message));
      }
    }).onError((e, _) {
      emit(HomeRouteErrorState(message: e.toString()));
    });
  }

  applyHomeRoute({required int id}) {
    emit(HomeRouteLoadingState());

    _homeRouteRepository.applyHomeRouteApi(id: id).then((value) {
      if (value.status) {
        emit(HomeRoutAppliedState());
      } else {
        emit(HomeRouteErrorState(message: value.message));
      }
    }).onError((e, _) {
      emit(HomeRouteErrorState(message: e.toString()));
    });
  }

  saveHomeRoute({required Map<String, dynamic> request}) async {
    emit(HomeRouteLoadingState());

    await _homeRouteRepository.saveHomeRouteApi(request: request).then((value) {
      if (value.status) {
        emit(HomeRouteSavedState());
      } else {
        emit(HomeRouteErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(HomeRouteErrorState(message: "Server error"));
    });
  }

  getLocationSuggestions(
      {required String text, required String sessionToken}) async {
    emit(MapBoxSuggestionLoadingState());

    await searchWithMapBox(search: text, sessionToken: sessionToken)
        .then((value) {
      if (value == null) {
        emit(HomeRouteErrorState(message: "Something went wrong"));
      } else {
        emit(MapBoxSuggestionsLoadedState(data: value.suggestions));
      }
    }).onError((error, stackTrace) {
      emit(HomeRouteErrorState(message: "Server error"));
    });
  }

  getLocationDetails(
      {required String mapBoxId, required String sessionToken}) async {
    emit(MapBoxSuggestionLoadingState());

    await getMapBoxLocation(mapBoxId: mapBoxId, sessionToken: sessionToken)
        .then((value) {
      if (value == null) {
        emit(HomeRouteErrorState(message: "Something went wrong"));
      } else {
        emit(MapBoxLocationDetailsState(data: value));
      }
    }).onError((error, stackTrace) {
      emit(HomeRouteErrorState(message: "Server error"));
    });
  }
}
