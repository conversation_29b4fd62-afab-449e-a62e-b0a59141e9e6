
// Main Model Class
class PlacesResponseModel {
  List<FeatureModel> features;
  List<String> query;

  PlacesResponseModel({
    required this.features,
    required this.query,
  });

  factory PlacesResponseModel.fromJson(Map<String, dynamic> json) {
    return PlacesResponseModel(
      features: List<FeatureModel>.from(json['features'].map((x) => FeatureModel.fromJson(x))),
      query: List<String>.from(json['query']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'features': List<dynamic>.from(features.map((x) => x.toJson())),
      'query': List<dynamic>.from(query),
    };
  }
}

// Feature Class
class FeatureModel {
  String id;
  String type;
  List<String> placeType;
  double relevance;
  Map<String, dynamic> properties;
  String text;
  String placeName;
  List<double> bbox;
  List<double> center;
  Geometry geometry;
  List<Context> context;

  FeatureModel({
    required this.id,
    required this.type,
    required this.placeType,
    required this.relevance,
    required this.properties,
    required this.text,
    required this.placeName,
    required this.bbox,
    required this.center,
    required this.geometry,
    required this.context,
  });

  factory FeatureModel.fromJson(Map<String, dynamic> json) {
    return FeatureModel(
      id: json['id'],
      type: json['type'],
      placeType: List<String>.from(json['place_type']),
      relevance: json['relevance'].toDouble(),
      properties: Map<String, dynamic>.from(json['properties']),
      text: json['text'],
      placeName: json['place_name'],
      bbox: List<double>.from(json['bbox'].map((x) => x.toDouble())),
      center: List<double>.from(json['center'].map((x) => x.toDouble())),
      geometry: Geometry.fromJson(json['geometry']),
      context: List<Context>.from(json['context'].map((x) => Context.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'place_type': List<dynamic>.from(placeType.map((x) => x)),
      'relevance': relevance,
      'properties': properties,
      'text': text,
      'place_name': placeName,
      'bbox': List<dynamic>.from(bbox.map((x) => x)),
      'center': List<dynamic>.from(center.map((x) => x)),
      'geometry': geometry.toJson(),
      'context': List<dynamic>.from(context.map((x) => x.toJson())),
    };
  }
}

// Geometry Class
class Geometry {
  String type;
  List<double> coordinates;

  Geometry({
    required this.type,
    required this.coordinates,
  });

  factory Geometry.fromJson(Map<String, dynamic> json) {
    return Geometry(
      type: json['type'],
      coordinates: List<double>.from(json['coordinates'].map((x) => x.toDouble())),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'coordinates': List<dynamic>.from(coordinates.map((x) => x)),
    };
  }
}

// Context Class
class Context {
  String id;
  String mapboxId;
  String? wikidata;
  String? shortCode;
  String text;

  Context({
    required this.id,
    required this.mapboxId,
    this.wikidata,
    this.shortCode,
    required this.text,
  });

  factory Context.fromJson(Map<String, dynamic> json) {
    return Context(
      id: json['id'],
      mapboxId: json['mapbox_id'],
      wikidata: json['wikidata'],
      shortCode: json['short_code'],
      text: json['text'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mapbox_id': mapboxId,
      'wikidata': wikidata,
      'short_code': shortCode,
      'text': text,
    };
  }
}
