class MapBoxLocationModel {
  MapBoxPoint point;
  String address;

  MapBoxLocationModel({
    required this.point,
    required this.address,
  });

  // fromJson factory
  factory MapBoxLocationModel.fromJson(Map<String, dynamic> json) {
    return MapBoxLocationModel(
      point: MapBoxPoint.fromJson(json['point']),
      address: json['address'],
    );
  }

  // toJson method
  Map<String, dynamic> toJson() {
    return {
      'point': point.toJson(),
      'address': address,
    };
  }
}

class MapBoxPoint {
  double longitude;
  double latitude;

  MapBoxPoint({
    required this.longitude,
    required this.latitude,
  });

  // fromJson factory
  factory MapBoxPoint.fromJson(Map<String, dynamic> json) {
    return MapBoxPoint(
      longitude: json['longitude'].toDouble(),
      latitude: json['latitude'].toDouble(),
    );
  }

  // toJson method
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}
