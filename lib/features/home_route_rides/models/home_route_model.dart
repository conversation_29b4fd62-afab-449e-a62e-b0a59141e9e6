class HomeRouteModel {
  int id;
  int userId;
  String title;
  String lat;
  String lng;
  int isActive;
  DateTime createdAt;
  DateTime updatedAt;

  HomeRouteModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.lat,
    required this.lng,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HomeRouteModel.fromJson(Map<String, dynamic> json) {
    return HomeRouteModel(
      id: json['id'],
      userId: json['user_id'],
      title: json['title'],
      lat: json['lat'],
      lng: json['lng'],
      isActive: json['is_active'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'lat': lat,
      'lng': lng,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
