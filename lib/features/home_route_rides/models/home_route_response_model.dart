import 'package:rooo_driver/features/home_route_rides/models/home_route_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class HomeRouteResponseModel extends ResponseModel<List<HomeRouteModel>> {
  HomeRouteResponseModel({
    required bool status,
    required String message,
    required List<HomeRouteModel>? data,
    required PaginationModel? pagination
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory HomeRouteResponseModel.fromJson(Map<String, dynamic> json) {
    return HomeRouteResponseModel(
       pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
      status: json['status'],
      message: json['message'],
     data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => HomeRouteModel.fromJson(e))
                .toList()
            : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((e) => e.toJson()).toList(), // Convert each VehicleModel to JSON
      'pagination': pagination?.toJson(), // Convert PaginationModel to JSON if not null
    };
  }

}
