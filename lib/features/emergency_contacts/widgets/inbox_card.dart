import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/emergency_contacts/cubit/emergency_contacts_cubit.dart';
import 'package:rooo_driver/features/emergency_contacts/models/emergency_contact_model.dart';

class EmergencyContactCard extends StatelessWidget {
  final EmergencyContactModel data;

  const EmergencyContactCard({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    _deleteEmergenctConatcts() {
      BlocProvider.of<EmergencyContactsCubit>(context)
          .deleteEmergencyContacts(id: data.id!);
    }

    return Card(
      child: ListTile(
          trailing: IconButton(
              onPressed: _deleteEmergenctConatcts,
              icon: Icon(
                Icons.delete,
                color: Colors.red,
              )),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Text(((data.contactNumber).toString())),
          ),

          // inboxList[index].created_at!).toString().substring(0,10)+" , "+((inboxList[index].created_at!).toString().substring(11,15)))),

          title: Text(
            data.title!,
            style: TextStyle(
              fontWeight: FontWeight.bold,
            ),
          )),
    );
  }
}
