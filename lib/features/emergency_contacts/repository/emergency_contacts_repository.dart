import 'package:rooo_driver/features/emergency_contacts/models/emergency_contact_response_model.dart';

import 'package:rooo_driver/network/NetworkUtils.dart';

class EmergencyContactsRepository {
  Future<EmergencyContactResponseModel> saveSOS({required Map request}) async {
    return EmergencyContactResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('save-sos',
            method: HttpMethod.POST, request: request)));
  }

  Future<EmergencyContactResponseModel> getSosList(
      {required int currentPage}) async {
    return EmergencyContactResponseModel.from<PERSON>son(await handleResponse(
        await buildHttpResponse('sos-list', method: HttpMethod.GET)));
  }

  Future<EmergencyContactResponseModel> deleteSosList({int? id}) async {
    return EmergencyContactResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('sos-delete/$id', method: HttpMethod.POST)));
  }
}
