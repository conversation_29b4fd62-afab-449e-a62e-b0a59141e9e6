
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/emergency_contacts/models/emergency_contact_response_model.dart';
import 'package:rooo_driver/features/emergency_contacts/repository/emergency_contacts_repository.dart';


abstract class EmergencyContactsState {}

class EmergencyContactsInitState extends EmergencyContactsState {}

class EmergencyContactsLoadingState extends EmergencyContactsState {}

class EmergencyContactsDeletedState extends EmergencyContactsState {}
// class EmergencyContactsDetailLoadedState extends EmergencyContactsState {
//   final String message;

//   EmergencyContactsDetailLoadedState({required this.message});
// }

class EmergencyContactsSavedState extends EmergencyContactsState {
  EmergencyContactsSavedState();
}

class EmergencyContactsLoadedState extends EmergencyContactsState {
  final EmergencyContactResponseModel emergencycontactsResponseModel;

  EmergencyContactsLoadedState({
    required this.emergencycontactsResponseModel,
  });
}

class EmergencyContactsErrorState extends EmergencyContactsState {
  final String message;

  EmergencyContactsErrorState({required this.message});
}

class EmergencyContactsCubit extends Cubit<EmergencyContactsState> {
  EmergencyContactsCubit() : super(EmergencyContactsInitState());

  EmergencyContactsRepository emergencycontactsRepository =
      EmergencyContactsRepository();

  void getEmergencyContacts({
    required int current_page,
  }) async {
    emit(EmergencyContactsLoadingState());
    await emergencycontactsRepository
        .getSosList(
      currentPage: current_page,
    )
        .then((value) {
      emit(EmergencyContactsLoadedState(emergencycontactsResponseModel: value));
    }).onError((error, stackTrace) {
      emit(EmergencyContactsErrorState(message: "Server error"));
    });
  }

  void addEmergencyContacts({
    required Map request,
  }) async {
    emit(EmergencyContactsLoadingState());
    await emergencycontactsRepository
        .saveSOS(
      request: request,
    )
        .then((value) {
      emit(EmergencyContactsSavedState());
    }).onError((error, stackTrace) {
      emit(EmergencyContactsErrorState(message: "Server error"));
    });
  }

  void deleteEmergencyContacts({
    required int id,
  }) async {
    emit(EmergencyContactsLoadingState());
    await emergencycontactsRepository
        .deleteSosList(
      id: id,
    )
        .then((value) {
      emit(EmergencyContactsDeletedState());
    }).onError((error, stackTrace) {
      emit(EmergencyContactsErrorState(message: "Server error"));
    });
  }

  // void deleteEmergencyContacts({required int id}) async {
  //   emit(EmergencyContactsLoadingState());
  //   await emergencycontactsRepository
  //       .deleteEmergencyContactsApi(id: id)
  //       .then((value) {
  //     emit(EmergencyContactsDeletedState());
  //   }).onError((error, stackTrace) {
  //     emit(EmergencyContactsErrorState(error_message: "Server error"));
  //   });
  // }

  // Future getEmergencyContactsDetailData({
  //   required int id,
  // }) async {
  //   emit(EmergencyContactsLoadingState());
  //   emergencycontactsRepository.g(id).then((value) {

  //     if(value!=null){

  //          emit(EmergencyContactsDetailData(data: value));
  //     }

  //   }).onError((error, stackTrace) {
  //     emit(EmergencyContactsErrorState(error_message: "Server error"));
  //   });
  // }
}
