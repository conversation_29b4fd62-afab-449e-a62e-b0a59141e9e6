import 'package:rooo_driver/features/emergency_contacts/models/emergency_contact_model.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class EmergencyContactResponseModel {
  List<EmergencyContactModel>? data;
  PaginationModel? pagination;
  String? message;

  EmergencyContactResponseModel({this.data, this.pagination, this.message});

  factory EmergencyContactResponseModel.fromJson(Map<String, dynamic> json) {
    return EmergencyContactResponseModel(
        data: json['data'] != null
            ? (json['data'] as List)
                .map((i) => EmergencyContactModel.fromJson(i))
                .toList()
            : null,
        pagination: json['pagination'] != null
            ? PaginationModel.fromJson(json['pagination'])
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.pagination != null) {
      data['pagination'] = this.pagination!.toJson();
    }

    data["message"] = this.message;
    return data;
  }
}
