import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/components/custom_text.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/inbox/cubit/inbox_cubit.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:webview_flutter/webview_flutter.dart';

class InboxDetailsScreen2 extends StatefulWidget {
  final String title;
  final int id;
  const InboxDetailsScreen2({super.key, required this.title, required this.id});

  @override
  State<InboxDetailsScreen2> createState() => _InboxDetailsScreen2State();
}

class _InboxDetailsScreen2State extends State<InboxDetailsScreen2> {
  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    super.initState();
    _getInboxDetail(id: widget.id);
  }

  String _description = "";
  String _emptyMessage = "";

  _getInboxDetail({required int id}) async {
    controller.setBackgroundColor(Colors.white);

    await BlocProvider.of<InboxCubit>(context).getInboxDetailsInbox(id: id);
  }

  _onPullToRefresh() {
    _getInboxDetail(id: widget.id);
  }

  _onDataLoaded({required String data}) {
    controller.loadHtmlString(data);
    _description = data;
    controller
        .setNavigationDelegate(NavigationDelegate(onPageFinished: (url) {}));
    ;
  }

  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: RoooAppbar(title: "Inbox details"),
        body: Padding(
          padding: screenPadding,
          child: BlocConsumer<InboxCubit, InboxState>(
            listener: (context, state) {
              if (state is InboxDetailLoaded) {
                _onDataLoaded(data: state.data);
              } else if (state is InboxErrorState) {
                GlobalMethods.infoToast(context,  errorMessage);
              }
            },
            builder: (context, state) {
              return ScreenBody(
                  onPullToRefresh: () async => await _onPullToRefresh(),
                  isLoading: state is InboxLoadingState,
                  child: Column(
                    children: [
                      CustomText(
                        color: Colors.black,
                        data: widget.title,
                        size: 15,
                        fontweight: FontWeight.bold,
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height,
                          child: WebViewWidget(controller: controller)),
                    ],
                  ),
                  isEmpty: _description.isEmpty,
                  emptyMessage: errorMessage);
            },
          ),
        ));
  }
}
