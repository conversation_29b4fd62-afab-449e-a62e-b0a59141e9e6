import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';
import 'package:flutter_native_contact_picker/model/contact.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:rooo_driver/features/emergency_contacts/cubit/emergency_contacts_cubit.dart';
import 'package:rooo_driver/features/emergency_contacts/models/emergency_contact_model.dart';
import 'package:rooo_driver/features/emergency_contacts/models/emergency_contact_response_model.dart';
import 'package:rooo_driver/features/emergency_contacts/widgets/inbox_card.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/StringExtensions.dart';

class EmergencyContactScreen extends StatefulWidget {
  const EmergencyContactScreen({super.key});

  @override
  State<EmergencyContactScreen> createState() => _EmergencyContactScreenState();
}

class _EmergencyContactScreenState extends State<EmergencyContactScreen> {
  int _currentPage = 1;
  int _totalPage = 1;
  ScrollController _scrollController = ScrollController();
  final FlutterNativeContactPicker _contactPicker =
      new FlutterNativeContactPicker();

  List<EmergencyContactModel> _emergencyContactList = [];
  String _emptyMesssage = "";

  _onScrolling() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_currentPage < _totalPage) {
          _currentPage++;

          _getEmergencyContactList(currentPage: _currentPage);
        }
      }
    });
  }

  _getEmergencyContactList({required int currentPage}) {
    BlocProvider.of<EmergencyContactsCubit>(context).getEmergencyContacts(
      current_page: currentPage,
    );
  }

  _addEmergencyContactList({String? name, String? contactNumber}) {
    Map request = {
      "title": name,
      "contact_number": contactNumber,
    };
    BlocProvider.of<EmergencyContactsCubit>(context)
        .addEmergencyContacts(request: request);
  }

  _onDataLoaded(
      {required EmergencyContactResponseModel emergencyContactResponseModel}) {
    _currentPage = emergencyContactResponseModel.pagination?.currentPage ?? 1;
    _totalPage = emergencyContactResponseModel.pagination?.totalPages ?? 1;
    _emergencyContactList = emergencyContactResponseModel.data ?? [];
    _emptyMesssage = emergencyContactResponseModel.message.toString();
  }

  _onPullToRefresh() {
    _currentPage = 1;
    _init();
  }

  _init() {
    _onScrolling();
    _getEmergencyContactList(currentPage: _currentPage);
  }

  _dispose() {
    _scrollController.dispose();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Emergency Contacts"),
      bottomNavigationBar:
          BlocBuilder<EmergencyContactsCubit, EmergencyContactsState>(
        builder: (context, state) {
          return BottomButton(
              notVisible: state is EmergencyContactsLoadingState,
              text: language.addContact,
              onPressed: () async {
                Contact? contact = await _contactPicker.selectContact();

                if (contact != null)
                  _addEmergencyContactList(
                      name: contact.fullName?.validate(),
                      contactNumber: contact.phoneNumbers!.first);
              });
        },
      ),
      body: BlocConsumer<EmergencyContactsCubit, EmergencyContactsState>(
        listener: (context, state) {
          if (state is EmergencyContactsLoadedState) {
            _onDataLoaded(
                emergencyContactResponseModel:
                    state.emergencycontactsResponseModel);
          } else if (state is EmergencyContactsErrorState) {
            GlobalMethods.errorToast(context, state.message);
          } else if (state is EmergencyContactsSavedState) {
            GlobalMethods.infoToast(context, "Emergency contact saved");
            _getEmergencyContactList(currentPage: _currentPage);
          } else if (state is EmergencyContactsDeletedState) {
            GlobalMethods.infoToast(context, "Emergency contact deleted");
            _getEmergencyContactList(currentPage: _currentPage);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              onPullToRefresh: () async => await _onPullToRefresh(),
              isLoading: state is EmergencyContactsLoadingState,
              isEmpty: _emergencyContactList.isEmpty,
              emptyMessage: _emptyMesssage,
              child: AnimationLimiter(
                child: ListView.separated(
                  controller: _scrollController,
                  padding: screenPadding,
                  physics: AlwaysScrollableScrollPhysics(),
                  shrinkWrap: true,
                  separatorBuilder: (context, index) => height10,
                  itemCount: _emergencyContactList.length,
                  itemBuilder: (BuildContext context, int index) {
                    EmergencyContactModel data = _emergencyContactList[index];

                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 1000),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                            child: EmergencyContactCard(
                          data: data,
                        )),
                      ),
                    );
                  },
                ),
              ));
        },
      ),
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

