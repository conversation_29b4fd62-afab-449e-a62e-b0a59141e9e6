import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';

import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class SelfieScreen extends StatefulWidget {
  const SelfieScreen({
    super.key,
  });

  @override
  State<SelfieScreen> createState() => _SelfieScreenState();
}

class _SelfieScreenState extends State<SelfieScreen> {
  String _emptyMesssage = "";

  ValueNotifier<File> _selfieImage = ValueNotifier(File("path"));

  _uploadSelfieImage({required MultipartRequest multiPartRequest}) {
    BlocProvider.of<RideFlowCubit>(context)
        .uploadSelfieImage(multiPartRequest: multiPartRequest);
  }

  _init() {}

  _dispose() {}

  @override
  void initState() {
    GlobalState.isLiveImageScreenOpened = true;
    super.initState();
    _init();
  }

  @override
  void dispose() {
    GlobalState.isLiveImageScreenOpened = false;
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RideFlowCubit, RideFlowState>(
      listener: (context, state) {
        if (state is RideFlowErrorState) {
          GlobalMethods.errorToast(context, state.message);
        } else if (state is SelfieUploadedState) {
          GlobalMethods.succesToast(context, "Selfie uploaded successfully");
          GlobalMethods.replaceScreen(
              context: context,
              screen: RideScreen(),
              screenIdentifier: ScreenIdentifier.rideScreen);
        }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: RoooAppbar(title: "Upload Selfie"),
            body: ScreenBody(
                isLoading: state is RideFlowLoadingState,
                isEmpty: false,
                emptyMessage: _emptyMesssage,
                child: Padding(
                  padding: screenPadding,
                  child: Column(
                    children: [
                      InkWell(
                          onTap: () {
                            GlobalMethods.checkCameraPermission(
                                onSuccess: () async {
                                  final ImagePicker picker = ImagePicker();

                                  final XFile? photo = await picker.pickImage(
                                    source: ImageSource.camera,
                                    preferredCameraDevice: CameraDevice.front,
                                    imageQuality: 50,
                                  );

                                  if (photo != null) {
                                    bool value =
                                        await GlobalMethods.checkImageSize(
                                            result: photo, context: context);
                                    if (value) {
                                      _selfieImage.value = File(photo.path);
                                    }
                                  }
                                },
                                context: context);
                          },
                          child: ValueListenableBuilder(
                            valueListenable: _selfieImage,
                            builder: (context, value, child) {
                              return Container(
                                height: MediaQuery.sizeOf(context).width,
                                width: MediaQuery.sizeOf(context).width,
                                decoration: BoxDecoration(
                                    borderRadius: appRadius,
                                    border: Border.all(
                                        color: AppColors.blackColor(context))),
                                child: AspectRatio(
                                  aspectRatio: 9 / 16,
                                  child: _selfieImage.value.path == "path"
                                      ? Icon(
                                          Icons.add,
                                          size: 50,
                                        )
                                      : Stack(
                                          children: [
                                           
                                            Container(
                                              decoration: BoxDecoration(
                                                  image: DecorationImage(
                                                      image: FileImage(
                                                          _selfieImage.value,
                                                          
                                                          ),
                                                          fit: BoxFit.cover,
                                                          ),
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  border: Border.all(
                                                      color:
                                                          AppColors.whiteColor(
                                                              context))),
                                            ),
                                            Positioned(
                                              right: 0,
                                              child: Card(
                                                color: Colors.black,
                                                elevation: 4,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius: BorderRadius.circular(8),
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsets.all(8.0),
                                                  child: Icon(
                                                    Icons.edit,
                                                    color: Colors.white,
                                                    size: 24,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                ),
                              );
                            },
                          )),
                      height20,
                      AppButton(
                          width: double.infinity,
                          text: "Upload",
                          onPressed: () async {
                            MultipartRequest multiPartRequest =
                                await getMultiPartRequest("save-live-image");
                            ;

                            multiPartRequest.files.add(
                                await MultipartFile.fromPath(
                                    'live_image', _selfieImage.value.path));

                            _uploadSelfieImage(
                                multiPartRequest: multiPartRequest);
                          })
                    ],
                  ),
                )));
      },
    );
  }
}



//////////////////////////////////////////////
/////////////////////////////////////////////
////////////////////////////////////////////
///////////////////////////////////////////

