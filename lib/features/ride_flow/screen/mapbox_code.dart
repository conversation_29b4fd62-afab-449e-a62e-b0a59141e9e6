import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:rooo_driver/global/constants/Colors.dart';
import 'package:rooo_driver/global/constants/app_cred.dart';
import 'package:rooo_driver/utils/Images.dart';
import 'package:http/http.dart' as http;

import '../../../global/constants/constants.dart';

class MapBoxScreen extends StatefulWidget {
  const MapBoxScreen({super.key});

  @override
  State<MapBoxScreen> createState() => _MapBoxScreenState();
}

class _MapBoxScreenState extends State<MapBoxScreen> {
  MapboxMap? mapboxMap;
  late PointAnnotationManager _pointAnnotationManager;
  late PolylineAnnotationManager _polylineAnnotationManager;

  Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory<OneSequenceGestureRecognizer>(
        () => EagerGestureRecognizer()), // For zooming gestures
  };

  ValueNotifier<String> _address = ValueNotifier("0");
  _onMapCreated(MapboxMap mapboxMap) async {
    this.mapboxMap = mapboxMap;
    geo.Position currentLocation = await geo.Geolocator.getCurrentPosition();

    // createPolyline(
    //     currentLocation:
    //         Position(currentLocation.longitude, currentLocation.latitude));
    // createMarkerPoint(
    //     currentLocation:
    //         Position(currentLocation.longitude, currentLocation.latitude));
    mapboxMap.location.updateSettings(
        LocationComponentSettings(enabled: true, pulsingEnabled: true));
  }

  int createRandomColor() {
    var random = Random();
    return Color.fromARGB(
            255, random.nextInt(255), random.nextInt(255), random.nextInt(255))
        .value;
  }

  _createPolyline({required Position currentLocation}) async {
    _polylineAnnotationManager =
        await mapboxMap!.annotations.createPolylineAnnotationManager();

    List<Position> coordinates =
        await getRouteCoordinates(currentLocation, Position(75.7670, 31.2232));

    // coordinates.forEach((v){
    //   createMarkerPoint(currentLocation: v);

    // });

    _polylineAnnotationManager.create(PolylineAnnotationOptions(
        lineOpacity: 1,
        lineWidth: 4,
        lineBorderWidth: 4,
        lineBorderColor: Colors.black.value,
        geometry: LineString(coordinates: coordinates)));
  }

  createMarkerPoint({required Position currentLocation}) async {
    _pointAnnotationManager =
        await mapboxMap!.annotations.createPointAnnotationManager();
    final ByteData bytes = await rootBundle.load(MyCurrentLocationIcon);
    final Uint8List list = bytes.buffer.asUint8List();

    _pointAnnotationManager.create(PointAnnotationOptions(
        image: list,
        iconSize: .3,
        geometry: Point(
            coordinates: Position(currentLocation.lng, currentLocation.lat))));
  }

  Future<List<Position>> getRouteCoordinates(
      Position origin, Position destination) async {
    // Replace with your token
    final url = Uri.parse(
        'https://api.mapbox.com/directions/v5/mapbox/driving/${origin.lng},${origin.lat};${destination.lng},${destination.lat}?geometries=geojson&access_token=${AppCred.mapBoxPublicTokenKey}');

    final response = await http.get(url);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final route = data['routes'][0];
      final geometry = route['geometry'];
      final coordinates = geometry['coordinates'];

      List<Position> polylineCoordinates = [];
      for (var coord in coordinates) {
        polylineCoordinates.add(Position(coord[0], coord[1]));
      }

      return polylineCoordinates;
    } else {
      throw Exception('Failed to load route');
    }
  }

  Timer? _debounce;

  // This function will be called to make the API request

  Future<void> _reverseGeocode(
      CameraChangedEventData cameraChangedEventData) async {
    _address.value = '';
    double lat =
        cameraChangedEventData.cameraState.center.coordinates.lat.toDouble();
    double lng =
        cameraChangedEventData.cameraState.center.coordinates.lat.toDouble();
    final String url =
        'https://api.mapbox.com/geocoding/v5/mapbox.places/$lat,$lng.json?access_token=${AppCred.mapBoxPublicTokenKey}&language=en';

    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['features'] != null && data['features'].isNotEmpty) {
          _address.value =
              data['features'][0]['place_name'] ?? 'Address not available';
        } else {
          _address.value = ('No address found for these coordinates');
        }
      } else {
        _address.value = ('Error fetching address');
      }
    } catch (e) {
      _address.value = ('Error: $e');
    }
  }

//   Future<void> _makeApiCall(CameraChangedEventData ?cameraChangedEventData) async {
//     if (cameraChangedEventData==null) {
//       _responseController.add('Please enter a search query');
//       return;
//     }
// double lat=cameraChangedEventData.cameraState.center.coordinates.lat.toDouble();
// double lng=cameraChangedEventData.cameraState.center.coordinates.lng.toDouble();
//     // Example API URL (Replace with your actual API URL)
// final String url =
//         'https://api.mapbox.com/geocoding/v5/mapbox.places/$lng,$lat.json?access_token=${AppCred.mapBoxPublicTokenKey}&language=en';

//     try {
//       final response = await http.get(Uri.parse(url));

//       if (response.statusCode == 200) {
//         // Successful API call
//         _responseController.add(jsonDecode(response.body)['data']); // Assuming 'data' in the response
//       } else {
//         _responseController.add('Error: ${response.statusCode}');
//       }
//     } catch (e) {
//       _responseController.add('Error: $e');
//     }
//   }

  // This function handles the debounce logic
  void _onChanged(CameraChangedEventData cameraChangedEventData) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel(); // Cancel the previous timer if it's still active
    }

    // Start a new timer to call _makeApiCall after 500ms
    _debounce = Timer(const Duration(milliseconds: 1000), () {
      _reverseGeocode(cameraChangedEventData);
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Stack(
        alignment: Alignment.center,
        children: [
          MapWidget(
            onCameraChangeListener: _onChanged,
            gestureRecognizers: gestureRecognizers,
            key: ValueKey("mapWidget"),
            onMapCreated: _onMapCreated,
          ),
          Container(
            color: Colors.red,
            height: 20,
            width: 20,
          ),
          Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: ValueListenableBuilder<String>(
                valueListenable: _address,
                builder: (context, value, child) {
                  if (value.isEmpty) {
                    return Container(
                        padding: screenPadding,
                        decoration: BoxDecoration(
                            borderRadius: appRadius,
                            color: AppColors.primaryColor(context)
                                .withOpacity(.5)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Loading....'),
                            CircularProgressIndicator()
                          ],
                        ));
                  }

                  return Container(
                      padding: screenPadding,
                      decoration: BoxDecoration(
                          borderRadius: appRadius,
                          color: AppColors.primaryColor(context)),
                      child: Text("Address: ${_address.value}"));
                },
              )),
        ],
      ),
    );
  }
}
