import 'package:geolocator/geolocator.dart';
import 'package:rooo_driver/global/state/global_state.dart';

class RideScreenUtils {
  static bool _isAlreadyInArrived = false;

  static Future<void> onNearArrivedLocation(
      {required double currentLatitude,
      required double currentLongitude,
      required double targetLatitude,
      required double targetLongitude,
      required Function callback}) async {
    if (_isAlreadyInArrived) {
      return;
    }
    num distanceInMeters = Geolocator.distanceBetween(
      currentLatitude,
      currentLongitude,
      targetLatitude,
      targetLongitude,
    );

    if (distanceInMeters <=
        (GlobalState.appSettingModel?.arrivedGeofencingLimit ?? 100)) {
      _isAlreadyInArrived = true;
      callback();
    }
  }
}
