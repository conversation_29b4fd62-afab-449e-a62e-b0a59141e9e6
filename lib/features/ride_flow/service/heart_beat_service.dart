import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'package:http/http.dart' as http;
import 'package:rooo_driver/global/constants/app_cred.dart';

/* globals related to this file */
ReceivePort? port;

class HeartBeatService {
  static HeartBeat? instance;
  static void start(String driverId, String token) {
    if (instance == null) {
      port = ReceivePort();
      instance = HeartBeat();
      instance!.sendSignal(driverId, token);
    }
  }

  static void stop() {
    if (instance != null) {
      port!.sendPort.send(false);
      instance = null;
    }
  }
}

/* only call if the driver is online */
class HeartBeat {
  Isolate? isolate;
  Map<String, String> header = {};

  HeartBeat() {
    port!.listen((message) {
      if (message is bool) {
        isolate?.kill();
        port!.close();
      }
    });
  }

  /* send signal to server -> driver is online */
  Future<void> sendSignal(
    String driverId,
    String token,
  ) async {
    isolate = await Isolate.spawn((message) async {
      _sendSignalLogic(driverId, token);
    }, port!.sendPort);
  }

  Future<void> _sendSignalLogic(
    String driverId,
    String token,
  ) async {
    await callAPi(driverId, token);

    Timer.periodic(Duration(seconds: 15), (t) async {
      callAPi(driverId, token);
    });
  }

  Future callAPi(
    String driverId,
    String token,
  ) async {
    header = {
      HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
      HttpHeaders.cacheControlHeader: 'no-cache',
      HttpHeaders.acceptHeader: 'application/json; charset=utf-8',
      HttpHeaders.authorizationHeader: 'Bearer $token',
      'Access-Control-Allow-Headers': '*',
      'Access-Control-Allow-Origin': '*',
      "user_type": "driver",
      "driver_id": driverId,
      // "language": sharedPref.getString(SELECTED_LANGUAGE_CODE).toString(),
    };
    // call API

   await http.get(Uri.parse("${AppCred.baseUrl}last-update-status"),
        headers: header);
    log("Heart beat called");
  }
}
