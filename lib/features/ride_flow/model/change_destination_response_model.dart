
import 'package:rooo_driver/features/ride_flow/model/destination_places_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class ChangeDestinationResponseModel extends ResponseModel<DestinationPlaceModel> {
  ChangeDestinationResponseModel({
    required bool status,
    required String message,
    required DestinationPlaceModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new UserResponseModel instance from a map.
  factory ChangeDestinationResponseModel.fromJson(Map<String, dynamic> json) {
    return ChangeDestinationResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? DestinationPlaceModel.fromJson(json['data'])
          : null,
    );
  }

  // Method to convert a UserResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
