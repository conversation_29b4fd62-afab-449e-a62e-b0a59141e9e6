
import 'package:rooo_driver/features/ride_flow/model/online_offline_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class OnlineOfflineResponseModel extends ResponseModel<OnlineOfflineModel> {
  OnlineOfflineResponseModel({
    required bool status,
    required String message,
    required OnlineOfflineModel? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new UserResponseModel instance from a map.
  factory OnlineOfflineResponseModel.fromJson(Map<String, dynamic> json) {
    return OnlineOfflineResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? OnlineOfflineModel.fromJson(json['data'])
          : null,
    );
  }

  // Method to convert a UserResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
