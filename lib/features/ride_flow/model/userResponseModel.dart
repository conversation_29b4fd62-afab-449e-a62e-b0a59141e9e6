

// import 'package:rooo_driver/global/models/response_model.dart';

// class UserResponseModel extends ResponseModel<UserModel> {
//   UserResponseModel({
//     required bool status,
//     required String message,
//     required UserModel? data,
//   }) : super(
//           status: status,
//           message: message,
//           data: data,
//         );

//   // Factory constructor for creating a new UserResponseModel instance from a map.
//   factory UserResponseModel.fromJson(Map<String, dynamic> json) {
//     return UserResponseModel(
//       status: json['status'],
//       message: json['message'],
//       data: json['data'] != null
//           ? UserModel.fromJson(json['data'])
//           : null,
//     );
//   }

//   // Method to convert a UserResponseModel instance to a map.
//   Map<String, dynamic> toJson() {
//     return {
//       'status': status,
//       'message': message,
//       'data': data?.toJson(),
//     };
//   }
// }
