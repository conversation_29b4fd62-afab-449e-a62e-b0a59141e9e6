class DestinationPlaceModel {
  int? id;
  int? riderId;
  int rideRequestId;
  String? title;
    String fromAdress;
        String toAdress;



  String latitude;
  String longitude;
  String currentLatitude;
  String currentLongitude;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? deletedAt;

  DestinationPlaceModel({
    this.id,
    required this.riderId,
    required this.fromAdress,
    required this.toAdress,
    required this.rideRequestId,
    required this.title,
    required this.latitude,
    required this.longitude,
    required this.currentLatitude,
    required this.currentLongitude,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  // From JSON
  factory DestinationPlaceModel.fromJson(Map<String, dynamic> json) {
    return DestinationPlaceModel(
      id: json['id'] as int?,
      riderId: json['rider_id'] as int?,
      rideRequestId: json['ride_request_id'] as int,
      title: json['title'] as String,
            fromAdress: json['fromAdress'] as String,

      toAdress: json['toAdress'] as String,

      latitude: json['latitude'] as String,
      longitude: json['longitude'] as String,
      currentLatitude: json['current_latitude'] as String,
      currentLongitude: json['current_longitude'] as String,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      deletedAt: json['deleted_at'] != null
          ? DateTime.parse(json['deleted_at'])
          : null,
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rider_id': riderId,
      'ride_request_id': rideRequestId,
      'title': title,
            'fromAdress': fromAdress,

      'toAdress': toAdress,

      'latitude': latitude,

      'longitude': longitude,
      'current_latitude': currentLatitude,
      'current_longitude': currentLongitude,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }
}
