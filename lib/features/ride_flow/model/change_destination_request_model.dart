class ChangeDestinationRequestModel {
  int rideRequestId;
  String destinationLatitude;
  String destinationLongitude;
  String destinationAddress;
  String currentLatitude;
  String currentLongitude;

  ChangeDestinationRequestModel(
      {required this.rideRequestId,
      required this.destinationLatitude,
      required this.destinationLongitude,
      required this.destinationAddress,
      required this.currentLatitude,
      required this.currentLongitude});

  // Convert a RideRequest object into a Map object
  Map<String, dynamic> toJson() {
    return {
      'ride_request_id': rideRequestId,
      'latitude': destinationLatitude,
      'longitude': destinationLongitude,
      'current_latitude': currentLatitude,
      'current_longitude': currentLongitude,
      'end_address': destinationAddress,
    };
  }

  // Extract a RideRequest object from a Map object
  factory ChangeDestinationRequestModel.fromJson(Map<String, dynamic> json) {
    return ChangeDestinationRequestModel(
      rideRequestId: json['ride_request_id'],
      destinationLatitude: json['end_latitude'],
      destinationLongitude: json['end_longitude'],
      currentLatitude: json['current_latitude'],
      currentLongitude: json['current_longitude'],
      destinationAddress: json['end_address'], 
    );
  }
}
