
class StopsModel {
  int id;
  String title;
    String ?currentAdress;

  double stopLat;
  double stopLng;


  double ?currentLat;
  double ?currentLng;
  String? type;
  int? remainingAmount;
  String? isDone;
  String? paymentId;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? timerStart; // Previously startTime
  bool? isArrived; // Previously isReached
  String? status; // Previously isCompleted
  Duration? totalTime; // Previously endTime

  StopsModel({
    required this.id,
    required this.title,
    required this.stopLat,
    this.currentAdress,
    required this.stopLng,
    this.type,
    this.remainingAmount,
    this.isDone,
    this.paymentId,
    this.createdAt,
    this.updatedAt,
    this.timerStart,
    this.isArrived,
    this.status,
    this.totalTime,
    this.currentLat,
    this.currentLng,
  });

  // Converts an instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,   

      'stop_lat': stopLat,
      'stop_lng': stopLng,
            'current_address': currentAdress,
         'current_lat': currentLat,
      'current_lng': currentLng,
      'type': type,
      'remaining_amount': remainingAmount,
      'is_done': isDone,
      'payment_id': paymentId,
      'timer_start': timerStart?.toIso8601String(),
      'is_arrived': isArrived,
      'status': status,
      'total_time': totalTime?.inSeconds, // Serialize Duration as total seconds
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Creates an instance from JSON
  factory StopsModel.fromJson(Map<String, dynamic> json) {
    return StopsModel(
      id: json['id'],
      title: json['title'],
            currentAdress: json['current_address'],

      stopLat: json['stop_lat'],
      stopLng: json['stop_lng'],
       currentLat: json['current_lat'],
      currentLng: json['current_lng'],
      type: json['type'],
      remainingAmount: json['remaining_amount'],
      isDone: json['is_done'],
      paymentId: json['payment_id'],
      timerStart: json['timer_start'] != null ? DateTime.parse(json['timer_start']) : null,
      isArrived: json['is_arrived'],
      status: json['status'],
      totalTime: json['total_time'] != null ? Duration(seconds: json['total_time']) : null,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  @override
  String toString() {
    return 'Stops(id: $id, title: $title, stopLat: $stopLat, stopLng: $stopLng, type: $type, remainingAmount: $remainingAmount, isDone: $isDone, paymentId: $paymentId, timerStart: $timerStart, isArrived: $isArrived, status: $status, totalTime: $totalTime, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}


