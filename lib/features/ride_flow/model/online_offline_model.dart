
class     OnlineOfflineModel {
  // String type;


  // UserData? user;
    int  status;

  String link;
  List<IncompleteSectionModel> incompleteList;

  OnlineOfflineModel({
    // required this.type,

    // this.user,
    required this.link,
    required this.status,
    required this.incompleteList,
  });

  // From JSON
  factory OnlineOfflineModel.fromJson(Map<String, dynamic> json) {
    return OnlineOfflineModel(
      // type: json['type'],
            status: json['status'],
                        // user: json['user']!=null?UserData.fromJson(json['user']):null,


      link: json['link'],
      incompleteList: (json['incomplete_list'] as List)
          .map((item) => IncompleteSectionModel.fromJson(item))
          .toList(),
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      // 'type': type,
            'status': status,
            // if(user!=null)"user":user,

      'link': link,
      'incomplete list': incompleteList.map((item) => item.toJson()).toList(),
    };
  }
}

class IncompleteSectionModel {
  String type;
  String link;
  String message;

  IncompleteSectionModel({
    required this.type,
    required this.link,
    required this.message,
  });

  // From JSON
  factory IncompleteSectionModel.fromJson(Map<String, dynamic> json) {
    return IncompleteSectionModel(
      type: json['type'],
      link: json['link'],
      message: json['message'],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'link': link,
      'message': message,
    };
  }
}
