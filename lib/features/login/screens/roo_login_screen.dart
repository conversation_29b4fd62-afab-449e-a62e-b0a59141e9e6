import 'package:aad_oauth/aad_oauth.dart';
import 'package:aad_oauth/model/config.dart';
import 'package:aad_oauth/model/failure.dart';
import 'package:aad_oauth/model/token.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;

import 'package:rooo_driver/features/login/models/microsoft_auth_model.dart';
import 'package:rooo_driver/features/ride_flow/screen/ride_screen.dart';
import 'package:rooo_driver/features/verify_otp/screens/password_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/verify_otp/screens/otp_screen.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with WidgetsBindingObserver {
  TextEditingController _numberController = TextEditingController();
  String _selectedCountryCode = '+61';
  String _selectedCountryIsoCode = 'AU';

  final _key = GlobalKey<FormState>();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _loginButtonKey = GlobalKey();

  double _lastBottomInset = 0;

  void _removeLocalAppleData() {
    sharedPref.remove(
      APPLE_ID,
    );
    sharedPref.remove(
      APPLE_FIRST_NAME,
    );
    sharedPref.remove(
      APPLE_LAST_NAME,
    );
    sharedPref.remove(
      APPLE_EMAIL,
    );
  }

  _saveAppleDataApi(
      {required String appleId,
      String? appleEmail,
      required String appleFirstName,
      required String appleLastName}) {
    Map<String, dynamic> request = {
      'apple_id': appleId,
      'email': appleEmail,
      "first_name": appleFirstName,
      "last_name": appleLastName
    };

    BlocProvider.of<LoginCubit>(context).saveAppleData(request: request);
  }

  _getAppledata({
    required String appleId,
  }) {
    Map<String, dynamic> request = {
      'apple_id': appleId,
    };

    BlocProvider.of<LoginCubit>(context).getAppleData(request: request);
  }

  _loginWithApple() async {
    hideKeyboard(context);

    // save to remote

    AuthorizationCredentialAppleID? appleUser;
    BlocProvider.of<LoginCubit>(context).emit(LoginLoadingState());

    try {
      appleUser = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
    } catch (error) {
      BlocProvider.of<LoginCubit>(context)
          .emit(LoginErrorState(message: "Server error"));
    }

    if (appleUser != null) {
      if (appleUser.userIdentifier != null) {
        String name = "";
        name = appleUser.givenName ?? "";
        String firstName = "";
        String lastName = "";

        List<String> nameList = name.split(" ");
        if (nameList.isNotEmpty) {
          firstName = nameList[0];
          if (nameList.length > 1) {
            lastName = nameList[1];
          }
        }
        _saveDataForAppleLocally(appleUser.userIdentifier!,
            appleUser.email ?? '', firstName, lastName);

        Map<String, dynamic> request = {
          'email': appleUser.email,
          'apple_id': appleUser.userIdentifier,
          "user_type": "driver",
          "first_name": firstName,
          "last_name": lastName
        };

        BlocProvider.of<LoginCubit>(context).socialLogin(request: request);
      } else {
        _getAppledata(appleId: appleUser.userIdentifier ?? "");
      }
    } else {
      BlocProvider.of<LoginCubit>(context)
          .emit(LoginErrorState(message: "Login failed"));
    }
  }

  loginWithNumber() {
    hideKeyboard(context);

    Map request = {
      "contact_number": _selectedCountryCode + _numberController.text,
      "user_type": "driver"
    };

    BlocProvider.of<LoginCubit>(context).login(request: request);
  }

  loginWithGoogle() async {
    hideKeyboard(context);

    GoogleSignInAccount? googleUser;
    BlocProvider.of<LoginCubit>(context).emit(LoginLoadingState());

    try {
      await GoogleSignIn().signOut();
      googleUser = await GoogleSignIn().signIn();
    } catch (error) {
      BlocProvider.of<LoginCubit>(context)
          .emit(LoginErrorState(message: "Server error"));
    }
    if (googleUser != null) {
      String name = "";
      name = googleUser.displayName ?? "";
      String firstName = "";
      String lastName = "";

      List<String> nameList = name.split(" ");
      if (nameList.isNotEmpty) {
        firstName = nameList[0];
        if (nameList.length > 1) {
          lastName = nameList[1];
        }
      }

      Map<String, dynamic> request = {
        'email': googleUser.email,
        'google_id': googleUser.id,
        "user_type": "driver",
        "first_name": firstName,
        "last_name": lastName,
      };

      BlocProvider.of<LoginCubit>(context).socialLogin(request: request);
    } else {
      BlocProvider.of<LoginCubit>(context).emit(LoginCanceledState());
    }
  }

  loginWithMicrosoft() async {
    hideKeyboard(context);

    MicrosoftAuthModel? microsoftUser;
    BlocProvider.of<LoginCubit>(context).emit(LoginLoadingState());

    final _microsoftSignIn = AadOAuth(Config(
      tenant: "common",
      clientId: "64297e4c-d40c-4480-9ea8-146e58ee2e75",
      responseType: "code",
      scope: "User.Read",
      redirectUri: "msal64297e4c-d40c-4480-9ea8-146e58ee2e75://auth",
      loader: const Center(child: CircularProgressIndicator()),
      navigatorKey: navigatorKey,
    ));
    bool isUserCanceled = false;

    try {
      var result = await _microsoftSignIn.login(refreshIfAvailable: true);

      await result.fold(
        (Failure failure) {
          if (failure.errorType ==
              ErrorType.accessDeniedOrAuthenticationCanceled) {
            isUserCanceled = true;
          }
        },
        (Token token) async {
          if (token.accessToken == null) {
            return;
          } else {
            final response = await http.get(
              Uri.parse('https://graph.microsoft.com/v1.0/me'),
              headers: {
                'Authorization': 'Bearer ${token.accessToken}',
              },
            );
            print(response.body);

            if (response.statusCode == 200) {
              microsoftUser =
                  MicrosoftAuthModel.fromJson(jsonDecode(response.body));
            }

            ;
          }
          print(
              'Logged in successfully, your access token: ${token.accessToken!}');
        },
      );
    } catch (error) {
      BlocProvider.of<LoginCubit>(context)
          .emit(LoginErrorState(message: "Server error"));
    }
    if (microsoftUser != null) {
      String name = "";
      name = microsoftUser?.displayName ?? "";
      String firstName = "";
      String lastName = "";
      log(microsoftUser?.mail ?? "");

      List<String> nameList = name.split(" ");
      if (nameList.isNotEmpty) {
        firstName = nameList[0];
        if (nameList.length > 1) {
          lastName = nameList[1];
        }
      }

      Map<String, dynamic> request = {
        'email': microsoftUser?.mail ?? "",
        'microsoft_id': microsoftUser?.id ?? "",
        "user_type": "driver",
        "first_name": firstName,
        "last_name": lastName,
      };

      BlocProvider.of<LoginCubit>(context).socialLogin(request: request);
    } else {
      if (isUserCanceled) {
        BlocProvider.of<LoginCubit>(context).emit(LoginCanceledState());
      } else {
        BlocProvider.of<LoginCubit>(context)
            .emit(LoginErrorState(message: "Login failed"));
      }
    }
  }

  Future<void> _saveDataForAppleLocally(String appleId, String appleEmail,
      String applFirstName, String appleLastName,
      {bool hasLocalData = false}) async {
    if (!hasLocalData) {
      await Future.wait([
        sharedPref.setString(APPLE_ID, appleId),
        sharedPref.setString(APPLE_FIRST_NAME, applFirstName),
        sharedPref.setString(APPLE_LAST_NAME, appleLastName),
        sharedPref.setString(APPLE_EMAIL, appleEmail),
      ]);
    }
    _saveAppleDataApi(
        appleId: appleId,
        appleFirstName: applFirstName,
        appleEmail: appleEmail,
        appleLastName: appleLastName);
  }

  @override
  void initState() {
    sharedPref.setBool(IS_FIRST_TIME, false);
    GlobalState.global_inbox_count.value = 0;
    GlobalState.global_opportunity_count.value = 0;
    GlobalState.global_care_count.value = 0;
    GlobalState.global_notification_count.value = 0;
    GlobalState.global_drawer_count.value = 0;
    GlobalState.chat_count.value = {};
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    hideKeyboard(context);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    if (bottomInset > 0 && _lastBottomInset == 0) {
      // Keyboard just opened
      Future.delayed(Duration(milliseconds: 100), () {
        final contextBtn = _loginButtonKey.currentContext;
        if (contextBtn != null) {
          Scrollable.ensureVisible(contextBtn,
              duration: Duration(milliseconds: 300), curve: Curves.easeIn);
        }
      });
    }
    _lastBottomInset = bottomInset;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: BlocConsumer<LoginCubit, LoginState>(
        listener: (context, state) async {
          if (state is LoginErrorState) {
            GlobalMethods.errorToast(context, state.message);
          }
          if (state is OtpSentState) {
            GlobalMethods.pushScreen(
                context: context,
                screen: OtpScreen(
                    loginKey: state.key,
                    countryCode: _selectedCountryCode,
                    phone: _numberController.text,
                    countryIsoCode: _selectedCountryIsoCode),
                screenIdentifier: ScreenIdentifier.OtpScreen);
          } else if (state is EnterPasswordState) {
            GlobalMethods.pushScreen(
                context: context,
                screen: PasswordScreen(
                    email: state.email,
                    loginKey: state.key,
                    countryCode: _selectedCountryCode,
                    phone: _numberController.text,
                    countryIsoCode: _selectedCountryIsoCode),
                screenIdentifier: ScreenIdentifier.OtpScreen);
          } else if (state is SignedInState) {
            await GlobalMethods.saveDataLocally(user: state.userData);
            GlobalMethods.pushScreen(
                context: context,
                screen: RideScreen(
                  isNewSignUp: state.userData.isNewSignUp ?? false,
                ),
                screenIdentifier: ScreenIdentifier.InitialScreen);
          } else if (state is SavedAppleData) {
            _removeLocalAppleData();
          } else if (state is LoadedAppleData) {
            Map<String, dynamic> request = {
              'email': state.appleData.email,
              'apple_id': state.appleData.email,
              "user_type": "driver",
              "first_name": state.appleData.firstName,
              "last_name": state.appleData.lastName
            };
            BlocProvider.of<LoginCubit>(context).socialLogin(request: request);
          }
        },
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: ScreenBody(
              isLoading: state is LoginLoadingState,
              isEmpty: false,
              emptyMessage: '',
              child: Column(
                children: [
                  Flexible(
                    child: Center(
                        child: Image.asset(
                      Theme.of(context).brightness == Brightness.dark
                          ? 'images/logo-white.png'
                          : 'images/logo-black.png',
                      width: MediaQuery.of(context).size.width - 150,
                    )),
                  ),
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: EdgeInsets.only(
                        top: screenPaddingValue,
                        left: screenPaddingValue,
                        right: screenPaddingValue,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            language.enterYourMobileNumber,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          height10,
                          Form(
                            key: _key,
                            child: TextFormField(
                              controller: _numberController,
                              autofocus: true,
                              maxLength: 10,
                              validator: (value) {
                                if ((value?.length ?? 0) < 9) {
                                  return "Please enter a valid mobile number";
                                }
                                return null;
                              },
                              keyboardType: TextInputType.phone,
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.normal),
                              decoration: InputDecoration(
                                  counterText: '',
                                  prefixIcon: Padding(
                                    padding: const EdgeInsets.all(14),
                                    child: Text(
                                      "+61",
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.normal),
                                    ),
                                  ),
                                  border: OutlineInputBorder()),
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                          height10,
                          AppButton(
                              key: _loginButtonKey,
                              width: double.infinity,
                              text: language.logIn,
                              onPressed: () async {
                                if (_key.currentState?.validate() ?? false) {
                                  if (_numberController.text.isNotEmpty) {
                                    loginWithNumber();
                                  } else {
                                    GlobalMethods.errorToast(
                                      context,
                                      language.enterYourMobileNumber,
                                    );
                                  }
                                }
                              }),
                          height5,
                          Align(
                              alignment: Alignment.center,
                              child: Text(
                                "OR",
                                style: TextStyle(fontSize: 10),
                              )),
                          height5,
                          InkWell(
                            onTap: () {
                              loginWithGoogle();
                            },
                            child: Container(
                              padding: EdgeInsets.all(7),
                              width: MediaQuery.sizeOf(context).width,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                    10,
                                  ),
                                  border: Border.all(
                                      color: AppColors.blackColor(context))),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset('images/ic_google.png',
                                      fit: BoxFit.cover, height: 25, width: 25),
                                  width4,
                                  Text(
                                    language.signInWithGoogle,
                                    style: TextStyle(
                                      color: AppColors.blackColor(context),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          height10,
                          InkWell(
                            onTap: () {
                              loginWithMicrosoft();
                            },
                            child: Container(
                              padding: EdgeInsets.all(7),
                              width: MediaQuery.sizeOf(context).width,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                    10,
                                  ),
                                  border: Border.all(
                                      color: AppColors.blackColor(context))),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset('images/ic_microsoft.png',
                                      fit: BoxFit.cover, height: 25, width: 25),
                                  width4,
                                  Text(
                                    "Sign up with Microsoft",
                                    style: TextStyle(
                                      color: AppColors.blackColor(context),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (Platform.isIOS)
                            Column(
                              children: [
                                height10,
                                InkWell(
                                  onTap: () {
                                    _loginWithApple();
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(7),
                                    width: MediaQuery.sizeOf(context).width,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          10,
                                        ),
                                        border: Border.all(
                                            color: AppColors.blackColor(context))),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                            Theme.of(context).brightness ==
                                                    Brightness.dark
                                                ? 'images/apple_icon_white.png'
                                                : 'images/apple_icon_black.png',
                                            fit: BoxFit.cover,
                                            height: 25,
                                            width: 25),
                                        width4,
                                        Text(
                                          "Sign up with Apple",
                                          style: TextStyle(
                                            color: AppColors.blackColor(context),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
