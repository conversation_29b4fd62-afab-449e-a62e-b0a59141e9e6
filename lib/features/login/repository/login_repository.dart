import 'package:rooo_driver/features/login/models/apple_data_response_model.dart';
import 'package:rooo_driver/features/login/models/login_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class LoginRepository {
  Future<LoginResponseModel> loginApi({required Map request}) async {
    return LoginResponseModel.from<PERSON>son(await handleResponse(
        await buildHttpResponse('custom-login',
            method: HttpMethod.POST, request: request)));
  }

   Future<StatusMessageModel> saveAppleData({required Map request}) async {
    return StatusMessageModel.from<PERSON>son(await handleResponse(
        await buildHttpResponse('save-apple-data',
            method: HttpMethod.POST, request: request)));
  }

    Future<AppleDataResponseModel> getAppleData({required Map request}) async {
    return AppleDataResponseModel.from<PERSON>son(await handleResponse(
        await buildHttpResponse('get-apple-data',
            method: HttpMethod.POST, request: request)));
  }



}
