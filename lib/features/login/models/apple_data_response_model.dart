import 'package:rooo_driver/features/login/models/apple_data_model.dart';
import 'package:rooo_driver/global/models/response_model.dart';

class AppleDataResponseModel extends ResponseModel<AppleData> {
  AppleDataResponseModel({
    required bool status,
    required String message,
    required AppleData? data,
  }) : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory AppleDataResponseModel.fromJson(Map<String, dynamic> json) {
    return AppleDataResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? AppleData.fromJson(json['data'])
          : null,
    );
  }

}
