import 'package:rooo_driver/global/models/UserDetailModel.dart';

class LoginData {
  UserData? user;
  String? key;
    String? login_screen;

  bool? isUserExists;

  LoginData({this.key, this.isUserExists});

  LoginData.fromJson(Map<String, dynamic> json) {
    // user = json['user'];
    key = json['key'];
        login_screen = json['login_screen'];

    isUserExists = json['is_user_exists'];
    user = json['user'] != null ? new UserData.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    // data['user'] = this.user;
    data['key'] = this.key;
        data['login_screen'] = this.login_screen;

    data['is_user_exists'] = this.isUserExists;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}
