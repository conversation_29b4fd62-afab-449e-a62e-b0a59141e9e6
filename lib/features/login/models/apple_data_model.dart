class AppleData {
  final int id;
  final String firstName;
    final String lastName;

  final String email;

  AppleData({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
  });


  // Create a User object from a Map (JSON)
  factory AppleData.fromJson(Map<String, dynamic> json) {
    return AppleData(
      id: json['id'],
      firstName: json['first_name'],
            lastName: json['last_name'],

      email: json['email'],
    );
  }
}
