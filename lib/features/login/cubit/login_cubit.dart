import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/login/models/apple_data_model.dart';
import 'package:rooo_driver/features/login/repository/login_repository.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/utils/Constants.dart';

abstract class LoginState {}

class LoginInitState extends LoginState {}

class LoginLoadingState extends LoginState {}

class OtpSentState extends LoginState {
  final String key;

  OtpSentState({required this.key});
}

class EnterPasswordState extends LoginState {
  final String key;
  final String email;

  EnterPasswordState({required this.email, required this.key});
}

class SavedAppleData extends LoginState {
  SavedAppleData();
}

class LoadedAppleData extends LoginState {
  final AppleData appleData;

  LoadedAppleData({required this.appleData});
}

class SignedInState extends LoginState {
  final UserData userData;

  SignedInState({required this.userData});
}

class LoginErrorState extends LoginState {
  final String message;
  LoginErrorState({required this.message,});
}

class LoginCanceledState extends LoginState {}

class LoginCubit extends Cubit<LoginState> {
  LoginRepository _loginRepository = LoginRepository();

  LoginCubit() : super(LoginInitState());

  void login({required Map request}) async {
    emit(LoginLoadingState());

    await _loginRepository.loginApi(request: request).then((value) {
      if (value.status ?? false) {
        if (value.data != null) {
          if (value.data?.isUserExists == true) {
            if (value.data?.login_screen == "password") {
              emit(EnterPasswordState(
                  email: value.data?.user?.email ?? "",
                  key: value.data!.key.toString()));
            } else if (value.data?.login_screen == "otp") {
              emit(OtpSentState(key: value.data!.key.toString()));
            }
          } else {
            emit(OtpSentState(key: value.data!.key.toString()));
          }
        } else {
          emit(LoginErrorState( message: serverErrorMessage));
        }
        // emit(OtpSentState(key: value.data!.key.toString()));
      } else {
        emit(LoginErrorState(message: value.message ?? "Server error"));
      }
    }).onError((error, stackTrace) {
      emit(LoginErrorState(message: "Server error"));
      print(error);
    });
  }

  void saveAppleData({required Map request}) async {
    emit(LoginLoadingState());

    await _loginRepository.saveAppleData(request: request).then((value) {
      if (value.status ?? false) {
        emit(SavedAppleData());
      } else {
        emit(LoginErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(LoginErrorState(message: "Server error"));
      print(error);
    });
  }

  void getAppleData({required Map request}) async {
    emit(LoginLoadingState());

    await _loginRepository.getAppleData(request: request).then((value) {
      if (value.status ?? false) {
        emit(LoadedAppleData(appleData: value.data!));
      } else {
        emit(LoginErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(LoginErrorState(message: "Server error"));
      print(error);
    });
  }

  void socialLogin({required Map request}) async {
    emit(LoginLoadingState());

    await _loginRepository.loginApi(request: request).then((value) {
      if (value.status ?? false) {
        if (value.data != null) {
          if (value.data?.isUserExists ?? false) {
            emit(SignedInState(userData: value.data!.user!));
          } else {
            emit(LoginErrorState(message: "User doesn't exists"));
          }
        } else {
          emit(LoginErrorState(message: serverErrorMessage));
        }
      } else {
        emit(LoginErrorState(message: value.message ?? serverErrorMessage));
      }
    }).onError((error, stackTrace) {
      emit(LoginErrorState(message: "Server error"));
      print(error);
    });
  }
}
