import 'package:rooo_driver/global/models/response_model.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/model/PaginationModel.dart';

class RegionResponseModel extends ResponseModel<List<ProvinceModel>> {
  RegionResponseModel(
      {required bool status,
      required String message,
      required List<ProvinceModel>? data,
      required PaginationModel? pagination})
      : super(
          status: status,
          message: message,
          data: data,
        );

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory RegionResponseModel.fromJson(Map<String, dynamic> json) {
    return RegionResponseModel(
      pagination: json["pagination"] != null
          ? PaginationModel.fromJson(json["pagination"])
          : null,
      status: json['status'] != null ? json["status"] : true,
      message: json['message'] != null ? json["message"] : "",
      data: json["data"] != null
          ? (json["data"] as List)
              .map((e) => ProvinceModel.fromJson(e))
              .toList()
          : null,
    );
  }
}
