import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/edit_profile/cubit/edit_profile_cubit.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class ChangeNameScreen extends StatefulWidget {
  final String? firstName;
  final String? lastName;
  const ChangeNameScreen({super.key, this.firstName, this.lastName});

  @override
  State<ChangeNameScreen> createState() => _ChangeNameScreenState();
}

class _ChangeNameScreenState extends State<ChangeNameScreen> {
  final _nameController = TextEditingController();

  _init() {
    if (widget.firstName != null) {
      _nameController.text = widget.firstName!;
    } 
    
    
    else {
      _nameController.text = widget.lastName!;
    }
  }

  _changeFirstName() {
    Map request = {"type": "name", "first_name": _nameController.text};

    BlocProvider.of<EditProfileCubit>(context).editName(request: request);
  }

  _changeLastName() {
    Map request = {"type": "lname", "last_name": _nameController.text};

    BlocProvider.of<EditProfileCubit>(context).editName(request: request);
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BlocBuilder<EditProfileCubit, EditProfileState>(
        builder: (context, state) {
          return BottomButton(
              text: language.updateText,
              onPressed: () {
                if (widget.firstName != null) {
                  _changeFirstName();
                } else {
                  _changeLastName();
                }
              },
              notVisible: state is EditProfileLoadingState);
        },
      ),
      appBar: RoooAppbar(title: "Update Name"),
      body: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileErrorState) {
            GlobalMethods.infoToast(context,  errorMessage);
          } else if (state is EditProfileNameRequestSentState) {
            GlobalMethods.showSuccessDialog(
                context: context, positiveAction: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                }, title: state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              isLoading: state is EditProfileLoadingState,
              isEmpty: false,
              emptyMessage: '',
              child: Padding(
                padding: screenPadding,
                child: AppTextField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: widget.firstName != null
                          ? language.firstName
                          : language.lastName,
                      hintText: widget.firstName != null
                          ? language.firstName
                          : language.lastName,
                    ),
                    textFieldType: TextFieldType.NAME),
              ));
        },
      ),
    );
  }
}
