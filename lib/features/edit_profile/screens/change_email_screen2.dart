import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pinput/pinput.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/widgets/app_button.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/edit_profile/cubit/edit_profile_cubit.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/network/RestApis.dart';
import 'package:rooo_driver/utils/Constants.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class ChangeEmailScreen extends StatefulWidget {
  final String email;
  const ChangeEmailScreen({
    super.key,
    required this.email,
  });

  @override
  State<ChangeEmailScreen> createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends State<ChangeEmailScreen> {
  final _emailController = TextEditingController();
  final _key = GlobalKey<FormState>();

  _init() {
    _emailController.text = widget.email;
  }

  bool _isEmailOTPSent = false;
  bool _isEmailOTPVerified = false;
  TextEditingController _emailOTPController = TextEditingController();
  Timer? _emailVerificationTimer;
  int _emailOTPCountdownTimer = 60;

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    super.dispose();
    _emailVerificationTimer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BlocBuilder<EditProfileCubit, EditProfileState>(
        builder: (context, state) {
          return BottomButton(
              text: _isEmailOTPSent
                  ? "Verify code"
                  : _isEmailOTPVerified
                      ? "Update email"
                      : "Send verification code",
              onPressed: () {
                if (_isEmailOTPVerified) {
                  _changeEmail();
                } else if (!_isEmailOTPSent) {
                  if (_key.currentState?.validate() ?? false) {
                    _showEmailVerificationDialog();
                  }
                } else if (_emailOTPController.text.trim().length < 6) {
                  GlobalMethods.infoToast(
                      context, "Please enter email verification code");
                } else if (!_isEmailOTPVerified) {
                  _changeEmail();
                }
              },
              notVisible: state is EditProfileLoadingState);
        },
      ),
      appBar: RoooAppbar(title: "Update Email"),
      body: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileErrorState) {
            setState(() {
              _emailOTPController.clear();
            });
            Navigator.pop(context);
            GlobalMethods.errorToast(context, errorMessage); 
          } else if (state is EditProfileEmailRequestSentState) {
            GlobalMethods.showSuccessDialog(
                context: context,
                positiveAction: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
                title: state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
              isLoading: state is EditProfileLoadingState,
              isEmpty: false,
              emptyMessage: '',
              child: Padding(
                padding: screenPadding,
                child: Form(
                  key: _key,
                  child: Column(
                    children: [
                      AppTextField(
                          controller: _emailController,
                          validator: (value) {
                            final RegExp _emailRegex = RegExp(
                              r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                            );
                            if (!_emailRegex.hasMatch(value.toString())) {
                              return "Please enter valid email";
                            } else {
                              return null;
                            }
                          },
                          decoration: InputDecoration(
                            labelText: language.email,
                            hintText: language.email,
                          ),
                          textFieldType: TextFieldType.EMAIL),
                      !_isEmailOTPSent || _isEmailOTPVerified
                          ? const SizedBox()
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Please enter the verification code"),
                                height10,
                                Pinput(
                                  controller: _emailOTPController,
                                  defaultPinTheme: PinTheme(
                                    width: 55,
                                    height: 55,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          width: .5,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  length: 6,
                                  onCompleted: (pin) {
                                    // _otp = pin;
                                  },
                                ),
                                height20,
                                _timerView(),
                              ],
                            ),
                    ],
                  ),
                ),
              ));
        },
      ),
    );
  }

  void _showEmailVerificationDialog() {
    if (widget.email == _emailController.text.trim()) {
      GlobalMethods.infoToast(context, "Please enter new email");
      return;
    }

    GlobalMethods.showInfoDialogNew(
      context: context,
      barrierDismissible: true,
      onClick: () {
        Navigator.of(context).pop();
        _sendEmailOtp(email: _emailController.text.trim(), resendOTP: false);
      },
      title:
          "A verification code will be sent to your email. Please verify your email address to continue",
    );
  }

  Future<void> _sendEmailOtp({
    required String email,
    required bool resendOTP,
  }) async {
    if (resendOTP) {
      setState(() {
        _emailOTPController.clear();
      });
    }
    _showSendingEmailOTP();
    await Future.delayed(Duration(seconds: 4));
    var response = await sendOTPToEmail(email: email);
    Navigator.of(context).pop();
    if (!response.status) {
      GlobalMethods.infoToast(context, response.message);
      return;
    }
    setState(() {
      _isEmailOTPSent = true;
    });
    _emailOTPCountdownTimer = 60;
    _emailVerificationTimer = Timer.periodic(Duration(seconds: 1), (v) {
      if (_emailOTPCountdownTimer == 0) {
        _emailVerificationTimer?.cancel();
      } else {
        setState(() {
          _emailOTPCountdownTimer = _emailOTPCountdownTimer - 1;
        });
      }
    });
  }

  void _showSendingEmailOTP() {
    GlobalMethods.showActivity(
      context: context,
      title: "Sending email verification code. Please wait...",
    );
  }

  void _showVerifyingEmail() {
    GlobalMethods.showActivity(
      context: context,
      title:
          "We are verifying your email. Please wait while we verify your email address.",
    );
  }

  Widget _timerView() {
    return Column(
      children: [
        Row(
          children: [
            Text(
              "Code not received?",
              style: TextStyle(),
            ),
            width10,
            _emailOTPCountdownTimer == 0
                ? AppButton(
                    text: "Resend",
                    onPressed: () async  {
                      _sendEmailOtp(
                          email: _emailController.text.trim(), resendOTP: true);
                    },
                  )
                : Text(
                    _emailOTPCountdownTimer.toString(),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ],
        ),
        height10,
      ],
    );
  }

  void _changeEmail() {
    _showVerifyingEmail();

    BlocProvider.of<EditProfileCubit>(context).editEmail(
        email: _emailController.text.trim(),
        otp: _emailOTPController.text.trim());
  }
}
