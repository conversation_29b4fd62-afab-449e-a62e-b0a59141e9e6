
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';

class ChangeProfileImageScreen extends StatefulWidget {
  final String? imageUrl;
  const ChangeProfileImageScreen({super.key, required this.imageUrl});

  @override
  State<ChangeProfileImageScreen> createState() =>
      _ChangeProfileImageScreenState();
}

class _ChangeProfileImageScreenState extends State<ChangeProfileImageScreen> {
  ValueNotifier<XFile> _profileImage = ValueNotifier(XFile(""));

  Future _changeProfileImage() async {
    if (_profileImage.value.path.isNotEmpty) {




      BlocProvider.of<EditProfileCubit>(context)
          .updateProfileImage(file: File(_profileImage.value.path));
    }else{
      GlobalMethods.infoToast(context, "Please upload an image");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BlocBuilder<EditProfileCubit, EditProfileState>(
        builder: (context, state) {
          return BottomButton(
              text: language.updateProfile,
              onPressed: _changeProfileImage,
              notVisible: state is EditProfileLoadingState);
        },
      ),
      appBar: RoooAppbar(title: "Change profile image"),
      body: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileErrorState) {
            GlobalMethods.infoToast(context,  errorMessage);
          } else if (state is EditProfileImageRequestSentState) {
            _profileImage.value = XFile('');

            GlobalMethods.showSuccessDialog(
                context: context,
                positiveAction: () {
                  closeScreen(context);
                },
                title: state.message);
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is EditProfileLoadingState,
            isEmpty: false,
            emptyMessage: "",
            child: Padding(
              padding: screenPadding,
              child: Column(
                children: [
                  Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      ValueListenableBuilder<XFile>(
                        valueListenable: _profileImage,
                        builder: (context, state, _) {
                          if (_profileImage.value.path.isEmpty) {


                            if(widget.imageUrl==null){
                                return Container(
                                height: 300,
                                decoration: BoxDecoration(
                                  border: Border.all(color: AppColors.blackColor(context)),
                                  shape: BoxShape.circle,
                                ),
                                child: CachedNetworkImage(
                                    imageBuilder: (context, imageProvider) {

                                      if(appStore.userProfile.isEmpty){
                                        return Text("Please update your profile picture");
                                      }
                                      return Container(
                                        
                                        height: 300,
                                        decoration: BoxDecoration(
                                          border: Border.all(color: AppColors.blackColor(context)),
                                            image: DecorationImage(
                                                fit: BoxFit.cover,
                                                image: imageProvider),
                                            shape: BoxShape.circle),
                                      );
                                    },
                                    imageUrl: appStore.userProfile),
                              );

                            }
                            else{

                            return Container(
                              height: 300,
                              decoration: BoxDecoration(
                                
                                border: Border.all(color: AppColors.blackColor(context)),
                                shape: BoxShape.circle),
                              child: CachedNetworkImage(
                                  imageBuilder: (context, imageProvider) {
                                    return Container(
                                      height: 300,
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.blackColor(context)),
                                          image: DecorationImage(
                                              fit: BoxFit.cover,
                                              image: imageProvider),
                                          shape: BoxShape.circle),
                                    );
                                  },
                                  imageUrl: widget.imageUrl!),
                            );
                          }
                            }
                          return Container(
                            height: 300,
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.blackColor(context)),
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                    fit: BoxFit.cover,
                                    image: FileImage(File(state.path)))),
                          );
                        },
                      ),
                      Card(
                          child: IconButton(
                              onPressed: () async {
                                XFile? file = await ImagePicker().pickImage(
                                    source: ImageSource.camera,
                                    imageQuality: 50);

                                if (file != null) {
                                        bool value = await GlobalMethods.checkImageSize(
                        result: file, context: context);

                    if (value) {

                                  _profileImage.value = file;
                    }
                                }
                              },
                              icon: Icon(Icons.edit)))
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
