import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pinput/pinput.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/global/constants/app_text_styles.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/edit_profile/cubit/edit_profile_cubit.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';

class ChangePhoneNumberScreen extends StatefulWidget {
  final String mobileNumber;

  const ChangePhoneNumberScreen({super.key, required this.mobileNumber});

  @override
  State<ChangePhoneNumberScreen> createState() =>
      _ChangePhoneNumberScreenState();
}

class _ChangePhoneNumberScreenState extends State<ChangePhoneNumberScreen> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TextEditingController _codeController = TextEditingController();

  FocusNode _codeFocus = FocusNode();
  FocusNode _mobileFocus = FocusNode();
  String _mobileNumber = '';
  String _countryCode = '+61';
  ValueNotifier<bool> isSendOTP = ValueNotifier(false);

  void _sendVerificationCode() {
    hideKeyboard(context);
    if (_mobileNumber.isEmpty) {
      GlobalMethods.infoToast(context, language.enterYourMobileNumber);
      return;
    } else if (_countryCode + _mobileNumber.trim() == widget.mobileNumber) {
      GlobalMethods.infoToast(context,
          "It is your current mobile number, Please enter a new number.");
      return;
    }

    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      Map request = {
        'contact_number2': _countryCode + _mobileNumber,
      };
      BlocProvider.of<EditProfileCubit>(context)
          .sendVerificationCode(request: request);
    }
  }

  _changeMobileNumber() {
    hideKeyboard(context);
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      if (_codeController.text.trim().length != 6) {
        GlobalMethods.infoToast(context, language.enterOTP);
      } else {
        Map request = {
          'mobile': _countryCode + _mobileNumber,
          'otp': _codeController.text.trim(),
        };
        BlocProvider.of<EditProfileCubit>(context)
            .changeMobileNumber(request: request);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BlocBuilder<EditProfileCubit, EditProfileState>(
        builder: (context, state) {
          return ValueListenableBuilder<bool>(
            valueListenable: isSendOTP,
            builder: (context, value, child) {
              if (value) {
                return BottomButton(
                    text: language.ChangeMobileTxt,
                    onPressed: _changeMobileNumber,
                    notVisible: state is EditProfileLoadingState);
              } else {
                return BottomButton(
                    text: "Verify phone",
                    onPressed: _sendVerificationCode,
                    notVisible: state is EditProfileLoadingState);
              }
            },
          );
        },
      ),
      appBar: RoooAppbar(title: "Change phone number"),
      body: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileErrorState) {
            GlobalMethods.infoToast(context, errorMessage);
            isSendOTP.value = false;
          } else if (state is EditProfileVerificationSentState) {
            if (state.response.status == false) {
              GlobalMethods.infoToast(
                  context, state.response.message.toString());
            } else if (state.response.status == true) {
              isSendOTP.value = true;
              GlobalMethods.infoToast(context, language.enterOTP);
            }
          } else if (state is EditProfileMobileNumberChangedState) {
            if (state.response.status == true) {
              GlobalMethods.showSuccessDialog(
                  context: context,
                  positiveAction: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  },
                  title: state.response.message.toString());
              //show message
            } else if (state.response.status == false) {
              GlobalMethods.infoToast(
                  context, state.response.message.toString());

              _codeController.clear();
            } else {
              GlobalMethods.infoToast(
                context,
                language.errorMsg,
              );
            }
          }
        },
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is EditProfileLoadingState,
            isEmpty: false,
            emptyMessage: "",
            child: Form(
              key: _formKey,
              child: Padding(
                padding: screenPadding,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      autofocus: true,
                      maxLength: 10,
                      validator: (value) {
                        if ((value?.length ?? 0) < 9) {
                          return "Please enter a valid mobile number";
                        }
                        return null;
                      },
                      onChanged: (value) {
                        if (value.startsWith("0")) {
                          _mobileNumber = value.substring(1);
                        } else {
                          _mobileNumber = value;
                        }
                      },
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                          counterText: "",
                          prefix: Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: Text(
                              "+61",
                              style: AppTextStyles.title(),
                            ),
                          ),
                          border: OutlineInputBorder()),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                    ),
                    // IntlPhoneField(
                    //   validator: (p0) {
                    //     if (int.tryParse((p0?.number) ?? '') == null) {
                    //       return language.InvalidMobileNumberTxt;
                    //     }
                    //     return null;
                    //   },
                    //   decoration: InputDecoration(
                    //     labelText: language.MobileNumberTxt,
                    //   ),
                    //   initialCountryCode: defaultCountryIsoCode,
                    //   onChanged: (phone) {
                    //     if (isSendOTP == false) {
                    //       isSendOTP.value = true;
                    //     }
                    //     _countryCode = phone.countryCode;
                    //     _mobileNumber = phone.number.trim();
                    //   },
                    //   onCountryChanged: (value) {
                    //     _countryCode = value.code;
                    //   },
                    // ),
                    ValueListenableBuilder<bool>(
                      valueListenable: isSendOTP,
                      builder: (context, value, child) {
                        return Visibility(
                          visible: value,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 20),
                              Text(
                                language.verifyOTPHeading,
                                style: TextStyle(),
                              ),
                              height10,
                              Pinput(
                                controller: _codeController,
                                length: 6,
                                onCompleted: (pin) {
                                  // _otp = pin;
                                },
                              ),
                            ],
                          ),
                        );
                      },
                    )
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
