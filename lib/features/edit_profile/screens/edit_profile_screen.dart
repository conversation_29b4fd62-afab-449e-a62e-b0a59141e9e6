import 'package:cached_network_image/cached_network_image.dart';
import 'package:rooo_driver/features/edit_profile/screens/change_profile_image_screen2.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/features/edit_profile/screens/change_email_screen2.dart';
import 'package:rooo_driver/features/edit_profile/screens/change_name_screen.dart';
import 'package:rooo_driver/features/edit_profile/screens/change_phone_number_screen.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';

class EditProfileScreen extends StatefulWidget {
  final bool? isGoogle;
  final bool isFromDahboard;
  const EditProfileScreen(
      {super.key, this.isGoogle, required this.isFromDahboard});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  ValueNotifier<XFile> _profileImage = ValueNotifier(XFile(""));

  final _emailController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  // final _userNameController = TextEditingController();

  // final _userNameFocus = FocusNode();
  final _firstNameFocus = FocusNode();
  final _lastNameFocus = FocusNode();
  final _mobileNumberFocus = FocusNode();
  final _emailFocus = FocusNode();

  UserDetailModel? _userDetails;

  ValueNotifier<String> _selected_gender = ValueNotifier("male");

  String _otherGender = "";

  List<String> _genderList_list = [
    "male",
    "female",
    "prefer not to say",
    "other"
  ];

  ScrollController _controller = ScrollController();

  ProvinceModel? _selectedProvince;
  List<ProvinceModel> _provinceData = [];
  List<ProvinceModel> _filtereedProvinceData = [];
  TextEditingController _provinceInputController = TextEditingController();
  FocusNode _provinceInputFocusNode = FocusNode();

  _init() async {
    await _getuserDetails();

    _getProvinceData();
  }

  _getuserDetails() async {
    await BlocProvider.of<EditProfileCubit>(context)
        .getUserDetails(userId: sharedPref.getInt(USER_ID)!);
  }

  _getProvinceData() {
    BlocProvider.of<EditProfileCubit>(context).getprovinceList();
  }

  _updateRegionId({required Map<String, dynamic> request}) {
    BlocProvider.of<EditProfileCubit>(context)
        .updateRegionProvinceId(request: request);
  }

  _updateGender({required Map<String, dynamic> request}) {
    BlocProvider.of<EditProfileCubit>(context).updateGender(request: request);
  }

  verify_email() async {
    BlocProvider.of<EditProfileCubit>(context).verifyEmail();
  }

  _onDataLoaded({required UserDetailModel userDetails}) {
    // _userNameController.text = userDetails.data!.username.toString();
    _emailController.text = userDetails.data!.email.toString();
    _firstNameController.text = userDetails.data!.firstName.toString();
    _lastNameController.text = userDetails.data!.lastName.toString();
    _phoneNumberController.text = userDetails.data!.contactNumber.toString();
    _selected_gender.value = userDetails.data?.gender ?? _selected_gender.value;
      _otherGender = userDetails.data?.otherGenderText ?? "";

    if (userDetails.data!.profileImage != null) {
      appStore.setUserProfile(userDetails.data!.profileImage.validate());
    }

    appStore.setUserEmail(userDetails.data!.email.validate());
    appStore.setFirstName(userDetails.data!.firstName.validate());

    sharedPref.setString(USER_EMAIL, userDetails.data!.email.validate());
    sharedPref.setString(FIRST_NAME, userDetails.data!.firstName.validate());
    sharedPref.setString(LAST_NAME, userDetails.data!.lastName.validate());
    sharedPref.setString(GENDER, userDetails.data!.gender.validate());
    sharedPref.setString(OTHER_GENDER_TEXT, userDetails.data!.otherGenderText.validate());

    appStore.setLoading(false);
  }

  @override
  void initState() {
    super.initState();

    _provinceInputFocusNode.addListener(() {
      if (!_provinceInputFocusNode.hasFocus) {
        setState(() {
          _filtereedProvinceData = [];
        });
      } else {
        if (_selectedProvince == null) {
          _filtereedProvinceData = _getFilteredProvinceData("");

          setState(() {
            _selectedProvince = null;
          });
        }
      }
    });
    _init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: language.editProfile),
      body: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileUserLoadedState) {
            _userDetails = state.userDetail;
            _onDataLoaded(userDetails: _userDetails!);
          } else if (state is EditProfileProvinceListLoadedState) {
            _provinceData = state.provinceList;
            if (_userDetails?.data?.province_id != null) {
              _selectedProvince = _provinceData.firstWhere(
                  (element) => element.id == _userDetails!.data!.province_id);
              setState(() {
                _provinceInputController.text = _selectedProvince!.provinceName;
              });
            }

            if (widget.isFromDahboard == true) {
              _controller.animateTo(
                _controller.position.maxScrollExtent + 1000,
                duration: Duration(milliseconds: 600),
                curve: Curves.easeInOut,
              );
            }
          } else if (state is EditProfileErrorState) {
            GlobalMethods.infoToast(context, errorMessage);
          } else if (state is RegionIdUpdatedState) {
            GlobalMethods.infoToast(context, "Updated Successfully");
            global_region_id = _selectedProvince!.id;
          } else if (state is GenderUpdatedState) {
            GlobalMethods.infoToast(context, "Updated Successfully");
            // global_region_id = _selected_region_id.value;
          } else if (state is EmailVerificationLinkSent) {
            GlobalMethods.infoToast(
                context, "Verification link sent successfully");
          }
        },
        builder: (context, state) {
          return ScreenBody(
              isLoading: state is EditProfileLoadingState,
              isEmpty: _userDetails == null,
              emptyMessage: "",
              child: SingleChildScrollView(
                controller: _controller,
                child: Padding(
                  padding: screenPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        alignment: Alignment.bottomRight,
                        children: [
                          ValueListenableBuilder<XFile>(
                            valueListenable: _profileImage,
                            builder: (context, state, _) {
                              if (_userDetails!.data?.profileImage != null) {
                                return Container(
                                  height: 300,
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          color: AppColors.whiteColor(context)),
                                      shape: BoxShape.circle),
                                  child: CachedNetworkImage(
                                      imageBuilder: (context, imageProvider) {
                                        return Container(
                                          height: 300,
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: AppColors.whiteColor(
                                                      context)),
                                              image: DecorationImage(
                                                  fit: BoxFit.cover,
                                                  image: imageProvider),
                                              shape: BoxShape.circle),
                                        );
                                      },
                                      imageUrl: _userDetails!.data!.profileImage
                                          .toString()),
                                );
                              }
                              return Container(
                                height: 300,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: AppColors.blackColor(context)),
                                  shape: BoxShape.circle,
                                ),
                                child: CachedNetworkImage(
                                    imageBuilder: (context, imageProvider) {
                                      if (appStore.userProfile.isEmpty) {
                                        return Text(
                                            "Please update your profile picture");
                                      }
                                      return Container(
                                        height: 300,
                                        decoration: BoxDecoration(
                                            border: Border.all(
                                                color: AppColors.whiteColor(
                                                    context)),
                                            image: DecorationImage(
                                                fit: BoxFit.cover,
                                                image: imageProvider),
                                            shape: BoxShape.circle),
                                      );
                                    },
                                    imageUrl: appStore.userProfile),
                              );
                            },
                          ),
                          Card(
                              child: IconButton(
                                  onPressed: () async {
                                    GlobalMethods.pushScreen(
                                        context: context,
                                        screen: ChangeProfileImageScreen(
                                            imageUrl: _userDetails!
                                                .data!.profileImage),
                                        screenIdentifier: ScreenIdentifier
                                            .ChangeProfileImageScreen);
                                    // launchScreen(
                                    //     context,
                                    //     ChangeProfileImageScreen(
                                    //         imageUrl: _userDetails!
                                    //             .data!.profileImage));
                                  },
                                  icon: Icon(Icons.edit)))
                        ],
                      ),
                      _getWaitingForApprovalView(key: "profile_image"),

                      height20,
                      Divider(),
                      // height10,
                      // AppTextField(
                      //   readOnly: true,

                      //   controller: _userNameController,
                      //   textFieldType: TextFieldType.EMAIL,

                      //   decoration: InputDecoration(
                      //     labelText: language.userName,
                      //     hintText: language.userName,
                      //   ),

                      //   // inputDecoration(context, label: language.email),
                      // ),
                      height20,
                      AppTextField(
                        readOnly: true,
                        controller: _emailController,
                        textFieldType: TextFieldType.EMAIL,
                        focus: _emailFocus,
                        nextFocus: _emailFocus,
                        decoration: InputDecoration(
                            labelText: language.email,
                            hintText: language.email,
                            suffixIcon: TextButton(
                                onPressed: () {
                                  GlobalMethods.pushScreen(
                                      context: context,
                                      screen: ChangeEmailScreen(
                                        email: _emailController.text,
                                      ),
                                      screenIdentifier:
                                          ScreenIdentifier.ChangeEmailScreen);
                                },
                                child: Icon(Icons.edit))),
                      ),
                      _getWaitingForApprovalView(key: "email"),
                      height10,

                      height10,
                      AppTextField(
                        readOnly: true,
                        focus: _firstNameFocus,
                        controller: _firstNameController,
                        textFieldType: TextFieldType.EMAIL,

                        decoration: InputDecoration(
                            labelText: language.firstName,
                            hintText: language.firstName,
                            suffixIcon: TextButton(
                                onPressed: () {
                                  GlobalMethods.pushScreen(
                                      context: context,
                                      screen: ChangeNameScreen(
                                        firstName: _firstNameController.text,
                                      ),
                                      screenIdentifier:
                                          ScreenIdentifier.ChangeNameScreen);
                                  // launchScreen(
                                  //     context,
                                  //     ChangeNameScreen(
                                  //       firstName: _firstNameController.text,
                                  //     ));
                                },
                                child: Icon(Icons.edit))),

                        // inputDecoration(context, label: language.email),
                      ),
                      _getWaitingForApprovalView(key: "first_name"),
                      height20,
                      AppTextField(
                        readOnly: true,

                        controller: _lastNameController,
                        textFieldType: TextFieldType.EMAIL,
                        focus: _lastNameFocus,
                        nextFocus: _emailFocus,
                        decoration: InputDecoration(
                            labelText: language.lastName,
                            hintText: language.lastName,
                            suffixIcon: TextButton(
                                onPressed: () {
                                  GlobalMethods.pushScreen(
                                      context: context,
                                      screen: ChangeNameScreen(
                                        lastName: _lastNameController.text,
                                      ),
                                      screenIdentifier:
                                          ScreenIdentifier.ChangeNameScreen);

                                  // launchScreen(

                                  //     context,
                                  //     ChangeNameScreen(
                                  //       lastName: _lastNameController.text,
                                  //     ));
                                },
                                child: Icon(Icons.edit))),

                        // inputDecoration(context, label: language.email),
                      ),
                      _getWaitingForApprovalView(key: "last_name"),
                      height20,
                      AppTextField(
                        readOnly: true,

                        controller: _phoneNumberController,
                        textFieldType: TextFieldType.PHONE,
                        focus: _mobileNumberFocus,
                        // nextFocus: _emailFocus,
                        decoration: InputDecoration(
                            labelText: language.phoneNumber,
                            hintText: language.phoneNumber,
                            suffixIcon: TextButton(
                                onPressed: () {
                                  GlobalMethods.pushScreen(
                                      context: context,
                                      screen: ChangePhoneNumberScreen(
                                          mobileNumber:
                                              _phoneNumberController.text),
                                      screenIdentifier: ScreenIdentifier
                                          .ChangePhoneNumberScreen);

                                  // launchScreen(
                                  //     context,
                                  //     ChangePhoneNumberScreen(
                                  //         mobileNumber:
                                  //             _phoneNumberController.text));
                                },
                                child: Icon(Icons.edit))),

                        // inputDecoration(context, label: language.email),
                      ),
                      _getWaitingForApprovalView(key: "contact_numbe"),

                      height20,
                      Divider(),
                      height20,
                      TextFormField(
                        focusNode: _provinceInputFocusNode,
                        decoration: InputDecoration(
                          label: Text("State"),
                          hintText: "Type to search...",
                          suffixIcon: Icon(
                            Icons.search,
                          ),
                        ),
                        controller: _provinceInputController,
                        validator: (value) {
                          if (_selectedProvince == null) {
                            return "Please select a valid state";
                          }
                          return null;
                        },
                        onChanged: (value) {
                          _filtereedProvinceData =
                              _getFilteredProvinceData(value);
                          if (_filtereedProvinceData.isEmpty) {
                            _filtereedProvinceData.add(
                              ProvinceModel(
                                id: -1,
                                regionId: -1,
                                regionName: "",
                                provinceName: "No state found",
                              ),
                            );
                          }
                          print(_filtereedProvinceData.length);
                          setState(() {
                            _selectedProvince = null;
                          });
                        },
                      ),
                      Card(
                        child: Container(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey.shade700
                              : Colors.grey.shade100,
                          height: _filtereedProvinceData.length > 0 ? 200 : 0,
                          child: ListView.separated(
                            // shrinkWrap: true,
                            // physics: NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              return ListTile(
                                title: Text(
                                  _filtereedProvinceData[index].provinceName,
                                  style: TextStyle(color: Colors.black),
                                ),
                                subtitle: Text(
                                    _filtereedProvinceData[index].regionName,
                                    style: TextStyle(color: Colors.black)),
                                onTap: () {
                                  if (_filtereedProvinceData[index].id == -1) {
                                    return;
                                  }
                                  _selectedProvince =
                                      _filtereedProvinceData[index];
                                  _provinceInputController.text =
                                      _selectedProvince!.provinceName;
                                  setState(() {
                                    _filtereedProvinceData = [];
                                  });
                                },
                              );
                            },
                            separatorBuilder: (context, index) =>
                                const Divider(),
                            itemCount: _filtereedProvinceData.length,
                          ),
                        ),
                      ),
                      height10,
                      AppButton(
                          // width: double.infinity,
                          text: "Update",
                          onPressed: () async  {
                            if (_selectedProvince == null) {
                              GlobalMethods.infoToast(
                                  context, "Please select a valid state");
                              return;
                            }

                            _updateRegionId(request: {
                              "type": "region",
                              "region_id": _selectedProvince!.regionId,
                              "province_id": _selectedProvince!.id,
                            });
                          }),
                      height20,
                      Divider(),
                      height20,

                      ValueListenableBuilder<String>(
                        valueListenable: _selected_gender,
                        builder: (context, value, child) {
                          if (_genderList_list.isEmpty ||
                              _selected_gender.value == -1) {
                            return SizedBox();
                          }
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("Gender", style: AppTextStyles.title()),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 2),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      width: 1,
                                      color: AppColors.primaryColor(context)
                                          .withOpacity(.6)),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String?>(
                                      isExpanded: true,
                                      value: value == -1
                                          ? _genderList_list[0]
                                          : value,
                                      items: _genderList_list.map((e) {
                                        return DropdownMenuItem<String>(
                                            value: e,
                                            child: Text(getTitleCase(e)));
                                      }).toList(),
                                      onChanged: (value) {
                                        _selected_gender.value = value!;
                                      }),
                                ),
                              ),
                              _selected_gender.value != "other"
                                  ? const SizedBox()
                                  : Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: AppTextField(
                                        textFieldType: TextFieldType.OTHER,
                                        label: "Please enter",
                                        initialValue: _otherGender,
                                        onChanged: (value) {
                                          _otherGender = value;
                                        },
                                      ),
                                    ),
                              height20,
                              AppButton(
                                  // width: double.infinity,
                                  text: "Update",
                                  onPressed: () async  {
                                    if (_selected_gender.value == "other") {

                                      if (_otherGender.trim().isEmpty) {
                                        GlobalMethods.infoToast(
                                            context, "Please enter gender");
                                        return;
                                      }
                                    }

                                    _updateGender(request: {
                                      "gender": _selected_gender.value,
                                      "other_gender_text": _selected_gender.value == "other" ? _otherGender : null,
                                    });
                                  }),
                            ],
                          );
                        },
                      )
                    ],
                  ),
                ),
              ));
        },
      ),
    );
  }

  List<ProvinceModel> _getFilteredProvinceData(String text) {
    return _provinceData
        .where((o) => o.provinceName.toLowerCase().contains(text.toLowerCase()))
        .toList();
  }

  Widget _getWaitingForApprovalView({
    required String key,
  }) {
    if (_userDetails == null || _userDetails!.data == null) {
      return const SizedBox();
    } else if (_userDetails!.data!.waitingForApproval == null) {
      return const SizedBox();
    }

    var data = _userDetails!.data!.waitingForApproval!
        .where(
          (o) => o.key.toLowerCase() == key.toLowerCase(),
        )
        .toList();
    if (data.isEmpty) {
      return const SizedBox();
    }
    return Text(
      data[0].message,
      style: TextStyle(fontStyle: FontStyle.italic, color: Colors.red),
    );
  }
}
