import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/edit_profile/repository/edit_profile_repository.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';

abstract class EditProfileState {}

class EditProfileLoadedState extends EditProfileState {}

class EditProfileInitialState extends EditProfileState {}

class EditProfileLoadingState extends EditProfileState {}

class EditProfileErrorState extends EditProfileState {
  final String message;
  EditProfileErrorState({required this.message});
}

class EditProfileUserLoadedState extends EditProfileState {
  final UserDetailModel userDetail;

  EditProfileUserLoadedState({required this.userDetail});
}

class EditProfileNameRequestSentState extends EditProfileState {
  final String message;

  EditProfileNameRequestSentState({required this.message});
}

class EmailVerificationLinkSent extends EditProfileState {
  EmailVerificationLinkSent();
}

class EditProfileEmailRequestSentState extends EditProfileState {
  final String message;
  EditProfileEmailRequestSentState({required this.message});
}

class EditProfileVerificationSentState extends EditProfileState {
  final StatusMessageModel response;

  EditProfileVerificationSentState({required this.response});
}

class EditProfileMobileNumberChangedState extends EditProfileState {
  final StatusMessageModel response;

  EditProfileMobileNumberChangedState({required this.response});
}

class RegionIdUpdatedState extends EditProfileState {
  RegionIdUpdatedState();
}

class GenderUpdatedState extends EditProfileState {
  GenderUpdatedState();
}

class EditProfileImageRequestSentState extends EditProfileState {
  final String message;

  EditProfileImageRequestSentState({required this.message});
}

class EditProfileRegionListLoadedState extends EditProfileState {
  final List<ProvinceModel> regionList;

  EditProfileRegionListLoadedState({required this.regionList});
}

class EditProfileProvinceListLoadedState extends EditProfileState {
  final List<ProvinceModel> provinceList;

  EditProfileProvinceListLoadedState({required this.provinceList});
}

class EditProfileCubit extends Cubit<EditProfileState> {
  EditProfileRepository _editProfileRepository = EditProfileRepository();
  EditProfileCubit() : super(EditProfileInitialState());

  getUserDetails({required int userId}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.getUserDetail(userId: userId).then((value) {
      emit(EditProfileUserLoadedState(userDetail: value));
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  getRegionList({required int userId}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.getRegionListAPi().then((value) {
      if (value.data != null) {
        emit(EditProfileRegionListLoadedState(regionList: value.data!));
      } else {
        emit(EditProfileErrorState(message: "Server error"));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  getprovinceList() async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.getProvinceListAPi().then((value) {
      if (value.status && value.data != null) {
        emit(EditProfileProvinceListLoadedState(provinceList: value.data!));
      } else {
        emit(EditProfileErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  updateRegionProvinceId({required Map<String, dynamic> request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .updateDriverProfileApi(request: request)
        .then((value) {
      if (value.status) {
        emit(RegionIdUpdatedState());
      } else {
        emit(EditProfileErrorState(message: value.message));
      }
    }).onError((e, _) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  updateGender({required Map<String, dynamic> request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .updateGenderApi(request: request)
        .then((value) {
      if (value.status) {
        emit(GenderUpdatedState());
      } else {
        emit(EditProfileErrorState(message: value.message));
      }
    }).onError((e, _) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  editName({required Map request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .updateDriverProfileApi(request: request)
        .then((value) {
      if (value.status) {
        emit(
            EditProfileNameRequestSentState(message: value.message.toString()));
      } else {
        emit(EditProfileErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  verifyEmail() async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.verifyEmailApi().then((value) {
      if (value.status) {
        emit(EmailVerificationLinkSent());
      } else {
        emit(EditProfileErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  Future<void> editEmail({
    required String email,
    required String otp,
  }) async {
    emit(EditProfileLoadingState());

    var request = {
      "email2": email,
      "otp": otp,
      "user_type": "driver",
    };
    await _editProfileRepository.changeEmailApi(request: request).then((value) {
      if (value.status) {
        emit(EditProfileEmailRequestSentState(
            message: value.message.toString()));
      } else {
        emit(EditProfileErrorState(message: value.message));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  sendVerificationCode({required Map request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .sendVerificationCodeApi(request: request)
        .then((value) {
      emit(EditProfileVerificationSentState(response: value));
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  changeMobileNumber({required Map request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .changeMobileNumberApi(request: request)
        .then((value) {
      emit(EditProfileMobileNumberChangedState(response: value));
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(message: "Server error"));
    });
  }

  updateProfileImage({required File file}) async {
    emit(EditProfileLoadingState());

    _editProfileRepository.changeProfileImage(file: file).then((value) {
      if (value.status) {
        emit(EditProfileImageRequestSentState(message: value.message));
      } else {
        emit(EditProfileErrorState(message: value.message));
      }
    }).onError((e, _) {
      emit(EditProfileErrorState(message: e.toString()));
    });

    // MultipartRequest multiPartRequest =
    //     await getMultiPartRequest('update-driver-profile');

    // multiPartRequest.files
    //     .add(await MultipartFile.fromPath('profile_image', file.path));
    // multiPartRequest.fields['type'] = 'profile_picture';
    // await send(
    //   multiPartRequest,
    // ).then((value) {
    //   if (value != null) {
    //     if (value.status) {
    //       emit(EditProfileImageRequestSentState(
    //           message: value.message.toString()));
    //     } else {
    //       emit(EditProfileErrorState(message: value.message));
    //     }
    //   } else {
    //     emit(EditProfileErrorState());
    //   }
    // }).onError((e, _) {
    //   emit(EditProfileErrorState(message: e.toString()));
    // });
  }
}
