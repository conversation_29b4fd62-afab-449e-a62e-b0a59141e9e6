import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart';
import 'package:rooo_driver/features/edit_profile/model/region_response_model.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/global/models/UserDetailModel.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';
import 'package:rooo_driver/network/RestApis.dart';

class EditProfileRepository {
  Future<UserDetailModel> getUserDetail({required int? userId}) async {
    return UserDetailModel.fromJson(await handleResponse(
        await buildHttpResponse('user-detail?id=$userId',
            method: HttpMethod.GET)));
  }

  Future<RegionResponseModel> getRegionListAPi() async {
  return RegionResponseModel.fromJson(
      await handleResponse(await buildHttpResponse(
    'region-list',
    method: HttpMethod.GET,
  )));
}



Future<ProvinceModelResponse> getProvinceListAPi() async {
  return ProvinceModelResponse.fromJson(
      await handleResponse(await buildHttpResponse(
    'province-list',
    method: HttpMethod.GET,
  )));
}

  Future<StatusMessageModel> updateDriverProfileApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-driver-profile',
            method: HttpMethod.POST, request: request)));
  }


  Future<StatusMessageModel> updateGenderApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-user-status',
            method: HttpMethod.POST, request: request)));
  }

  Future<StatusMessageModel> verifyEmailApi(
      ) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('sent-verification-email',
            method: HttpMethod.GET, )));
  }

  Future<StatusMessageModel> changeEmailApi({required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-email',
            method: HttpMethod.POST, request: request)));
  }

  Future<StatusMessageModel> sendVerificationCodeApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-contact-number',
            method: HttpMethod.POST, request: request)));
  }

  Future<StatusMessageModel> changeMobileNumberApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('validate-otp-contact2',
            method: HttpMethod.POST, request: request)));
  }

  Future<StatusMessageModel> changeProfileImage({
    required File file,
  }) async {
    MultipartRequest multiPartRequest =
        await getMultiPartRequest('update-driver-profile');

    multiPartRequest.files
        .add(await MultipartFile.fromPath('profile_image', file.path));
    multiPartRequest.fields['type'] = 'profile_picture';
  return      StatusMessageModel.fromJson(jsonDecode(await sendMultiPartRequest(multiPartRequest))   );

  }


  Future<StatusMessageModel> updateRegionIdProvinceIdApi(
    {required Map request}) async {
  return StatusMessageModel.fromJson(await handleResponse(
      await buildHttpResponse('update-driver-profile',
          method: HttpMethod.POST, request: request)));
}

}
