import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/features/care/models/care_details_response_model.dart';
import 'package:rooo_driver/features/care/models/care_return_message_response.dart';
import 'package:rooo_driver/features/care/models/carelist_response_model.dart';
import 'package:rooo_driver/features/care/repository/care_repository.dart';

abstract class CareState {}

class CareInitialState extends CareState {}

class CareLoadingState extends CareState {}

class CareLoadedState extends CareState {
  final CareResponseModel careResponseModel;

  CareLoadedState({required this.careResponseModel});
}

class CareAddedState extends CareState {
  final String message;

  CareAddedState({required this.message});
}

class CareMessageSendingState extends CareState {}

class CareDetailLoadedState extends CareState {
  final CareDetailsResponseModel CareDetailResponseModel;

  CareDetailLoadedState({required this.CareDetailResponseModel});
}

class CareMessageSentState extends CareState {
  final CareReturnMessageResponse careResponseMessage;

  CareMessageSentState({required this.careResponseMessage});
}

class CareErrorState extends CareState {
  final String message;

  CareErrorState({required this.message});
}

class CareCubit extends Cubit<CareState> {
  CareRepository _careRepository = CareRepository();
  CareCubit() : super(CareInitialState());

  getCarePendingList({required currentPage}) async {
    emit(CareLoadingState());
    await _careRepository
        .getRoooCarePendingListApi(page: currentPage)
        .then((value) {
      emit(CareLoadedState(careResponseModel: value));
    });
  }

  getCareClosedList({required page}) async {
    emit(CareLoadingState());
    await _careRepository.getRoooCareCompletedListApi(page: page).then((value) {
      emit(CareLoadedState(careResponseModel: value));
    }).onError((error, stackTrace) {
      emit(CareErrorState(message: "Server error"));
    });
  }

  AddnewCare({required Map request}) async {
    emit(CareLoadingState());
    await _careRepository.addCareApi(req: request).then((value) {
      if (value.status) {
        emit(CareAddedState(message: value.message.toString()));
      } else {
        emit(CareErrorState(
          message: value.message,
        ));
      }
    });
  }

  getCareDetail({required Map request}) async {
    emit(CareLoadingState());
    await _careRepository.getCareDetailApi(request: request).then((value) {
      emit(CareDetailLoadedState(CareDetailResponseModel: value));
    }).onError((error, stackTrace) {
      emit(CareLoadingState());
    });
  }

  sendCareMessage({required Map request}) async {
    emit(CareMessageSendingState());
    await _careRepository.sendCareMessageApi(request: request).then((value) {
      if (value.status == true) {
        emit(CareMessageSentState(careResponseMessage: value));
      } else {
        emit(CareErrorState(message: value.message ?? "Server error"));
      }
    });
  }
}
