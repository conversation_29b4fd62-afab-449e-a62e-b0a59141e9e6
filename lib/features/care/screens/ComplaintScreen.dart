import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/ComplaintModel.dart';
import 'package:rooo_driver/model/DriverRatting.dart';
import 'package:rooo_driver/model/RiderModel.dart';

class ComplaintScreen extends StatefulWidget {
  final DriverRatting driverRatting;
  final RiderModel? riderModel;
  final ComplaintModel? complaintModel;
  final bool isFromHistory;

  const ComplaintScreen({
    super.key,
    required this.driverRatting,
    required this.isFromHistory,
    this.complaintModel,
    this.riderModel,
  });

  @override
  ComplaintScreenState createState() => ComplaintScreenState();
}

class ComplaintScreenState extends State<ComplaintScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController subController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  bool _isLoadigData = false;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    if (widget.complaintModel != null) {}
  }

  Future<void> saveComplainDriver() async {
    if (formKey.currentState!.validate()) {
      setState(() {
        _isLoadigData = true;
      });
      Map req = {
        "driver_id": widget.riderModel!.driverId,
        "rider_id": widget.riderModel!.riderId,
        "ride_request_id": widget.riderModel!.id,
        "complaint_by": "driver",
        "subject": subController.text.trim(),
        "description": descriptionController.text.trim(),
        "status": "pending",
      };
      await saveComplain(request: req).then((value) {
        setState(() {
          _isLoadigData = false;
        });

        if (value.status) {
          GlobalMethods.succesToast(context, value.message);
          Navigator.pop(context);
          if (widget.isFromHistory) {
            Navigator.pop(context);
          }
        } else {
          GlobalMethods.errorToast(context, value.message);
        }
      }).onError((error, stackTrace) {
        setState(() {
          _isLoadigData = false;
        });
        GlobalMethods.errorToast(context, "Something went wrong");
      });
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: Scaffold(
        appBar: RoooAppbar(
          title: "Complaint",
          isDarkOverlay: false,
        ),
        body: Stack(
          children: [
            Form(
              key: formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: MediaQuery.sizeOf(context).width,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          (widget.riderModel?.driverName ?? "").isEmpty
                              ? const SizedBox()
                              : Padding(
                                  padding: const EdgeInsets.only(bottom: 20.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(35),
                                        child: commonCachedNetworkImage(
                                            widget
                                                .riderModel!.driverProfileImage,
                                            height: 70,
                                            width: 70,
                                            fit: BoxFit.cover),
                                      ),
                                      const SizedBox(width: 16),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const SizedBox(height: 8),
                                          Text(
                                              widget.riderModel!.driverName
                                                  .validate(),
                                              style: boldTextStyle()),
                                          const SizedBox(height: 8),
                                          if (widget.driverRatting.rating !=
                                              null)
                                            RatingBar.builder(
                                              direction: Axis.horizontal,
                                              glow: false,
                                              allowHalfRating: false,
                                              ignoreGestures: true,
                                              wrapAlignment:
                                                  WrapAlignment.spaceBetween,
                                              itemCount: 5,
                                              itemSize: 20,
                                              initialRating: double.parse(widget
                                                  .driverRatting.rating
                                                  .toString()),
                                              itemPadding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 0),
                                              itemBuilder: (context, _) =>
                                                  const Icon(Icons.star,
                                                      color: Colors.amber),
                                              onRatingUpdate: (rating) {
                                                //
                                              },
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                        ],
                      ),
                    ),
                    TextFormField(
                      maxLength: 100,
                      validator: (value) {
                        if ((value ?? "").trim().isEmpty) {
                          return "Please enter subject";
                        }
                        return null;
                      },
                      controller: subController,
                      decoration: const InputDecoration(
                          labelText: "Please enter subject"),
                      // readOnly: widget.complaintModel != null ? true : false,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: descriptionController,
                      validator: (value) {
                        if ((value ?? "").trim().isEmpty) {
                          return "Please enter description";
                        }
                        return null;
                      },
                      // readOnly: widget.complaintModel != null ? true : false,
                      decoration: const InputDecoration(
                          labelText: "Please enter message"),
                      minLines: 2,
                      maxLines: 5,
                    ),
                    const SizedBox(height: 16),
                    // if (widget.complaintModel == null)
                    AppButton(
                      text: "Save",
                      width: MediaQuery.sizeOf(context).width,
                      onPressed: () async {
                        saveComplainDriver();
                      },
                    ),
                  ],
                ),
              ),
            ),
            _isLoadigData ? const AppLoader() : const SizedBox(),
          ],
        ),
      ),
    );
  }
}
