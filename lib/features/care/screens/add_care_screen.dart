import 'package:flutter/gestures.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/bottom_button.dart';
import 'package:rooo_driver/global/widgets/screen_body.dart';
import 'package:rooo_driver/utils/Extensions/app_textfield.dart';
import 'package:url_launcher/url_launcher.dart';

class AddCareScreen extends StatefulWidget {
  final int? rideId;
  const AddCareScreen({
    super.key,
    this.rideId,
  });

  @override
  State<AddCareScreen> createState() => _AddCareScreenState();
}

class _AddCareScreenState extends State<AddCareScreen> {
  addCare() {
    Map<String, dynamic> request = {
      "subject": title_controller.text,
      "message": message_controller.text,
      "ride_id": widget.rideId,
    };
    BlocProvider.of<CareCubit>(context).AddnewCare(request: request);
  }

  final title_controller = TextEditingController();
  final message_controller = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomButton(
          text: "Send",
          onPressed: () {
            if (title_controller.text.isEmpty) {
              GlobalMethods.errorToast(context, "Please enter valid title",seconds: 4);
            } else if (message_controller.text.isEmpty) {
              GlobalMethods.errorToast(context, "Please enter valid message",seconds: 4);
            } else {
              addCare();
            }
          },
          notVisible: false),
      appBar: RoooAppbar(title: "Add Request"),
      body: BlocConsumer<CareCubit, CareState>(
        builder: (context, state) {
          return ScreenBody(
            isLoading: state is CareLoadingState,
            isEmpty: false,
            emptyMessage: "",
            child: Column(
              children: [
                Padding(
                  padding: screenPadding,
                  child: Column(
                    children: [
                      AppTextField(
                        textFieldType: TextFieldType.OTHER,
                        controller: title_controller,
                        maxLength: 100,
                        decoration: InputDecoration(
                          hintText: language.pleaseEnterSubject,
                        ),
                      ),
                      height10,
                      AppTextField(
                          minLines: 4,
                          textFieldType: TextFieldType.OTHER,
                          controller: message_controller,
                          maxLines: 6,
                          decoration:
                              InputDecoration(hintText: language.messageTxt)),
                      // height20,
                      // RichText(
                      //   text: TextSpan(
                      //       text: language.careInfo,
                      //       style: TextStyle(
                      //         color: AppColors.blackColor(context),
                      //       ),
                      //       children: [
                      //         TextSpan(
                      //           text: ' ' +
                      //               (GlobalState.appSettingModel?.settingModel
                      //                           ?.contactEmail ??
                      //                       "")
                      //                   .toString(),
                      //           recognizer: TapGestureRecognizer()
                      //             ..onTap = () {
                      //               launchUrl(
                      //                   Uri.parse('mailto:' +
                      //                       (GlobalState
                      //                                   .appSettingModel
                      //                                   ?.settingModel!
                      //                                   .contactEmail ??
                      //                               "")
                      //                           .toString()),
                      //                   mode: LaunchMode.externalApplication);
                      //             },
                      //           style: TextStyle(color: Colors.blue),
                      //         )
                      //       ]),
                      // ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
        listener: (context, state) {
          if (state is CareErrorState) {
            GlobalMethods.errorToast(
               context,
               "Error loading data",
              );
          }
          if (state is CareAddedState) {
            GlobalMethods.succesToast(context, state.message);
            Navigator.pop(context, true);
          }
        },
      ),
    );
  }
}
