import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:rooo_driver/features/care/screens/RideHelpdetailsScreen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/widgets/empty_widget.dart';

class CreateRideHelpTabScreen extends StatefulWidget {
  final String? status;
  final Function() indicatorUpdater;

  CreateRideHelpTabScreen({
    this.status,
    required this.indicatorUpdater,
  });

  @override
  CreateCareTabScreenState createState() => CreateCareTabScreenState();
}

class CreateCareTabScreenState extends State<CreateRideHelpTabScreen>
    with AutomaticKeepAliveClientMixin {
  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;
  List<RideHelpData> helpData = [];
  String emptyDataMsg = '';
  bool _isLoadingData = true;

  @override
  void initState() {
    super.initState();
    init();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          currentPage++;
          init();
        }
      }
    });
  }

  Future<void> init() async {
    setState(() {
      _isLoadingData = true;
    });
    late Future<RideHelp?> task;
    if (widget.status == "pending") {
      task = getPendingHelps(
        page: currentPage,
      );
    } else {
      task = getCompletedHelps(
        page: currentPage,
      );
    }
    await task.then((value) {
      if (value != null) {
        if (widget.status == "pending") {
          widget.indicatorUpdater();
        }

        setState(() {
          _isLoadingData = false;
        });
        currentPage = value.pagination.currentPage!;
        totalPage = value.pagination.totalPages!;
        emptyDataMsg = value.message ?? '';
        if (currentPage == 1) {
          helpData.clear();
        }
        setState(() {
          helpData.addAll(value.data);
        });
      } else {
        GlobalMethods.errorToast(context, "Something went wrong");
      }
    }).onError((error, stackTrace) {
      setState(() {
        _isLoadingData = false;
      });

      GlobalMethods.errorToast(context, "Something went wrong");
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        AnimationLimiter(
          child: RefreshIndicator(
            onRefresh: () {
              currentPage = 1;
              return init();
            },
            child: RefreshIndicator(
              onRefresh: () {
                currentPage = 1;
                return init();
              },
              child: ListView.builder(
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: helpData.length,
                  controller: scrollController,
                  padding: const EdgeInsets.only(
                      top: 8, bottom: 8, left: 16, right: 16),
                  itemBuilder: (_, index) {
                    RideHelpData data = helpData[index];
                    return AnimationConfiguration.staggeredList(
                      delay: const Duration(milliseconds: 200),
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        child: IntrinsicHeight(
                          child: inkWellWidget(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => RideHelpDetailsScreen(
                                    helpData: data,
                                    isClosed: widget.status == "closed",
                                    cardStateUpdater: () {
                                      setState(() {
                                        data.help_count = 0;
                                      });
                                    },
                                  ),
                                ),
                              );
                            },
                            child: Container(
                              margin: const EdgeInsets.only(top: 8, bottom: 8),
                              decoration: BoxDecoration(
                                // color: Colors.white,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.4),
                                    blurRadius: 10,
                                    spreadRadius: 0,
                                    offset: const Offset(0.0, 0.0),
                                  ),
                                ],
                              ),
                              child: Card(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(4),
                                          topRight: Radius.circular(4),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Issue #${data.id}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              color: Colors.white,
                                            ),
                                          ),
                                          Text(
                                            data.created_at != null
                                                ? DateTime.parse(
                                                        data.created_at!)
                                                    .toLocal()
                                                    .toString()
                                                    .substring(0, 16)
                                                    .replaceAll('T', ' ')
                                                : '',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(12),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            data.subject ?? '',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            data.description ?? '',
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.grey[800]
                                            : Colors.grey[100],
                                        borderRadius: BorderRadius.only(
                                          bottomLeft: Radius.circular(4),
                                          bottomRight: Radius.circular(4),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Status: ' + data.status.toUpperCase(),
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? Colors.grey[300]
                                                  : Colors.grey[600],
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                          if (data.help_count != null &&
                                              data.help_count! > 0)
                                            Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 8, vertical: 2),
                                              decoration: BoxDecoration(
                                                color: Colors.red,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Text(
                                                data.help_count.toString(),
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
            ),
          ),
        ),
        Visibility(
          visible: _isLoadingData,
          child: loaderWidget(),
        ),
        if (helpData.isEmpty && !_isLoadingData)
          RooEmptyWidegt(title: emptyDataMsg),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class RideHelp {
  PaginationModel pagination;
  List<RideHelpData> data;
  String? message;
  RideHelp({required this.pagination, required this.data, this.message});

  factory RideHelp.fromJson(Map<String, dynamic> json) {
    List<RideHelpData> data = [];
    for (var i = 0; i < json['data'].length; i++) {
      data.add(
        RideHelpData.fromJson(
          json['data'][i],
        ),
      );
    }
    return RideHelp(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: data,
        message: json['message']);
  }
}

class RideHelpData {
  int id;
  int ride_request_id;
  int help_count;
  String subject;
  String status;
  String description;
  String booking_date;
  String booking_from;
  String booking_to;
  String created_at;
  RideHelpData({
    required this.id,
    required this.booking_date,
    required this.booking_from,
    required this.booking_to,
    required this.created_at,
    required this.help_count,
    required this.ride_request_id,
    required this.subject,
    required this.description,
    required this.status,
  });

  factory RideHelpData.fromJson(Map<String, dynamic> json) {
    return RideHelpData(
      id: json['id'] as int,
      booking_date: json['booking_date'] as String,
      booking_from: json['booking_from'] as String,
      booking_to: json['booking_to'] as String,
      created_at: json['created_at'] as String,
      help_count: json['help_count'] as int,
      ride_request_id: json['ride_request_id'] as int,
      subject: json['subject'] as String,
      description: json['description'] as String,
      status: json['status'] as String,
    );
  }
}

class RideHelpComment {
  final int id;
  final int careId;
  final int userId;
  final String addedBy;
  final String? status;
  final String comment;
  final DateTime createdAt;
  final DateTime? updatedAt;

  RideHelpComment({
    required this.id,
    required this.careId,
    required this.userId,
    required this.addedBy,
    required this.status,
    required this.comment,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RideHelpComment.fromMap(Map<String, dynamic> map) {
    return RideHelpComment(
      id: map['id'] as int,
      careId: map['complaint_id'] as int,
      userId: map['user_id'] as int,
      addedBy: map['added_by'] as String,
      status: map['status'] as String?,
      comment: map['comment'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] == null
          ? null
          : DateTime.parse(map['updated_at'] as String),
    );
  }
}

class RideHelpDetails {
  PaginationModel pagination;
  List<RideHelpComment> data;
  String? message;
  RideHelpDetails({required this.pagination, required this.data, this.message});

  factory RideHelpDetails.fromJson(Map<String, dynamic> json) {
    List<RideHelpComment> data = [];
    for (var i = 0; i < json['data']['helpComment'].length; i++) {
      data.add(
        RideHelpComment.fromMap(
          json['data']['helpComment'][i],
        ),
      );
    }
    return RideHelpDetails(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: data,
        message: json['message']);
  }
}

class HelpCommentRequest {
  final int userId;
  final String addedBy;
  final String complaintId;
  final String comment;

  HelpCommentRequest({
    required this.userId,
    required this.addedBy,
    required this.complaintId,
    required this.comment,
  });

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'added_by': addedBy,
      'complaint_id': complaintId,
      'comment': comment,
    };
  }
}

class HelpCommentResponse {
  final bool status;
  final String message;
  final RideHelpComment? data;

  HelpCommentResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory HelpCommentResponse.fromMap(Map<String, dynamic> map) {
    return HelpCommentResponse(
      status: map["status"] as bool,
      message: map["message"] as String,
      data: map["data"] == null ? null : RideHelpComment.fromMap(map["data"]),
    );
  }
}
