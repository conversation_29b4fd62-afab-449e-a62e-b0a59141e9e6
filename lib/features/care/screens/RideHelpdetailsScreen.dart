import 'package:intl/intl.dart';
import 'package:rooo_driver/features/care/screens/CreateRideHelpTabScreen.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class RideHelpDetailsScreen extends StatefulWidget {
  final RideHelpData helpData;
  final bool isClosed;
  final Function() cardStateUpdater;

  const RideHelpDetailsScreen({
    super.key,
    required this.helpData,
    required this.cardStateUpdater,
    this.isClosed = false,
  });

  @override
  State<RideHelpDetailsScreen> createState() => _CareDetailsScreenState();
}

class _CareDetailsScreenState extends State<RideHelpDetailsScreen> {
  List<RideHelpComment> _messages = [];

  TextEditingController _messageCont = TextEditingController();

  FocusNode _focusNode = FocusNode();

  late int userId;

  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;
  bool _isLoadingData = true;

  @override
  void initState() {
    userId = sharedPref.getInt(USER_ID)!;

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          currentPage++;

          _loadData();
        }
      }
    });

    _loadData();

    super.initState();
  }

  @override
  void dispose() {
    setState(() {
      _isLoadingData = false;
    });
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoadingData = true;
    });

    var result = await getRideHelpDetailsData(
        helpId: widget.helpData.id, page: currentPage);
    if (result == null) {
      GlobalMethods.errorToast(context, "Something went wrong");
    } else {
      try {
        totalPage = result.pagination.totalPages ?? 1;

        if (currentPage == 1) {
          _messages.clear();
        }
        _messages.addAll(result.data);

        widget.cardStateUpdater();
        setState(() {
          _isLoadingData = false;
        });
      } catch (e) {
        print("object");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: Scaffold(
        appBar: RoooAppbar(title: widget.helpData.subject),
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                bottom: widget.isClosed
                    ? 0
                    : Platform.isIOS
                        ? 90
                        : 70,
              ),
              child: ListView.builder(
                controller: scrollController,
                reverse: true,
                padding: const EdgeInsets.all(
                  10,
                ),
                itemBuilder: (context, index) {
                  return RideHelpChatWidget(data: _messages[index]);
                },
                itemCount: _messages.length,
              ),
            ),
            widget.isClosed
                ? const SizedBox()
                : Positioned(
                    bottom: Platform.isIOS ? 28 : 7,
                    left: 0,
                    right: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: radius(),
                          color: Theme.of(context).cardColor,
                          boxShadow: [
                            const BoxShadow(
                              spreadRadius: 0.5,
                              blurRadius: 0.5,
                            ),
                          ],
                        ),
                        padding: const EdgeInsets.only(left: 8, right: 8),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                focusNode: _focusNode,
                                controller: _messageCont,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "Message",
                                  hintStyle: secondaryTextStyle(),
                                  contentPadding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                ),
                                textCapitalization:
                                    TextCapitalization.sentences,
                                keyboardType: TextInputType.multiline,
                                minLines: 1,
                                style: primaryTextStyle(),
                                textInputAction: TextInputAction.newline,
                                onSubmitted: (s) {
                                  sendMessage();
                                },
                                cursorHeight: 20,
                                maxLines: 5,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(
                                Icons.send,
                              ),
                              onPressed: () {
                                sendMessage();
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
            Visibility(
              visible: _isLoadingData,
              child: loaderWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> sendMessage() async {
    if (_messageCont.text.trim().isNotEmpty) {
      hideKeyboard(context);
      setState(() {
        _isLoadingData = true;
      });
      var result = await saveRideHelpMessage(
        HelpCommentRequest(
          userId: userId,
          addedBy: "driver",
          complaintId: widget.helpData.id.toString(),
          comment: _messageCont.text.trim(),
        ),
      );

      if (result == null || result.status == false) {
        GlobalMethods.errorToast(context, "Something went wrong");
      } else {
        _messageCont.text = "";
        _messages.insert(0, result.data!);
      }
      setState(() {
        _isLoadingData = false;
      });
    }
  }
}

class RideHelpChatWidget extends StatefulWidget {
  final RideHelpComment data;

  RideHelpChatWidget({required this.data});

  @override
  _RideHelpChatWidgetState createState() => _RideHelpChatWidgetState();
}

class _RideHelpChatWidgetState extends State<RideHelpChatWidget> {
  String? images;

  void initState() {
    super.initState();
    init();
  }

  init() async {}

  @override
  Widget build(BuildContext context) {
    String time;

    DateTime date = DateTime.fromMillisecondsSinceEpoch(
        widget.data.createdAt.millisecondsSinceEpoch);
    if (date.day == DateTime.now().day) {
      time = DateFormat('hh:mm a').format(DateTime.fromMillisecondsSinceEpoch(
          widget.data.createdAt.millisecondsSinceEpoch));
    } else {
      time = DateFormat('dd-mm-yyyy hh:mm a').format(
          DateTime.fromMillisecondsSinceEpoch(
              widget.data.createdAt.millisecondsSinceEpoch));
    }

    Widget chatItem(String? messageTypes) {
      switch (messageTypes) {
        case "text":
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: widget.data.addedBy == "driver"
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
            children: [
              Text(widget.data.comment,
                  style: TextStyle(
                    color: Colors.black,
                  ),
                  maxLines: null),
              const SizedBox(height: 8),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    time,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          );

        default:
          return Container();
      }
    }

    return GestureDetector(
      onLongPress: widget.data.addedBy == "driver" ? null : () async {},
      child: Container(
        margin: const EdgeInsets.only(top: 2, bottom: 2),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: widget.data.addedBy == "driver"
              ? CrossAxisAlignment.end
              : CrossAxisAlignment.start,
          mainAxisAlignment: widget.data.addedBy == "driver"
              ? MainAxisAlignment.end
              : MainAxisAlignment.start,
          children: [
            Container(
              margin: widget.data.addedBy == "driver"
                  ? EdgeInsets.only(
                      top: 0.0,
                      bottom: 0.0,
                      left: isRTL ? 0 : MediaQuery.sizeOf(context).width * 0.25,
                      right: 8)
                  : EdgeInsets.only(
                      top: 2.0,
                      bottom: 2.0,
                      left: 8,
                      right:
                          isRTL ? 0 : MediaQuery.sizeOf(context).width * 0.25),
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                boxShadow: Theme.of(context).brightness == Brightness.dark
                    ? null
                    : defaultBoxShadow(),
                color: Colors.grey.shade200,

                // widget.data.addedBy == "driver"
                //     ? Colors.grey.shade200
                //     : Globals.isDarkModeOn
                //         ? Colors.grey
                //         : Colors.grey[300],
                borderRadius: widget.data.addedBy == "driver"
                    ? BorderRadius.only(
                        bottomLeft: radiusCircular(12),
                        topLeft: radiusCircular(12),
                        bottomRight: radiusCircular(12),
                        topRight: radiusCircular(12))
                    : BorderRadius.only(
                        bottomLeft: radiusCircular(0),
                        topLeft: radiusCircular(12),
                        bottomRight: radiusCircular(12),
                        topRight: radiusCircular(12)),
              ),
              child: chatItem("text"),
            ),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }

  Color _getTimeColor(String addedBy) {
    return (addedBy == "driver") ? Colors.white : Colors.black;
  }
}
