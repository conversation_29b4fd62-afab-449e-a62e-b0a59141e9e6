import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rooo_driver/global/constants/constants.dart';
import 'package:rooo_driver/global/globalMethods/global_method.dart';
import 'package:rooo_driver/global/widgets/ios_padding.dart';
import 'package:rooo_driver/components/rooo_appbar.dart';
import 'package:rooo_driver/features/care/cubit/care_cubit.dart';
import 'package:rooo_driver/features/care/models/care_details_response_model.dart';
import 'package:rooo_driver/features/care/models/send_message_model.dart';
import 'package:rooo_driver/features/care/widgets/care_chat_item_widget.dart';
import 'package:rooo_driver/global/widgets/empty_widget.dart';
import 'package:rooo_driver/global/widgets/app_loader.dart';
import 'package:rooo_driver/main.dart';
import 'package:rooo_driver/utils/Common.dart';
import 'package:rooo_driver/utils/Constants.dart';

class NewCareDetailsScreen extends StatefulWidget {
  final int id;
  final bool isClosed;

  const NewCareDetailsScreen({
    super.key,
    required this.id,
    required this.isClosed,
  });

  @override
  State<NewCareDetailsScreen> createState() => _NewCareDetailsScreenState();
}

class _NewCareDetailsScreenState extends State<NewCareDetailsScreen> {
  ScrollController _controller = ScrollController();
  List<CareComment> carecomments = [];
  String apiMessage = "";
  final messageController = TextEditingController();
  final messageFocus = FocusNode();

  String title = "Fetching details...";
  @override
  void initState() {
    super.initState();
    getCareDetail();
  }

  getCareDetail() {
    BlocProvider.of<CareCubit>(context)
        .getCareDetail(request: {"care_id": widget.id});
  }

  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: title),
      body: BlocConsumer<CareCubit, CareState>(
        listener: (context, state) {
          if (state is CareDetailLoadedState) {
            carecomments =
                state.CareDetailResponseModel.data?.carecomment ?? [];

            apiMessage = state.CareDetailResponseModel.data!.message.toString();
            setState(() {
              title =
                  state.CareDetailResponseModel.data?.subject ?? "Care Details";
            });
          }
          if (state is CareMessageSentState) {
            carecomments.add(state.careResponseMessage.careComment!);

            _scrollDown();
            messageController.clear();
          }
        },
        builder: (context, state) {
          if (state is CareLoadingState) {
            return AppLoader();
          } else {
            if (carecomments.isNotEmpty) {
              return Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height,
                    child: Padding(
                      padding: !widget.isClosed
                          ? EdgeInsets.only(
                              bottom: Platform.isIOS
                                  ? screenPaddingValue * 6
                                  : screenPaddingValue * 4)
                          : EdgeInsets.only(
                              bottom: Platform.isIOS
                                  ? screenPaddingValue * 2
                                  : screenPaddingValue),
                      child: SingleChildScrollView(
                        reverse: true,
                        controller: _controller,
                        child: Column(
                          children: [
                            ListView.builder(
                              padding: screenPadding,
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: carecomments.length,
                              itemBuilder: (context, index) {
                                CareComment data = carecomments[index];

                                return CareChatItemWidget(data: data);
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  !widget.isClosed ? ChatInputBox() : SizedBox()
                ],
              );
            } else {
              return RooEmptyWidegt(title: "Empty ");
            }
          }
        },
      ),
    );
  }

  ChatInputBox() {
    return IosPadding(
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: messageController,
              // decoration: InputDecoration(
              //   border: InputBorder.none,
              //   hintText: language.writeMessage,
              //   hintStyle: secondaryTextStyle(),
              //   contentPadding: EdgeInsets.symmetric(horizontal: 8),
              // ),
              focusNode: messageFocus,
              textCapitalization: TextCapitalization.sentences,
              keyboardType: TextInputType.multiline,
              minLines: 1,
              textInputAction:
                  mIsEnterKey ? TextInputAction.send : TextInputAction.newline,
              onSubmitted: (s) {
                sendMessage();
              },
              maxLines: 5,
            ),
          ),
          width20,
          inkWellWidget(
            child: BlocBuilder<CareCubit, CareState>(
              builder: (context, state) {
                if (state is CareMessageSendingState) {
                  return SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                      ));
                } else {
                  return Icon(Icons.send, size: 25);
                }
              },
            ),
            onTap: () {
              sendMessage();
            },
          )
        ],
      ),
    );
  }

  sendMessage() {
    SendMessageModel message = SendMessageModel(
        careId: widget.id.toString(),
        addedBy: "driver",
        comment: messageController.text,
        userId: sharedPref.getInt(USER_ID));
    if (messageController.text.isEmpty) {
      GlobalMethods.infoToast(context, "Type something");
    } else {
      BlocProvider.of<CareCubit>(context)
          .sendCareMessage(request: message.toJson());
    }
  }

  void _scrollDown() {
    _controller.animateTo(
      _controller.position.minScrollExtent - 100,
      duration: Duration(milliseconds: 500),
      curve: Curves.fastOutSlowIn,
    );
  }
}
