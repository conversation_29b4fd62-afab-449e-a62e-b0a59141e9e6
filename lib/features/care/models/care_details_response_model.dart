
class CareDetailsResponseModel {

  String? message;
  CareDetailData? data;

  CareDetailsResponseModel({this.message, this.data,});

  CareDetailsResponseModel.fromJson(Map<String, dynamic> json) {
     
    message = json['message'];
    data = json['data'] != null ? new CareDetailData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;

    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class CareDetailData {
  int? id;
  int? driverId;
  String? subject;
  String? message;
  int? status;
  String? createdBy;
  String? createdAt;
  String? updatedAt;
  Null deletedAt;
  List<CareComment>? carecomment;

  CareDetailData(
      {this.id,
      this.driverId,
      this.subject,
      this.message,
      this.status,
      this.createdBy,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.carecomment});

  CareDetailData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    driverId = json['driver_id'];
    subject = json['subject'];
    message = json['message'];
    status = json['status'];
    createdBy = json['created_by'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
    if (json['carecomment'] != null) {
      carecomment = <CareComment>[];
      json['carecomment'].forEach((v) {
        carecomment!.add(new CareComment.fromMap(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['driver_id'] = this.driverId;
    data['subject'] = this.subject;
    data['message'] = this.message;
    data['status'] = this.status;
    data['created_by'] = this.createdBy;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    if (this.carecomment != null) {
    }
    return data;
  }
}

class CareComment {
  final int id;
  final int careId;
  final int userId;
  final String addedBy;
  final String? status;
  final String comment;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CareComment({
    required this.id,
    required this.careId,
    required this.userId,
    required this.addedBy,
    required this.status,
    required this.comment,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareComment.fromMap(Map<String, dynamic> map) {
    return CareComment(
      id: map['id'] as int,
      careId: map['care_id'] as int,
      userId: map['user_id'] as int,
      addedBy: map['added_by'] as String,
      status: map['status'] as String?,
      comment: map['comment'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] == null
          ? null
          : DateTime.parse(map['updated_at'] as String),
    );
  }
}

class CareCommentRequest {
  final int userId;
  final String addedBy;
  final String careId;
  final String comment;

  CareCommentRequest({
    required this.userId,
    required this.addedBy,
    required this.careId,
    required this.comment,
  });

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'added_by': addedBy,
      'care_id': careId,
      'comment': comment,
    };
  }
}

class CareCommentResponse {
  final bool status;
  final String message;
  final CareComment? data;

  CareCommentResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory CareCommentResponse.fromMap(Map<String, dynamic> map) {
    return CareCommentResponse(
      status: map["status"] as bool,
      message: map["message"] as String,
      data: map["data"] == null ? null : CareComment.fromMap(map["data"]),
    );
  }
}
