class SendMessageModel {
  int? userId;
  String? addedBy;
  String? careId;
  String? comment;

  SendMessageModel({this.userId, this.addedBy, this.careId, this.comment});

  SendMessageModel.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    addedBy = json['added_by'];
    careId = json['care_id'];
    comment = json['comment'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this.userId;
    data['added_by'] = this.addedBy;
    data['care_id'] = this.careId;
    data['comment'] = this.comment;
    return data;
  }
}
