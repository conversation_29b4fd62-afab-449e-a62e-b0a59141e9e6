class CareModel {
  int id;
  String subject;
  String ?message;
  String status;
  int? driver_id;
  int? care_count;
  String? created_at;
  String? updated_at;

  CareModel({
    required this.id,
    this.message,
    required this.subject,
    required this.status,
    this.driver_id,
    this.care_count,
    this.created_at,
    this.updated_at,
  });

  factory CareModel.fromJson(Map<String, dynamic> json) {
    return CareModel(
      id: json['id'],
      subject: json['subject'],
      message: json['message'],
      status: json['status'] ?? "",
      driver_id: json['driver_id'],
      care_count: json['care_count'],
      created_at: json['created_at'],
      updated_at: json['updated_at'],
    );
  }
}