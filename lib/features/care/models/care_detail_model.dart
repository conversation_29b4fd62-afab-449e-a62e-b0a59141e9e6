import 'package:rooo_driver/features/care/models/care_details_response_model.dart';

class CareDetailModel {
  int? id;
  int? driverId;
  String? subject;
  String? message;
  int? status;
  String? createdBy;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  List<CareComment>? carecomment;

  CareDetailModel(
      {this.id,
      this.driverId,
      this.subject,
      this.message,
      this.status,
      this.createdBy,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.carecomment});

  CareDetailModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    driverId = json['driver_id'];
    subject = json['subject'];
    message = json['message'];
    status = json['status'];
    createdBy = json['created_by'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
    if (json['carecomment'] != null) {
      carecomment = <CareComment>[];
      json['carecomment'].forEach((v) {
        carecomment!.add(new CareComment.fromMap(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['driver_id'] = this.driverId;
    data['subject'] = this.subject;
    data['message'] = this.message;
    data['status'] = this.status;
    data['created_by'] = this.createdBy;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    if (this.carecomment != null) {}
    return data;
  }
}
