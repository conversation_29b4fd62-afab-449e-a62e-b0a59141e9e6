import 'package:rooo_driver/features/care/models/care_details_response_model.dart';

class CareReturnMessageResponse {
  String? message;
  CareComment? careComment;
  bool? status;

  CareReturnMessageResponse({this.message, this.careComment, this.status});

  CareReturnMessageResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    careComment =
        json['data'] != null ? new CareComment.fromMap(json['data']) : null;
    status = json['status'];
  }
}

class Complaint {
  int? id;
  int? driverId;
  String? subject;
  String? message;
  int? status;
  String? createdBy;
  String? createdAt;
  String? updatedAt;
  Null deletedAt;

  Complaint(
      {this.id,
      this.driverId,
      this.subject,
      this.message,
      this.status,
      this.createdBy,
      this.createdAt,
      this.updatedAt,
      this.deletedAt});

  Complaint.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    driverId = json['driver_id'];
    subject = json['subject'];
    message = json['message'];
    status = json['status'];
    createdBy = json['created_by'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['driver_id'] = this.driverId;
    data['subject'] = this.subject;
    data['message'] = this.message;
    data['status'] = this.status;
    data['created_by'] = this.createdBy;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    return data;
  }
}
