import 'package:rooo_driver/features/care/models/care_details_response_model.dart';
import 'package:rooo_driver/features/care/models/care_return_message_response.dart';
import 'package:rooo_driver/features/care/models/carelist_response_model.dart';
import 'package:rooo_driver/model/StatusMessageModel.dart';
import 'package:rooo_driver/network/NetworkUtils.dart';

class CareRepository {
  Future<CareResponseModel> getRoooCarePendingListApi(
      {required int page}) async {
    return CareResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('care-pending-list', method: HttpMethod.GET)));
  }

  Future<CareResponseModel> getRoooCareCompletedListApi(
      {required int page}) async {
    return CareResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('care-completed-list',
            method: HttpMethod.GET)));
  }

  Future<CareDetailsResponseModel> getCareDetailApi(
      {required Map request}) async {
    return CareDetailsResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('care-detailed-list',
            method: HttpMethod.POST, request: request)));
  }

  Future<CareReturnMessageResponse> sendCareMessageApi(
      {required Map request}) async {
    return CareReturnMessageResponse.fromJson(await handleResponse(
        await buildHttpResponse('carecomment-store',
            method: HttpMethod.POST, request: request)));
  }

  Future<StatusMessageModel> addCareApi(
      {required Map<dynamic, dynamic> req}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse("care-save",
            request: req, method: HttpMethod.POST)));
  }
}
