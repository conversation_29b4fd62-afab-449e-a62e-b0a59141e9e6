
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:rooo_driver/features/emergency_contacts/screens/emergency_contact_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/model/ContactNumberListModel.dart';
import 'package:rooo_driver/utils/Extensions/Loader.dart';
import 'package:url_launcher/url_launcher.dart';


class SafetyToolKitScreen extends StatefulWidget {
  final int? rideId;
  final int? regionId;

  SafetyToolKitScreen({this.rideId, this.regionId});

  @override
  SafetyToolKitScreenState createState() => SafetyToolKitScreenState();
}

class SafetyToolKitScreenState extends State<SafetyToolKitScreen> {
  List<ContactModel> sosListData = [];
  LatLng? sourceLocation;

  bool sendNotification = false;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    getCurrentUserLocation();
    appStore.setLoading(true);
    await getSosList(regionId: widget.regionId).then((value) {
      appStore.setLoading(false);

      sosListData.addAll(value.data!);
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);
      log("Server error");
    });
  }

  Future<void> getCurrentUserLocation() async {
    final geoPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,forceAndroidLocationManager:true ,);
    sourceLocation = LatLng(geoPosition.latitude, geoPosition.longitude);
  }

  Future<void> adminSosNotify() async {
    sendNotification = false;
    appStore.setLoading(true);
    Map req = {
      "ride_request_id": widget.rideId,
      "latitude": sourceLocation!.latitude,
      "longitude": sourceLocation!.longitude,
    };
    await adminNotify(request: req).then((value) {
      appStore.setLoading(false);
      sendNotification = true;
      setState(() {});
    }).catchError((error) {
      appStore.setLoading(false);

      log("Server error");
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Observer(builder: (context) {
      return Scaffold(
        appBar: RoooAppbar(title: "Safet tool kit"),
        body: Padding(
          padding: screenPadding,
          child: Column(
            children: [
              Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.black,
                        ),
                        padding: EdgeInsets.symmetric(vertical: 20),
                        width: double.infinity,
                        // color: dangerColor,
                        child: CircleAvatar(
                            radius: 20,
                            backgroundColor: Colors.black,
                            child: Icon(Icons.warning_amber,
                                color: Colors.white, size: 40)),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          children: [
                            SizedBox(height: 8),
                            Text(language.useInCaseOfEmergency,
                                ),
                            SizedBox(height: 16),
                            inkWellWidget(
                              onTap: () {
                                launchUrl(Uri.parse('tel: 911'));
                              },
                              child: CustomText(
                                data: "call 911",
                                color: Colors.red,
                                size: 25,
                              ),
                            ),
                            height15,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(language.notifyAdmin,
                                        style:
                                            boldTextStyle(color: Colors.white)),
                                    if (sendNotification) SizedBox(height: 4),
                                    if (sendNotification)
                                      Text(language.notifiedSuccessfully,
          
                                      style: TextStyle(color: Colors.green),
                                       ),
                                  ],
                                ),
                                inkWellWidget(
                                  onTap: () {
                                    adminSosNotify();
                                  },
                                  child: Icon(
                                    Icons.notification_add_outlined,
                                  ),
                                ),
                              ],
                            ),
                            height20,
                            inkWellWidget(
                              onTap: () {
                                closeScreen(context);
          
          
                                GlobalMethods.pushScreen(context: context, screen: EmergencyContactScreen(), screenIdentifier: ScreenIdentifier.EmergencyContactScreen);
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                      language.AddTxt +
                                          " " +
                                          language.emergencyContact,
                                      style: boldTextStyle(color: Colors.white)),
                                  Icon(
                                    Icons.add,
                                    color: Colors.white,
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            sosListData.length==0?
          
                             Text( "No contacts, please add new",style: AppTextStyles.header(),):
                            SizedBox(
                              height: MediaQuery.of(context).size.height * .2,
                              child: ListView.builder(
                                  itemCount: sosListData.length,
                                  physics: AlwaysScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemBuilder: (_, index) {
          
                                    
                                    return Padding(
                                      padding: EdgeInsets.only(top: 8, bottom: 8),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                  sosListData[index]
                                                      .title
                                                      .validate(),
                                                  style: boldTextStyle(
                                                      color: Colors.white)),
                                              SizedBox(height: 4),
                                              Text(
                                                  sosListData[index]
                                                      .contactNumber
                                                      .validate(),
                                                  style: boldTextStyle(
                                                      color: Colors.white)),
                                            ],
                                          ),
                                          inkWellWidget(
                                            onTap: () {
                                              launchUrl(
                                                  Uri.parse(
                                                      'tel:${sosListData[index].contactNumber}'),
                                                  mode: LaunchMode
                                                      .externalApplication);
                                            },
                                            child: Icon(
                                              Icons.call,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: appStore.isLoading,
                  child: IntrinsicHeight(
                    child: Loader(),
                  ),
                ),
              ],
            ),
            ],
          ),
        ),
      );
    });
  }
}
