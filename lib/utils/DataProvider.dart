import 'package:flutter/material.dart';

import '../model/LanguageDataModel.dart';
import '../model/TextModel.dart';

List<TexIModel> getBookList() {
  List<TexIModel> list = [];
  list.add(TexIModel(title: 'Home', iconData: Icons.home));
  list.add(TexIModel(title: 'Work', iconData: Icons.work));
  list.add(TexIModel(title: 'Recently', iconData: Icons.history));
  return list;
}

List<TexIModel> getCarList() {
  List<TexIModel> list = [];
  list.add(TexIModel(title: 'Premium', img: 'images/ic_Premium.png', subTitle: '75'));
  list.add(TexIModel(title: 'Economy', img: 'images/ic_economy.png', subTitle: '25'));
  list.add(TexIModel(title: 'Standard', img: 'images/ic_standard.png', subTitle: '45'));
  return list;
}

List<LanguageDataModel> languageList() {
  return [
    LanguageDataModel(id: 1, name: 'English', subTitle: 'English', languageCode: 'en', fullLanguageCode: 'en-US', flag: 'images/flag/ic_us.png'),
    LanguageDataModel(id: 2, name: 'Hindi', subTitle: 'हिंदी', languageCode: 'hi', fullLanguageCode: 'hi-IN', flag: 'images/flag/ic_india.png'),
    LanguageDataModel(id: 3, name: 'French', subTitle: 'Français', languageCode: 'fr', fullLanguageCode: 'fr-FR',
        flag: 'images/flag/ic_france.png'),
    LanguageDataModel(id: 4, name: 'Punjabi', subTitle: 'ਪੰਜਾਬੀ', languageCode: 'pa', fullLanguageCode: 'pa-IN',
        flag: 'images/flag/ic_india.png'),
  ];
}