name: flutter_callkit_incoming_yoer
description: Flutter Callkit Incoming to show callkit screen in your Flutter app.
version: 2.0.4+1
homepage: https://github.com/hiennguyen92/flutter_callkit_incoming
repository: https://github.com/hiennguyen92/flutter_callkit_incoming
issue_tracker: https://github.com/hiennguyen92/flutter_callkit_incoming/issues

false_secrets:
  - /example/**

environment:
  sdk: ">=2.18.0 <4.0.0"
  flutter: ">=1.20.0"


dependencies:
  flutter:
    sdk: flutter
  json_annotation: ^4.8.0

dev_dependencies:
  build_runner: ^2.4.4
  flutter_lints: ^2.0.1
  flutter_test:
    sdk: flutter
  json_serializable: ^6.6.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.hiennv.flutter_callkit_incoming
        pluginClass: FlutterCallkitIncomingPlugin
      ios:
        pluginClass: FlutterCallkitIncomingPlugin

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
