group 'com.example.flutter_callkit_incoming'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '2.0.20'
    repositories {
        google()
        jcenter()
    }

    dependencies {
        //classpath 'com.android.tools.build:gradle:4.1.0'
        //classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        
        classpath 'com.android.tools.build:gradle:7.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdk 34

     compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    if (project.android.hasProperty("namespace")) {
        namespace "com.hiennv.flutter_callkit_incoming"
    }
    
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdk 23
        targetSdk 34

        consumerProguardFiles 'consumer-rules.pro'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.core:core-ktx:1.10.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.11.1'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.11.1'
}
