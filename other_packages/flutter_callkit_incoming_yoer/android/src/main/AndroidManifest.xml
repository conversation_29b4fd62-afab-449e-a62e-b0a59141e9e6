<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.hiennv.flutter_callkit_incoming">

    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS"/>

    <application>

        <activity
            android:name="com.hiennv.flutter_callkit_incoming.CallkitIncomingActivity"
            android:taskAffinity="com.hiennv.flutter_callkit_incoming.INCOMING_CALL_AFFINITY"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:turnScreenOn="true"
            android:configChanges="orientation"
            android:exported="true"
            android:theme="@style/CallkitIncomingTheme">
            <intent-filter>
                <action android:name="${applicationId}.com.hiennv.flutter_callkit_incoming.ACTION_CALL_INCOMING" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:exported="false"
            android:theme="@style/TranslucentTheme"
            android:name="com.hiennv.flutter_callkit_incoming.TransparentActivity"/>

        <receiver
            android:name="com.hiennv.flutter_callkit_incoming.CallkitIncomingBroadcastReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="${applicationId}.com.hiennv.flutter_callkit_incoming.ACTION_CALL_INCOMING" />
                <action android:name="${applicationId}.com.hiennv.flutter_callkit_incoming.ACTION_CALL_ACCEPT" />
                <action android:name="${applicationId}.com.hiennv.flutter_callkit_incoming.ACTION_CALL_DECLINE" />
                <action android:name="${applicationId}.com.hiennv.flutter_callkit_incoming.ACTION_CALL_ENDED" />
                <action android:name="${applicationId}.com.hiennv.flutter_callkit_incoming.ACTION_CALL_TIMEOUT" />
            </intent-filter>
        </receiver>

        <service
            android:enabled="true"
            android:exported="true"
            android:name="com.hiennv.flutter_callkit_incoming.CallkitSoundPlayerService"/>

    </application>
</manifest>
