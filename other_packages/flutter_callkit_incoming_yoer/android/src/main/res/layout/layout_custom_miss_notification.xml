<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvNameCaller"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Compat.Notification.Title"
                android:textSize="@dimen/size_text_action" />


            <TextView
                android:id="@+id/tvNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Compat.Notification.Line2"
                android:textSize="@dimen/size_text_action" />

        </LinearLayout>


        <ImageView
            android:id="@+id/ivAvatar"
            android:layout_width="@dimen/base_margin_x4"
            android:layout_height="@dimen/base_margin_x4"
            android:scaleType="centerCrop"
            android:visibility="visible"
            android:src="@drawable/ic_default_avatar"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_margin"
        android:orientation="horizontal">


        <LinearLayout
            android:id="@+id/llCallback"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/rounded_button_accept"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCallback"
                style="@style/Widget.Compat.NotificationActionText"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/base_margin_x4"
                android:gravity="center"
                android:text="@string/text_call_back"
                android:textAllCaps="false"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </LinearLayout>


</LinearLayout>

    <!--style="@style/Widget.Compat.NotificationActionText"-->