<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/ivBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#0955fa"
        android:scaleType="centerCrop"
        tools:ignore="ContentDescription" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false"
        android:orientation="vertical">


        <com.hiennv.flutter_callkit_incoming.widgets.RippleRelativeLayout
            android:id="@+id/llBackgroundAnimation"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/base_margin_revert_x5"
            android:layout_weight="1"
            android:fitsSystemWindows="false">

            <ImageView
                android:id="@+id/ivLogo"
                android:layout_width="match_parent"
                android:layout_height="@dimen/base_margin_x2_5"
                android:layout_above="@id/ivAvatar"
                android:layout_centerHorizontal="true"
                android:adjustViewBounds="true"
                android:visibility="invisible"
                android:scaleType="fitCenter"
                android:layout_marginBottom="@dimen/base_margin_x5"
                android:src="@drawable/ic_logo"
                tools:ignore="ContentDescription" />


            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/ivAvatar"
                android:layout_width="@dimen/size_avatar"
                android:layout_height="@dimen/size_avatar"
                android:visibility="invisible"
                android:layout_centerInParent="true"
                android:src="@drawable/ic_default_avatar"
                app:civ_border_color="#80ffffff"
                app:civ_border_width="1dp" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/ivAvatar"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/base_margin_x2"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvNameCaller"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:autoSizeMaxTextSize="@dimen/size_text_name"
                    android:autoSizeMinTextSize="12sp"
                    android:autoSizeStepGranularity="2sp"
                    android:autoSizeTextType="uniform"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/size_text_name"
                    app:autoSizeMaxTextSize="@dimen/size_text_name"
                    app:autoSizeMinTextSize="12sp"
                    app:autoSizeStepGranularity="2sp"
                    app:autoSizeTextType="uniform"
                    tools:ignore="MissingPrefix"
                    tools:targetApi="o"
                    tools:text="Caller Name" />

                <TextView
                    android:id="@+id/tvNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/action_text"
                    android:textSize="@dimen/size_text_action"
                    tools:text="Some info" />

            </LinearLayout>

        </com.hiennv.flutter_callkit_incoming.widgets.RippleRelativeLayout>


        <LinearLayout
            android:id="@+id/llAction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginBottom="0dp"
            android:fitsSystemWindows="true"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                tools:ignore="UseCompoundDrawables">

                <com.hiennv.flutter_callkit_incoming.widgets.RippleRelativeLayout
                    android:layout_width="@dimen/size_button_x2_5"
                    android:layout_height="@dimen/size_button_x2_5"
                    app:ripple_amount="4"
                    app:ripple_radius="@dimen/base_margin_x1_5"
                    app:ripple_scale="4.5">

                    <ImageView
                        android:id="@+id/ivDeclineCall"
                        android:layout_width="@dimen/size_button"
                        android:layout_height="@dimen/size_button"
                        android:layout_centerInParent="true"
                        android:background="@drawable/bg_button_decline"
                        android:padding="@dimen/base_margin_x1_5"
                        android:src="@drawable/ic_decline"
                        tools:ignore="ContentDescription" />

                    <TextView
                        android:id="@+id/tvDecline"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/ivDeclineCall"
                        android:layout_marginBottom="@dimen/base_margin"
                        android:gravity="center"
                        android:text="@string/text_decline"
                        android:textColor="@color/action_text"
                        android:textSize="@dimen/size_text_action" />

                </com.hiennv.flutter_callkit_incoming.widgets.RippleRelativeLayout>

            </LinearLayout>

            <Space
                android:layout_width="@dimen/base_margin_x5"
                android:layout_height="0dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:orientation="vertical"
                tools:ignore="UseCompoundDrawables">


                <com.hiennv.flutter_callkit_incoming.widgets.RippleRelativeLayout
                    android:layout_width="@dimen/size_button_x2_5"
                    android:layout_height="@dimen/size_button_x2_5"
                    app:ripple_amount="4"
                    app:ripple_radius="@dimen/base_margin_x1_5"
                    app:ripple_scale="4.5">


                    <ImageView
                        android:id="@+id/ivAcceptCall"
                        android:layout_width="@dimen/size_button"
                        android:layout_height="@dimen/size_button"
                        android:layout_centerInParent="true"
                        android:background="@drawable/bg_button_accept"
                        android:padding="@dimen/base_margin_x1_5"
                        android:src="@drawable/ic_accept"
                        tools:ignore="ContentDescription" />

                    <TextView
                        android:id="@+id/tvAccept"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/ivAcceptCall"
                        android:layout_marginBottom="@dimen/base_margin"
                        android:gravity="center"
                        android:text="@string/text_accept"
                        android:textColor="@color/action_text"
                        android:textSize="@dimen/size_text_action" />


                </com.hiennv.flutter_callkit_incoming.widgets.RippleRelativeLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>