<?xml version="1.0" encoding="utf-8"?>
<resources>


    <style name="CallkitIncomingTheme" parent="@style/Theme.AppCompat">

        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>

    </style>

    <style name="DialogTheme" parent="@style/Theme.AppCompat.Light.Dialog.Alert">

    </style>


    <style name="TranslucentTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:background">@android:color/transparent</item>
        <item name="background">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowNoDisplay">true</item>
    </style>

</resources>